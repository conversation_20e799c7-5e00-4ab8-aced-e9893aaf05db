package com.foshan.form;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.boss.IBossRes;

import io.swagger.annotations.ApiModel;

@ApiModel(value = "获取审核列表请求对象(GetAuditProductSpecificationListRes)")
@JsonInclude(Include.NON_NULL)
public class BossAccountInfo implements IBossRes {
	private String addr;
	private Integer areaid;
	private String cardno;
	private String cardtype;
	private String city;
	private String custid;
	private String custname;
	private String houseid;
	private String maintaindev;
	private String mobile;
	private String nettype;
	private String patchid;
	private String pgroupid;
	private String pgroupname;
	private String phone;
	private String status;	
	private String message;

	public BossAccountInfo() {
		super();
		// TODO 自动生成的构造函数存根
	}

	public BossAccountInfo(String addr, Integer areaid, String cardno, String cardtype, String city, String custid,
			String custname, String houseid, String maintaindev, String mobile, String nettype, String patchid,
			String pgroupid, String pgroupname, String phone, String status, String message) {
		super();
		this.addr = addr;
		this.areaid = areaid;
		this.cardno = cardno;
		this.cardtype = cardtype;
		this.city = city;
		this.custid = custid;
		this.custname = custname;
		this.houseid = houseid;
		this.maintaindev = maintaindev;
		this.mobile = mobile;
		this.nettype = nettype;
		this.patchid = patchid;
		this.pgroupid = pgroupid;
		this.pgroupname = pgroupname;
		this.phone = phone;
		this.status = status;
		this.message = message;
	}

	public String getAddr() {
		return addr;
	}

	public void setAddr(String addr) {
		this.addr = addr;
	}

	public Integer getAreaid() {
		return areaid;
	}

	public void setAreaid(Integer areaid) {
		this.areaid = areaid;
	}

	public String getCardno() {
		return cardno;
	}

	public void setCardno(String cardno) {
		this.cardno = cardno;
	}

	public String getCardtype() {
		return cardtype;
	}

	public void setCardtype(String cardtype) {
		this.cardtype = cardtype;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCustid() {
		return custid;
	}

	public void setCustid(String custid) {
		this.custid = custid;
	}

	public String getCustname() {
		return custname;
	}

	public void setCustname(String custname) {
		this.custname = custname;
	}

	public String getHouseid() {
		return houseid;
	}

	public void setHouseid(String houseid) {
		this.houseid = houseid;
	}

	public String getMaintaindev() {
		return maintaindev;
	}

	public void setMaintaindev(String maintaindev) {
		this.maintaindev = maintaindev;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getNettype() {
		return nettype;
	}

	public void setNettype(String nettype) {
		this.nettype = nettype;
	}

	public String getPatchid() {
		return patchid;
	}

	public void setPatchid(String patchid) {
		this.patchid = patchid;
	}

	public String getPgroupid() {
		return pgroupid;
	}

	public void setPgroupid(String pgroupid) {
		this.pgroupid = pgroupid;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getPgroupname() {
		return pgroupname;
	}

	public void setPgroupname(String pgroupname) {
		this.pgroupname = pgroupname;
	}

	@Override
	public String toString() {
		return "BossAccountInfo [addr=" + addr + ", areaid=" + areaid + ", cardno=" + cardno + ", cardtype=" + cardtype
				+ ", city=" + city + ", custid=" + custid + ", custname=" + custname + ", houseid=" + houseid
				+ ", maintaindev=" + maintaindev + ", mobile=" + mobile + ", nettype=" + nettype + ", patchid="
				+ patchid + ", pgroupid=" + pgroupid + ", pgroupname=" + pgroupname + ", phone=" + phone + ", status="
				+ status + ", message=" + message + "]";
	}

}
