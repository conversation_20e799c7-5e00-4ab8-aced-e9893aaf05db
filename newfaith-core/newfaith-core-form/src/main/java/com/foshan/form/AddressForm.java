package com.foshan.form;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
@ApiModel(value="地址对象(AddressForm)")
@JsonInclude(Include.NON_NULL)
public class AddressForm implements IForm{

	private static final long serialVersionUID = -6132375744014885892L;
	@ApiModelProperty(value = "地址值",example="1")
	private Integer value;
	@ApiModelProperty(value = "地址名称")
	private String text;
	@ApiModelProperty(value = "行政级别： 0国家 1省 2市3区县4镇5街道",example="1")
	private Integer level;
	@ApiModelProperty(value = "父级行政地址value",example="1")
	private Integer parentValue;
	@ApiModelProperty(value = "地址列表")
	private List<AddressForm> children = new ArrayList<AddressForm>();
	public AddressForm() {
		super();
		// TODO Auto-generated constructor stub
	}

	public AddressForm(Integer value, String text,Integer level,Integer parentValue) {
		super();
		this.value = value;
		this.text = text;
		this.level = level;
		this.parentValue =  parentValue;
	}
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

	public Integer getValue() {
		return value;
	}

	public void setValue(Integer value) {
		this.value = value;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

	public Integer getLevel() {
		return level;
	}

	public void setLevel(Integer level) {
		this.level = level;
	}

	public Integer getParentValue() {
		return parentValue;
	}

	public void setParentValue(Integer parentValue) {
		this.parentValue = parentValue;
	}

	public List<AddressForm> getChildren() {
		return children;
	}

	public void setChildren(List<AddressForm> children) {
		this.children = children;
	}

}
