package com.foshan.form;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@ApiModel(value = "积分对象(PointsRealForm)")
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class PointsRealForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = 486338289520848904L;
	private Integer pointsRealId;
	private Integer memberId;
	private String smartcardId;
	private String userCode;
	private String phone;
	private Integer serviceId;
	private Integer deviceType;
	private Integer points;
	private String pointsTime;
	private Integer pointsYear;
	private Integer pointsMonth;
	private Integer pointsDay;
	private Integer pointsType;
	private String pointsDetail;

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
