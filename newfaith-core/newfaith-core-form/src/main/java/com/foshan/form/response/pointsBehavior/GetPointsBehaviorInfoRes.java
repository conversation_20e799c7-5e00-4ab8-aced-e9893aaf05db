package com.foshan.form.response.pointsBehavior;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.PointsBehaviorForm;
import com.foshan.form.PointsPoolForm;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;

@ApiModel(value = "获取积分行为信息返回对象（GetPointsBehaviorInfoRes）")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetPointsBehaviorInfoRes extends BaseResponse {

    private static final long serialVersionUID = -7906345765152667737L;

    public GetPointsBehaviorInfoRes() {
        super();
    }

    public GetPointsBehaviorInfoRes(String ret, String retInfo) {
        super(ret, retInfo);
    }

    private PointsBehaviorForm pointsBehaviorForm;

    public PointsBehaviorForm getPointsBehaviorForm() {
        return pointsBehaviorForm;
    }

    public void setPointsBehaviorForm(PointsBehaviorForm pointsBehaviorForm) {
        this.pointsBehaviorForm = pointsBehaviorForm;
    }
}
