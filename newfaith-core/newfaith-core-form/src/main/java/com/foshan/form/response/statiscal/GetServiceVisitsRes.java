package com.foshan.form.response.statiscal;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.ServiceForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取业务访问量返回对象")
@JsonInclude(Include.NON_NULL)
public class GetServiceVisitsRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 2785943757612838183L;
	@ApiModelProperty(value = "业务列表")
	List<ServiceForm> serviceList = new ArrayList<ServiceForm>();

	
}
