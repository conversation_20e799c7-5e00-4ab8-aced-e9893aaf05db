package com.foshan.form;



import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="区域对象(RegionForm)")
@JsonInclude(Include.NON_NULL)
public class RegionVisitForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2455081831063551152L;
	@ApiModelProperty(value = "区域名称")
	private String regionName;
	@ApiModelProperty(value = "区域访问量",example="1")
	private Integer regionVisit;

	
	public RegionVisitForm(String regionName, Integer regionVisit) {
		super();
		this.regionName = regionName;
		this.regionVisit = regionVisit;
	}

	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
