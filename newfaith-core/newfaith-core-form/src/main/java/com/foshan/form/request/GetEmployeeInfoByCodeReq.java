package com.foshan.form.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="媒资请求参数(AssetReq)")
public class GetEmployeeInfoByCodeReq extends BasePageRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = -2370958733302274617L;
	@ApiModelProperty(name="微信code")
	private String code;
	@ApiModelProperty(name="2:企业号微信,默认值，3:微信小程序",example="2")
	private Integer type = 2;


	
}
