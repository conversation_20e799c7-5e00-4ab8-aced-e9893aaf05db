package com.foshan.form;


import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="业务对象(ServiceForm)")
@JsonInclude(Include.NON_NULL)
public class ServiceForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = 3716644072631380749L;
	@ApiModelProperty(value = "业务Id",example="1")
	private Integer serviceId;
	@ApiModelProperty(value = "业务编码")
	private String serviceCode;
	@ApiModelProperty(value = "业务名称")
	private String serviceName;
	@ApiModelProperty(value = "业务标识，暂时无用，下个版本计划删除")
	private String serviceGroup;
	@ApiModelProperty(value = "业务状态 0--无效 1--有效",example="1")
	private Integer serviceState;
	@ApiModelProperty(value = "业务主栏目Id",example="1")
	private Integer serviceColumnId;
	@ApiModelProperty(value = "业务创建时间",example="2019-01-01 00:00:00")
	private String createTime;
	@ApiModelProperty(value = "父级业务Id",example="1")
	private Integer parentServiceId;
	@ApiModelProperty(value = "业务总访问量",example="1")
	private Integer totalVisits;
    @ApiModelProperty(value = "网关盒子访问地址")
    private String ottUrl;
    @ApiModelProperty(value = "机顶盒访问地址")
    private String stbUrl;
	@ApiModelProperty(value = "业务开始时间",example="2019-01-01 00:00:00")
	private String startTime;
	@ApiModelProperty(value = "业务结束时间",example="2019-01-01 00:00:00")
	private String endTime;
	@ApiModelProperty(value = "业务描述")
	private String comment;
	@ApiModelProperty(value = "子业务列表")
	protected List<ServiceForm> subServiceList = new ArrayList<ServiceForm>();
	@ApiModelProperty(value = "业务栏目列表")
	protected List<ColumnForm> subColumnList = new ArrayList<ColumnForm>();

	
	public ServiceForm(Integer serviceId, String serviceCode, String serviceName, String serviceGroup,
			Integer serviceState, Integer serviceColumnId, String createTime, Integer parentServiceId) {
		super();
		this.serviceId = serviceId;
		this.serviceCode = serviceCode;
		this.serviceName = serviceName;
		this.serviceGroup = serviceGroup;
		this.serviceState = serviceState;
		this.serviceColumnId = serviceColumnId;
		this.createTime = createTime;
		this.parentServiceId = parentServiceId;
	}

	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
