package com.foshan.form.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="文章请求参数(ArticleReq)")
public class ArticleReq extends AssetReq {
	
	/**
	 * 
	 */
	public static final long serialVersionUID = 978023732674079740L;
	@ApiModelProperty(value = "文章Id",example="1")
	public Integer articleId;
	@ApiModelProperty(value = "标题")
	public String title;
	@ApiModelProperty(value = "文章子标题")
	public String subtitle;
	@ApiModelProperty(value = "文章内容")
	public String content;
	@ApiModelProperty(value = "文章发布者")
	public String userName;
	@ApiModelProperty(value = "是否推荐 0：不推荐，1：推荐", example="1")
	public Integer isRecommend;
	@ApiModelProperty(value = "排序值", example="1")
	public Integer orderNumber;
	@ApiModelProperty(value = "审核状态 0:待审核 1：正常 2：审核不通过", example="1")
	public Integer status;
	@ApiModelProperty(value = "审核意见")
	public String idea;
	@ApiModelProperty(value = "内容类型：0文章、1点播媒资、2、直播、3外部视频、4外部链接链接、5内部导航", example="1")
	public Integer contentType;
	@ApiModelProperty(value = "内容URL")
	public String contentUrl;
	@ApiModelProperty(value = "内容来源或出处")
	public String source;
	@ApiModelProperty(value = "所关联内容资源的名称")
	public String resourceName;
	@ApiModelProperty(value = "创建时间")
	public String createTime;
	@ApiModelProperty(value = "最后修改时间")
	public String lastModifyTime;
	@ApiModelProperty(value = "媒资列表")
	public String assetList;
	@ApiModelProperty(value = "栏目id")
	public String columnId;

}
