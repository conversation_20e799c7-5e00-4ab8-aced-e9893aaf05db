package com.foshan.form.response.assetSpec;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.AssetSpecForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取媒资规格列表返回对象(AddAssetSpecRes)")
@JsonInclude(Include.NON_NULL)
public class GetAssetSpecListRes extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -500942693587173834L;
	@ApiModelProperty(value = "媒资规格列表")
	private List<AssetSpecForm> assetSpecFormList = new ArrayList<AssetSpecForm>();

	public GetAssetSpecListRes(int currentPage, int pageSize, int total, int totalResult) {
		super(currentPage, pageSize, total, totalResult);
		// TODO Auto-generated constructor stub
	}

}
