package com.foshan.form;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="微信参数对象(WxServiceForm)")
@JsonInclude(Include.NON_NULL)
public class WxServiceForm implements IForm {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -8411849600814461886L;
	
	@ApiModelProperty(value = "ID",example="1")
	private Integer wxServiceId;
	@ApiModelProperty(value = "服务名称")
	private String serviceName;
	@ApiModelProperty(value = "")
	private String serviceCode;
	@ApiModelProperty(value = "agentId")
	private String agentId;
	@ApiModelProperty(value = "appId")
	private String appId;
	@ApiModelProperty(value = "appSecret")
	private String appSecret;
	@ApiModelProperty(value = "部门ID",example="1")
	private Integer departmentId;
	@ApiModelProperty(value = "服务类型 0：微信小程序；1：微信公众号；2：微信企业号；3：微信企业小程",example="1")
	private Integer serviceType;
	@ApiModelProperty(value = "有郊期,秒",example="1")
	private Integer expires;
	@ApiModelProperty(value = "token")
	private String accessToken;	
	@ApiModelProperty(value = "获取token时间")
	private String getTokenTime;	
	
	

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
