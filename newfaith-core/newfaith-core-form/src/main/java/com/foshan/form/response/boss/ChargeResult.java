package com.foshan.form.response.boss;

public class ChargeResult implements IBossRes {
	@Override
	public String toString() {
		return "ChargeResult [custid=" + custid + ", orderid=" + orderid + ", serialno=" + serialno + ", servid="
				+ servid + ", requestid=" + requestid + ", status=" + status + ", message=" + message + "]";
	}

	private String custid;
	private String orderid;
	private String serialno;
	private String servid;
	private String requestid;
	private String status;
	private String message;

	public ChargeResult() {
		super();
		// TODO 自动生成的构造函数存根
	}

	public ChargeResult(String custid, String orderid, String serialno, String servid, String requestid, String status,
			String message) {
		super();
		this.custid = custid;
		this.orderid = orderid;
		this.serialno = serialno;
		this.servid = servid;
		this.requestid = requestid;
		this.status = status;
		this.message = message;
	}

	public String getCustid() {
		return custid;
	}

	public void setCustid(String custid) {
		this.custid = custid;
	}

	public String getOrderid() {
		return orderid;
	}

	public void setOrderid(String orderid) {
		this.orderid = orderid;
	}

	public String getSerialno() {
		return serialno;
	}

	public void setSerialno(String serialno) {
		this.serialno = serialno;
	}

	public String getServid() {
		return servid;
	}

	public void setServid(String servid) {
		this.servid = servid;
	}

	public String getRequestid() {
		return requestid;
	}

	public void setRequestid(String requestid) {
		this.requestid = requestid;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

}
