package com.foshan.form;

import java.util.ArrayList;
import java.util.List;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="文章对象(ArticleForm)")
@JsonInclude(Include.NON_NULL)
public class ArticleForm implements IForm {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -6745185486699430984L;
	@ApiModelProperty(value = "文章Id",example="1")
	private Integer articleId;
	@ApiModelProperty(value = "文章标题")
	private String title;
	@ApiModelProperty(value = "文章子标题")
	private String subtitle;
	@ApiModelProperty(value = "文章内容")
	private String content;
	@ApiModelProperty(value = "文章发布者")
	private String userName;
	@ApiModelProperty(value = "是否推荐 0：不推荐，1：推荐",example="1")
	private Integer isRecommend;
	@ApiModelProperty(value = "排序值",example="1")
	private Integer orderNumber;
	@ApiModelProperty(value = "审核状态 0:待审核 1：正常 2：审核不通过",example="1")
	private Integer status;
	@ApiModelProperty(value = "审核意见")
	private String idea;
	@ApiModelProperty(value = "内容类型：0文章、1点播媒资、2、直播、3外部视频、4外部链接链接、5内部导航",example="1")
	private Integer contentType;
	@ApiModelProperty(value = "当contentType=0时，忽略该字段; 当contentType=1时，BO:providerId/assetId或IP:providerId/assetId/programId;当contentType=2时，DVB:timestamp/channelId直播 timestamp为开始播入时间;当contentType=4时,外部视频时，该字段 为外视频链接的URL,视频格式为HLS、MP4; 当contentType=5外部链接，该字段为URL地址;当contentType=6内部导航")
	private String contentUrl;
	@ApiModelProperty(value = "内容来源或出处")
	private String source;
	@ApiModelProperty(value = "所关联内容资源的名称")
	private String resourceName;
	@ApiModelProperty(value = "创建时间")
	private String createTime;
	@ApiModelProperty(value = "最后修改时间")
	private String lastModifyTime;
	@ApiModelProperty(value = "文章发布时间")
	private String publishTime;
	@ApiModelProperty(value = "状态 0--无效数据  1--有效数据",example="1")
	protected Integer state;
	private List<ArticleAssetForm> articleAssetList = new ArrayList<ArticleAssetForm>();
	@ApiModelProperty(value = "可见区域")
	private List<RegionForm> regionList = new ArrayList<RegionForm>();
	

	public ArticleForm(Integer articleId,String title,String subtitle,String content,String userName,
			Integer isRecommend,Integer orderNumber,Integer status,String idea,Integer contentType,
			String contentUrl,String source,String resourceName,String createTime,String lastModifyTime,
			String publishTime,Integer state,List<ArticleAssetForm> articleAssetList) {
		this.articleId = articleId;
		this.title = title;
		this.subtitle = subtitle;
		this.content = content;
		this.userName = userName;
		this.isRecommend = isRecommend;
		this.orderNumber = orderNumber;
		this.status = status;
		this.idea = idea;
		this.contentType = contentType;
		this.contentUrl = contentUrl;
		this.source = source;
		this.resourceName = resourceName;
		this.createTime = createTime;
		this.lastModifyTime = lastModifyTime;
		this.publishTime = publishTime;
		this.state = state;
		this.articleAssetList = articleAssetList;
	}
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}



}
