package com.foshan.form;




import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="用户对象(UserForm)")
@JsonInclude(Include.NON_NULL)
public class UserForm implements IForm {

	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1530674507761689360L;
	@ApiModelProperty(value = "用户Id",example="1")
	private Integer userId;
	@ApiModelProperty(value = "用户编号")
	private String userCode;
	@ApiModelProperty(value = "登录名称")
	private String userName;
	@ApiModelProperty(value = "密码")
	private String userPassword;
	@ApiModelProperty(value = "用户姓名")
	private String name;
	@ApiModelProperty(value = "用户电话")
	private String phone;
	@ApiModelProperty(value = "用户状态 0--无效 1--有效")
	private Integer userState;
	@ApiModelProperty(value = "用户头像")
	private String userImage;
	@ApiModelProperty(value = "创建时间")
	private String createTime;
	@ApiModelProperty(value = "最近修改时间")
	private String updateTime;
	@ApiModelProperty(value = "最后修改密码时间")
	private String lastPwdModifyTime;
	@ApiModelProperty(value = "所属角色列表")
	private List<RoleForm> roleList =  new ArrayList<RoleForm>();
	@ApiModelProperty(value = "所属区域列表")
	private List<RegionForm> regionList =  new ArrayList<RegionForm>();

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

	@Override
	public String toString() {
		return "UserForm [userId=" + userId + ", userCode=" + userCode + ", userName=" + userName + ", userPassword="
				+ userPassword + ", name=" + name + ", phone=" + phone + ", userState=" + userState + ", userImage="
				+ userImage + ", createTime=" + createTime + ", updateTime=" + updateTime + ", roleList=" + roleList
				+ ", regionList=" + regionList + "]";
	}
}
