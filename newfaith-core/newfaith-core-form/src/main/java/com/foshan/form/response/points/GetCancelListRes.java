package com.foshan.form.response.points;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.AccountExchangeForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取奖品核销列表返回对象(GetCancelListRes)")
@JsonInclude(Include.NON_NULL)
public class GetCancelListRes extends BasePageResponse {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -1746895866717097132L;
	@ApiModelProperty(value = "兑换奖品列表")
	private List<AccountExchangeForm> exchangeList = new ArrayList<AccountExchangeForm>();

	

	public GetCancelListRes(int currentPage, int pageSize, int total, int totalResult) {
		super(currentPage, pageSize, total, totalResult);
		// TODO Auto-generated constructor stub
	}

	public GetCancelListRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	
	
}
