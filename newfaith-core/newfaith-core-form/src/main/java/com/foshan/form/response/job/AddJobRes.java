package com.foshan.form.response.job;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "新增任务组返回对象(AddJobRes)")
@JsonInclude(Include.NON_NULL)
public class AddJobRes extends BaseResponse{
	/**
	 * 
	 */
	private static final long serialVersionUID = -1744364850057529849L;
	@ApiModelProperty(value = "任务组Id",example="1")
	private Integer jobGroupId;
	@ApiModelProperty(value = "任务Id",example="1")
	private Integer jobId;
	@ApiModelProperty(value = "任务组名称")
	private String jobGroupName;
	@ApiModelProperty(value = "任务组信息")
	private String jobGroupInfo;
	@ApiModelProperty(value = "任务名称")
	private String jobName;
	@ApiModelProperty(value = "任务说明")
	private String jobInfo;
	@ApiModelProperty(value = "任务执行类")
	private String jobClass;
	@ApiModelProperty(value = "任务执行方法")
	private String jobMethod;
	@ApiModelProperty(value = "任务状态 0--无效 1--有效",example="1")
	private Integer jobState;
	@ApiModelProperty(value = "任务cron表达式")
	private String jobCron;
	
	public AddJobRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}
	public AddJobRes(Integer jobGroupId, Integer jobId, String jobGroupName, String jobGroupInfo, String jobName,
			String jobInfo, String jobClass, String jobMethod, Integer jobState, String jobCron) {
		super();
		this.jobGroupId = jobGroupId;
		this.jobId = jobId;
		this.jobGroupName = jobGroupName;
		this.jobGroupInfo = jobGroupInfo;
		this.jobName = jobName;
		this.jobInfo = jobInfo;
		this.jobClass = jobClass;
		this.jobMethod = jobMethod;
		this.jobState = jobState;
		this.jobCron = jobCron;
	}
	
	@Override
	public String toString() {
		return "AddJobRes [jobGroupId=" + jobGroupId + ", jobId=" + jobId + ", jobGroupName=" + jobGroupName
				+ ", jobGroupInfo=" + jobGroupInfo + ", jobName=" + jobName + ", jobInfo=" + jobInfo + ", jobClass="
				+ jobClass + ", jobMethod=" + jobMethod + ", jobState=" + jobState + ", jobCron=" + jobCron + "]";
	}
	
	
}
