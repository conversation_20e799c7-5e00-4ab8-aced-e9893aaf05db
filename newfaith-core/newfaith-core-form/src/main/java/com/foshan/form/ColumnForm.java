package com.foshan.form;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="基础栏目对象(ColumnForm)")
@JsonInclude(Include.NON_NULL)
public class ColumnForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2370946955805579071L;
	@ApiModelProperty(value = "栏目Id",example="1")
	private Integer columnId;
	@ApiModelProperty(value = "业务Id",example="1")
	private Integer serviceId;
	@ApiModelProperty(value = "父栏目Id",example="1")
	private Integer parentColumnId;
	@ApiModelProperty(value = "栏目编号")
	private String columnCode;
	@ApiModelProperty(value = "栏目名称")
	private String columnName;
	@ApiModelProperty(value = "栏目类型 ：0--图文 1-音视频 2--投票  3--问卷 4--排行榜 5--抽奖 6--中奖结果 7--商城栏目  8--商城推荐栏目",example="1")
	private Integer columnType;
	@ApiModelProperty(value = "栏目层级",example="1")
	private Integer columnLevel;
	@ApiModelProperty(value = "栏目信息")
	private String columnInfo;
	@ApiModelProperty(value = "栏目上架状态 0--无效 1--有效",example="1")
	private Integer columnState;
	@ApiModelProperty(value = "推荐标识：0--不推荐  1--推荐",example="1")
	private Integer commendFlag;
	@ApiModelProperty(value = "映射第三方系统栏目Id")
	private String mappingFolderId;
	@ApiModelProperty(value = "映射第三方系统栏目名称")
	private String mappingFolderName;
	@ApiModelProperty(value = "映射外部系统：1--高清互动BO平台 2--IP视频平台",example="1")
	private Integer mappingSystem;
	@ApiModelProperty(value = "是否公共栏目 0--不是  1--是",example="1")
	private Integer isGlobal;
	@ApiModelProperty(value = "栏目数据状态：0--无效 1--有效",example="1")
	private Integer state;
	@ApiModelProperty(value = "栏目排序",example="1")
	private Integer orders;
	@ApiModelProperty(value = "显示目标终端： 0-电视端 1-移动端 2-通用",example="1")
	private Integer targetType;
	@ApiModelProperty(value = "栏目总访问量",example="1")
	private Integer totalVisits;
	@ApiModelProperty(value = "栏目路径")
	private String columnPath;
	@ApiModelProperty(value = "栏目是否可操作：false--不可操作 true--可操作")
	private Boolean isOperation;
	@ApiModelProperty(value = "栏目海报路径")
	private AssetForm columnImage;
	@ApiModelProperty(value = "栏目移动端海报路径")
	private AssetForm columnPhoneImage;
	@ApiModelProperty(value = "子栏目列表")
	private List<ColumnForm> subColumnList = new ArrayList<ColumnForm>();
	@ApiModelProperty(value = "栏目所属区域列表")
	private List<RegionForm> regionList = new ArrayList<RegionForm>();


	public ColumnForm(Integer columnId, Integer serviceId, Integer parentColumnId, String columnCode, String columnName,
			Integer columnType, Integer columnLevel, String columnInfo, Integer columnState, Integer commendFlag,
			String mappingFolderId, String mappingFolderName, Integer mappingSystem, Integer orders, Integer targetType,
			Integer isGlobal, Integer state, Boolean isOperation) {
		super();
		this.columnId = columnId;
		this.serviceId = serviceId;
		this.parentColumnId = parentColumnId;
		this.columnCode = columnCode;
		this.columnName = columnName;
		this.columnType = columnType;
		this.columnLevel = columnLevel;
		this.columnInfo = columnInfo;
		this.columnState = columnState;
		this.commendFlag = commendFlag;
		this.mappingFolderId = mappingFolderId;
		this.mappingFolderName = mappingFolderName;
		this.mappingSystem = mappingSystem;
		this.orders = orders;
		this.targetType = targetType;
		this.isGlobal = isGlobal;
		this.state = state;
		this.isOperation = isOperation;
	}

	public ColumnForm(Integer columnId, String columnCode, String columnName, String columnPath, Integer targetType,
			Integer isGlobal, Integer state,Boolean isOperation) {
		super();
		this.columnId = columnId;
		this.columnCode = columnCode;
		this.columnName = columnName;
		this.targetType = targetType;
		this.columnPath = columnPath;
		this.isGlobal = isGlobal;
		this.columnState = state;
		this.isOperation = isOperation;
	}

	

	@Override
	public int compareTo(Object o) {
		return 0;
	}

}
