package com.foshan.form.response.pointsBehavior;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.PointsBehaviorForm;
import com.foshan.form.PointsPoolForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;

import java.util.ArrayList;
import java.util.List;

@ApiModel(value = "获取积分行为信息列表返回对象(GetPointsBehaviorListRes)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetPointsBehaviorListRes extends BasePageResponse {

    private static final long serialVersionUID = -5638877874926418922L;
    private List<PointsBehaviorForm> pointsBehaviorList = new ArrayList<>();

    public GetPointsBehaviorListRes() {
        super();
    }

    public GetPointsBehaviorListRes(int currentPage, int pageSize, int total, int totalResult) {
        super(currentPage, pageSize, total, totalResult);
    }

    public GetPointsBehaviorListRes(int currentPage, int pageSize, int total, int totalResult, List<PointsBehaviorForm> pointsBehaviorList) {
        super(currentPage, pageSize, total, totalResult);
        this.pointsBehaviorList = pointsBehaviorList;
    }

    public GetPointsBehaviorListRes(String ret, String retInfo) {
        super(ret, retInfo);
    }

    public List<PointsBehaviorForm> getPointsBehaviorList() {
        return pointsBehaviorList;
    }

    public void setPointsBehaviorList(List<PointsBehaviorForm> pointsBehaviorList) {
        this.pointsBehaviorList = pointsBehaviorList;
    }
}




