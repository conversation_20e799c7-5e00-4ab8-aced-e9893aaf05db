package com.foshan.form.response.job;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.JobForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取任务组信息返回对象(GetJobGroupInfoRes)")
@JsonInclude(Include.NON_NULL)
public class GetJobGroupInfoRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -210351467390369314L;
	@ApiModelProperty(value = "任务Id",example="1")
	private Integer jobGroupId;
	@ApiModelProperty(value = "任务组名称")
	private String jobGrupName;
	@ApiModelProperty(value = "任务组信息")
	private String jobGroupInfo;
	@ApiModelProperty(value = "任务组状态 0--无效 1--有效",example="1")
	private Integer jobGroupState;
	@ApiModelProperty(value = "任务组创建时间", example = "2019-01-01 00:00:00")
	private String createTime;
	@ApiModelProperty(value = "任务组升级时间", example = "2019-01-01 00:00:00")
	private String updateTime;
	@ApiModelProperty(value = "任务列表")
	private List<JobForm> jobList = new ArrayList<JobForm>();

	
	public GetJobGroupInfoRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	public GetJobGroupInfoRes(Integer jobGroupId, String jobGrupName, String jobGroupInfo, Integer jobGroupState,
			String createTime, String updateTime, List<JobForm> jobList) {
		super();
		this.jobGroupId = jobGroupId;
		this.jobGrupName = jobGrupName;
		this.jobGroupInfo = jobGroupInfo;
		this.jobGroupState = jobGroupState;
		this.createTime = createTime;
		this.updateTime = updateTime;
		this.jobList = jobList;
	}

	
	@Override
	public String toString() {
		return "GetJobGroupInfoRes [jobGroupId=" + jobGroupId + ", jobGrupName=" + jobGrupName + ", jobGroupInfo="
				+ jobGroupInfo + ", jobGroupState=" + jobGroupState + ", createTime=" + createTime + ", updateTime="
				+ updateTime + ", jobList=" + jobList + "]";
	}

}
