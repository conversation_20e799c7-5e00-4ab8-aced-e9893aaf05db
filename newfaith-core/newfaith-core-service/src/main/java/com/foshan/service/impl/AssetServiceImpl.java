package com.foshan.service.impl;



import static java.util.Comparator.comparingInt;

import com.foshan.entity.AccountEntity;
import com.foshan.entity.UserEntity;
import com.foshan.model.permssion.AccountPrincipalModel;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.query.Query;
import org.hibernate.transform.Transformers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.AssetInteractionEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.AssetForm;
import com.foshan.form.ServiceForm;
import com.foshan.form.request.AssetReq;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.asset.AddAssetRes;
import com.foshan.form.response.asset.GetAssetListRes;
import com.foshan.form.response.asset.ModifyAssetRes;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.IAssetService;
import com.foshan.service.annotation.Audit;



@Transactional
@Service("assetService")
public class AssetServiceImpl extends GenericService implements IAssetService {
	private final static Logger logger  = LoggerFactory.getLogger(AssetServiceImpl.class);
	@Override
	@Audit(operate = "新增媒资")
	public IResponse addAsset(AssetReq req) {
		// TODO Auto-generated method stub
		AddAssetRes res = new AddAssetRes();
		if (StringUtils.isNotEmpty(req.getAssetCode())) {
			AssetEntity asset = new AssetEntity();
			AssetForm assetForm = new AssetForm();
			BeanUtils.copyProperties(req, asset);
			BeanUtils.copyProperties(req,res);
			asset.setPackageCount(0);
			asset.setPackageFlag(EntityContext.ASSET_PACKAGE_FLAG_INVALID);
			if (null != req.getParentAssetId()) {
				AssetEntity parentAsset = assetDao.get(req.getParentAssetId());
				if (null != parentAsset) {
					asset.setParentAsset(parentAsset);
					parentAsset.setPackageFlag(EntityContext.ASSET_PACKAGE_FLAG_VALID);
					parentAsset.setPackageCount(
							null != parentAsset.getPackageCount() ? parentAsset.getPackageCount() + 1 : 1);
					assetForm.setParentAssetId(parentAsset.getId());
				} else {
					res.setRet("0001");
					res.setRetInfo("要增加媒资的媒资包不存在！！！");
				}
			}
			try {
				Integer assetId = (Integer) assetDao.save(asset);
				assetForm.setAssetId(assetId);
			} catch (Exception ex) {
				logger.error(ex.getMessage());
				res.setRet("0001");
				res.setRetInfo("要增加的媒资已存在！！！");
				return res;
			}
			res.setAssetForm(assetForm);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "修改媒资")
	public IResponse modifyAsset(AssetReq req) {
		// TODO Auto-generated method stub
		ModifyAssetRes res = new ModifyAssetRes();
		AssetForm assetForm = new AssetForm();
		if (null != req.getAssetId()) {
			AssetEntity asset = assetDao.get(req.getAssetId());
			if (null != asset) {
				asset.setAssetCode(
						StringUtils.isNotEmpty(req.getAssetCode()) ? req.getAssetCode() : asset.getAssetCode());
				asset.setAssetName(
						StringUtils.isNotEmpty(req.getAssetName()) ? req.getAssetName() : asset.getAssetName());
				asset.setAssetState(null != req.getAssetState() ? req.getAssetState() : asset.getAssetState());
				asset.setAssetType(null != req.getAssetType() ? req.getAssetType() : asset.getAssetType());
				asset.setServiceCode(
						StringUtils.isNotEmpty(req.getServiceCode()) ? req.getServiceCode() : asset.getServiceCode());
				asset.setImageFile(
						StringUtils.isNotEmpty(req.getImageFile()) ? req.getImageFile() : asset.getImageFile());
				asset.setSmallImageFile(StringUtils.isNotEmpty(req.getSmallImageFile()) ? req.getSmallImageFile()
						: asset.getSmallImageFile());
				asset.setTimeLength(null != req.getTimeLength() ? req.getTimeLength() : asset.getTimeLength());
				asset.setAssetOrders(null!=req.getAssetOrders()?req.getAssetOrders():asset.getAssetOrders());
				/*
				 * 判断媒资包的绑定情况： 1、如果请求父媒资为空，若请求媒资的父媒资不为空则变更媒资包关系；
				 * 2、如过请求父媒资不为空，则不论请求媒资的父媒资是否为空均进行媒资包关系调整
				 */
				AssetEntity oldParentAsset = asset.getParentAsset();
				if (null != req.getParentAssetId()) {
					AssetEntity newParentAsset = assetDao.get(req.getParentAssetId());
					if (null != newParentAsset) {
						asset.setParentAsset(newParentAsset);
						newParentAsset.setPackageCount(
								null != newParentAsset.getPackageCount() ? newParentAsset.getPackageCount() + 1 : 1);
						newParentAsset.setPackageFlag(EntityContext.ASSET_PACKAGE_FLAG_VALID);
						assetForm.setParentAssetId(newParentAsset.getId());
						if (null != oldParentAsset) {
							oldParentAsset.setPackageCount(oldParentAsset.getPackageCount() - 1);
							oldParentAsset.setPackageFlag(
									oldParentAsset.getPackageCount() == 0 ? EntityContext.ASSET_PACKAGE_FLAG_INVALID
											: EntityContext.ASSET_PACKAGE_FLAG_VALID);
						}
						// 如果修改的媒资绑定了投票选手，则因该媒资变更为媒资包的子媒资，则删除相应的投票选手数据

					} else {
						assetForm.setParentAssetId(req.getParentAssetId());
						res.setRet("0001");
						res.setRetInfo("要变更的媒资包不存在！！！");
						return res;
					}

				} else {
					if (null != oldParentAsset) {
						oldParentAsset.setPackageCount(oldParentAsset.getPackageCount() - 1);
						oldParentAsset.setPackageFlag(
								oldParentAsset.getPackageCount() == 0 ? EntityContext.ASSET_PACKAGE_FLAG_INVALID
										: EntityContext.ASSET_PACKAGE_FLAG_VALID);
						asset.setParentAsset(null);
					}
				}
				BeanUtils.copyProperties(asset,assetForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				assetForm.setAssetId(req.getAssetId());
				res.setRet("0001");
				res.setRetInfo("要修改的媒资不存在！！！");
			}
			res.setAssetForm(assetForm);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse getAssetList(AssetReq req) {
		// TODO Auto-generated method stub
		GetAssetListRes response = new GetAssetListRes();
		Object userObj = getPrincipal(true);
		if (userObj instanceof AccountPrincipalModel){
			AccountPrincipalModel account = (AccountPrincipalModel) userObj;
			req.setAccountId(account.getId());
		}

		Page<AssetEntity> page = new Page<AssetEntity>();
		try {
			String assetName = req.getAssetName();
			Integer type = req.getAssetType();
			//Object userObj = getPrincipal(true);
			page.setPageSize(req.getPageSize());
			page.setCurrentPage(req.getRequestPage());
			StringBuilder hql =  new StringBuilder("");
			if(StringUtils.isNotEmpty(req.getColumnId())){
				hql.append("SELECT a.* FROM t_asset a JOIN t_article ar ON a.assetId=ar.id JOIN t_upshelf_column b ON b.`resourceId`=ar.id" +
						" and " +
						"b.columnId IN " +
						"("+req.getColumnId()+")");
			}else {
				hql.append("SELECT a.* FROM t_asset a JOIN t_article ar ON a.assetId=ar.id where 1=1");
			}

			if (null == getCurrentUser()) {
				hql.append(" and ar.status = 1");
			}

			if(StringUtils.isNoneEmpty(assetName)) {
				hql.append(" and a.assetName like '%"+ assetName + "%'");
			}
			if(StringUtils.isNoneEmpty(req.getAssetCode())) {
				hql.append(" and a.assetCode like '%"+ req.getAssetCode() + "%'");
			}
			if(StringUtils.isNoneEmpty(req.getSummaryShort())) {
				hql.append(" and a.summaryShort like '%"+ req.getSummaryShort() + "%'");
			}
			if(null != req.getAssetId()) {
				hql.append(" and a.id like '%"+ req.getAssetId() + "%'");
			}
			if(type != null) {
				hql.append(" and a.assetType = " + type);
			}

			hql.append(null != req.getOrderByLike() && req.getOrderByLike() == 1 ? " order by a.recommendCount desc" : " order by a.createTime desc");

			System.out.println(hql.toString());
			Query query =
					assetDao.createSQLQuery(hql.toString()).addEntity(AssetEntity.class);
			Integer totalResult = query.getResultList().size();
			query.setMaxResults(null != req.getPageSize() ? req.getPageSize() : 10);
			query.setFirstResult((null != req.getRequestPage() ? req.getRequestPage() - 1 : 1 - 1) * (null != req.getPageSize() ?
					req.getPageSize() :
					10));



//			Page<AssetEntity> queryPage = assetDao.queryPage(page,hql.toString());

			query.getResultList().forEach(entity -> {
				AssetEntity asset = (AssetEntity)entity;
				AssetForm form = new AssetForm();
				BeanUtils.copyProperties(entity, form);
				form.setRecommendCount(null == form.getRecommendCount()?0:form.getRecommendCount());
				form.setIsRecommend(0);
				if(null!=req.getAccountId()) {
					for(AssetInteractionEntity o : asset.getAssetInteractionList()){
						if(o.getInteractionType()==0 && null==o.getParentAssetInteraction() && null !=o.getAccount() &&
								o.getAccount().getId().equals(req.getAccountId())) {
							form.setIsRecommend(1);
						}
					}
				}
				if(null!=req.getSmartcardId()) {
					for(AssetInteractionEntity o : asset.getAssetInteractionList()){
						if(o.getInteractionType()==0 && null==o.getParentAssetInteraction() && null != o.getSmartcardId() &&
								o.getSmartcardId().equals(req.getSmartcardId())) {
							form.setIsRecommend(1);
						}
					}
				}
				form.setAssetId(asset.getId());
				response.getAssetFormList().add(form);
			});
			
			response.setTotalResult(totalResult);
			response.setPageSize(page.getPageSize());
			response.setCurrentPage(page.getCurrentPage());
			response.setTotal(0 == totalResult%req.getPageSize() ?totalResult/req.getPageSize():totalResult/req.getPageSize()+1);
			response.setRet(ResponseContext.RES_SUCCESS_CODE);
			response.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			
		
		} catch (Exception e) {
			logger.info("查询媒资列表时出错{}", e);
			response.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
			response.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
		}

		return response;

	}

//	@Override
//	public Response deleteAsset(AssetReq req) {
//		// TODO Auto-generated method stub
//		BaseResponse res = new BaseResponse();
//		if (null != req.getAssetId()) {
//			AssetEntity asset = assetDao.get(req.getAssetId());
//			if (null != asset) {
//				// 删除投票用户的映射关系
//				VoteContestantEntity contestant = (VoteContestantEntity) voteContestantDao.createQuery(
//						"select a from VoteContestantEntity a inner join a.asset b where b.assetCode=?",
//						asset.getAssetCode()).uniqueResult();
//				if (null != contestant)
//					contestant.setAsset(asset);
//
//				// 判断要删除媒资是否为媒资包或媒资包子媒资
//				AssetEntity parentAsset = asset.getParentAsset();
//
//				if (null != parentAsset) {
//					parentAsset.setPackageCount(parentAsset.getPackageCount() - 1);
//					parentAsset.setPackageFlag(parentAsset.getPackageCount() == 0 ? EntityContext.ASSET_PACKAGE_FLAG_INVALID
//							: EntityContext.ASSET_PACKAGE_FLAG_VALID);
//				} else if (null != asset.getSubAssetSet()) {
//					asset.getSubAssetSet().forEach(o -> {
//						o.setParentAsset(null);
//						assetDao.delete(o);
//					});
//				}
//
//				assetDao.delete(asset);
//			}
//			res.setRet(ResponseContext.RES_SUCCESS_CODE);
//			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		} else {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//		}
//		return res;
//	}
	
	@Override
	public IResponse getAssetInfo(AssetReq req) {
		// TODO Auto-generated method stub
		ModifyAssetRes res = new ModifyAssetRes();
		AssetForm assetForm = new AssetForm();
		if (null != req.getAssetId()) {
			AssetEntity asset = assetDao.get(req.getAssetId());
			if (null != asset) {
				assetForm = getAsset(asset, EntityContext.PRODUCT_LIST_DETAIL_FLAG_ALL);
				assetForm.setIsRecommend(0);
				if(null!=req.getAccountId()) {
					for(AssetInteractionEntity o : asset.getAssetInteractionList()){
						if(o.getInteractionType()==0 && null==o.getParentAssetInteraction() && null!= o.getAccount() &&
								o.getAccount().getId().equals(req.getAccountId())) {
							assetForm.setIsRecommend(1);
						}
					}
				}
				if(null!=req.getSmartcardId()) {
					for(AssetInteractionEntity o : asset.getAssetInteractionList()){
						if(o.getInteractionType()==0 && null==o.getParentAssetInteraction() && null!= o.getSmartcardId() &&
								o.getSmartcardId().equals(req.getSmartcardId())) {
							assetForm.setIsRecommend(1);
						}
					}
				}
				assetForm.setRecommendCount(null == asset.getRecommendCount()?0:asset.getRecommendCount());
				res.setAssetForm(assetForm);
				assetForm.getSubAssetList().sort(comparingInt(AssetForm::getAssetOrders));
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				assetForm.setAssetId(req.getAssetId());
				res.setRet("0001");
				res.setRetInfo("媒资不存在！！！");
			}
			res.setAssetForm(assetForm);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

}
