package com.foshan.entity;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_organization")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class OrganizationEntity extends EntityObject {

	/**
	 * 组织
	 */
	private static final long serialVersionUID = -7305962704778780621L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '组织名称'")
	private String name;
	@Column(columnDefinition = "varchar(128) comment '组织代号'")
	private String code;
	@Column(columnDefinition = "varchar(128) comment '组织地址'")
	private String address;
	@Column(columnDefinition = "varchar(30) comment '组织联系电话'")
	private String tel;
	@Column(columnDefinition = "varchar(30) comment '组织联系人'")
	private String contacts; ;
	@Column(columnDefinition = "varchar(64) comment '组织邮箱'")
	private String email;
	@ManyToOne(targetEntity = AssetEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "organizationImageId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private AssetEntity logo;
	@ManyToMany(targetEntity = RegionEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_organization_region", joinColumns = @JoinColumn(name = "organizationId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "regionId", referencedColumnName = "id"))
	@JsonIgnore
	private List<RegionEntity> regionList = new ArrayList<>();
	


	

	
	

}
