package com.foshan.entity;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.util.DateUtil;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 票券模版
 */
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_giftroll_model")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class TicketSettingEntity extends EntityObject {

	private static final long serialVersionUID = 3876347631534589645L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '奖券名称'")
	private String modelName;
	@Column(columnDefinition = "varchar(64) comment '奖券标题'")
	private String title;
	@Column(columnDefinition = "DECIMAL(5,2) comment '奖券金额 单位：元'")
	private BigDecimal modelAmount;
	@Column(columnDefinition = "int(11) default 0 comment '奖券发行数量'")
	private Integer modelCount;
	@Column(columnDefinition = "int(11) default 0 comment '已领取数量'")
	private Integer receivedCount;
	@Column(columnDefinition = "Timestamp comment '开始时间'")
	private Timestamp startTime;
	@Column(columnDefinition = "Timestamp comment '结束时间'")
	private Timestamp endTime;
	@Column(columnDefinition = "int(1) comment '奖券类型  0--抵扣券 1--兑换券 2--积分券 3--现金券 4--产品券 5--运费券, 6--商城优惠券'")
	private Integer modelType;
	@Column(columnDefinition = "int(1) comment '奖券有效期类型 N=0 为startTime,endTime设置的固定时间段，N>0时为，领券后N天内有效'")
	private Integer modelPeriodType;
	@Column(columnDefinition = "int(1) comment '是否第三方券 0--否 1--是'")
	private Integer isThirdCard;
	@Column(columnDefinition = "int(8) comment '奖券设置状态 0--未生效 1--生效 2--关闭'")
	private Integer modelState;
	@Column(columnDefinition = "varchar(3000) comment '奖券信息'")
	private String modelInfo;
	@Column(columnDefinition = "int(11) comment '奖券关联第三方产品Id'")
	private Integer productId;
	@Column(columnDefinition = "int default 1 comment '每人限制领取多少张，默认为1'")
	private Integer limitNum;

	
	public TicketSettingEntity(String modelName, String title, BigDecimal modelAmount, Integer modelCount, Integer remainingDays,
			Timestamp startTime, Timestamp endTime, Integer modelType) {
		super();
		this.modelName = modelName;
		this.modelAmount = modelAmount;
		this.modelCount = modelCount;
		this.title = title;
		//this.remainingDays = remainingDays;
		this.startTime = startTime;
		this.endTime = endTime;
		this.modelType = modelType;
	}

	public Integer getRemainingDays() {
		return (int)DateUtil.subDate(new Date(endTime.getTime()),new Date());
	}


	@Override
	public String toString() {
		return "GiftRollModelEntity [id=" + id + ", modelName=" + modelName + ", modelAmount=" + modelAmount
				+ ", modelCount=" + modelCount + ", remainingDays=" + getRemainingDays() + ", startTime=" + startTime
				+ ", endTime=" + endTime + ", modelType=" + modelType + ", modelState=" + modelState + ", modelInfo="
				+ modelInfo + ", productId=" + productId + ", receivedCount=" + receivedCount + ", limitNum=" + limitNum + "]";
	}

}
