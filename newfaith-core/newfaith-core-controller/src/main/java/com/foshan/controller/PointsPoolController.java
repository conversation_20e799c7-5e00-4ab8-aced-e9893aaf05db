package com.foshan.controller;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.request.PointsPoolReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.pointsPool.AddPointsPoolRes;
import com.foshan.form.response.pointsPool.GetPointsPoolInfoRes;
import com.foshan.form.response.pointsPool.GetPointsPoolListRes;
import com.foshan.form.response.pointsPool.ModifyPointsPoolRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@Api(tags = "融合平台--积分池管理模块")
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
public class PointsPoolController extends BaseController {

    private final static Logger logger = LoggerFactory.getLogger(PointsPoolController.class);

    @ApiOperation(value = "新增积分池", httpMethod = "POST",notes = "新增积分池")
    @ResponseBody
    @RequestMapping(value = "/addPointsPool",method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public AddPointsPoolRes addPointsPool(@RequestBody PointsPoolReq req) throws JsonProcessingException {
        long start = System.currentTimeMillis();
        logger.info("接收==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==请求数据："
                + mapper.writeValueAsString(req));
        AddPointsPoolRes res = (AddPointsPoolRes) pointsPoolService.addPointsPool(req);
        long end = System.currentTimeMillis();
        logger.info(
                "==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==处理时间:" + (end - start) + "毫秒!");
        return res;
    }

    @ApiOperation(value = "删除积分池", httpMethod = "POST",notes = "删除积分池")
    @ResponseBody
    @RequestMapping(value = "/deletePointsPool",method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public GenericResponse deletePointsPool(@RequestBody PointsPoolReq req) throws JsonProcessingException {
        long start = System.currentTimeMillis();
        logger.info("接收==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==请求数据："
                + mapper.writeValueAsString(req));
        GenericResponse res = (GenericResponse) pointsPoolService.deletePointsPool(req);
        long end = System.currentTimeMillis();
        logger.info(
                "==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==处理时间:" + (end - start) + "毫秒!");
        return res;
    }

    @ApiOperation(value = "查询积分池", httpMethod = "POST",notes = "查询积分池")
    @ResponseBody
    @RequestMapping(value = "/getPointsPoolInfo",method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public GetPointsPoolInfoRes getPointsPoolInfo(@RequestBody PointsPoolReq req) throws JsonProcessingException {
        long start = System.currentTimeMillis();
        logger.info("接收==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==请求数据："
                + mapper.writeValueAsString(req));
        GetPointsPoolInfoRes res = (GetPointsPoolInfoRes) pointsPoolService.getPointsPoolInfo(req);
        long end = System.currentTimeMillis();
        logger.info(
                "==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==处理时间:" + (end - start) + "毫秒!");
        return res;
    }

    @ApiOperation(value = "修改积分池", httpMethod = "POST",notes = "修改积分池")
    @ResponseBody
    @RequestMapping(value = "/modifyPointsPool",method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModifyPointsPoolRes modifyPointsPool(@RequestBody PointsPoolReq req) throws JsonProcessingException {
        long start = System.currentTimeMillis();
        logger.info("接收==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==请求数据："
                + mapper.writeValueAsString(req));
        ModifyPointsPoolRes res = (ModifyPointsPoolRes) pointsPoolService.modifyPointsPool(req);
        long end = System.currentTimeMillis();
        logger.info(
                "==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==处理时间:" + (end - start) + "毫秒!");
        return res;
    }

    @ApiOperation(value = "查询积分池列表", httpMethod = "POST",notes = "查询积分池列表")
    @ResponseBody
    @RequestMapping(value = "/getPointsPoolList",method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public GetPointsPoolListRes getPointsPoolList(@RequestBody PointsPoolReq req) throws JsonProcessingException {
        long start = System.currentTimeMillis();
        logger.info("接收==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==请求数据："
                + mapper.writeValueAsString(req));
        GetPointsPoolListRes res = (GetPointsPoolListRes) pointsPoolService.getPointsPoolList(req);
        long end = System.currentTimeMillis();
        logger.info(
                "==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==处理时间:" + (end - start) + "毫秒!");
        return res;
    }
}
