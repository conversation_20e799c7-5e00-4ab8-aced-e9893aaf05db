package com.foshan.controller;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.request.ColumnReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.column.AddColumnRes;
import com.foshan.form.response.column.GetColumnListRes;
import com.foshan.form.response.column.ModifyColumnRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "融合平台--栏目模块")
@RestController
public class ColumnController extends BaseController {


	// 增加栏目
	@ApiOperation(value = "新增栏目(AddColumn)", httpMethod = "POST", notes = "新增栏目<p>1:serviceId和columnName不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addColumn", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddColumnRes addColumn(@RequestBody ColumnReq req, HttpServletRequest request)
			throws JsonProcessingException {

		AddColumnRes res = (AddColumnRes) columnService.addColumn(req);
		return res;
	}

	// 修改栏目
	@ApiOperation(value = "修改栏目(ModifyColumn)", httpMethod = "POST", notes = "修改栏目<p>1:columnId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyColumn", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyColumnRes modifyColumn(@RequestBody ColumnReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyColumnRes res = (ModifyColumnRes) columnService.modifyColumn(req);
		return res;
	}

	// 删除栏目
	@ApiOperation(value = "删除栏目(DeleteColumn)", httpMethod = "POST", notes = "删除栏目<p>1:columnId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteColumn", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteColumn(@RequestBody ColumnReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) columnService.deleteColumn(req);
		return res;
	}

	// 获取栏目列表
	@ApiOperation(value = "获取栏目列表(GetColumnList)", httpMethod = "POST", notes = "获取栏目列表<p>1:columnId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getColumnList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetColumnListRes getColumnList(@RequestBody ColumnReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetColumnListRes res = (GetColumnListRes) columnService.getColumnList(req);
		return res;
	}

}
