#--------------newfaith平台自定义参数-------------------#
#-----------1、平台通用参数在faith节点下直接添加---------#
#-----------2、其它模块级、子系统级参数在下层节点添加-----#

faith:
  #业务编码(ServiceGroup)的预设长度
  serviceCodeLength: 3
  #返回数据Log打印开关
  returnDataPrintFlag: false
  #投票业务上传投票照片保存路径
  uploadImagePath: /usr/resource/wildfly/vote/images
  #问卷业务批量导入题库文件保存路径
  uploadExcelPath: /usr/resource/wildfly/vote/excel
  #默认查询深度
  defaultGetDataDepth: 10
  #批量处理行为数据批次大小
  visitRealTransBatchNum: 20000
  #BOSS地址
  bossIp: **************
  #BOSS端口
  bossPort: 8080
  #BOSS用户信息路径
  bossInfoPath: /faith/integate/getUserInfo
  #BOSS充值路径
  bossChargePath: /faith/integate/gizRreeCharging
  #商城图片地址 /data/resource/wildfly-shop/faithImages/assetImages
  #assetFilePath: /data/resource/wildfly-shop/faithImages/assetImages
  #正式社区
  assetFilePath: /data/resource/community/file/
  #商城图片相对地址
  assetFileUrl: /file
  #图片压缩比例宽*高
  imageWidthAndHeight: 1280*720
  #缩略中图片压缩比例宽*高
  middleImageWidthAndHeight: 640*360
  #缩略小图图片压缩比例宽*高
  smallImageWidthAndHeight: 265*152
  #默认开户
  registAccountFlag: 0
  #用户信息获取标识
  accountInfoFlag: 0
  #登录失败次数限制
  authLimit: 15
  #应用url前缀，用作资源权限判断
  appPrefix: party
  #开启短信图片验证码
  enablePicVerifyCode: 0
  test:
    accountId: 2
 #微信公众平台参数
  wechat:
    appid: wx745c5330771d1af7
    appsecret: a9e2109cc9ab80756935f517856bd9ef
 #微信小程序参数
  miniProgram:
    appid: wxb1d31bc1efdd2901
    appsecret: 357931a7a27e2f9e75f9f88123dd3170
 #微信企业号参数
  wxEnterprice:
    agentid: 1000016
    appid: wxb68b2d5717de3d8d
    appsecret: p2h1twWoq7tFHZIknc8CGiaTYuYDyVwwxaThdMktNkg
 #微信小程序企业号接口参数
  wxEnterpriceMiniProgram:
    agentid: 1000018
    appid: wxb68b2d5717de3d8d
    appsecret: WmbTXPhjw0uiO8XVGRCWc69LRovSj4WZfAlBtxSzXwk
  #短信平台接口机地址
  smsInterfaceUrl: https://fsnb.96956.com.cn/shop/sendSms 
  #会员/公众用户的角色ID,多个以逗号隔开
  memberRoleId: 3
 #account类型，sql方式插入数据到t_account表时使用
  accountType: C
  #小程序登录自动注册开关，0-关，1-开
  miniprogramAutoRegist: 1
  #登录验证短信发送次数限制
  smsSendLimit: 5
  #短信验证时间间隔，单位分钟；如设置为2，2分钟内不允许再发
  smsTimeInterval: 2
  #短信有效时间（分钟）
  smsEffective: 5
  #修改密码短信验证允许输错次数
  smsIntendedEffect: 3
  #智能卡绑定账号个数限制，1个卡只能绑定5个账号
  bindSmartcardIdLimit: 5
  #智能卡号绑定的验证短信发送次数限制
  bindSmartcardIdSmsSendLimit: 10
  #科大讯飞语音授权接口——获取token
  speechSounds: 
    openAuthUrl: http://iflytek.gcable.cn:8087/IPTV-Auth/v1/openAuth
    appid: f10cad3c
    appsecret: 908474aec7ec6417
  #授权登录
  loginAuthenticationUrl:
    #h5授权页面
    html: http://fsnb.96956.com.cn/gcableshop/#/tvAuthLogin?loginTicket=
    #微信网页授权
    wechat: https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx745c5330771d1af7&redirect_uri=http%3A%2F%2Ffsdb.96956.com.cn%2Fauth.html&response_type=code&scope=snsapi_base&state=
#智慧社区
community: 
  #支付模块付款服务地址
  paymentServiceUrl: http://127.0.0.1:8080/community/payment/pay
  refundServiceUrl: http://127.0.0.1:8080/community/payment/refund
  queryPaymentResultServiceUrl: http://127.0.0.1:8080/community/payment/query
  queryRefundResultServiceUrl: http://127.0.0.1:8080/community/payment/queryRefundResult
  paymentMerchantCode: puhuameiju_yjnsq
  paymentSecret: yjnGcable#86
  #支付模块付款结果通知地址
  paymentNotifyUrl: http://127.0.0.1:8080/community/paymentNotify
  refundNotifyUrl: http://127.0.0.1:8080/community/refundNotify
  paymentReturnUrl: /paymentNotify
  #管理费预收月份
  advanceReceiptsMonth: 12
  #开户行行号
  bankNo: "{\"中国光大银行\":\"************\",\"中国银行\":\"************\",\"交通银行\":\"************\",\"农业银行\":\"************\",\"农村信用合作社\":\"************\",\"工商银行\":\"************\",\"广发银行\":\"************\",\"农商银行\":\"************\",\"建设银行\":\"************\",\"招商银行\":\"************\",\"邮政储蓄银行\":\"************\"}"
  #总表分摊结果
  exportAllocationListUrl: http://127.0.0.1:8080/community/exportAllocationList
  invoiceUrl: http://localhost:9292/community_dev/
  #金蝶对账文件保存路径
  kingdeeFilePath: /data/kingdee
  #金蝶系统对接基础数据导入开始时间
  kingdeeBaseImportStartDate: 2023-02-01
  #金蝶插件访问地址
  kingdeeUrl: http://localhost:9494/kingdee
    #科耐系统割接时间(yyyy-MM-dd 00:00:00)
  cutoverDate: 2023/01/28 00:00:00
  #16区优惠设置
  benefit:
    #取消优惠后的物业费单价（元）,数字保留两位小数
    orginPrice: 3.18
    #需要计算优惠收费项目ID
    benefitPayitemId: 190
    #优惠政策的开始时间，设置开始前一天的23点59分59秒(yyyy-MM-dd 23:59:59)
    benefitProlicyStartTime: 2022/12/31 23:59:59
    
shop: 
  #各种SN的前缀配置
  sn:
    orderPrefix: ODOD
    orderPaymentPrefix: ODPM
    orderRefundsPrefix: ODRF
    orderReturnsPrefix: ODRT
    orderShippingPrefix: ODSP
    paymentSessionPrefix: ODPS
    productPrefix: PROD
  #微信支付商户API证书存放根目录 实际部署时配置 /data/resource/wildfly-shop/certFiles
  #certFileRootDir: /data/resource/wildfly-shop/certFiles

  #默认产品价格有效期,单位：年
  defaultProductPriceValidPeriod: 5
  
  #客服测试环境webService地址
  customerServiceUrl: http://*************:8083/cc-server/services/WorkAssignService?wsdl
  #客服webService用户名,测试环境
  customerServiceName: cc_datang
  #客服webService密码，测试环境
  customerServicePasswd: bjdv1234

  #客服正式版webService地址
  #customerServiceUrl: http://*************:8080/cc-server/services/WorkAssignService?wsdl
  #客服webService用户名,正式环境：cc_datang
  #customerServiceName: cc_datang
  #客服webService密码，正式环境：26b2def2a6a5f1ef1a0672d380f931ea
  #customerServicePasswd: 26b2def2a6a5f1ef1a0672d380f931ea

  #订单过期时间，单位小时
  orderExpirationTime: 24
  #订单发货后，系统自动确认订单时间，单位天,默认14天
  autoConfirmReceiptTime: 14
  
  #产品规格信息获取开关
  #Brand
  brand:
    auditFlag: true
    specificationsFlag: true
    templeteFlag: true
    skuFlag: true
  #ProductCategory
  category:
    auditFlag: false
    specificationsFlag: true
    templeteFlag: true
    skuFlag: true
  #store
  store:
    auditFlag: false
    specificationsFlag: true
    templeteFlag: true
    skuFlag: true
  #Column
  column:
    auditFlag: false
    specificationsFlag: true
    templeteFlag: true
    skuFlag: false
  #ProductGroup
  group:
    auditFlag: false
    specificationsFlag: true
    templeteFlag: true
    skuFlag: true
  #TV
  tv:
    auditFlag: false
    specificationsFlag: false
    templeteFlag: true
    skuFlag: false
  #Mobile
  mobile:
    auditFlag: false
    specificationsFlag: true
    templeteFlag: true
    skuFlag: true

  #航天开发票--测试--地址
  invoiceServiceUrl: http://www.aisinogz.com:19876/AisinoFp-test/eliWebService.ws?wsdl
  #航天开发票--正式(域名)--地址
  #invoiceServiceUrl: http://www.aisinogz.com:19876/AisinoFp_gdgdwl/eliWebService.ws?wsdl
  #航天开发票--正式(IP)--地址
  #invoiceServiceUrl: http://*************:29876/AisinoFp_gdgdwl/eliWebService.ws?wsdl
  
  #UTVGO演唱会服务地址
  utvgoLiveSyncUrl: http://*************/activity-client-web/activity/freeCardController/syncLiveCard.utvgo
  utvgoLiveCancelUrl: http://*************/activity-client-web/activity/freeCardController/cancelLiveCard.utvgo
  utvgoGetLiveInfoUrl: http://*************/activity-client-web/activity/freeCardController/findLiveCardDetails.utvgo
  #点播视频产品购买后的有效播放时间,单位：天
  videoValidTime: 3
  #自营分级审核
  firstLevelRefundAuditUserId: 171
  secondLevelRefundAuditUserId: 172
  thirdLevelRefundAuditUserId: 173
  #测试环境
  #boss.integrateUrl = http://************:7003/boss2_task/integrate/integate!bossRequest
  boss.integrateUrl: http://localhost:5666/boss2_task/integrate/integate!bossRequest
  #正式环境
  #boss.integrateUrl = http://***********:80/boss2_task/integrate/integate!bossRequest
  
  #支付模块付款服务地址
  paymentServiceUrl: http://127.0.0.1:8080/community/payment/pay
  refundServiceUrl: http://127.0.0.1:8080/community/payment/refund
  queryPaymentResultServiceUrl: http://127.0.0.1:8080/community/payment/query
  queryRefundResultServiceUrl: http://127.0.0.1:8080/community/payment/queryRefundResult
  paymentMerchantCode: puhuameiju_yjnsq
  paymentSecret: yjnGcable#86
  #支付模块付款结果通知地址
  paymentNotifyUrl: http://127.0.0.1:8080/community/paymentNotify
  refundNotifyUrl: http://127.0.0.1:8080/community/refundNotify
  paymentReturnUrl: /paymentNotify
  
  boss:
    system: CHANNEL
    deptid: 6755999
    clientcode: FSXBGL
    clientpwd: a4ba8b1de2647f978c5604ba8432a14d
    version: 1
  #1：直接对BOSS；2：对接FAITH，再由FAITH对BOSS
  dockingBoss: 2

#支付插件相关的参数
payment:
  #如果使用nginx反向代理对外提供服务，需要配置nginx对外服务的IP,微信H5支付需要用到  
  #我们的实际环境************ ；测试环境***************
  nginxWanIp: ***************
  #微信支付商户API证书存放根目录 实际部署时配置 /data/resource/wildfly-shop/certFiles
  #正式社区环境
  #certFileRootDir: /data/resource/communityFile/certFiles
  certFileRootDir: /data/resource/communityFile/certFiles
  #支付平台的网址 http://localhost:9191/community
  #正式社区环境
  #siteUrl: https://fsdb.96956.com.cn/community
  siteUrl: https://fsdb.96956.com.cn/community
  #微信H5及公众号支付完成支付后，跳转回应用的页面地址http://fsdb.96956.com.cn/gcableshop/#/order ，会自动在配置的URL后面增加 orderId=xxx&paymentSessionSn=xxx
  #目前暂无H5支付应用，暂时不需要理会配置的内容
  wxH5PaymentCallBackUrl: "https://fsdb.96956.com.cn/communityApp/#/order"
  #是否开启支付的模拟测试模式,1为调试模式，0为正式使用。调试模式所有支付及退款操作将不产生实际的费用，方便测试。商用环境时必须配置为0，切记！！！！
  paymentDebugMode: 1
  
#短信
sms:
  #短信平台地址,sms.war调用短信平台接口发送短信时使用
  smsUrl: https://fsnb.96956.com.cn/shop/sendSms 

