package com.foshan.entity.vote;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.RoleEntity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_role")
@org.hibernate.annotations.Table(appliesTo = "t_role",comment="投票平台角色")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
@DiscriminatorValue("V")
public class VoteRoleEntity extends RoleEntity{/**
	 * 
	 */
	private static final long serialVersionUID = 7892814153756584436L;

}
