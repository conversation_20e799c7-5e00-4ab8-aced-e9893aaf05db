hazelcast: 
  management-center:
    scripting-enabled: false
    console-enabled: false
  
  network: 
    join: 
      multicast:
        enabled: false
        multicast-group: *********
        multicast-port: 54265
        multicast-time-to-live: 32
        multicast-timeout-seconds: 2
    interfaces:
      enabled: false
      interfaces:
        - 127.0.0.1
  map:
    default:
      time-to-live-seconds: 120
      eviction:
        eviction-policy: LFU
        max-size-policy: PER_NODE
        size: 30000
    loginTicketCache:
      time-to-live-seconds: 300
      eviction:
        eviction-policy: LFU
        max-size-policy: PER_NODE
        size: 10000
    shiro-activeSessionCache:
      time-to-live-seconds: 900
      eviction:
        eviction-policy: LFU
        max-size-policy: PER_NODE
        size: 30000
    shiroCache:
      time-to-live-seconds: 900
      eviction:
        eviction-policy: LFU
        max-size-policy: PER_NODE
        size: 30000
    wechatCache:
      time-to-live-seconds: 7000
      eviction:
        eviction-policy: LFU
        max-size-policy: PER_NODE
        size: 30000
    thirdPlatformTokenCache: 
      time-to-live-seconds: 0
      eviction:
        eviction-policy: LFU
        max-size-policy: PER_NODE
        size: 30
    baiwang-tokenCache: 
      time-to-live-seconds: 15000
      eviction:
        eviction-policy: LFU
        max-size-policy: PER_NODE
        size: 300
    smsAuthCache:
      time-to-live-seconds: 500
      eviction:
        eviction-policy: LFU
        max-size-policy: PER_NODE
        size: 2000
    smsCache:
      time-to-live-seconds: 500
      eviction:
        eviction-policy: LFU
        max-size-policy: PER_NODE
        size: 2000
    userObjectCache:
      time-to-live-seconds: 7200
      eviction:
        eviction-policy: LFU
        max-size-policy: PER_NODE
        size: 100000

   