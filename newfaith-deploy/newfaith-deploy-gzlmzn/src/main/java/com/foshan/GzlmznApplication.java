package com.foshan;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.transaction.annotation.EnableTransactionManagement;


@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
@EnableTransactionManagement
@EnableCaching
@ComponentScan(basePackages = "com.foshan")
public class GzlmznApplication {
	
	/**
	 * 配置说明
	 * app配置：应用名称gzlmzn、端口9090
	 * hazelcast配置：可信ip（**************-206），数据组播地址（：*********）、端口（54327）
	 * logbak配置日志存放路径（/var/log/gzlmzn）、日志名称（${LOG_HOME}/gzlmzn_%d{yyyyMMdd}_%i.log）
	 * 服务器配置：开放服务端口，开放hazelcast管理端口（默认，5701-5800/tcp），数据端口(54328/udp)
	 */
	public static void main(String[] args) {
		SpringApplication springApplication = new SpringApplication(GzlmznApplication.class);
		springApplication.run(args);
	}
}
