<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans-4.1.xsd">
	<!-- http://www.springframework.org/schema/security http://www.springframework.org/schema/security/spring-security-4.2.xsd"> -->

	<!-- 智慧社区shiro配置文件 -->

	<bean id="lifecycleBeanPostProcessor"
		class="org.apache.shiro.spring.LifecycleBeanPostProcessor" />

	<bean
		class="org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator"
		depends-on="lifecycleBeanPostProcessor" />

	<bean
		class="org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor">
		<property name="securityManager" ref="securityManager" />
	</bean>

	<!-- 管理员realm -->
	<bean id="coreShiroRealm"
		class="com.foshan.service.permission.shiro.CoreShiroRealm">
		<property name="extendAuthorizationList">
			<list>
				<!-- <ref bean="sampleExtendAuthorization" /> -->
			</list>
		</property>
		<!-- 未参透credentialsMatcher，在realm中实现登录尝试次数限制 -->
		<!-- <property name="credentialsMatcher" ref="credentialsMatcher"/> -->
	</bean>

	<!-- 验证次数限制 -->
	<!-- <bean id="credentialsMatcher" class="com.foshan.service.permission.shiro.RetryLimitCredentialsMatcher"> 
		<property name="hashAlgorithmName" value="md5" /> <property name="hashIterations" 
		value="1" /> </bean> -->

	<!-- 公众账号 -->
	<bean id="accountRealm"
		class="com.foshan.service.permission.shiro.AccountRealm">
		<property name="accountExtendAuthenticationList">
			<list>
				<!-- <ref bean="sampleExtendAuthentication" /> -->
			</list>
		</property>
	</bean>

	<!-- shiro封装shiroCacheManager -->
	<bean id="shiroCacheManager"
		class="com.foshan.util.HazelcastCacheManager">
		<!-- <property name="cacheManager" ref="cacheManager"></property> -->
	</bean>
	<!-- <bean id="shiroCacheManager" class="com.foshan.service.permission.shiro.SpringHazelcastCacheManagerWrapper"> 
		</bean> -->

	<!-- 自定义的会话管理器，sessionid记录到响应头 -->
	<bean id="sessionManager"
		class="com.foshan.service.permission.shiro.sessionManager.DefaultHeaderSessionManager">
		<property name="sessionDAO" ref="sessionDAO" />
		<!-- sessionid的cookie改名，防止被servlet容器篡改 -->
		<property name="sessionIdCookie.name" value="x-auth-token" />
		<!-- <property name="sessionIdCookie.secure" value="true" />
		<property name="sessionIdCookie.httpOnly" value="true" /> -->
		<property name="globalSessionTimeout" value="1200000" />
		<property name="cacheManager" ref="shiroCacheManager"></property>
	</bean>
	<bean id="sessionDAO"
		class="org.apache.shiro.session.mgt.eis.EnterpriseCacheSessionDAO">
		<property name="activeSessionsCacheName"
			value="shiro-activeSessionCache" />
		<property name="cacheManager" ref="shiroCacheManager"></property>
	</bean>

	<bean id="securityManager"
		class="org.apache.shiro.web.mgt.DefaultWebSecurityManager">
		<property name="realms">
			<list>
				<ref bean="coreShiroRealm" />
				<ref bean="accountRealm" />
			</list>
		</property>
		<property name="authenticator" ref="authenticator"></property>
		<property name="rememberMeManager" ref="rememberMeManager" />
		<property name="sessionManager" ref="sessionManager"></property>
		<property name="cacheManager" ref="shiroCacheManager"></property>
	</bean>

	<!-- 配置多Realm认证 -->
	<!-- <bean id="authenticator" class="org.apache.shiro.authc.pam.ModularRealmAuthenticator"> -->
	<bean id="authenticator"
		class="com.foshan.service.permission.shiro.authenticator.MultiModularRealmAuthenticator">
		<property name="realms">
			<list>
				<ref bean="coreShiroRealm" />
				<ref bean="accountRealm" />
			</list>
		</property>
	</bean>

	<bean id="shiroDefinitionsService"
		class="com.foshan.service.permission.impl.ShiroDefinitionsServiceImpl">
		<property name="filterChainDefinitions">
			<value>
				/login = anon
				/accountLogin = anon
				/logout = anon
				/getVerifyCode = anon
				/unauthorized = anon
				/unLogin = anon
				<!-- /member/getMemberAppToken = whitelistFilter[1] /getLoginAuthQrCode 
					= whitelistFilter[2] /member/getStbMemberList = whitelistFilter[2] /getAccountLoginTicket 
					= whitelistFilter[2] -->
				<!-- /member/** = roles[member] -->
				<!-- /member/test = roles[member] -->
				<!-- /reloadDefinitions = roles[admin] -->
				<!-- /** = anon -->
			</value>
		</property>
	</bean>

	<bean id="coreChainDefinitions"
		class="com.foshan.service.permission.shiro.CoreChainDefinitions">
		<property name="shiroDefinitionsService"
			ref="shiroDefinitionsService" />
	</bean>

	<!-- 入口filter，注入spring，web.xml中配置由DelegatingFilterProxy代理 -->
	<bean id="shiroFilter"
		class="org.apache.shiro.spring.web.ShiroFilterFactoryBean">
		<property name="securityManager" ref="securityManager" />
		<property name="loginUrl" value="/unLogin" />
		<!-- <property name="successUrl" value="/index.html" /> -->
		<property name="unauthorizedUrl" value="/unauthorized" />

		<property name="filterChainDefinitionMap"
			ref="coreChainDefinitions" />

		<!-- 自定义过滤规则，filter链 -->
		<property name="filters">
			<map>
				<entry key="whitelistFilter">
					<bean
						class="com.foshan.service.permission.shiro.filter.WhitelistFilter" />
				</entry>
				<entry key="perms">
                    <bean
                        class="com.foshan.service.permission.shiro.filter.RestPermissionFilter" />
                </entry>
			</map>
		</property>
	</bean>

	<!--刷新shiro会话时间的filter，一定要在shiroFilter之后加载，否则无法获取权限主体subject-->
	<bean id="ShiroSessionFilter" class="com.foshan.service.permission.shiro.filter.ShiroSessionFilter"/>

	<bean id="rememberMeCookie"
		class="org.apache.shiro.web.servlet.SimpleCookie">
		<constructor-arg value="rememberMeShiro" /><!-- 浏览器中cookie的名字 -->
		<property name="httpOnly" value="true" /><!--document对象中就看不到cookie了 -->
		<property name="maxAge" value="2592000" /><!-- 30天 -->
	</bean>

	<!-- rememberMe管理器 -->
	<bean id="rememberMeManager"
		class="org.apache.shiro.web.mgt.CookieRememberMeManager">
		<!--秘钥要16位，24位或32位字符的Base64编码。1234567890abcdef编码后是MTIzNDU2Nzg5MGFiY2RlZg== -->
		<property name="cipherKey"
			value="#{T(org.apache.shiro.codec.Base64).decode('RlNjYXR2QDIwMTZhYmNkZQ==')}" />
		<!-- <property name="cipherKey" value="#{T(com.foshan.service.permission.shiro.GenerateCipherKey).generateNewKey()}" 
			/> -->
		<property name="cookie" ref="rememberMeCookie" />
	</bean>

</beans>