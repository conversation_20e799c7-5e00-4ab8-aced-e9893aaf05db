<?xml version="1.0"?>
<project
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd"
        xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.foshan</groupId>
        <artifactId>newfaith-deploy</artifactId>
        <version>V200R001B03</version>
    </parent>
    <artifactId>newfaith-deploy-businessOpportunity</artifactId>
    <name>newfaith-deploy-businessOpportunity</name>
    <url>http://maven.apache.org</url>
    <dependencies>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>newfaith-exp-businessOpportunity</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>newfaith-resources</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--热部署 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional>
        </dependency>

    </dependencies>
    <build>
        <finalName>businessOpportunity</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
