package com.foshan;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication
@EntityScan("com.foshan.entity")
@EnableTransactionManagement
@ComponentScan(basePackages = "com.foshan")
public class KingdeeApplication {
	public static void main(String[] args) {
		SpringApplication vote = new SpringApplication(KingdeeApplication.class);
		vote.run(args);
	}
}
