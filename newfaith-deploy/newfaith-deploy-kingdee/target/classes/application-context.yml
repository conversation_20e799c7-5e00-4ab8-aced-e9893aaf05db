#--------------newfaith平台自定义参数-------------------#
#-----------1、平台通用参数在faith节点下直接添加---------#
#-----------2、其它模块级、子系统级参数在下层节点添加-----#

faith:
  #业务编码(ServiceGroup)的预设长度
  serviceCodeLength: 3
  #返回数据Log打印开关
  returnDataPrintFlag: false
  #投票业务上传投票照片保存路径
  uploadImagePath: /usr/resource/wildfly/vote/images
  #问卷业务批量导入题库文件保存路径
  uploadExcelPath: /usr/resource/wildfly/vote/excel
  #默认查询深度
  defaultGetDataDepth: 10
  #批量处理行为数据批次大小
  visitRealTransBatchNum: 20000
  #BOSS地址
  bossIp: **************
  #BOSS端口
  bossPort: 8080
  #BOSS用户信息路径
  bossInfoPath: /faith/integate/getUserInfo
  #BOSS充值路径
  bossChargePath: /faith/integate/gizRreeCharging
  #商城图片地址 /data/resource/wildfly-shop/faithImages/assetImages
  #assetFilePath: /data/resource/wildfly-shop/faithImages/assetImages
  #正式社区
  assetFilePath: /data/resource/community/file/
  #商城图片相对地址
  assetFileUrl: /file
  #图片压缩比例宽*高
  imageWidthAndHeight: 1280*720
  #缩略中图片压缩比例宽*高
  middleImageWidthAndHeight: 640*360
  #缩略小图图片压缩比例宽*高
  smallImageWidthAndHeight: 265*152
  #默认开户
  registAccountFlag: 0
  #用户信息获取标识
  accountInfoFlag: 0
  #登录失败次数限制
  authLimit: 15
  #应用url前缀，用作资源权限判断
  appPrefix: party
  test:
    accountId: 2
 #微信公众平台参数
  wechat:
    appid: wx745c5330771d1af7
    appsecret: a9e2109cc9ab80756935f517856bd9ef
 #微信小程序参数
  miniProgram:
    appid: wxb1d31bc1efdd2901
    appsecret: 357931a7a27e2f9e75f9f88123dd3170
 #微信企业号参数
  wxEnterprice:
    agentid: 1000016
    appid: wxb68b2d5717de3d8d
    appsecret: p2h1twWoq7tFHZIknc8CGiaTYuYDyVwwxaThdMktNkg
 #微信小程序企业号接口参数
  wxEnterpriceMiniProgram:
    agentid: 1000018
    appid: wxb68b2d5717de3d8d
    appsecret: WmbTXPhjw0uiO8XVGRCWc69LRovSj4WZfAlBtxSzXwk
  #短信平台接口机地址
  smsInterfaceUrl: http://127.0.0.1:8082/shop/sendSms 
  #会员/公众用户的角色ID,多个以逗号隔开
  memberRoleId: 2
 #account类型，sql方式插入数据到t_account表时使用
  accountType: C
  #小程序登录自动注册开关，0-关，1-开
  miniprogramAutoRegist: 0
  #登录验证短信发送次数限制
  smsSendLimit: 5
  #短信验证时间间隔，单位分钟；如设置为2，2分钟内不允许再发
  smsTimeInterval: 2
  #短信有效时间（分钟）
  smsEffective: 5
  #修改密码短信验证允许输错次数
  smsIntendedEffect: 3
  #智能卡绑定账号个数限制，1个卡只能绑定5个账号
  bindSmartcardIdLimit: 5
  #智能卡号绑定的验证短信发送次数限制
  bindSmartcardIdSmsSendLimit: 10
  #科大讯飞语音授权接口——获取token
  speechSounds: 
    openAuthUrl: http://iflytek.gcable.cn:8087/IPTV-Auth/v1/openAuth
    appid: f10cad3c
    appsecret: 908474aec7ec6417
  #授权登录
  loginAuthenticationUrl:
    #h5授权页面
    html: http://fsnb.96956.com.cn/gcableshop/#/tvAuthLogin?loginTicket=
    #微信网页授权
    wechat: https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx745c5330771d1af7&redirect_uri=http%3A%2F%2Ffsdb.96956.com.cn%2Fauth.html&response_type=code&scope=snsapi_base&state=

shop: 
  #航天开发票--测试--地址
  invoiceServiceUrl: http://www.aisinogz.com:19876/AisinoFp-test/eliWebService.ws?wsdl
  #航天开发票--正式(域名)--地址
  #invoiceServiceUrl: http://www.aisinogz.com:19876/AisinoFp_gdgdwl/eliWebService.ws?wsdl
  #航天开发票--正式(IP)--地址
  #invoiceServiceUrl: http://*************:29876/AisinoFp_gdgdwl/eliWebService.ws?wsdl
  
  #UTVGO演唱会服务地址
  utvgoLiveSyncUrl: http://*************/activity-client-web/activity/freeCardController/syncLiveCard.utvgo
  utvgoLiveCancelUrl: http://*************/activity-client-web/activity/freeCardController/cancelLiveCard.utvgo
  utvgoGetLiveInfoUrl: http://*************/activity-client-web/activity/freeCardController/findLiveCardDetails.utvgo
  #点播视频产品购买后的有效播放时间,单位：天
  videoValidTime: 3
party:
  #党员的角色ID,多个以逗号隔开
  memberRoleId: 9
  loginAddress: http://fs.gcable.net/goodgoodstudy/#/?type=bind&smartcardId=
  checkinAddress: http://fs.gcable.net/goodgoodstudy/#/?type=signup
  commentAddress: http://fs.gcable.net/goodgoodstudy/#/?type=movie
  
#智慧社区
community: 
  #正式社区支付模块付款服务地址
  #正式社区环境
  #paymentServiceUrl: http://127.0.0.1:8080/community_dev/payment/pay
  #refundServiceUrl: http://127.0.0.1:8080/community_dev/payment/refund
  #queryPaymentResultServiceUrl: http://127.0.0.1:8080/community_dev/payment/query
  paymentServiceUrl: http://127.0.0.1:9292/community_dev/payment/pay
  refundServiceUrl: http://127.0.0.1:9292/community_dev/payment/refund
  queryPaymentResultServiceUrl: http://127.0.0.1:9292/community_dev/payment/query
  paymentMerchantCode: puhuameiju_yjnsq
  paymentSecret: yjnGcable#86
  #正式社区支付模块付款结果通知地址
  #正式社区环境
  #paymentNotifyUrl: http://127.0.0.1:8080/community_dev/paymentNotify
  #refundNotifyUrl: http://127.0.0.1:8080/community_dev/refundNotify
  paymentNotifyUrl: http://127.0.0.1:9292/community_dev/paymentNotify
  refundNotifyUrl: http://127.0.0.1:9292/community_dev/refundNotify
  paymentReturnUrl: /paymentNotify
  #管理费预收月份
  advanceReceiptsMonth: 12
  #开户行行号
  bankNo: "{\"中国光大银行\":\"************\",\"中国银行\":\"************\",\"交通银行\":\"************\",\"农业银行\":\"************\",\"农村信用合作社\":\"************\",\"工商银行\":\"************\",\"广发银行\":\"************\",\"农商银行\":\"************\",\"建设银行\":\"************\",\"招商银行\":\"************\",\"邮政储蓄银行\":\"************\"}"
  #总表分摊结果
  #exportAllocationListUrl: http://127.0.0.1:8080/community/exportAllocationList
  exportAllocationListUrl: http://127.0.0.1:9191/community/exportAllocationList
  invoiceUrl: http://localhost:9292/community/bWInvoice


  
#智慧农业，广州老米接口机参数
gzlmzn:
  #token获取/刷新
  baseURL:  https://dfadmin.prod.gzlmzn.com
  appid: FSHZS 
  secret: nrDdIaICgOULxfEGtI5cNO1PCKzLWvwl

#支付插件相关的参数
payment:
  #如果使用nginx反向代理对外提供服务，需要配置nginx对外服务的IP,微信H5支付需要用到  
  #我们的实际环境************ ；测试环境***************
  nginxWanIp: ***************
  #微信支付商户API证书存放根目录 实际部署时配置 /data/resource/wildfly-shop/certFiles
  #正式社区环境
  #certFileRootDir: /data/resource/communityFile/certFiles
  certFileRootDir: /data/resource/wildfly-shop/certFiles
  #支付平台的网址 http://localhost:9191/community
  #正式社区环境
  #siteUrl: https://fs.gcable.cn/community
  siteUrl: http://fsdb.96956.com.cn/community_dev
  #微信H5及公众号支付完成支付后，跳转回应用的页面地址http://fsdb.96956.com.cn/gcableshop/#/order ，会自动在配置的URL后面增加 orderId=xxx&paymentSessionSn=xxx
  wxH5PaymentCallBackUrl: "http://fsdb.96956.com.cn/communityApp/#/order"
  #是否开启支付的模拟测试模式,1为调试模式，0为正式使用。调试模式所有支付及退款操作将不产生实际的费用，方便测试。商用环境时必须配置为0，切记！！！！
  paymentDebugMode: 1
  
  
