spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    druid:
      kingdee:
        #金蝶本地测试地址
        #url: **********************************************************
        #username: sa
        #password: welcome123
        #金蝶入账正式地址
        url: ****************************************************************************************************************;
        username: user2
        password: Gz2023@0203zY
        #金蝶测试地址
        #url: ************************************************************************************************;
        #username: user
        #password: Ab0203#2023Ba
        validation-query: select 1
      ###---多数据源配置结束---###
      #初始化连接大小
      initial-size: 50
      #最小空闲连接数
      min-idle: 50
      #最大连接数
      max-active: 1000
      # 配置获取连接等待超时的时间
      max-wait: 120000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      validation-query: select 1
      validation-query-timeout: 10000
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      #useGlobalDataSourceStat: true # 合并多个DruidDataSource的监控数据
      web-stat-filter:
        enabled: true
        url-pattern: "/*"
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
        session-stat-max-count: 1000
        session-stat-enable: true
        profile-enable: true
      #druid数据源监控的用户名和密码
      stat-view-servlet:
        enabled: true
        url-pattern: "/druid/*"
        login-username: admin
        login-password: FScatv@2016
        allow: 127.0.0.1
        reset-enable: true
  jpa:
    database-platform: org.hibernate.dialect.SQLServer2016Dialect
    show-sql: true
    open-in-view: false
    hibernate:
      ddl-auto: update
      #Hibernate生成数据表的字段按实体类属性名保持一致
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    properties:
      hibernate:
        current_session_context_class: org.springframework.orm.hibernate5.SpringSessionContext
        #解决hibernate5版本flush报无事务错误
        allow_update_outside_transaction: true
        dialect: org.hibernate.dialect.SQLServer2016Dialect
        show-sql: true
        format-sql: true
        cache:
          use_query_cache: true
          use_second_level_cache: true
          region:
            factory_class: com.hazelcast.hibernate.HazelcastCacheRegionFactory
            #factory_class: org.hibernate.cache.jcache.internal.JCacheRegionFactory
        javax:
          cache:
            missing_cache_strategy: create