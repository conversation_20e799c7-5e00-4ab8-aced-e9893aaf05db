package com.foshan.form.hospital.request;

import com.foshan.form.request.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="体检结果列表信息查询请求对象(GetMedicalResultListRequest)")
public class GetMedicalResultListRequest extends BaseRequest {

	/**
	 * 体检结果列表信息查询请求对象
	 */
	private static final long serialVersionUID = -8613624394456762106L;

	@ApiModelProperty(value = "诊疗卡类型 1-院内诊疗卡 2-社保卡 3-医保卡 4-佛山健康卡",example="1")
	private Integer patCardType;
	@ApiModelProperty(value = "诊疗卡号", example = "123456")
	private String patCardNo;
	@ApiModelProperty(value = "",example="分时开始时间 格式：YYYY-MM-DD")
	private String beginDate;
	@ApiModelProperty(value = "",example="分时结束时间 格式：YYYY-MM-DD")
	private String endDate;


}
