package com.foshan.form.hospital.response;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.hospital.DoctorForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取医生列表返回列表对象(GetDoctorListRes)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetDoctorListRes extends BasePageResponse {

	/**
	 * 获取医生列表信息列表响应对象
	 */
	private static final long serialVersionUID = -4054673239093305307L;
	
	@ApiModelProperty(value = "医生列表")
	private List<DoctorForm> doctorList = new ArrayList<DoctorForm>();

}
