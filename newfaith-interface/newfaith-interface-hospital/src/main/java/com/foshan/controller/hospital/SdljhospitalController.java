package com.foshan.controller.hospital;

import com.foshan.form.hospital.request.*;
import com.foshan.form.hospital.response.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.response.GenericResponse;

@Api(tags = "融合平台--顺德伦教医院接口机")
@RestController
@RequestMapping("/hospital")
public class SdljhospitalController extends BaseHospitalController {

    @PostConstruct
    public void initHospital() {
        sdljhospitalService.gainHospitalParameter();
    }

    // 重新获取医院配置信息
    @ApiOperation(value = "重新获取医院配置信息(gainHospitalParameter)", httpMethod = "POST", notes = "重新获取医院配置信息，传空{}即可；")
    @ResponseBody
    @RequestMapping(value = "/gainHospitalParameter", method = {
            RequestMethod.POST}, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse gainHospitalParameter(@RequestBody GetDoctorListRequest req) {
        GenericResponse res = (GenericResponse) sdljhospitalService.gainHospitalParameter();
        return res;
    }

    // 获取科室列表信息
    @ApiOperation(value = "获取科室列表信息(getDepartmentList)", httpMethod = "POST", notes = "获取科室列表信息，传空{}即可；")
    @ResponseBody
    @RequestMapping(value = "/getDepartmentList", method = {
            RequestMethod.POST}, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetDepartmentListRes getDepartmentList(@RequestBody GetDepartmentListRequest req) {
        GetDepartmentListRes res = (GetDepartmentListRes) sdljhospitalService.getDepartmentList(req);
        return res;
    }

    // 获取医生列表信息
    @ApiOperation(value = "获取医生列表信息(getDoctorList)", httpMethod = "POST", notes = "获取医生列表信息，传空{}即可；")
    @ResponseBody
    @RequestMapping(value = "/getDoctorList", method = {
            RequestMethod.POST}, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetDoctorListRes getDoctorList(@RequestBody GetDoctorListRequest req) {
        GetDoctorListRes res = (GetDoctorListRes) sdljhospitalService.getDoctor(req);
        return res;
    }

    // 获取医生列表信息
    @ApiOperation(value = "获取预约号源医生列表信息(getSourceDoctorList)", httpMethod = "POST", notes = "获取医生列表信息，传空{}即可；")
    @ResponseBody
    @RequestMapping(value = "/getSourceDoctorList", method = {
            RequestMethod.POST}, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetDoctorListRes getSourceDoctorList(@RequestBody GetDoctorListRequest req) {
        GetDoctorListRes res = (GetDoctorListRes) sdljhospitalService.getSourceDoctorList(req);
        return res;
    }


    // 患者信息查询
    @ApiOperation(value = "患者信息查询(getPatient)", httpMethod = "POST", notes = "患者信息查询，传空{}即可；")
    @ResponseBody
    @RequestMapping(value = "/getPatient", method = {
            RequestMethod.POST}, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetPatientRes getPatient(@RequestBody GetPatientRequest req) {
        GetPatientRes res = (GetPatientRes) sdljhospitalService.getPatient(req);
        return res;
    }


    //预约挂号
    @ApiOperation(value = "预约挂号(appointment)", httpMethod = "POST", notes = "预约挂号，传空{}即可；")
    @ResponseBody
    @RequestMapping(value = "/appointment", method = {
            RequestMethod.POST}, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public AppointmentRes appointment(@RequestBody AppointmentRequest req) {
        AppointmentRes res = (AppointmentRes) sdljhospitalService.appointment(req);
        return res;
    }


    //取消预约挂号
    @ApiOperation(value = "取消预约挂号(cancelAppointment)", httpMethod = "POST", notes = "取消预约挂号，传orderNum,cancelReason即可；")
    @ResponseBody
    @RequestMapping(value = "/cancelAppointment", method = {
            RequestMethod.POST}, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public AppointmentRes cancelAppointment(@RequestBody AppointmentRequest req) {
        AppointmentRes res = (AppointmentRes) sdljhospitalService.cancelAppointment(req);
        return res;
    }


    //获取预约号源信息
    @ApiOperation(value = "获取预约号源信息(getSourceInformation)", httpMethod = "POST", notes = "获取预约号源信息，传空{}即可；")
    @ResponseBody
    @RequestMapping(value = "/getSourceInformation", method = {
            RequestMethod.POST}, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetSourceInformationRes getSourceInformation(@RequestBody GetSourceInformationRequest req) {
        GetSourceInformationRes res = (GetSourceInformationRes) sdljhospitalService.getSourceInformation(req);
        return res;
    }


    //获取服务时间
    @ApiOperation(value = "获取服务时间(getServerTime)", httpMethod = "POST", notes = "获取服务时间，传空{}即可；")
    @ResponseBody
    @RequestMapping(value = "/getServerTime", method = {
            RequestMethod.POST}, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetServerTimeRes getServerTime(@RequestBody GetDoctorListRequest req) {
        GetServerTimeRes res = (GetServerTimeRes) sdljhospitalService.getServerTime();
        return res;
    }

    // 获取预约列表
    @ApiOperation(value = "获取预约列表(getAppointmentOrderList)", httpMethod = "POST", notes = "获取预约列表，传空{}即可；")
    @ResponseBody
    @RequestMapping(value = "/getAppointmentOrderList", method = {
            RequestMethod.POST}, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetAppointmentOrderListRes getAppointmentOrderList(@RequestBody AppointmentRequest req) {
        GetAppointmentOrderListRes res = (GetAppointmentOrderListRes) sdljhospitalService.getAppointmentOrderList(req);
        return res;
    }

    // 获取检查报告详情
    @ApiOperation(value = "获取检查报告详情(getMedicalResultInfo)", httpMethod = "POST", notes = "获取检查报告详情；")
    @ResponseBody
    @RequestMapping(value = "/getMedicalResultInfo", method = {
            RequestMethod.POST}, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetMedicalResultInfoRes getMedicalResultInfo(@RequestBody GetMedicalResultInfoRequest req) {
        GetMedicalResultInfoRes res = (GetMedicalResultInfoRes) sdljhospitalService.getMedicalResultInfo(req);
        return res;
    }

    // 获取检查报告列表详情
    @ApiOperation(value = "获取检查报告列表(getMedicalResultList)", httpMethod = "POST", notes = "获取检查报告列表；")
    @ResponseBody
    @RequestMapping(value = "/getMedicalResultList", method = {
            RequestMethod.POST}, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetMedicalResultListRes getMedicalResultList(@RequestBody GetMedicalResultListRequest req) {
        GetMedicalResultListRes res = (GetMedicalResultListRes) sdljhospitalService.getMedicalResultList(req);
        return res;
    }

    // 获取检验结果详情
    @ApiOperation(value = "获取检验结果详情(getInspectReportInfo)", httpMethod = "POST", notes = "获取检验结果详情；")
    @ResponseBody
    @RequestMapping(value = "/getInspectReportInfo", method = {
            RequestMethod.POST}, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetInspectReportInfoRes getInspectReportInfo(@RequestBody GetInspectReportInfoRequest req) {
        GetInspectReportInfoRes res = (GetInspectReportInfoRes) sdljhospitalService.getInspectReportInfo(req);
        return res;
    }

    // 获取检验结果列表详情
    @ApiOperation(value = "获取检验结果列表(getInspectReportList)", httpMethod = "POST", notes = "获取检验结果列表；")
    @ResponseBody
    @RequestMapping(value = "/getInspectReportList", method = {
            RequestMethod.POST}, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetInspectReportListRes getInspectReportList(@RequestBody GetInspectReportListRequest req) {
        GetInspectReportListRes res = (GetInspectReportListRes) sdljhospitalService.getInspectReportList(req);
        return res;
    }

    //查询家庭医生签约信息
    @ApiOperation(value = "查询家庭医生签约信息(familySignByIdCard)", httpMethod = "POST", notes = "查询家庭医生签约信息；")
    @ResponseBody
    @RequestMapping(value = "/familySignByIdCard", method = {
            RequestMethod.POST}, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetFamilySignByIdCardInfoRes familySignByIdCard(@RequestBody PublicHealthRequest req) {
        GetFamilySignByIdCardInfoRes res = (GetFamilySignByIdCardInfoRes) sdljhospitalService.familySignByIdCard(req);
        return res;
    }

    //查询家庭医生签约信息
    @ApiOperation(value = "查询家庭医生签约信息(healthCheckByIdCard)", httpMethod = "POST", notes = "healthCheckByIdCard；")
    @ResponseBody
    @RequestMapping(value = "/healthCheckByIdCard", method = {
            RequestMethod.POST}, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetHealthCheckByIdCardInfoRes healthCheckByIdCard(@RequestBody PublicHealthRequest req) {
        GetHealthCheckByIdCardInfoRes res = (GetHealthCheckByIdCardInfoRes) sdljhospitalService.healthCheckByIdCard(req);
        return res;
    }

}
