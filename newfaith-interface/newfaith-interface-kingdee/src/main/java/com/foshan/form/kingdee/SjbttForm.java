package com.foshan.form.kingdee;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "收据表体")
@JsonInclude(Include.NON_NULL)
public class SjbttForm implements IForm {
	/**
	* 
	*/
	private static final long serialVersionUID = 1533259871006439671L;

	@ApiModelProperty(value = "收据编号")
	private String fBillNo;
	@ApiModelProperty(value = "收费项目")
	private String fText4;
	@ApiModelProperty(value = "属期")
	private String fText5;
	@ApiModelProperty(value = "金额")
	private BigDecimal fAmount8;
	@ApiModelProperty(value = "别墅管理费+别墅管理费项目")
	private String fText6;
	@ApiModelProperty(value = "别墅管理费+金额")
	private BigDecimal fAmount10;
	@ApiModelProperty(value = "别墅管理费+不含税金额")
	private BigDecimal fAmount11;
	@ApiModelProperty(value = "别墅管理费+税额")
	private BigDecimal fAmount12;
	@ApiModelProperty(value = "物业管理费+物业管理费项目")
	private String fText7;
	@ApiModelProperty(value = "物业管理费+金额")
	private BigDecimal fAmount13;
	@ApiModelProperty(value = "物业管理费+不含税金额")
	private BigDecimal fAmount14;
	@ApiModelProperty(value = "物业管理费+税额")
	private BigDecimal fAmount15;
	@ApiModelProperty(value = "花园管理费+花园管理费项目")
	private String fText8;
	@ApiModelProperty(value = "花园管理费+金额")
	private BigDecimal fAmount16;
	@ApiModelProperty(value = "花园管理费+不含税金额")
	private BigDecimal fAmount17;
	@ApiModelProperty(value = "花园管理费+税额")
	private BigDecimal fAmount18;
	@ApiModelProperty(value = "车位管理费+车位管理费项目")
	private String fText9;
	@ApiModelProperty(value = "车位管理费+金额")
	private BigDecimal fAmount19;
	@ApiModelProperty(value = "车位管理费+不含税金额")
	private BigDecimal fAmount20;
	@ApiModelProperty(value = "车位管理费+税额")
	private BigDecimal fAmount21;
	@ApiModelProperty(value = "施工服务费+施工服务费项目")
	private String fText10;
	@ApiModelProperty(value = "施工服务费+金额")
	private BigDecimal fAmount22;
	@ApiModelProperty(value = "施工服务费+不含税金额")
	private BigDecimal fAmount23;
	@ApiModelProperty(value = "施工服务费+税额")
	private BigDecimal fAmount24;
	@ApiModelProperty(value = "花园及停车位管理费+花园及停车位管理费项目")
	private String fText11;
	@ApiModelProperty(value = "花园及停车位管理费+金额")
	private BigDecimal fAmount25;
	@ApiModelProperty(value = "花园及停车位管理费+不含税金额")
	private BigDecimal fAmount26;
	@ApiModelProperty(value = "花园及停车位管理费+税额")
	private BigDecimal fAmount27;
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	

	@ApiModelProperty(value = "景观大道路灯电费+景观大道路灯电费项目")
	private String fText12;
	@ApiModelProperty(value = "景观大道路灯电费+金额")
	private BigDecimal fAmount28;
	@ApiModelProperty(value = "景观大道路灯电费+不含税金额")
	private BigDecimal fAmount29;
	@ApiModelProperty(value = "景观大道路灯电费+税额")
	private BigDecimal fAmount30;
	@ApiModelProperty(value = "公共电费分摊+公共电费分摊项目")
	private String fText13;
	@ApiModelProperty(value = "公共电费分摊+金额")
	private BigDecimal fAmount31;
	@ApiModelProperty(value = "公共电费分摊+不含税金额")
	private BigDecimal fAmount32;
	@ApiModelProperty(value = "公共电费分摊+税额")
	private BigDecimal fAmount33;
	@ApiModelProperty(value = "二区车库公共电费+二区车库公共电费项目")
	private String fText14;
	@ApiModelProperty(value = "二区车库公共电费+金额")
	private BigDecimal fAmount34;
	@ApiModelProperty(value = "二区车库公共电费+不含税金额")
	private BigDecimal fAmount35;
	@ApiModelProperty(value = "二区车库公共电费+税额")
	private BigDecimal fAmount36;
	@ApiModelProperty(value = "三区车库公共电费+三区车库公共电费项目")
	private String fText15;
	@ApiModelProperty(value = "三区车库公共电费+金额")
	private BigDecimal fAmount37;
	@ApiModelProperty(value = "三区车库公共电费+不含税金额")
	private BigDecimal fAmount38;
	@ApiModelProperty(value = "三区车库公共电费+税额")
	private BigDecimal fAmount39;
	@ApiModelProperty(value = "空中花园公共电费+空中花园公共电费项目")
	private String fText16;
	@ApiModelProperty(value = "空中花园公共电费+金额")
	private BigDecimal fAmount40;
	@ApiModelProperty(value = "空中花园公共电费+不含税金额")
	private BigDecimal fAmount41;
	@ApiModelProperty(value = "空中花园公共电费+税额")
	private BigDecimal fAmount42;
	@ApiModelProperty(value = "车库公共电费+车库公共电费项目")
	private String fText17;
	@ApiModelProperty(value = "车库公共电费+金额")
	private BigDecimal fAmount43;
	@ApiModelProperty(value = "车库公共电费+不含税金额")
	private BigDecimal fAmount44;
	@ApiModelProperty(value = "车库公共电费+税额")
	private BigDecimal fAmount45;
	@ApiModelProperty(value = "露天车位管理服务费+露天车位管理服务费项目")
	private String fText18;
	@ApiModelProperty(value = "露天车位管理服务费+金额")
	private BigDecimal fAmount46;
	@ApiModelProperty(value = "露天车位管理服务费+不含税金额")
	private BigDecimal fAmount47;
	@ApiModelProperty(value = "露天车位管理服务费+税额")
	private BigDecimal fAmount48;
	@ApiModelProperty(value = "场地费+场地费项目")
	private String fText19;
	@ApiModelProperty(value = "场地费+金额")
	private BigDecimal fAmount49;
	@ApiModelProperty(value = "场地费+不含税金额")
	private BigDecimal fAmount50;
	@ApiModelProperty(value = "场地费+税额")
	private BigDecimal fAmount51;
	@ApiModelProperty(value = "出租车位管理服务费+出租车位管理服务费项目")
	private String fText20;
	@ApiModelProperty(value = "出租车位管理服务费+金额")
	private BigDecimal fAmount52;
	@ApiModelProperty(value = "出租车位管理服务费+不含税金额")
	private BigDecimal fAmount53;
	@ApiModelProperty(value = "出租车位管理服务费+税额")
	private BigDecimal fAmount54;
	@ApiModelProperty(value = "商铺物业管理费+商铺物业管理费项目")
	private String fText21;
	@ApiModelProperty(value = "商铺物业管理费+金额")
	private BigDecimal fAmount55;
	@ApiModelProperty(value = "商铺物业管理费+不含税金额")
	private BigDecimal fAmount56;
	@ApiModelProperty(value = "商铺物业管理费+税额")
	private BigDecimal fAmount57;
	@ApiModelProperty(value = "工程维修+工程维修项目")
	private String fText22;
	@ApiModelProperty(value = "工程维修+金额")
	private BigDecimal fAmount58;
	@ApiModelProperty(value = "工程维修+不含税金额")
	private BigDecimal fAmount59;
	@ApiModelProperty(value = "工程维修+税额")
	private BigDecimal fAmount60;
	@ApiModelProperty(value = "小区临时停车收费+小区临时停车收费项目")
	private String fText23;
	@ApiModelProperty(value = "小区临时停车收费+金额")
	private BigDecimal fAmount61;
	@ApiModelProperty(value = "小区临时停车收费+不含税金额")
	private BigDecimal fAmount62;
	@ApiModelProperty(value = "小区临时停车收费+税额")
	private BigDecimal fAmount63;
	@ApiModelProperty(value = "车库公共水费分摊+车库公共水费分摊项目")
	private String fText24;
	@ApiModelProperty(value = "车库公共水费分摊+金额")
	private BigDecimal fAmount64;
	@ApiModelProperty(value = "车库公共水费分摊+不含税金额")
	private BigDecimal fAmount65;
	@ApiModelProperty(value = "车库公共水费分摊+税额")
	private BigDecimal fAmount66;
	@ApiModelProperty(value = "日常办公费+日常办公费项目")
	private String fText25;
	@ApiModelProperty(value = "日常办公费+金额")
	private BigDecimal fAmount67;
	@ApiModelProperty(value = "日常办公费+不含税金额")
	private BigDecimal fAmount68;
	@ApiModelProperty(value = "日常办公费+税额")
	private BigDecimal fAmount69;
	@ApiModelProperty(value = "砂石等费用+砂石等费用项目")
	private String fText26;
	@ApiModelProperty(value = "砂石等费用+金额")
	private BigDecimal fAmount70;
	@ApiModelProperty(value = "砂石等费用+不含税金额")
	private BigDecimal fAmount71;
	@ApiModelProperty(value = "砂石等费用+税额")
	private BigDecimal fAmount72;
	@ApiModelProperty(value = "损害财产赔偿款+损害财产赔偿款项目")
	private String fText27;
	@ApiModelProperty(value = "损害财产赔偿款+含税金额")
	private BigDecimal fAmount73;
	@ApiModelProperty(value = "损害财产赔偿款+不含税金额")
	private BigDecimal fAmount74;
	@ApiModelProperty(value = "损害财产赔偿款+税额")
	private BigDecimal fAmount75;
	@ApiModelProperty(value = "有偿绿化服务费+有偿绿化服务费项目")
	private String fText28;
	@ApiModelProperty(value = "有偿绿化服务费+金额")
	private BigDecimal fAmount76;
	@ApiModelProperty(value = "有偿绿化服务费+不含税金额")
	private BigDecimal fAmount77;
	@ApiModelProperty(value = "有偿绿化服务费+税额")
	private BigDecimal fAmount78;
	@ApiModelProperty(value = "有偿清洁服务费+有偿清洁服务费项目")
	private String fText29;
	@ApiModelProperty(value = "有偿清洁服务费+金额")
	private BigDecimal fAmount79;
	@ApiModelProperty(value = "有偿清洁服务费+不含税金额")
	private BigDecimal fAmount80;
	@ApiModelProperty(value = "有偿清洁服务费+税额")
	private BigDecimal fAmount81;
	@ApiModelProperty(value = "IC卡工本费+IC卡工本费项目")
	private String fText30;
	@ApiModelProperty(value = "IC卡工本费+金额")
	private BigDecimal fAmount82;
	@ApiModelProperty(value = "IC卡工本费+不含税金额")
	private BigDecimal fAmount83;
	@ApiModelProperty(value = "IC卡工本费+税额")
	private BigDecimal fAmount84;
	@ApiModelProperty(value = "社区活动收入+社区活动收入项目")
	private String fText31;
	@ApiModelProperty(value = "社区活动收入+金额")
	private BigDecimal fAmount85;
	@ApiModelProperty(value = "社区活动收入+不含税金额")
	private BigDecimal fAmount86;
	@ApiModelProperty(value = "社区活动收入+税额")
	private BigDecimal fAmount87;
	@ApiModelProperty(value = "证件工本费+证件工本费项目")
	private String fText32;
	@ApiModelProperty(value = "证件工本费+金额")
	private BigDecimal fAmount88;
	@ApiModelProperty(value = "证件工本费+不含税金额")
	private BigDecimal fAmount89;
	@ApiModelProperty(value = "证件工本费+税额")
	private BigDecimal fAmount90;
	@ApiModelProperty(value = "违约金+违约金项目")
	private String fText33;
	@ApiModelProperty(value = "违约金+金额")
	private BigDecimal fAmount91;
	@ApiModelProperty(value = "违约金+不含税金额")
	private BigDecimal fAmount92;
	@ApiModelProperty(value = "违约金+税额")
	private BigDecimal fAmount93;
	@ApiModelProperty(value = "代收车位租金+代收车位租金项目")
	private String fText34;
	@ApiModelProperty(value = "代收车位租金+金额")
	private BigDecimal fAmount94;
	@ApiModelProperty(value = "代收车位租金+不含税金额")
	private BigDecimal fAmount95;
	@ApiModelProperty(value = "代收车位租金+税额")
	private BigDecimal fAmount96;
	@ApiModelProperty(value = "代收代缴房屋办证费+代收代缴房屋办证费项目")
	private String fText35;
	@ApiModelProperty(value = "代收代缴房屋办证费+金额")
	private BigDecimal fAmount97;
	@ApiModelProperty(value = "代收代缴房屋办证费+不含税金额")
	private BigDecimal fAmount98;
	@ApiModelProperty(value = "代收代缴房屋办证费+税额")
	private BigDecimal fAmount99;
	@ApiModelProperty(value = "员工餐费+员工餐费项目")
	private String fText36;
	@ApiModelProperty(value = "员工餐费+金额")
	private BigDecimal fAmount100;
	@ApiModelProperty(value = "员工餐费+不含税金额")
	private BigDecimal fAmount101;
	@ApiModelProperty(value = "员工餐费+税额")
	private BigDecimal fAmount102;
	@ApiModelProperty(value = "代收电费+代收电费项目")
	private String fText37;
	@ApiModelProperty(value = "代收电费+金额")
	private BigDecimal fAmount103;
	@ApiModelProperty(value = "代收电费+不含税金额")
	private BigDecimal fAmount104;
	@ApiModelProperty(value = "代收电费+税额")
	private BigDecimal fAmount105;
	@ApiModelProperty(value = "宿舍物业管理费+宿舍物业管理费项目")
	private String fText38;
	@ApiModelProperty(value = "宿舍物业管理费+金额")
	private BigDecimal fAmount106;
	@ApiModelProperty(value = "宿舍物业管理费+不含税金额") 
	private BigDecimal fAmount107;
	@ApiModelProperty(value = "宿舍物业管理费+税额") 
	private BigDecimal fAmount108;
	@ApiModelProperty(value = "垃圾清运费+垃圾清运费项目") 
	private String fText39;
	@ApiModelProperty(value = "垃圾清运费+金额") 
	private BigDecimal fAmount109;
	@ApiModelProperty(value = "垃圾清运费+不含税金额") 
	private BigDecimal fAmount110;
	@ApiModelProperty(value = "垃圾清运费+税额") 
	private BigDecimal fAmount111;
	@ApiModelProperty(value = "代收水费+代收水费项目") 
	private String fText40;
	@ApiModelProperty(value = "代收水费+金额") 
	private BigDecimal fAmount112;
	@ApiModelProperty(value = "代收水费+不含税金额") 
	private BigDecimal fAmount113;
	@ApiModelProperty(value = "代收水费+税额") 
	private BigDecimal fAmount114;
	@ApiModelProperty(value = "物业管理费保证金+物业管理费保证金项目") 
	private String fText41;
	@ApiModelProperty(value = "物业管理费保证金+金额") 
	private BigDecimal fAmount115;
	@ApiModelProperty(value = "物业管理费保证金+不含税金额") 
	private BigDecimal fAmount116;
	@ApiModelProperty(value = "物业管理费保证金+税额") 
	private BigDecimal fAmount117;
	@ApiModelProperty(value = "重型机械进场押金+重型机械进场押金项目") 
	private String fText42;
	@ApiModelProperty(value = "重型机械进场押金+金额") 
	private BigDecimal fAmount118;
	@ApiModelProperty(value = "重型机械进场押金+不含税金额") 
	private BigDecimal fAmount119;
	@ApiModelProperty(value = "重型机械进场押金+税额") 
	private BigDecimal fAmount120;
	@ApiModelProperty(value = "租金保证金+租金保证金项目") 
	private String fText43;
	@ApiModelProperty(value = "租金保证金+金额") 
	private BigDecimal fAmount121;
	@ApiModelProperty(value = "租金保证金+不含税金额") 
	private BigDecimal fAmount122;
	@ApiModelProperty(value = "租金保证金+税额") 
	private BigDecimal fAmount123;
	@ApiModelProperty(value = "装修保证金+装修保证金项目") 
	private String fText44;
	@ApiModelProperty(value = "装修保证金+金额") 
	private BigDecimal fAmount124;
	@ApiModelProperty(value = "装修保证金+不含税金额") 
	private BigDecimal fAmount125;
	@ApiModelProperty(value = "装修保证金+税额") 
	private BigDecimal fAmount126;
	@ApiModelProperty(value = "宿舍租金+宿舍租金") 
	private String fText45;
	@ApiModelProperty(value = "宿舍租金+金额") 
	private BigDecimal fAmount127;
	@ApiModelProperty(value = "宿舍租金+不含税金额") 
	private BigDecimal fAmount128;
	@ApiModelProperty(value = "宿舍租金+税额") 
	private BigDecimal fAmount129;
	@ApiModelProperty(value = "公共设施保证金+公共设施保证金项目") 
	private String fText46;
	@ApiModelProperty(value = "公共设施保证金+金额") 
	private BigDecimal fAmount130;
	@ApiModelProperty(value = "公共设施保证金+不含税金额") 
	private BigDecimal fAmount131;
	@ApiModelProperty(value = "公共设施保证金+税额") 
	private BigDecimal fAmount132;
	@ApiModelProperty(value = "穿梭车车费+穿梭车车费项目") 
	private String fText47;
	@ApiModelProperty(value = "穿梭车车费+金额") 
	private BigDecimal fAmount133;
	@ApiModelProperty(value = "穿梭车车费+不含税金额") 
	private BigDecimal fAmount134;
	@ApiModelProperty(value = "穿梭车车费+税额") 
	private BigDecimal fAmount135;
	@ApiModelProperty(value = "游泳池门票+游泳池门票项目") 
	private String fText48;
	@ApiModelProperty(value = "游泳池门票+金额") 
	private BigDecimal fAmount136;
	@ApiModelProperty(value = "游泳池门票+不含税金额") 
	private BigDecimal fAmount137;
	@ApiModelProperty(value = "游泳池门票+税额") 
	private BigDecimal fAmount138;
	@ApiModelProperty(value = "水电周转保证金+水电周转保证金项目") 
	private String fText49;
	@ApiModelProperty(value = "水电周转保证金+金额") 
	private BigDecimal fAmount139;
	@ApiModelProperty(value = "水电周转保证金+不含税金额") 
	private BigDecimal fAmount140;
	@ApiModelProperty(value = "水电周转保证金+税额") 
	private BigDecimal fAmount141;
	@ApiModelProperty(value = "商铺租金+商铺租金项目") 
	private String fText50;
	@ApiModelProperty(value = "商铺租金+金额") 
	private BigDecimal fAmount142;
	@ApiModelProperty(value = "商铺租金+不含税金额") 
	private BigDecimal fAmount143;
	@ApiModelProperty(value = "商铺租金+税额") 
	private BigDecimal fAmount144;
	@ApiModelProperty(value = "公共水费分摊+公共水费分摊项目") 
	private String fText51;
	@ApiModelProperty(value = "公共水费分摊+金额") 
	private BigDecimal fAmount145;
	@ApiModelProperty(value = "公共水费分摊+不含税金额") 
	private BigDecimal fAmount146;
	@ApiModelProperty(value = "公共水费分摊+税额") 
	private BigDecimal fAmount147;
	@ApiModelProperty(value = "小卖部食品+小卖部食品项目") 
	private String fText52;
	@ApiModelProperty(value = "小卖部食品+金额") 
	private BigDecimal fAmount148;
	@ApiModelProperty(value = "小卖部食品+不含税金额") 
	private BigDecimal fAmount149;
	@ApiModelProperty(value = "小卖部食品+税额") 
	private BigDecimal fAmount150;
	@ApiModelProperty(value = "总金额") 
	private BigDecimal fAmount151;
	@ApiModelProperty(value = "花园及停车位管理费违约金+项目") 
	private String fText56;
	@ApiModelProperty(value = "花园及停车位管理费违约金+金额") 
	private BigDecimal fAmount9;
	@ApiModelProperty(value = "花园及停车位管理费违约金+不含税金额") 
	private BigDecimal fAmount152;
	@ApiModelProperty(value = "花园及停车位管理费违约金+税额") 
	private BigDecimal fAmount153;
	@ApiModelProperty(value = "露天车位管理服务费违约金+项目") 
	private String fText57;
	@ApiModelProperty(value = "露天车位管理服务费违约金+金额") 
	private BigDecimal fAmount154;
	@ApiModelProperty(value = "露天车位管理服务费违约金+不含税金额") 
	private BigDecimal fAmount155;
	@ApiModelProperty(value = "露天车位管理服务费违约金+税额") 
	private BigDecimal fAmount156;
	@ApiModelProperty(value = "车位管理费违约金+项目") 
	private String fText58;
	@ApiModelProperty(value = "车位管理费违约金+金额") 
	private BigDecimal fAmount157;
	@ApiModelProperty(value = "车位管理费违约金+不含税金额") 
	private BigDecimal fAmount158;
	@ApiModelProperty(value = "车位管理费违约金+税额") 
	private BigDecimal fAmount159;
	@ApiModelProperty(value = "有偿服务费+有偿服务费项目") 
	private String fText59;
	@ApiModelProperty(value = "有偿服务费+金额") 
	private BigDecimal fAmount160;
	@ApiModelProperty(value = "有偿服务费+不含税金额") 
	private BigDecimal fAmount161;
	@ApiModelProperty(value = "有偿服务费+税额") 
	private BigDecimal fAmount162;
	@ApiModelProperty(value = "露天车位管理服务费2+项目") 
	private String fText60;
	@ApiModelProperty(value = "露天车位管理服务费2+含税金额") 
	private BigDecimal fAmount163;
	@ApiModelProperty(value = "露天车位管理服务费2+不含税金额") 
	private BigDecimal fAmount164;
	@ApiModelProperty(value = "露天车位管理服务费2+税额") 
	private BigDecimal fAmount165;
	@ApiModelProperty(value = "露天车位管理服务费3+项目") 
	private String fText61;
	@ApiModelProperty(value = "露天车位管理服务费3+含税金额") 
	private BigDecimal fAmount166;
	@ApiModelProperty(value = "露天车位管理服务费3+不含税金额") 
	private BigDecimal fAmount167;
	@ApiModelProperty(value = "露天车位管理服务费3+税额") 
	private BigDecimal fAmount168;
	@ApiModelProperty(value = "露天车位管理服务费4+项目") 
	private String fText62;
	@ApiModelProperty(value = "露天车位管理服务费4+含税金额") 
	private BigDecimal fAmount169;
	@ApiModelProperty(value = "露天车位管理服务费4+不含税金额") 
	private BigDecimal fAmount170;
	@ApiModelProperty(value = "露天车位管理服务费4+税额") 
	private BigDecimal fAmount171;
	@ApiModelProperty(value = "商业街临时停车费+项目") 
	private String fText67;
	@ApiModelProperty(value = "商业街临时停车费+金额") 
	private BigDecimal fAmount177;
	@ApiModelProperty(value = "商业街临时停车费+不含税金额") 
	private BigDecimal fAmount178;
	@ApiModelProperty(value = "商业街临时停车费+税额") 
	private BigDecimal fAmount179;
	@ApiModelProperty(value = "儿童公园及精灵屋门票款+项目") 
	private String fText63;
	@ApiModelProperty(value = "儿童公园及精灵屋门票款+含税金额") 
	private BigDecimal fAmount172;
	@ApiModelProperty(value = "儿童公园及精灵屋门票款+不含税金额") 
	private BigDecimal fAmount173;
	@ApiModelProperty(value = "儿童公园及精灵屋门票款+税额") 
	private BigDecimal fAmount174;
	@ApiModelProperty(value = "电费+电费项目") 
	private String fText68;
	@ApiModelProperty(value = "电费+金额") 
	private BigDecimal fAmount183;
	@ApiModelProperty(value = "电费+不含税金额") 
	private BigDecimal fAmount184;
	@ApiModelProperty(value = "电费+税额") 
	private BigDecimal fAmount185;
	@ApiModelProperty(value = "水费+水费项目") 
	private String fText69;
	@ApiModelProperty(value = "水费+水费金额") 
	private BigDecimal fAmount186;
	@ApiModelProperty(value = "水费+不含税金额") 
	private BigDecimal fAmount187;
	@ApiModelProperty(value = "水费+税额") 
	private BigDecimal fAmount188;
	@ApiModelProperty(value = "往来款+往来款项目") 
	private String fText70;
	@ApiModelProperty(value = "往来款+往来款金额") 
	private BigDecimal fAmount189;

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
