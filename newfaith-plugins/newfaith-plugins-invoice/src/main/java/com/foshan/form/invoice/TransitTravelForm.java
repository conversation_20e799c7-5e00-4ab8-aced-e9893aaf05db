package com.foshan.form.invoice;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="旅客运输服务(TransitTravelForm)")
@JsonInclude(Include.NON_NULL)
public class TransitTravelForm {
	@ApiModelProperty(value = "出行人")
	private String traveler;
	@ApiModelProperty(value = "出行日期, yyyyMMdd格式")
	private String travelDate;
	@ApiModelProperty(value = "出行人证件类型")
	private String idType;
	@ApiModelProperty(value = "身份证件号码")
	private String idCard;
	@ApiModelProperty(value = "起运地")
	private String fromPlace;
	@ApiModelProperty(value = "到达地")
	private String toPlace;
	@ApiModelProperty(value = "等级")
	private String grade;
	@ApiModelProperty(value = "交通工具类型1:飞机2：火车3: 长途汽车4: 公共交通5: 出租车6: 汽车7: 船舶9: 其他")
	private String vehicleType;
}
