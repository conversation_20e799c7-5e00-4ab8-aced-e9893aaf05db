package com.foshan.form.invoice;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonInclude(Include.NON_NULL) 
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class MxxxForm implements IForm {
	/**
	 * 商品明细信息
	 */
	private static final long serialVersionUID = -551364465816926433L;

	@ApiModelProperty(value = "单据行号")
	private String djhh;
	@ApiModelProperty(value = "发票行性质;0:正常行;1:折扣行(折扣行金额负);2:被折扣行(被折扣行金额正)")
	private String fphxz;
	@ApiModelProperty(value = "商品名称")
	private String spmc;
	@ApiModelProperty(value = "税收编码;长度19位")
	private String ssbm;
	@ApiModelProperty(value = "规格型号")
	private String ggxh;
	@ApiModelProperty(value = "计量单位;成品油只能为升或吨")
	private String jldw;
	@ApiModelProperty(value = "含税单价")
	private String hsdj;
	@ApiModelProperty(value = "商品数量")
	private String spsl;
	@ApiModelProperty(value = "含税金额;含税标志为1时必填 小数点2位")
	private String hsje;
	@ApiModelProperty(value = "不含税单价")
	private String bhsdj;
	@ApiModelProperty(value = "不含税金额;含税标志为0时必填 小数点2位")
	private String bhsje;
	@ApiModelProperty(value = "税率")
	private String tax;
	@ApiModelProperty(value = "税额;含税标志为0时必填 小数点2位")
	private String se;
	@ApiModelProperty(value = "零税率标识;0:正常税率(默认)1:免税2:不征税3:普通零税率")
	private String lslbs;
	@ApiModelProperty(value = "优惠政策标识,0:使用优惠政策;1:不使用(默认)")
	private String yhzcbs;
	@ApiModelProperty(value = "增值税特殊管理,优惠政策标识为0时必填")
	private String zzstsgl;
	@ApiModelProperty(value = "商品简称")
	private String spfwjc;
	@ApiModelProperty(value = "商品批次")
	private String sppc;
	@ApiModelProperty(value = "商品代码")
	private String spdm;
	@ApiModelProperty(value = "商品分类")
	private String spfl;


	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
