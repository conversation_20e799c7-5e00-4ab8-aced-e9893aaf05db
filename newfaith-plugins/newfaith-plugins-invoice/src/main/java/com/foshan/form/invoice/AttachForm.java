package com.foshan.form.invoice;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="附加要素(AttachForm)")
@JsonInclude(Include.NON_NULL)
public class AttachForm {
	@ApiModelProperty(value = "附加信息名称")
	private String attachName;
	@ApiModelProperty(value = "附加信息内容")
	private String content;
}
