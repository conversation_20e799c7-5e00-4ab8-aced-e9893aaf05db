package com.foshan.form.invoice.request;

import com.foshan.form.request.AuditlogInfo;
import com.foshan.form.request.IRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="百旺开已开发票查询请求参数(BWRedInfoQueryReq)")
public class BWRedInfoQueryReq implements IRequest,AuditlogInfo{

	/**
	 * 
	 */
	private static final long serialVersionUID = 809806864453431632L;
	@ApiModelProperty(value = "")
	private String apiName;
	@ApiModelProperty(value = "销货单位税号,纳税人识别号")
	private String sellerTaxCode;
	@ApiModelProperty(value = "开票终端编号")
	private String terminalCode;
	@ApiModelProperty(value = "确认单的UUID")
	private String redInfoId;
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
