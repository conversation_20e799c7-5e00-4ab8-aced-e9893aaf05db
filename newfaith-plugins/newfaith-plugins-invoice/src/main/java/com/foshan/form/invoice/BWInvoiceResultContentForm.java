package com.foshan.form.invoice;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonInclude(Include.NON_NULL) 
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class BWInvoiceResultContentForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2269825737678896957L;
	@ApiModelProperty(value = "单据编号(全局唯一)",example="1")
	private String djbh;

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
