package com.foshan.service.invoice.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foshan.service.invoice.IInvoiceUrlService;

@Transactional
@Service("invoiceUrlService")
public class InvoiceUrlServiceImpl extends GenericInvoiceService implements IInvoiceUrlService {
	private final static Logger logger = LoggerFactory.getLogger(InvoiceUrlServiceImpl.class);
	
}
