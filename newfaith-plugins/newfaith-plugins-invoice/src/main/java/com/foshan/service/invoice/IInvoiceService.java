package com.foshan.service.invoice;

import com.foshan.entity.invoice.InvoiceAccountEntity;
import com.foshan.form.invoice.request.BWQueryReq;
import com.foshan.form.invoice.request.BWRedApplyReq;
import com.foshan.form.invoice.request.BWRedInfoQueryReq;
import com.foshan.form.invoice.request.InvoiceReq;
import com.foshan.form.invoice.request.RedInfoOperateReq;
import com.foshan.form.invoice.request.RedIssueReq;
import com.foshan.form.response.IResponse;

public interface IInvoiceService {

	public IResponse invoice(InvoiceReq req);
	public IResponse queryInvoiceResult(InvoiceReq req,InvoiceAccountEntity account );
	public IResponse withdrawalRedRushApplication(InvoiceReq req);
	public IResponse queryWithdrawalRedRushApplicationResult(InvoiceReq req);
	public IResponse queryQuota();
	
	public IResponse updateToken();
	public IResponse bWQuery(BWQueryReq req);
	public IResponse bWRedApply(BWRedApplyReq req);
	public IResponse bWRedInfoQuery(BWRedInfoQueryReq req);
	public IResponse redIssue(RedIssueReq req);
	public IResponse redInfoOperate(RedInfoOperateReq req);
	
	
}
