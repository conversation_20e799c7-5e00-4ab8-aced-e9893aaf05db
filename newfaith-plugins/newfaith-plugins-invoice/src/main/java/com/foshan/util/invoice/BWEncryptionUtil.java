package com.foshan.util.invoice;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.SocketTimeoutException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.apache.http.HttpEntity;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.util.DateUtil;
import com.foshan.util.HttpClientUtil;
import com.foshan.util.JsonUtil;


public class BWEncryptionUtil {

	private static final ObjectMapper mapper = new ObjectMapper();
	/**
	 * md5+sha-1加密
	 * 
	 * @param inputText 要加密的内容（明文密码+用户盐值）
	 */
	public static String md5AndSha(String inputText) {
		return sha(md5(inputText));
	}

	/**
	 * md5加密
	 */
	public static String md5(String inputText) {
		return encrypt(inputText, "md5");
	}

	/**
	 * sha加密
	 */
	public static String sha(String inputText) {
		return encrypt(inputText, "sha-1");
	}

	/**
	 * md5或者sha-1加密
	 * 
	 * @param inputText     要加密的内容
	 * @param algorithmName 加密算法名称：md5或者sha-1，不区分大小写
	 */
	public  static String encrypt(String inputText, String algorithmName) {
		if (inputText == null || "".equals(inputText.trim())) {
			throw new IllegalArgumentException("请输入要加密的内容");
		}
		if (algorithmName == null || "".equals(algorithmName.trim())) {
			algorithmName = "md5";
		}
		String encryptText = null;
		try {
			MessageDigest m = MessageDigest.getInstance(algorithmName);
			m.update(inputText.getBytes("UTF8"));
			byte[] s = m.digest();
			return hex(s);
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return encryptText;
	}

	/**
	 * 返回十六进制字符串
	 */
	private static String hex(byte[] arr) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < arr.length; ++i) {
			sb.append(Integer.toHexString((arr[i] & 0xFF) | 0x100).substring(1, 3));
		}
		return sb.toString();
	}
	
	
	/**
	 * 给TOP请求签名。
	 *
	 * @param params 所有字符型的TOP请求参数
	 * @param secret 签名密钥
	 * @return 签名
	 * @throws Exception
	 */
	public static String signTopRequest(Map<String, String> params, String secret, String body) throws Exception {
		// 第一步：检查参数是否已经排序
		ArrayList<String> keys = new ArrayList<String>(params.keySet());
		Collections.sort(keys);
		// 第二步：把所有参数名和参数值串在一起
		StringBuilder query = new StringBuilder();
		query.append(secret);
		for (String key : keys) {
			String value = params.get(key);
			if (!isNull(key) && !isNull(value)) {
				query.append(key).append(value);
			}
		}
		JsonNode node = mapper.readTree(body);
		body = mapper.writeValueAsString(node);
		query.append(body);
		query.append(secret);
		// 第三步：使用MD5加密
		byte[] bytes;
		MessageDigest md5 = null;
		try {
			md5 = MessageDigest.getInstance("MD5");
		} catch (NoSuchAlgorithmException ignored) {
			throw new Exception(ignored);
		}
		bytes = md5.digest(query.toString().getBytes("UTF-8"));
		// 第四步：把二进制转化为大写的十六进制
		StringBuilder sign = new StringBuilder();
		for (byte b : bytes) {
			String hex = Integer.toHexString(b & 0xFF);
			if (hex.length() == 1) {
				sign.append("0");
			}
			sign.append(hex.toUpperCase());
		}
		return sign.toString();
	}
	
	public static boolean isNull(String str) {
		return (str == null || "".equals(str) ? true : false);
	} 
	
	
//	public static void main(String[] args) {
//		String time = System.currentTimeMillis()+"";
//		System.out.println(System.currentTimeMillis());
//		String url ="http://kptest.kaipiaoba.cn/bwkp/api/router/rest?method=baiwang.oauth.token&username="
//				+ "554433221100001&password=9822394f3f4b9428a7277372291bc3982e534f31&client_id=&client_secret=&timestamp="+time;
//		String password =encrypt(md5("bw8888888cfcd078-18be4eda-b84e76ab-186ff7e3"),"sha-1");
//		
//		System.out.println(password);
//		
//		String s="";
//		try {
//			s = HttpClientUtil.jsonPost(url,"UTF-8","",null);
//		} catch (ClientProtocolException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		} catch (IOException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
//		System.out.println(s);
//		
//	}
	
	private static String apiName ="baiwang.invoice.issue";//API名称
	private static String appKey = "10001008";// appKey
	private static String appSecret="3bdc2d57-efa94210-a115620a-f3279da8"; 
	private static String token = "13ca2c82b40146a482e591f45fa305ed"; 
	
	public static void main(String[] args) throws Exception {

//		Map<String, String> textParams = new HashMap<String, String>();
//
//		textParams.put("appid", "c5388e45f57048e0aa89246a7036e24b");
//		String content = Base64Util.encode("{\"djbh\":\"\",\"kpzddm\":\"91440607675150429J_QD01\"}");
//		textParams.put("content", content);
//		textParams.put("serviceid", "QDP-QYXX-10002");
//
//		//String signString = SignUtil.sign("0", "18618d634a904d94b2e1247e63b73d2e", textParams);
//		String signString = MD5Util.getMd5("c5388e45f57048e0aa89246a7036e24b"+"18618d634a904d94b2e1247e63b73d2e"+content+"QDP-QYXX-10002");
//		
//		String requestBody = "{\"appid\":\"c5388e45f57048e0aa89246a7036e24b\",\"serviceid\":\"QDP-QYXX-10002\",\"signType\":\"1\",\"signature\":\""+signString+"\",\"content\":\""+content+"\"}";
//		
//		//System.out.println("sign:"+signString);
//		
//		String url ="http://218.28.172.28:10017/api/xxgl/v2/tbkp.do";
//		//String s = HttpClientUtil.jsonPost(url,"UTF-8",requestBody,null);
//		//System.out.println(s);
//		System.out.println(Base64Util.decode("eyJkamJoIjoiMjAyMzA3MDUwMDAwMTAiLCJrcHpkZG0iOiI5MTQ0MDYwNzY3NTE1MDQyOUpfUUQwMSIsImZwbHhkbSI6IjAyIiwia3BseCI6IjAiLCJ0ZHlzbHhkbSI6IiIsInpzZnMiOiIwIiwicWRieiI6IjAiLCJ4ZmR6ZGgiOiLplIDotKfljZXkvY0tMTAwODYiLCJ4ZnloemgiOiLpk7booYzotKblj7ctMTIzNDU2Iiwia2htYyI6IuWQleeBvOmUiyDkuInljLoxOOW6pzEwNCIsImtoc2giOiIiLCJraGR6ZGgiOiLkvZvlsbHluILkuInmsLTljLropb/ljZfooZfpgZPph5Hms4nlpKfpgZMz5Y+35b6h5rGf5Y2X5Zu96ZmF56S+5Yy6Iiwia2h5aHpoIjoiIiwiZ21mTW9iaWxlIjoiIiwiZ21mRW1haWwiOiI2NzUyMTMzNThAcXEuY29tIiwiaHNqZSI6IjguNTAiLCJoamplIjoiNy41MiIsImhqc2UiOiIwLjk4Iiwia2NlIjoiMCIsImtqbHkiOiIiLCJmcGRtIjoiIiwiZnBobSI6IiIsInNmd3p6ZnAiOiIiLCJ6cHB6ZG0iOiIiLCJoc2J6IjoiIiwiYnoiOiIiLCJnc2RtIjoiOTE0NDA2MDc2NzUxNTA0MjlKIiwiYm1kbSI6IiIsInloZG0iOiI0OGZmODFmZTgxNzI0ZWNlOGEzOGNkZTMyZDE2MTE0MSIsImRqcnEiOiIyMDIzLTA3LTA1Iiwic2pseSI6IiIsImtwciI6IumCk+aVj+i0niIsInNrciI6IuaUtuasvuS6uiIsImZociI6IuWkjeaguOS6uiIsImt6MSI6IiIsImt6MiI6IiIsImt6MyI6IiIsIm14eHgiOlt7ImRqaGgiOiIxIiwiZnBoeHoiOiIwIiwic3BtYyI6Iui9rOWUrueUteWKmyIsInNzYm0iOiIxMTAwMTAxMDIwMjAwMDAwMDAwIiwiZ2d4aCI6IjIwMjMuMDQiLCJqbGR3Ijoi5pyIIiwiaHNkaiI6IjAuNTAiLCJzcHNsIjoiMSIsImhzamUiOiIwLjUwIiwiYmhzZGoiOiIiLCJiaHNqZSI6IjAuNDQiLCJ0YXgiOiIwLjEzIiwic2UiOiIwLjA2IiwibHNsYnMiOiIwIiwieWh6Y2JzIjoiMSIsInp6c3RzZ2wiOiIiLCJzcGZ3amMiOiIiLCJzcHBjIjoiIiwic3BkbSI6IiIsInNwZmwiOiIifSx7ImRqaGgiOiIyIiwiZnBoeHoiOiIwIiwic3BtYyI6Iui9rOWUrueUteWKmyIsInNzYm0iOiIxMTAwMTAxMDIwMjAwMDAwMDAwIiwiZ2d4aCI6IjIwMjMuMDQiLCJqbGR3Ijoi5pyIIiwiaHNkaiI6IjguMDAiLCJzcHNsIjoiMSIsImhzamUiOiI4LjAwIiwiYmhzZGoiOiIiLCJiaHNqZSI6IjcuMDgiLCJ0YXgiOiIwLjEzIiwic2UiOiIwLjkyIiwibHNsYnMiOiIwIiwieWh6Y2JzIjoiMSIsInp6c3RzZ2wiOiIiLCJzcGZ3amMiOiIiLCJzcHBjIjoiIiwic3BkbSI6IiIsInNwZmwiOiIifV19"));
//		
		
		//ReturnBo b = doPostHttp(url,requestBody,"UTF-8","UTF-8");
		//System.out.println(md5AndSha("bw888888d12c10c7-ec73468e-9b5f33d4-942fd29c"));
		//System.out.println(System.currentTimeMillis());
		

		
		String datetime =  "20240104130454";
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        LocalDateTime ldt = LocalDateTime.parse(datetime,dtf);
        ldt=ldt.minusMinutes(10); 
        System.out.println(ldt);
        DateTimeFormatter fa = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String datetime2 = ldt.format(fa);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        try {
            Date strTime = sdf.parse( datetime2);
            System.out.println(DateUtil.format(strTime,1));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        
	}
	
/*	  public static ReturnBo doPostHttp(String url, String bodyContent, String encoding, String resultEncoding)
	  {
//	    if (!isHttpUrl(url)) {
//	      return getErrorResult(new StringBuilder().append("非法的URL:").append(url).toString());
//	    }

	    if (StrUtil.isBlank(encoding)) {
	      encoding = "UTF-8";
	    }

	    if (StrUtil.isBlank(resultEncoding)) {
	      resultEncoding = "UTF-8";
	    }

	    RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(120000)
	      .setConnectionRequestTimeout(120000)
	      .setSocketTimeout(300000)
	      .build();

	    HttpPost postRequest = new HttpPost(url);

	    postRequest.setConfig(requestConfig);

	    postRequest.setHeader("Cache-Control", "no-cache");
	    postRequest.setHeader("Pragma", "no-cache");

	    if (StrUtil.isNotBlank(bodyContent)) {
	      StringEntity stringEntity = new StringEntity(bodyContent, encoding);
	      postRequest.setEntity(stringEntity);
	    }

	    String result = null;
	    int code = 0;
	    try { CloseableHttpClient httpClient = HttpClientBuilder.create().build(); Throwable localThrowable8 = null;
	      try {
	        CloseableHttpResponse response = httpClient.execute(postRequest);

	        Throwable localThrowable9 = null;
	        try
	        {
	          int status = response.getStatusLine().getStatusCode();
	          if (status == 200) {
	            HttpEntity entity = response.getEntity();
	            if (entity != null) {
	              result = EntityUtils.toString(entity, resultEncoding);
	              EntityUtils.consume(entity);
	            }
	            ReturnBo localReturnBo = getSuccessResult("请求成功", result);

	            if (response != null) if (localThrowable9 != null) try { response.close(); } catch (Throwable localThrowable) { localThrowable9.addSuppressed(localThrowable); } else response.close(); 
	            if (httpClient != null) if (localThrowable8 != null) try { httpClient.close(); } catch (Throwable localThrowable1) { localThrowable8.addSuppressed(localThrowable1); } else httpClient.close();
	            return localReturnBo;
	          }
	          code = -1;
	          result = new StringBuilder().append("返回码：").append(status).toString();
	        }
	        catch (Throwable localThrowable3)
	        {
	          localThrowable9 = localThrowable3; throw localThrowable3;
	        }
	        finally
	        {
	          if (response != null) if (localThrowable9 != null) try { response.close(); } catch (Throwable localThrowable4) { localThrowable9.addSuppressed(localThrowable4); } else response.close();
	        }
	      }
	      catch (Throwable localThrowable6)
	      {
	        localThrowable8 = localThrowable6; throw localThrowable6;
	      }
	      finally
	      {
	        if (httpClient != null) if (localThrowable8 != null) try { httpClient.close(); } catch (Throwable localThrowable7) { localThrowable8.addSuppressed(localThrowable7); } else httpClient.close();  
	      } } catch (ConnectTimeoutException e) { code = 9;
	      result = "服务器请求超时！";
	    } catch (SocketTimeoutException e) {
	      code = 9;
	      result = "服务器响应的超时";
	    } catch (IOException e) {
	      code = 7;
	      result = "IO异常，请检查网络是否连通";
	    } catch (Exception e) {
	      code = -1;
	      result = "不明异常";
	    } finally {
	      try {
	        postRequest.releaseConnection();
	      } catch (Exception localException7) {
	      }
	    }
	    return getResult(code, new StringBuilder().append("请求失败:").append(result).toString());
	  }

	  public static ReturnBo getResult(int code, String msg, Object data)
	  {
	    return new ReturnBo(code, msg, 0, data);
	  }
	  public static ReturnBo getResult(int code, String msg)
	  {
	    return new ReturnBo(code, msg, 0, null);
	  }
	  public static ReturnBo getSuccessResult(String msg)
	  {
	    return getResult(0, msg, null);
	  }

	  public static ReturnBo getSuccessResult(String msg, Object data)
	  {
	    return getResult(0, msg, data);
	  }*/
	
}
