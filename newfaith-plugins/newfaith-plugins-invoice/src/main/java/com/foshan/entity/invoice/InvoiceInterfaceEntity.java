package com.foshan.entity.invoice;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_invoice_interface")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class InvoiceInterfaceEntity implements IEntityBean {

	/**
	 * 发票接口
	 */
	private static final long serialVersionUID = 2477092686927307210L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '接口名称'")
	private String interfaceName;
	@Column(columnDefinition = "varchar(64) comment '接口名称'")
	private String interfaceCode;
	@Column(columnDefinition = "varchar(256) comment '接口描述'")
	private String description;
	@Column(columnDefinition = "varchar(3000) comment '参数'")
	private String parameter;


	
	@OneToMany(targetEntity = InvoicePostEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "invoiceInterfaceId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<InvoicePostEntity> postList = new ArrayList<InvoicePostEntity>();
	
	@ManyToOne(targetEntity = InvoiceUrlEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "invoiceUrlId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private InvoiceUrlEntity invoiceUrl;
	
}
