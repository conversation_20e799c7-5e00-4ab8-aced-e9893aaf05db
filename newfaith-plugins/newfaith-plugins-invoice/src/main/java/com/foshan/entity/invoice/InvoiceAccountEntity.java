package com.foshan.entity.invoice;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_invoice_account")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class InvoiceAccountEntity implements IEntityBean {

	/**
	 * 发票账户
	 */
	private static final long serialVersionUID = -756140615701510563L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '账户名称'")
	private String accountName;
	@Column(columnDefinition = "varchar(64) comment '密码'")
	private String password;
	@Column(columnDefinition = "varchar(500) comment '备注'")
	private String comment;
	@Column(columnDefinition = "varchar(3000) comment '参数'")
	private String parameter;
	@Column(columnDefinition = "int(1) comment '数据状态 0-无效 1-有效'")
	private Integer state;
	@Column(columnDefinition = "int(1) comment '账户类型 0:百望；1：航天；2：第三方；'")
	private Integer accountType;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '注册时间'")
	private Timestamp createTime;
	
	@ManyToOne(targetEntity = InvoiceAccountEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentInvoiceAccountId", referencedColumnName = "id", nullable = true)
	private InvoiceAccountEntity parentInvoiceAccount;
	
	@OneToMany(targetEntity = InvoiceAccountEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentInvoiceAccountId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<InvoiceAccountEntity> subAccountList = new ArrayList<InvoiceAccountEntity>();
	

	
	@ManyToMany(targetEntity = InvoiceUrlEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_invoice_account_url", joinColumns = @JoinColumn(name = "accountId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "urlId", referencedColumnName = "id"))
	@JsonIgnore
	private List<InvoiceUrlEntity> accountUrlList = new ArrayList<InvoiceUrlEntity>();
	

	@OneToMany(targetEntity = SellerInformationEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "accountId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<SellerInformationEntity> sellerInformationList = new ArrayList<SellerInformationEntity>();

}
