package com.foshan.service.auditlog.aspect;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import javax.servlet.http.HttpServletRequest;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.web.subject.WebSubject;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.foshan.dao.auditlog.IAuditlogDao;
import com.foshan.entity.auditlog.AuditlogEntity;
import com.foshan.form.request.BaseRequest;
import com.foshan.form.request.IRequest;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.response.IResponse;
import com.foshan.model.permssion.PrincipalModel;
import com.foshan.service.annotation.Audit;
import com.foshan.util.IpV4Util;

import cn.hutool.core.util.ArrayUtil;

@Aspect
@Component
public class AuditlogAspect {
    /**
     * 管理员用户审计日志处理切面
     * @see Audit 的实现
     */

    ThreadLocal<String> ip = new ThreadLocal<String>();
    ThreadLocal<String> requestBody = new ThreadLocal<String>();
    ThreadLocal<Integer> userId = new ThreadLocal<Integer>();
    ThreadLocal<String> name = new ThreadLocal<String>();
    ThreadLocal<String> userName = new ThreadLocal<String>();
    ThreadLocal<String> operate = new ThreadLocal<String>();
    ThreadLocal<String> auditInfo = new ThreadLocal<String>();

    private final static Logger logger = LoggerFactory.getLogger(AuditlogAspect.class);
    @Autowired	
    private IAuditlogDao auditlogDao;

    @Pointcut("@annotation(com.foshan.service.annotation.Audit)")
    public void audit() {
    }

    @Around("audit()")
    public Object aroundExec(ProceedingJoinPoint pjp) throws Throwable {

        //操作名称
        MethodSignature ms = (MethodSignature) pjp.getSignature();
        Method method = ms.getMethod();
        Audit auditAnnotation = method.getAnnotation(Audit.class);
        operate.set(auditAnnotation.operate());
        
        //获取审计数据
        //要求指定请求参数的名称，默认req
        Object[] args = pjp.getArgs();
        String[] parameterNames = ms.getParameterNames();
        int reqIndex = ArrayUtil.indexOf(parameterNames, auditAnnotation.getReqName());
        if(reqIndex != ArrayUtil.INDEX_NOT_FOUND) {
        	BaseRequest req = (BaseRequest) args[reqIndex];
        	auditInfo.set(req.transformAuditInfo());
        }else {
        	auditInfo.set("找不到名为req的参数");
		}

        //请求相关数据
        //通过了shiro获取主体数据
        HttpServletRequest request = (HttpServletRequest) ((WebSubject) SecurityUtils.getSubject()).getServletRequest();
        Object req = pjp.getArgs()[0];
        ip.set(IpV4Util.getIpAddr(request));
        requestBody.set(null!=req ? req.toString():"");

        // 用户信息数据
        // 登录接口在返回时补充处理
        if (SecurityUtils.getSubject() != null && SecurityUtils.getSubject().getPrincipals() != null) {
            Object principal = SecurityUtils.getSubject().getPrincipals().asList().get(1);
            if (principal instanceof PrincipalModel) {
                Class<?> userClass = PrincipalModel.class;
                Method getIdMethod = userClass.getMethod("getId");
                Method getNameMethod = userClass.getMethod("getName");
                Method getUserNameMethod = userClass.getMethod("getUserName");
                userId.set((Integer) getIdMethod.invoke(principal));
                name.set((String) getNameMethod.invoke(principal));
                userName.set((String) getUserNameMethod.invoke(principal));
            }
        }

        Object result = pjp.proceed();

        return result;
    }

    /**
     * 有返回
     *
     * @param res
     */
    @AfterReturning(pointcut = "audit()", returning = "res")
    public void doAfterReturning(IResponse res) {
        //未登录的用户不作记录
//        if (SecurityUtils.getSubject() == null || SecurityUtils.getSubject().getPrincipals() == null) {
//            return;
//        }

        //审计数据处理
        //存管理员用户的数据
        Object principal = null;
        if (SecurityUtils.getSubject() != null && SecurityUtils.getSubject().getPrincipals() != null) {
            principal = SecurityUtils.getSubject().getPrincipals().asList().get(1);
        }
        /**
         * 如果已经登录，则记录管理员的日志
         * 如果未登录，则记录日志，不能反映用户类型
         */
        AuditlogEntity auditlogEntity = new AuditlogEntity();
        if (principal != null && principal instanceof PrincipalModel) {
            /*处理登录成功*/
            if (userId.get() == null) {
                Class<?> userClass = PrincipalModel.class;
                try {
                    auditlogEntity.setUserId((Integer) userClass.getMethod("getId").invoke(principal));
                    auditlogEntity.setName((String) userClass.getMethod("getName").invoke(principal));
                    auditlogEntity.setUserName((String) userClass.getMethod("getUserName").invoke(principal));
                } catch (IllegalAccessException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                } catch (IllegalArgumentException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                } catch (NoSuchMethodException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                } catch (SecurityException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            } else {
                auditlogEntity.setUserId(userId.get());
                auditlogEntity.setName(name.get());
                auditlogEntity.setUserName(userName.get());
            }
            /*处理登录成功 结束*/
            auditlogEntity.setIp(ip.get());
            auditlogEntity.setRequest(requestBody.get());
            auditlogEntity.setOperate(operate.get());
            auditlogEntity.setOperateResult(null!=res ? ((BaseResponse) res).getRetInfo() :"");
            auditlogEntity.setRet(null!=res ? ((BaseResponse) res).getRet() : "");
            auditlogEntity.setResponse(null!=res ? res.toString() :"");
            auditlogEntity.setAuditInfo(auditInfo.get());
            //审计数据存数据库
            auditlogDao.save(auditlogEntity);
        }else{
            //处理没登录或未成功登录的情况
            auditlogEntity.setIp(ip.get());
            auditlogEntity.setRequest(requestBody.get());
            auditlogEntity.setOperate(operate.get());
            auditlogEntity.setOperateResult(((BaseResponse) res).getRetInfo());
            auditlogEntity.setRet(((BaseResponse) res).getRet());
            auditlogEntity.setResponse(res.toString());
            auditlogEntity.setAuditInfo(auditInfo.get());
            //审计数据存数据库
            auditlogDao.save(auditlogEntity);
        }
    }

    /**
     * 无返回
     *
     * @param joinPoint
     */
    @AfterReturning(pointcut = "audit()")
    public void doAfterReturning(JoinPoint joinPoint) {

    }
}
