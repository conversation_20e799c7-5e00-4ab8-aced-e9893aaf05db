package com.foshan.form.websocket.request;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "websocket配置请求(WebsocketReq)")
public class WebsocketReq extends BasePageRequest {

	/**
	 * websocket配置请求
	 */
	private static final long serialVersionUID = 451066456068738830L;
	
	@ApiModelProperty(value = "ID", example = "1")
	private Integer id;
	@ApiModelProperty(value = "名称", example = "1")
	private String name;
	@ApiModelProperty(value = "websocket的url")
	private String url;
	@ApiModelProperty(value = "最大连接数", example = "1")
	private Integer maxConnection;

}
