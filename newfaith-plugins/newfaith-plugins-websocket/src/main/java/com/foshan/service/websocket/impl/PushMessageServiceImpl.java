package com.foshan.service.websocket.impl;

import java.util.Objects;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.websocket.request.EventReq;
import com.foshan.service.websocket.IPushMessageService;
import com.foshan.service.websocket.netty.NettyConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.topic.ITopic;
import com.hazelcast.topic.Message;
import com.hazelcast.topic.MessageListener;

import io.netty.channel.Channel;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;

@Service
@Transactional
public class PushMessageServiceImpl extends GenericWebsocketService implements IPushMessageService,MessageListener<EventReq> {

	@Autowired
	HazelcastInstance hazelcastInstance;
	
	/**
	 * 推送消息到终端，通过hazelcast广播消息
	 * websocket服务端接收到消息后判断是否自身的channel所建立的连接，是则转发，否则不处理。
	 */
    @Override
    public BaseResponse pushMessageToOne(EventReq req) {
    	GenericResponse res = new GenericResponse();
    	ITopic<EventReq> eventTopic = hazelcastInstance.getTopic("event");
    	eventTopic.publish(req);
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        return res;
    }

    @Override
    public void pushMsgToAll(EventReq req) {
    	 
        
    }

	@Override
	public void onMessage(Message<EventReq> message) {
		EventReq req = message.getMessageObject();
		Channel channel = NettyConfig.getChannel(req.getSmartcardId());
        if (Objects.isNull(channel)) {
            return;
        }
        ObjectMapper mapper = new ObjectMapper();
        try {
			channel.writeAndFlush(new TextWebSocketFrame(mapper.writeValueAsString(req)));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
	}
}