package com.foshan.controller.websocket;

import javax.annotation.PostConstruct;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.foshan.form.websocket.request.EventReq;
import com.hazelcast.topic.ITopic;
import com.hazelcast.topic.MessageListener;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "websocket事件模块")
@RestController
public class PushMessageController extends BaseWebsocketController {
    @ApiOperation(value = "推送事件(pushOne)", httpMethod = "POST", notes = "推送事件给绑定的终端（smartcardid)")
    @ResponseBody
    @RequestMapping(value = "/pushMessageToOne", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse pushMessageToOne(@RequestBody EventReq req) {
    	BaseResponse res = pushMessageService.pushMessageToOne(req);
    	return res;
    }
    
    @SuppressWarnings("unchecked")
	@PostConstruct
    public void eventSubscriberInit() {
    	ITopic<EventReq> topic = hazelcastInstance.getTopic("event");
    	topic.addMessageListener((MessageListener<EventReq>) pushMessageService);
    	System.out.println("eventSubscriber init!");
    }
}
