package com.foshan.controller.vote;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.annotation.vote.CheckAdnormalVote;
import com.foshan.form.vote.request.VoteReq;
import com.foshan.form.vote.response.GetUserVoteInfoRes;
import com.foshan.form.vote.response.VoteRes;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

@Api(tags = "融合投票插件--投票模块")
@Slf4j
@RestController
public class VoteController extends BaseVoteController {
	// 投票
	@CheckAdnormalVote
	@PostMapping(value = "/addVote", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public VoteRes addVote(@RequestBody VoteReq req, HttpServletRequest request) throws JsonProcessingException {
		VoteRes res = (VoteRes) voteService.addVote(req);
		return res;

	}

	// 免登录投票
	@CheckAdnormalVote
	@PostMapping(value = "/addVoteWithoutLogin", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public VoteRes addVoteWithoutLogin(@RequestBody VoteReq req, HttpServletRequest request) throws JsonProcessingException {
		VoteRes res = (VoteRes) voteService.addVoteWithoutLogin(req);
		return res;

	}

	// 批量投票
	@PostMapping(value = "/addBatchVote", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public VoteRes addBatchVote(@RequestBody VoteReq req, HttpServletRequest request) throws JsonProcessingException {
		VoteRes res = (VoteRes) voteService.addBatchVote(req);
		return res;
	}

	// 获取用户投票信息
	@PostMapping(value = "/getUserVoteInfo", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetUserVoteInfoRes getUserVoteInfo(@RequestBody VoteReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetUserVoteInfoRes res = (GetUserVoteInfoRes) voteService.getUserVoteInfo(req);
		return res;
	}

	// 投票
//	@CheckAdnormalVote
	@PostMapping(value = "/getLessVoteNum", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public VoteRes getLessVoteNum(@RequestBody VoteReq req, HttpServletRequest request) throws JsonProcessingException {
		VoteRes res = (VoteRes) voteService.getLessVoteNum(req);
		return res;

	}

	@PostConstruct
	public void initAccountVote() {
		log.info("开始初始化用户投票数据！");
		voteService.initVoteMap();
	}

	@PreDestroy
	public void submitVoteMap() {
		log.info("开始保存用户投票数据");
		voteService.submitVoteMap();
	}
}
