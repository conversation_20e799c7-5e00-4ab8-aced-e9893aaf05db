package com.foshan.entity.vote;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;
import com.foshan.entity.GiftRollModelEntity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_lucky_prize")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class LuckyPrizeEntity implements IEntityBean {

	/**
	 * 抽奖活动奖品
	 */
	private static final long serialVersionUID = 5221394051618450670L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(10) comment '奖品编号'")
	private String prizeCode;
	@Column(columnDefinition = "varchar(10) comment '冲突编号，配置成一样的code就不会抽同code的奖品'")
	private String conflictCode;
	@Column(columnDefinition = "varchar(100) comment '奖品名称'")
	private String prizeName;
	@Column(columnDefinition = "varchar(200) comment '奖品图片'")
	private String prizeImage;
	@Column(columnDefinition = "int(5) comment '奖品数量'")
	private Integer prizeCount;
	@Column(columnDefinition = "double comment '中奖概率'")
	private Double odds;
	@Column(columnDefinition = "int(1) comment '奖品类别 0--实物  1--节目包  2--宽带包 3--电子券'")
	private Integer prizeType;
	@Column(columnDefinition = "int(1) comment '显示标识 0--不显示  1--显示'")
	private Integer showFlag;
	@ManyToOne(targetEntity = GiftRollModelEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "modelId", referencedColumnName = "id", nullable = true)
	private GiftRollModelEntity giftRollModel;
	@ManyToMany(targetEntity = LuckyRuleEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_lucky_rule_prize", 
	joinColumns = @JoinColumn(name = "prizeId", referencedColumnName = "id"), 
	inverseJoinColumns = @JoinColumn(name = "luckyRuleId", referencedColumnName = "id"))
	@JsonIgnore
	private List<LuckyRuleEntity> luckyRuleList = new ArrayList<>();

	
}
