package com.foshan.form.vote.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

@JsonInclude(Include.NON_NULL)  
public class ModifyQuestionPlanRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -2403307682390311488L;
	private Integer columnId;
	private Integer questionPlanId;
	private String questionPlanName;
	private String questionPlanDetail;
	private String questionPlanStartTime;
	private String questionPlanEndTime;
	private Integer durationTime;
	private Integer questionCount;
	private Integer questionPlanState;
	private Integer totalScore;

	public ModifyQuestionPlanRes() {
		super();
		// TODO Auto-generated constructor stub
	}

	public ModifyQuestionPlanRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	public ModifyQuestionPlanRes(Integer columnId, Integer questionPlanId, String questionPlanName,
			String questionPlanDetail, String questionPlanStartTime, String questionPlanEndTime, Integer durationTime,
			Integer questionCount, Integer questionPlanState, Integer totalScore) {
		super();
		this.columnId = columnId;
		this.questionPlanId = questionPlanId;
		this.questionPlanName = questionPlanName;
		this.questionPlanDetail = questionPlanDetail;
		this.questionPlanStartTime = questionPlanStartTime;
		this.questionPlanEndTime = questionPlanEndTime;
		this.durationTime = durationTime;
		this.questionCount = questionCount;
		this.questionPlanState = questionPlanState;
		this.totalScore = totalScore;
	}

	public Integer getColumnId() {
		return columnId;
	}

	public void setColumnId(Integer columnId) {
		this.columnId = columnId;
	}

	public Integer getQuestionPlanId() {
		return questionPlanId;
	}

	public void setQuestionPlanId(Integer questionPlanId) {
		this.questionPlanId = questionPlanId;
	}

	public String getQuestionPlanName() {
		return questionPlanName;
	}

	public void setQuestionPlanName(String questionPlanName) {
		this.questionPlanName = questionPlanName;
	}

	public String getQuestionPlanDetail() {
		return questionPlanDetail;
	}

	public void setQuestionPlanDetail(String questionPlanDetail) {
		this.questionPlanDetail = questionPlanDetail;
	}

	public String getQuestionPlanStartTime() {
		return questionPlanStartTime;
	}

	public void setQuestionPlanStartTime(String questionPlanStartTime) {
		this.questionPlanStartTime = questionPlanStartTime;
	}

	public String getQuestionPlanEndTime() {
		return questionPlanEndTime;
	}

	public void setQuestionPlanEndTime(String questionPlanEndTime) {
		this.questionPlanEndTime = questionPlanEndTime;
	}

	public Integer getDurationTime() {
		return durationTime;
	}

	public void setDurationTime(Integer durationTime) {
		this.durationTime = durationTime;
	}

	public Integer getQuestionCount() {
		return questionCount;
	}

	public void setQuestionCount(Integer questionCount) {
		this.questionCount = questionCount;
	}

	public Integer getQuestionPlanState() {
		return questionPlanState;
	}

	public void setQuestionPlanState(Integer questionPlanState) {
		this.questionPlanState = questionPlanState;
	}

	public Integer getTotalScore() {
		return totalScore;
	}

	public void setTotalScore(Integer totalScore) {
		this.totalScore = totalScore;
	}

	@Override
	public String toString() {
		return "ModifyQuestionPlanRes [columnId=" + columnId + ", questionPlanId=" + questionPlanId
				+ ", questionPlanName=" + questionPlanName + ", questionPlanDetail=" + questionPlanDetail
				+ ", questionPlanStartTime=" + questionPlanStartTime + ", questionPlanEndTime=" + questionPlanEndTime
				+ ", durationTime=" + durationTime + ", questionCount=" + questionCount + ", questionPlanState="
				+ questionPlanState + ", totalScore=" + totalScore + ", ret=" + ret + ", retInfo=" + retInfo + "]";
	}

}
