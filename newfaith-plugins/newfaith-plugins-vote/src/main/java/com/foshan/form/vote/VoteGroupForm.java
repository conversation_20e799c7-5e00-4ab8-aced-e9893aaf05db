package com.foshan.form.vote;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonInclude(Include.NON_EMPTY)
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class VoteGroupForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = 3134577325272130420L;
	private Integer groupId;
	private Integer columnId;
	private Integer serviceId;
	private String groupCode;
	private String groupInfo;
	private String groupName;
	private String groupImage;
	private String startTime;
	private String endTime;
	private Integer groupState;
	private Integer specialTimeFlag;
	private Integer specialVoteFlag;
	private Integer contestantCount;
	private Integer totalVisits;
	private Integer voteFlag;

	public VoteGroupForm(Integer groupId, Integer columnId, Integer serviceId, String groupCode, String groupInfo,
			String groupName, String groupImage, String startTime, String endTime, Integer groupState,
			Integer specialTimeFlag, Integer specialVoteFlag, Integer contestantCount) {
		super();
		this.groupId = groupId;
		this.columnId = columnId;
		this.serviceId = serviceId;
		this.groupCode = groupCode;
		this.groupInfo = groupInfo;
		this.groupName = groupName;
		this.groupImage = groupImage;
		this.startTime = startTime;
		this.endTime = endTime;
		this.groupState = groupState;
		this.specialTimeFlag = specialTimeFlag;
		this.specialVoteFlag = specialVoteFlag;
		this.contestantCount = contestantCount;
	}

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
