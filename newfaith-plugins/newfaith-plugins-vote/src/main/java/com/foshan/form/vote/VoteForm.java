package com.foshan.form.vote;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonInclude(Include.NON_NULL)
@Getter
@Setter
@NoArgsConstructor
public class VoteForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = 847960370911502496L;
	private String smartcardId;
	private String phone;
	private String userCode;
	private String voteTime;

	public VoteForm(String smartcardId, String phone, String userCode, String voteTime) {
		super();
		this.smartcardId = smartcardId;
		this.phone = phone;
		this.userCode = userCode;
		this.voteTime = voteTime;
	}

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
