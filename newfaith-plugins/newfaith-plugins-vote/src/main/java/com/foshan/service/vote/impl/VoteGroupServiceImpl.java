package com.foshan.service.vote.impl;

import static java.util.stream.Collectors.groupingBy;

import java.sql.Timestamp;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map.Entry;
import java.util.TreeMap;

import com.foshan.entity.PlatformUserEntity;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.entity.ColumnEntity;
import com.foshan.entity.ServiceEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.vote.VoteGroupEntity;
import com.foshan.entity.vote.vo.VoteResultGroupVo;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.vote.RankForm;
import com.foshan.form.vote.VoteContentForm;
import com.foshan.form.vote.request.VoteGroupReq;
import com.foshan.form.vote.response.AddVoteGroupRes;
import com.foshan.form.vote.response.GetRankRes;
import com.foshan.form.vote.response.GetVoteGroupContentRes;
import com.foshan.form.vote.response.ModifyVoteGroupRes;
import com.foshan.form.vote.response.SearchVoteGroupContentRes;
import com.foshan.service.vote.IVoteGroupService;
import com.foshan.util.DateUtil;

@Transactional
@Service("voteGroupService")
public class VoteGroupServiceImpl extends GenericVoteService implements IVoteGroupService {
	private final static Logger logger = LoggerFactory.getLogger(VoteGroupServiceImpl.class);

	@Override
	public IResponse addVoteGroup(VoteGroupReq req) {
		// TODO Auto-generated method stub
		AddVoteGroupRes res = new AddVoteGroupRes();
		if (null != req.getServiceId() && null != req.getColumnId()) {
			ColumnEntity column = columnDao.get(req.getColumnId());
			if (null != column) {

				VoteGroupEntity group = new VoteGroupEntity();
				group.setGroupCode(req.getGroupCode());
				group.setGroupName(req.getGroupName());
				group.setGroupInfo(req.getGroupInfo());
				group.setGroupImage(req.getGroupImage());
				group.setGroupState(
						null != req.getGroupState() ? req.getGroupState() : EntityContext.RECORD_STATE_VALID);
				group.setServiceId(column.getService().getId());
				group.setSpecialTimeFlag(req.getSpecialTimeFlag());
				try {
					if (null != req.getSpecialTimeFlag() && req.getSpecialTimeFlag() == 1) {
						if (StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime())) {
							group.setStartTime(new Timestamp(DateUtil.parseLongFormat(req.getStartTime()).getTime()));
							group.setEndTime(new Timestamp(DateUtil.parseLongFormat(req.getEndTime()).getTime()));
						} else {
							res.setRet("0001");
							res.setRetInfo("投票开始结束时间不能为空！！！");
							return res;
						}
					} else {
						group.setSpecialTimeFlag(EntityContext.RECORD_STATE_INVALID);
						group.setStartTime(new Timestamp(System.currentTimeMillis()));
						group.setEndTime(group.getStartTime());
					}
				} catch (ParseException ex) {
					res.setRet("0001");
					res.setRetInfo("时间格式不正确！！！");
					return res;
				}
				group.setSpecialVoteFlag(null != req.getSpecialVoteFlag() ? req.getSpecialVoteFlag()
						: EntityContext.RECORD_STATE_INVALID);
				group.setColumnId(column.getId());
				Integer groupId = (Integer) voteGroupDao.save(group);
				res.setGroupId(groupId);

				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo("投票栏目不存在！！！");
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		logger.info("返回数据：" + res.toString());
		return res;
	}

	@Override
	public IResponse modifyVoteGroup(VoteGroupReq req) {
		// TODO Auto-generated method stub
		ModifyVoteGroupRes res = new ModifyVoteGroupRes();
		if (null != req.getGroupId()) {
			VoteGroupEntity group = voteGroupDao.get(req.getGroupId());
			if (null != group) {
				group.setGroupCode(
						StringUtils.isNotEmpty(req.getGroupCode()) ? req.getGroupCode() : group.getGroupCode());
				group.setGroupName(
						StringUtils.isNotEmpty(req.getGroupName()) ? req.getGroupName() : group.getGroupName());
				group.setGroupInfo(
						StringUtils.isNotEmpty(req.getGroupInfo()) ? req.getGroupInfo() : group.getGroupInfo());
				group.setGroupImage(
						StringUtils.isNotEmpty(req.getGroupImage()) ? req.getGroupImage() : group.getGroupImage());
				group.setGroupState(null != req.getGroupState() ? req.getGroupState() : group.getGroupState());
				group.setSpecialVoteFlag(
						null != req.getSpecialVoteFlag() ? req.getSpecialVoteFlag() : group.getSpecialVoteFlag());

				try {
					if (null != req.getSpecialTimeFlag() && req.getSpecialTimeFlag() != group.getSpecialTimeFlag()
							&& group.getSpecialTimeFlag() == 0) {
						if (StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime())) {
							group.setStartTime(new Timestamp(DateUtil.parseLongFormat(req.getStartTime()).getTime()));
							group.setEndTime(new Timestamp(DateUtil.parseLongFormat(req.getEndTime()).getTime()));
						} else {
							res.setRet("0001");
							res.setRetInfo("投票开始结束时间不能为空！！！");
							return res;
						}
					} else if (group.getSpecialTimeFlag() == 1 && req.getSpecialTimeFlag() == 1) {
						if (StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime())) {
							group.setStartTime(new Timestamp(DateUtil.parseLongFormat(req.getStartTime()).getTime()));
							group.setEndTime(new Timestamp(DateUtil.parseLongFormat(req.getEndTime()).getTime()));
						} else {
							res.setRet("0001");
							res.setRetInfo("投票开始结束时间不能为空！！！");
							return res;
						}
					} else if (group.getSpecialTimeFlag() == 1 && req.getSpecialTimeFlag() == 1) {
						group.setStartTime(null);
						group.setEndTime(null);
					}
				} catch (ParseException ex) {
					res.setRet("0001");
					res.setRetInfo("时间格式不正确！！！");
					return res;
				}
				group.setSpecialTimeFlag(
						null != req.getSpecialTimeFlag() ? req.getSpecialTimeFlag() : group.getSpecialTimeFlag());

				res.setGroupId(req.getGroupId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setGroupId(req.getGroupId());
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo("要修改的投票组不存在！！！");
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		logger.info("返回数据：" + res.toString());
		return res;
	}

	@Override
	public IResponse deleteVoteGroup(VoteGroupReq req) {
		// TODO Auto-generated method stub
		BaseResponse res = new GenericResponse();
		if (null != req.getGroupId()) {
			VoteGroupEntity group = voteGroupDao.get(req.getGroupId());
			if (null != group) {
				// group.getParentGroup().getSubGroupList().remove(group);
				// group.setParentGroup(null);
				voteGroupDao.delete(group);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo("要删除的投票组不存在！！！");
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		logger.info("返回数据：" + res.toString());
		return res;
	}

	@SuppressWarnings({ "unchecked", "null" })
	@Override
	public IResponse getVoteGroupContent(VoteGroupReq req) {
		// TODO Auto-generated method stub
		GetVoteGroupContentRes res = new GetVoteGroupContentRes();
		if (null != req.getServiceId() && null != req.getColumnId() && null != req.getGroupId()
				&& (StringUtils.isNotEmpty(req.getSmartcardId()) || StringUtils.isNotEmpty(req.getPhone())
						|| StringUtils.isNotEmpty(req.getUserCode()))) {
			VoteGroupEntity group = voteGroupDao.getUniqueByHql("from VoteGroupEntity a where a.serviceId="
					+ req.getServiceId() + " and a.columnId=" + req.getColumnId() + " and a.id=" + req.getGroupId());
			if (null != group) {
				Integer serviceNum = voteContestantDao
						.countHqlResult("select a from VoteContestantEntity a where a.serviceId=" + req.getServiceId()
								+ " and a.contestantState=1");
				Integer groupNum = voteContestantDao.countHqlResult(
						"select a from VoteContestantEntity a inner join a.groupList b where b.id=" + req.getGroupId()
								+ " and a.serviceId=" + req.getServiceId() + " and a.contestantState=1");

				if (groupNum > 0) {

					String sql = "select f.assetCode,f.assetName,f.assetType,b.contestantCode,b.id,b.contestantName,d.columnCode,"
							+ "f.imageFile,f.packageFlag,b.serviceid,f.smallImageFile,f.timeLength,count(a.id) as votes,b.contestantDesc "
							+ "from t_vote_result a right join t_vote_contestant b on a.contestantId=b.id "
							+ "inner join t_group_contestant e1 on b.id=e1.votecontestantid inner join t_vote_group e on e1.votegroupid=e.id "
							+ "inner join t_column d on d.id=b.columnId " + "inner join t_asset f on b.assetid=f.id "
							+ "where b.serviceId=" + req.getServiceId() + " and b.columnId=" + req.getColumnId()
							+ " and e.id=" + req.getGroupId() + " " + "and b.contestantState=1 ";

					StringBuilder sqlBuilder = new StringBuilder(sql);
					Object userObj =  getPrincipal(true);
					if (null != userObj && userObj instanceof PlatformUserEntity) {
						sqlBuilder.append(StringUtils.isNotEmpty(req.getAssetStates())
								? " and f.assetState in (" + req.getAssetStates() + ") "
								: " and f.assetState in (2) ");
					}

					sqlBuilder.append(" group by b.id ");

					// 根据页面请求构造排序参数
					String[] orderParameters = req.getOrderParameters();
					if (null != orderParameters || orderParameters.length > 0) {
						String orderStr = "order by ";
						for (String orderParameter : orderParameters) {
							String[] para = orderParameter.split("\\|");
							orderStr += para[0] + " " + para[1] + ",";
						}
						sql += orderStr.substring(0, orderStr.length() - 1);
					}
					// 根据构造查询语句查询结果数据

					List<Object[]> resultList = (List<Object[]>) voteGroupDao.createSQLQuery(sql)
							.setFirstResult(
									req.getRequestPage() <= 1 ? 0 : (req.getRequestPage() - 1) * req.getPageSize())
							.setMaxResults(req.getPageSize()).list();

					res.setPageSize(req.getPageSize());
					res.setCurrentPage(req.getRequestPage());
					res.setGroupId(req.getGroupId());
					res.setTotal(groupNum == 0 ? 0 : (groupNum - 1) / req.getPageSize() + 1);
					res.setTotalResult(groupNum);
					// 根据分页结果构造返回值
					resultList.forEach(o -> {
						VoteContentForm voteContent = new VoteContentForm();
						voteContent.setAssetId(null != o[0] ? o[0].toString() : null);
						voteContent.setAssetName(null != o[1] ? o[1].toString() : null);
						voteContent.setAssetType(null != o[2] ? Integer.parseInt(o[2].toString()) : null);
						voteContent.setContestantCode(null != o[3] ? o[3].toString() : null);
						voteContent.setContestantId(null != o[4] ? Integer.parseInt(o[4].toString()) : null);
						voteContent.setContestantName(null != o[5] ? o[5].toString() : null);
						voteContent.setColumnCode(null != o[6] ? o[6].toString() : null);
						voteContent.setColumnId(req.getColumnId());
						voteContent.setGroupId(group.getId());
						voteContent.setGroupNum(groupNum);
						voteContent.setImageFile(null != o[7] ? o[7].toString() : null);
						voteContent.setPackageFlag(null != o[8] ? Integer.parseInt(o[8].toString()) : null);
						voteContent.setServiceId(null != o[9] ? Integer.parseInt(o[9].toString()) : null);
						voteContent.setServiceNum(serviceNum);
						voteContent.setSmallImageFile(null != o[10] ? o[10].toString() : null);
						voteContent.setTimeLength(null != o[11] ? Integer.parseInt(o[11].toString()) : null);
						voteContent.setVotes(null != o[12] ? Integer.parseInt(o[12].toString()) : null);
						voteContent.setContestantDesc(null != o[13] ? o[13].toString() : null);
						res.getContestantList().add(voteContent);
					});
				}
				res.setGroupId(group.getId());
				res.setGroupName(group.getGroupName());
				res.setGroupInfo(group.getGroupInfo());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

			} else {
				res.setGroupId(req.getGroupId());
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo("投票分组不存在！！！");
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@SuppressWarnings({ "unchecked" })
	@Override
	public IResponse searchVoteGroupContent(VoteGroupReq req) {
		// TODO Auto-generated method stub
		SearchVoteGroupContentRes res = new SearchVoteGroupContentRes();
		if (null != req.getServiceId()) {
			ServiceEntity service = serviceDao.get(req.getServiceId());
			if (null != service) {
				// 构造排序字符串
				String[] orderParameters = req.getOrderParameters();
				String orderStr = "order by ";
				if (null != orderParameters && orderParameters.length > 0) {
					for (String orderParameter : orderParameters) {
						String[] para = orderParameter.split("\\|");
						orderStr += para[0] + " " + para[1] + ",";
					}
					orderStr = orderStr.substring(0,
							orderStr.length() > 9 ? orderStr.length() - 1 : orderStr.length() - 9);
				}

				// 按搜索条件查询数据
				List<Object[]> resultList = null;
				Integer totalResult = 0;

				StringBuilder baseHql = new StringBuilder(
						"select f.assetCode,f.assetName,f.assetType,b.contestantCode,b.id as contestantId,b.contestantName,"
								+ "d.columncode,f.imageFile,f.packageFlag,f.smallImageFile,f.timeLength,b.serviceId,b.columnId,"
								+ "e.id as groupId,count(a.id) as votes "
								+ "from t_vote_result a right join t_vote_contestant b on a.contestantId=b.id "
								+ "right join t_column d on b.columnid=d.id "
								+ "right join t_group_contestant e1 on b.id=e1.votecontestantid "
								+ "right join t_vote_group e on e1.votegroupid=e.id "
								+ "right join t_asset f on b.assetid=f.id ");
				StringBuilder countHql = new StringBuilder(
						"select count(b) from VoteContestantEntity b inner join b.groupList e inner join b.asset f ");

				String searchStr = StringUtils.isNotEmpty(req.getSearchParameter())
						? "and CONCAT_WS(' ',b.contestantName,b.contestantCode,b.contestantPhone,b.contestantKeyWords) like '%"
								+ req.getSearchParameter() + "%'"
						: "";
				if (null != req.getColumnId() && null != req.getGroupId()) {
					baseHql.append("where b.serviceId=" + req.getServiceId() + " and b.columnId=" + req.getColumnId()
							+ " and e.id=" + req.getGroupId() + " and contestantState=1 ").append(searchStr)
							.append(" group by b.id ").append(orderStr);

					totalResult = ((Long) voteContestantDao.createQuery(countHql
							.append("where b.serviceId=" + req.getServiceId() + " and b.columnId=" + req.getColumnId()
									+ " and e.id=" + req.getGroupId() + " and contestantState=1  ")
							.append(searchStr).toString()).uniqueResult()).intValue();
					resultList = (List<Object[]>) voteGroupDao.createSQLQuery(baseHql.toString())
							.setFirstResult(
									req.getRequestPage() <= 1 ? 0 : (req.getRequestPage() - 1) * req.getPageSize())
							.setMaxResults(req.getPageSize()).list();
				} else if (null != req.getColumnId() && null == req.getGroupId()) {
					baseHql.append("where b.serviceId=" + req.getServiceId() + " and b.columnId=" + req.getColumnId()
							+ "  and contestantState=1 ").append(searchStr).append(" group by b.id ").append(orderStr);
					totalResult = ((Long) voteContestantDao
							.createQuery(countHql
									.append("where b.serviceId=" + req.getServiceId() + " and b.columnId="
											+ req.getColumnId() + "  and contestantState=1 ")
									.append(searchStr).toString())
							.uniqueResult()).intValue();
					resultList = (List<Object[]>) voteGroupDao.createSQLQuery(baseHql.toString())
							.setFirstResult(
									req.getRequestPage() <= 1 ? 0 : (req.getRequestPage() - 1) * req.getPageSize())
							.setMaxResults(req.getPageSize()).list();
				} else if (null == req.getColumnId() && null != req.getGroupId()) {
					baseHql.append("where b.serviceId=" + req.getServiceId() + " and e.id=" + req.getGroupId()
							+ "  and contestantState=1 ").append(searchStr).append(" group by b.id ").append(orderStr);
					totalResult = ((Long) voteContestantDao
							.createQuery(countHql.append("where b.serviceId=" + req.getServiceId() + " and e.id="
									+ req.getGroupId() + "  and contestantState=1 ").append(searchStr).toString())
							.uniqueResult()).intValue();
					resultList = (List<Object[]>) voteGroupDao.createSQLQuery(baseHql.toString())
							.setFirstResult(
									req.getRequestPage() <= 1 ? 0 : (req.getRequestPage() - 1) * req.getPageSize())
							.setMaxResults(req.getPageSize()).list();
				} else {
					baseHql.append("where b.serviceId=" + req.getServiceId() + "  and contestantState=1 ")
							.append(searchStr).append(" group by b.id ").append(orderStr);

					totalResult = ((Long) voteContestantDao.createQuery(
							countHql.append("where b.serviceId=" + req.getServiceId() + "  and contestantState=1 ")
									.append(searchStr).toString())
							.uniqueResult()).intValue();

					resultList = (List<Object[]>) voteGroupDao.createSQLQuery(baseHql.toString())
							.setFirstResult(
									req.getRequestPage() <= 1 ? 0 : (req.getRequestPage() - 1) * req.getPageSize())
							.setMaxResults(req.getPageSize()).list();
				}

				res.setPageSize(req.getPageSize());
				res.setCurrentPage(req.getRequestPage());
				res.setTotal(totalResult == 0 ? 0 : (totalResult - 1) / req.getPageSize() + 1);
				res.setTotalResult(totalResult);

				// 根据分页结果构造返回值
				resultList.forEach(o -> {
					VoteContentForm voteContent = new VoteContentForm();
					voteContent.setAssetId(null != o[0] ? o[0].toString() : null);
					voteContent.setAssetName(null != o[1] ? o[1].toString() : null);
					voteContent.setAssetType(null != o[2] ? Integer.parseInt(o[2].toString()) : null);
					voteContent.setContestantCode(null != o[3] ? o[3].toString() : null);
					voteContent.setContestantId(null != o[4] ? Integer.parseInt(o[4].toString()) : null);
					voteContent.setContestantName(null != o[5] ? o[5].toString() : null);
					voteContent.setColumnCode(null != o[6] ? o[6].toString() : null);
					voteContent.setImageFile(null != o[7] ? o[7].toString() : null);
					voteContent.setPackageFlag(null != o[8] ? Integer.parseInt(o[8].toString()) : null);
					voteContent.setSmallImageFile(null != o[9] ? o[9].toString() : null);
					voteContent.setTimeLength(null != o[10] ? Integer.parseInt(o[10].toString()) : null);
					voteContent.setServiceId(null != o[11] ? Integer.parseInt(o[11].toString()) : null);
					voteContent.setColumnId(null != o[12] ? Integer.parseInt(o[12].toString()) : null);
					voteContent.setGroupId(null != o[13] ? Integer.parseInt(o[13].toString()) : null);
					voteContent.setVotes(null != o[14] ? Integer.parseInt(o[14].toString()) : null);
					res.getContestantList().add(voteContent);
				});

				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo("查询业务不能为空！！！");
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@SuppressWarnings("unchecked")
	@Override
	public IResponse getRank(VoteGroupReq req) {
		/*
		 * 该接口根据请求的参数不同返回不同的排行结果： 1、只传serviceId则返回该业务所有投票子栏目的分组排行
		 * 2、请求serviceId和投票子栏目folderId，则返回当前投票子栏目下的各组排行 3、请求groupId则返回当前投票组下的投票排行
		 */
		GetRankRes res = new GetRankRes();
		if (null != req.getServiceId() && null != req.getTop() && req.getTop() > 0) {
			ServiceEntity service = serviceDao.get(req.getServiceId());
			StringBuilder baseHql = new StringBuilder(
					"select new com.foshan.entity.vote.vo.VoteResultGroupVo(b.serviceId,b.columnId,concat('',b.columnId) as columnName,f.id as groupId,f.groupName,b.id as contestantId,b.contestantCode,b.contestantName,b.contestantPhone,c.imageFile,c.smallImageFile,count(a.columnCode) as votes)  "
							+ "from VoteResultEntity a right join a.voteContestant b inner join b.asset c inner join b.groupList f ");

			if (null != service) {
				List<VoteResultGroupVo> resultList = null;
				if (null == req.getColumnId() && null == req.getGroupId()) {
					StringBuilder baseGroupOrderHql = new StringBuilder(
							"group by b.columnId,b.id order by votes desc,b.serviceId,b.columnId,f.id");
					String serviceHql = baseHql.append(" where b.serviceId=" + req.getServiceId() + "  and contestantState=1 ")
							.append(baseGroupOrderHql).toString();
					resultList = voteResultDao.createQuery(serviceHql).list();
					// 分组
					HashMap<String, List<VoteResultGroupVo>> collect = (HashMap<String, List<VoteResultGroupVo>>) resultList
							.stream().collect(groupingBy(VoteResultGroupVo::getColumnName));
					collect.entrySet().forEach(o -> {
						RankForm rankForm = new RankForm();
						rankForm.setRankName(o.getKey());
//						rankForm.setRankId(columnDao.getUniqueByProperty("columnName", o.getKey()).getId());
						o.getValue()
								.subList(0, req.getTop() >= o.getValue().size() ? o.getValue().size() : req.getTop())
								.forEach(a -> {
									rankForm.getContestants()
											.add(new VoteContentForm(a.getColumnName(),a.getContestantId(), a.getGroupName(),
													a.getContestantCode(), a.getContestantName(),a.getImageFile(),a.getSmallImageFile(),
													null != a.getVotes() ? a.getVotes().intValue() : 0,a.getServiceId(),a.getColumnId(),a.getGroupId()));
								});
						res.getRankList().add(rankForm);
					});
				} else if (null != req.getColumnId() && null == req.getGroupId()) {
					StringBuilder baseGroupOrderHql = new StringBuilder(
							"group by b.columnId,b.id order by b.serviceId,b.columnId,f.id,votes desc");
					ColumnEntity column = columnDao.get(req.getColumnId());
					
					if(column.getSubColumnList().size()>0) {
						StringBuilder columnList = new StringBuilder("");
						for(ColumnEntity f:column.getSubColumnList()) {
							columnList.append(f.getId()+",");
						}
						baseHql.append(" where b.serviceId=" + req.getServiceId() + "  and b.columnId in("
								+ columnList.substring(0,columnList.length()-1) + ")  and contestantState=1 ").append(baseGroupOrderHql);
					}else {
						baseHql.append(" where b.serviceId=" + req.getServiceId() + "  and b.columnId="
								+ req.getColumnId() + "  and contestantState=1 ").append(baseGroupOrderHql);
					}
					
		    		String serviceHql = baseHql.toString();
					resultList = voteResultDao.createQuery(serviceHql).list();
					// 分组
					HashMap<Long, List<VoteResultGroupVo>> totalResultCollect = (HashMap<Long, List<VoteResultGroupVo>>) resultList
							.stream().collect(groupingBy(VoteResultGroupVo::getVotes));
					// 排序
					TreeMap<Long, List<VoteResultGroupVo>> tt = new TreeMap<Long, List<VoteResultGroupVo>>();
					totalResultCollect.entrySet().forEach(o -> {
						tt.put(o.getKey(), o.getValue());
					});

					for (Entry<Long, List<VoteResultGroupVo>> o : tt.descendingMap().entrySet()) {
						for (VoteResultGroupVo obj : o.getValue()) {
							RankForm rankForm = new RankForm();
							System.out.println(obj.getVotes());
							rankForm.getContestants()
									.add(new VoteContentForm(obj.getColumnName(), obj.getContestantId(), obj.getGroupName(),
											obj.getContestantCode(), obj.getContestantName(),obj.getImageFile(),obj.getSmallImageFile(), 
											null != obj.getVotes() ? obj.getVotes().intValue() : 0,obj.getServiceId(),obj.getColumnId(),obj.getGroupId()));
							res.getRankList().add(rankForm);
						}
					}
					
//					// 分组
//					HashMap<String, List<VoteResultGroupVo>> collect = (HashMap<String, List<VoteResultGroupVo>>) resultList
//							.stream().collect(groupingBy(VoteResultGroupVo::getGroupName));
//					collect.entrySet().forEach(o -> {
//						RankForm rankForm = new RankForm();
//						rankForm.setRankName(o.getKey());
//						rankForm.setRankId(voteGroupDao.getUniqueByProperty("groupName", o.getKey()).getId());
//						o.getValue()
//								.subList(0, req.getTop() >= o.getValue().size() ? o.getValue().size() : req.getTop())
//								.forEach(a -> {
//									rankForm.getContestants()
//											.add(new VoteContentForm(a.getFolderAssetName(), a.getGroupName(),
//													a.getContestantCode(), a.getContestantName(),
//													null != a.getVotes() ? a.getVotes().intValue() : 0));
//								});
//						res.getRankList().add(rankForm);
//					});
				} else {
					StringBuilder baseGroupOrderHql = new StringBuilder(
							"group by b.columnId,b.id order by b.serviceId,b.columnId,f.id,votes desc");
					String serviceHql = baseHql.append(" where b.serviceId=" + req.getServiceId() + "  and f.id="
							+ req.getGroupId() + "  and contestantState=1 ").append(baseGroupOrderHql).toString();
					resultList = voteResultDao.createQuery(serviceHql).setMaxResults(req.getTop()).list();
					// 分组
					HashMap<Long, List<VoteResultGroupVo>> totalResultCollect = (HashMap<Long, List<VoteResultGroupVo>>) resultList
							.stream().collect(groupingBy(VoteResultGroupVo::getVotes));
					// 排序
					TreeMap<Long, List<VoteResultGroupVo>> tt = new TreeMap<Long, List<VoteResultGroupVo>>();
					totalResultCollect.entrySet().forEach(o -> {
						tt.put(o.getKey(), o.getValue());
					});

					for (Entry<Long, List<VoteResultGroupVo>> o : tt.descendingMap().entrySet()) {
						for (VoteResultGroupVo obj : o.getValue()) {
							RankForm rankForm = new RankForm();
							rankForm.getContestants()
									.add(new VoteContentForm(obj.getColumnName(),obj.getContestantId(), obj.getGroupName(),
											obj.getContestantCode(), obj.getContestantName(),obj.getImageFile(),obj.getSmallImageFile(),
											null != obj.getVotes() ? obj.getVotes().intValue() : 0,obj.getServiceId(),obj.getColumnId(),obj.getGroupId()));
							res.getRankList().add(rankForm);
						}
					}
				}
//				if (null == req.getGroupId()) {
//					res.getRankList().sort(comparingInt(RankForm::getRankId));
//				}
				res.setServiceId(req.getServiceId());
				res.setColumnId(req.getColumnId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet("0001");
				res.setRetInfo("该业务不存在！！！");
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@SuppressWarnings("unchecked")
	@Override
	public IResponse getTotalRank(VoteGroupReq req) {
		GetRankRes res = new GetRankRes();
		if (null != req.getServiceId() && null != req.getTop() && req.getTop() > 0) {
			ServiceEntity service = serviceDao.get(req.getServiceId());
			
			StringBuilder baseHql = new StringBuilder(
					"select new com.foshan.entity.vote.vo.VoteResultGroupVo(b.serviceId,b.columnId,concat('',b.columnId) as columnName,f.id as groupId,f.groupName,b.id as contestantId,b.contestantCode,b.contestantName,b.contestantPhone,c.imageFile,c.smallImageFile,count(a.columnCode) as votes)  "
							+ "from VoteResultEntity a inner join a.voteContestant b inner join b.asset c inner join b.groupList f ");

			if (null != service) {
				List<VoteResultGroupVo> resultList = null;

					StringBuilder baseGroupOrderHql = new StringBuilder(
							"group by b.id order by votes desc,b.serviceId,b.columnId,f.id");
					String serviceHql = baseHql.append(" where b.serviceId=" + req.getServiceId() + "  and contestantState=1 ")
							.append(baseGroupOrderHql).toString();
					resultList = voteResultDao.createQuery(serviceHql).list();
					
					RankForm rankForm = new RankForm();
					resultList.subList(0, req.getTop() >= resultList.size() ? resultList.size() : req.getTop())
					.forEach(a -> {
									rankForm.getContestants()
											.add(new VoteContentForm(a.getColumnName(),a.getContestantId(), a.getGroupName(),
													a.getContestantCode(), a.getContestantName(),a.getImageFile(),a.getSmallImageFile(),
													null != a.getVotes() ? a.getVotes().intValue() : 0,a.getServiceId(),a.getColumnId(),a.getGroupId()));
								});
//					// 分组
//					HashMap<String, List<VoteResultGroupVo>> collect = (HashMap<String, List<VoteResultGroupVo>>) resultList
//							.stream().collect(groupingBy(VoteResultGroupVo::getFolderAssetName));
//					collect.entrySet().forEach(o -> {
//						RankForm rankForm = new RankForm();
////						rankForm.setRankName(o.getKey());
//						rankForm.setRankId(voteFolderDao.getUniqueByProperty("folderAssetName", o.getKey()).getId());
//						o.getValue()
//								.subList(0, req.getTop() >= o.getValue().size() ? o.getValue().size() : req.getTop())
//								.forEach(a -> {
//									rankForm.getContestants()
//											.add(new VoteContentForm(a.getFolderAssetName(),a.getContestantId(), a.getGroupName(),
//													a.getContestantCode(), a.getContestantName(),a.getImageFile(),a.getSmallImageFile(),
//													null != a.getVotes() ? a.getVotes().intValue() : 0,a.getServiceId(),a.getFolderId(),a.getGroupId()));
//								});
//						res.getRankList().add(rankForm);
//					});
					
					res.getRankList().add(rankForm);
			} else {
				res.setRet("0001");
				res.setRetInfo("该业务不存在！！！");
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

}
