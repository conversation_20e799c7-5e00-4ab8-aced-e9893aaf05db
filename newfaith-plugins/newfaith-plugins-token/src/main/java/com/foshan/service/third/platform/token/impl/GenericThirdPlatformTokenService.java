package com.foshan.service.third.platform.token.impl;

import com.foshan.dao.third.platform.token.IThirdPlatformTokenDao;
import com.foshan.entity.third.platform.token.ThirdPlatformTokenEntity;
import com.foshan.service.impl.GenericService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class GenericThirdPlatformTokenService extends GenericService {

    @Autowired
    protected IThirdPlatformTokenDao thirdPlatformTokenDao;

    public List<ThirdPlatformTokenEntity> getAllThirdPlatformToken(){
        String hql = "from ThirdPlatformTokenEntity where state = 1";
        List tokenList = thirdPlatformTokenDao.query(hql);
        return tokenList;
    }
}
