package com.foshan.service.third.platform.token;

import com.foshan.form.third.platform.token.request.endpoint.GetThirdPlatformTokenReq;
import com.foshan.form.third.platform.token.response.endpoint.GetThirdPlatformTokenRes;

public interface IThirdPlatformTokenEndpointService {
    /**
     * 终端获取第三方平台token
     * 1.根据第三方平台code查询缓存
     * 2.如果无缓存则去第三方平台获取token，并存入缓存（在处理响应数据的时候存入缓存）
     * 3.如果缓存存在则判断token是否允许刷新及刷新时间有效性后处理并返回
     * 3.1 允许刷新
     * 3.1.1 检查刷新token到期时间是否在有效期内，小于等于5分钟的需要刷新token后返回（token允许刷新）
     * 3.1.2 刷新token到期时间大于5分钟不做处理，直接返回
     * 3.2 不允许刷新或刷新token已过期
     * 3.2.1 检查token到期时间，小于等于5分钟则重新获取
     * 3.2.2 大于5分钟则直接返回
     * @param req
     * @return
     */
    public GetThirdPlatformTokenRes getThirdPlatformToken(GetThirdPlatformTokenReq req);
}
