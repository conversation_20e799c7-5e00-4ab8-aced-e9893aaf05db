package com.foshan.form.third.platform.token.request.management;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取第三方平台token列表返回对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetThirdPlatformTokenListReq extends BasePageRequest {
    private static final long serialVersionUID = -632762566743467961L;

}
