package com.foshan.form.third.platform.token;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@ApiModel(value = "第三方平台token对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@NoArgsConstructor
public class ThirdPlatformTokenForm implements IForm {
    private static final long serialVersionUID = -6818591921901065627L;
    @ApiModelProperty(value = "令牌在本平台的id")
    private Integer id;
    @ApiModelProperty(value = "第三方平台代码")
    private String thirdPlatformCode;
    @ApiModelProperty(value = "获取第三方平台代码请求的url")
    private String url;
    @ApiModelProperty(value = "获取第三方平台代码请求的方法")
    private String requestMethod;
    @ApiModelProperty(value = "获取第三方平台代码请求的参数")
    private String params;
    @ApiModelProperty(value = "获取第三方平台代码请求的消息体")
    private String body;
    @ApiModelProperty(value = "第三方平台返回token的字段")
    private String tokenKey;
    @ApiModelProperty(value = "第三方平台返回刷新token的字段")
    private String refreshTokenKey;
    @ApiModelProperty(value = "第三方token有效时间，单位：秒")
    private Integer effectiveTime;
    @ApiModelProperty(value = "该token请求的描述")
    private String description;
    @ApiModelProperty(value = "该token请求的备忘")
    private String memo;
    @ApiModelProperty(value="第三方token公钥")
    private String appKey;
    @ApiModelProperty(value="第三方token密钥")
    private String secretKey;
    @Override
    public int compareTo(Object o) {
        return 0;
    }
}
