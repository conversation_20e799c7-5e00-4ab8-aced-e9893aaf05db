package com.foshan.form.third.platform.token.response.endpoint;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "终端获取第三方平台token响应的对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetThirdPlatformTokenRes extends BaseResponse {

    @ApiModelProperty(value = "第三方平台token")
    private String thirdPlatformToken;

}
