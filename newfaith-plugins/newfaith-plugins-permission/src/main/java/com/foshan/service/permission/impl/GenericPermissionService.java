package com.foshan.service.permission.impl;

import java.util.List;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.Subject;

import com.foshan.entity.AccountEntity;
import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.service.impl.GenericService;

public class GenericPermissionService extends GenericService {
	
	/**
	 *  查询当前登录用户的实体
	 * @param returnFullEntity 是否返回完整entity，true返回完整的entity，false返回shire中存的entity。shiro缓存了id、电话、微信openid，设置为false时，只有这三个数据。
	 * @return PlatformUserEntity或AccountEntity的object，使用的时候进行强转 用户没登录或rememberme
	 *         cookie过期则返回null
	 */
//	protected Object getPrincipal(boolean returnFullEntity){
//		Subject curUser = SecurityUtils.getSubject();
//		PrincipalCollection principals = curUser.getPrincipals();
//		if(null!=principals&&!principals.isEmpty()){
//			@SuppressWarnings("unchecked")
//			List<Object> principalList = principals.asList();
//			Object principal = (null != principalList.get(1) ? principalList.get(1) : null);
//			if(!returnFullEntity) {
//				return principal;
//			}
//			if (principal instanceof AccountEntity) {
//				AccountEntity shiroAccount = (AccountEntity) principal;
//				String sql = "select * from t_account where id = '" + shiroAccount.getId() + "' and userState = '" + EntityContext.RECORD_STATE_VALID + "'";
//				Object account = accountDao.getUniqueBySql(sql);
//				return account;
//			}else if(principal instanceof PlatformUserEntity) {
//				PlatformUserEntity shiroPlatformUser = (PlatformUserEntity) principal;
//				String sql = "select * from t_user where id = '" + shiroPlatformUser.getId() + "' and userState = '" + EntityContext.RECORD_STATE_VALID + "'";
//				Object platformUser = platformUserDao.getUniqueBySql(sql);
//				return platformUser;
//			}
//			
//			return null;
//		}else{
//			return null;
//		}
//	}
}
