package com.foshan.service.permission.shiro.sessionManager;

import javax.annotation.Resource;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.AuthorizationException;
import org.apache.shiro.session.Session;
import org.apache.shiro.session.SessionException;
import org.apache.shiro.session.mgt.SessionContext;
import org.apache.shiro.session.mgt.SessionKey;
import org.apache.shiro.web.session.HttpServletSession;
import org.apache.shiro.web.session.mgt.ServletContainerSessionManager;
import org.apache.shiro.web.session.mgt.WebSessionManager;
import org.apache.shiro.web.util.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.ehcache.EhCacheCacheManager;

import net.sf.ehcache.Cache;
import net.sf.ehcache.CacheManager;
import net.sf.ehcache.Element;

/**
 * 暂停研究
 * <AUTHOR>
 *
 */
public class DefaultCookieAndHeaderSessionManager extends ServletContainerSessionManager implements WebSessionManager {
	
	//@Resource(name = "cacheManager")
	protected EhCacheCacheManager cacheManager;
	
	private static final Logger log = LoggerFactory.getLogger(DefaultCookieAndHeaderSessionManager.class);

	private final String X_AUTH_TOKEN = "x-auth-token";
	
	public DefaultCookieAndHeaderSessionManager() {
		super();
		// TODO Auto-generated constructor stub
	}

	@Override
	public Session start(SessionContext context) throws AuthorizationException {
		// TODO Auto-generated method stub
		return createSession(context);
	}

	@Override
	public Session getSession(SessionKey key) throws SessionException {
		HttpServletRequest request = WebUtils.getHttpRequest(key);
		HttpServletResponse response = WebUtils.getHttpResponse(key);
		HttpSession httpSession = request.getSession(false);
		Session session = null;
		if(null == httpSession) {//token方式获取session
			String sessionId = getSessionIdHeaderValue(request, response);
			if(StringUtils.isNoneBlank(sessionId)) {
		        //获取ehcache的httpSession
				CacheManager ehCacheManager = cacheManager.getCacheManager();
				Cache cache = ehCacheManager.getCache("XAuthTokenCache");
				session = (Session) cache.get(sessionId).getObjectValue();
			}
		}else {//cookie方式获取session
			session = createSession(httpSession, request.getRemoteHost());
		}
		return session;
	}

	@Override
	protected Session createSession(SessionContext sessionContext) throws AuthorizationException {
		if (!WebUtils.isHttp(sessionContext)) {
            String msg = "SessionContext must be an HTTP compatible implementation.";
            throw new IllegalArgumentException(msg);
        }

		HttpServletRequest request = WebUtils.getHttpRequest(sessionContext);
		HttpServletResponse response = WebUtils.getHttpResponse(sessionContext);
		HttpSession httpSession = request.getSession();
		String sessionId = httpSession.getId();
		response.setHeader(this.X_AUTH_TOKEN, sessionId);
		String host = getHost(sessionContext);
		Session session = createSession(httpSession, host);
		// httpSession存入
		CacheManager ehCacheManager = cacheManager.getCacheManager();
		Cache cache = ehCacheManager.getCache("XAuthTokenCache");
		Element element = new Element(sessionId, session);
        cache.put(element);
        return session;
	}
	
	private String getHost(SessionContext context) {
        String host = context.getHost();
        if (host == null) {
            ServletRequest request = WebUtils.getRequest(context);
            if (request != null) {
                host = request.getRemoteHost();
            }
        }
        return host;
    }
	
	@Override
	protected Session createSession(HttpSession httpSession, String host) {
		// TODO Auto-generated method stub
		return new HttpServletSession(httpSession, host);
	}

	// 请求头中获取 sessionId 并把sessionId 放入 response 中
	private String getSessionIdHeaderValue(ServletRequest request, ServletResponse response) {
		if (!(request instanceof HttpServletRequest)) {
			log.debug("Current request is not an HttpServletRequest - cannot get session ID cookie.  Returning null.");
			return null;
		} else {
			HttpServletRequest httpRequest = (HttpServletRequest) request;

			// 在request 中 读取 x-auth-token 信息 作为 sessionId
			String sessionId = httpRequest.getHeader(this.X_AUTH_TOKEN);

			// 每次读取之后 都把当前的 sessionId 放入 response 中
			HttpServletResponse httpResponse = (HttpServletResponse) response;

			if (StringUtils.isNotEmpty(sessionId)) {
				httpResponse.setHeader(this.X_AUTH_TOKEN, sessionId);
			}

			return sessionId;
		}
	}

}
