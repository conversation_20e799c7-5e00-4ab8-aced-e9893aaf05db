package com.foshan.service.group.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.entity.group.PhysicalGroupEntity;
import com.foshan.entity.group.PhysicalGroupRelationEntity;
import com.foshan.form.group.request.PhysicalGroupReq;
import com.foshan.form.group.response.physicalgroup.GetPhysicalGroupRes;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.group.IPhysicalGroupService;

@Transactional
@Service("physicalGroupService")
public class PhysicalGroupServiceImpl extends GenericGroupService implements IPhysicalGroupService {

	@Override
	public IResponse addPhysicalGroup(PhysicalGroupReq req) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public IResponse deletePhysicalGroup(PhysicalGroupReq req) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public IResponse modifyPhysicalGroup(PhysicalGroupReq req) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public IResponse getPhysicalGroup(PhysicalGroupReq req) {
		GetPhysicalGroupRes res = new GetPhysicalGroupRes();
		Integer groupId = req.getGroupId();
		if(null != groupId) {
			PhysicalGroupEntity physicalGroup = physicalGroupDao.get(groupId);
			res.setGroupId(physicalGroup.getId());
			res.setGroupName(physicalGroup.getGroupName());
			res.setGroupCode(physicalGroup.getGroupCode());
			res.setGroupLevel(physicalGroup.getGroupLevel());
			res.setInitial(physicalGroup.getInitial());
			res.setAdminId(physicalGroup.getAdminId());
			res.setAdminName(physicalGroup.getAdminName());
			res.setAdminPhone(physicalGroup.getAdminPhone());
		}else {
			String deviceSerialNumber = req.getDeviceSerialNumber();
			String deviceType = req.getDeviceType();
			if(StringUtils.isBlank(deviceSerialNumber) || StringUtils.isBlank(deviceType)) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				return res;
			}
			String hql = String.join(" ", "from PhysicalGroupRelationEntity where deviceSerialNumber =" , deviceSerialNumber, "and deviceType =", deviceType);
			PhysicalGroupRelationEntity physicalGroupRelation = physicalGroupRelationDao.getUniqueByHql(hql);
			PhysicalGroupEntity physicalGroup = physicalGroupRelation.getPhysicalGroup();
			res.setGroupId(physicalGroup.getId());
			res.setGroupName(physicalGroup.getGroupName());
			res.setGroupCode(physicalGroup.getGroupCode());
			res.setGroupLevel(physicalGroup.getGroupLevel());
			res.setInitial(physicalGroup.getInitial());
			res.setAdminId(physicalGroup.getAdminId());
			res.setAdminName(physicalGroup.getAdminName());
			res.setAdminPhone(physicalGroup.getAdminPhone());
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public IResponse getPhysicalGroupList(PhysicalGroupReq req) {
		// TODO Auto-generated method stub
		return null;
	}

}
