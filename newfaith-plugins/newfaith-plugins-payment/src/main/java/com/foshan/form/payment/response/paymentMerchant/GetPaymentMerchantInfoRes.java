package com.foshan.form.payment.response.paymentMerchant;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.payment.PaymentMerchantForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="(GetPaymentMerchantInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetPaymentMerchantInfoRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "对象")
	private PaymentMerchantForm paymentMerchantForm ; 

}
