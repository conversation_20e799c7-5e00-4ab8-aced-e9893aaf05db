package com.foshan.form.payment.response.paymentMerchant;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="(ModifyPaymentMerchantRes)")
@JsonInclude(Include.NON_NULL)
public  class ModifyPaymentMerchantRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer paymentMerchantId;
  @ApiModelProperty(value = "签约时间")
    private String auditValidTime;
    @ApiModelProperty(value = "自动审核 1--有效，0--无效",example="1")
    private Integer autoAudit;
    @ApiModelProperty(value = "商户地址")
    private String merchantAddress;
    @ApiModelProperty(value = "商户负责人姓名")
    private String merchantChargeName;
    @ApiModelProperty(value = "联系电话")
    private String merchantChargePhone;
    @ApiModelProperty(value = "商户号")
    private String merchantCode;
    @ApiModelProperty(value = "邮箱")
    private String merchantMail;
    @ApiModelProperty(value = "商户类型模式 ：0--个人 1--个体 2--企业 3--其它",example="1")
    private Integer merchantType;
    @ApiModelProperty(value = "商户名称")
    private String paymentMerchantName;
    @ApiModelProperty(value = "接口报文签名用的盐，商户需注意保密")
    private String secret;
    @ApiModelProperty(value = "客服电话")
    private String servicePhone;
    @ApiModelProperty(value = "商户状态 1--有效，0--无效",example="1")
    private Integer status;
  
}
