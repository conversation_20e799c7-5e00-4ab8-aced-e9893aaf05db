package com.foshan.form.payment.request;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="(PaymentSessionReq)")
public  class PaymentSessionReq extends BasePageRequest {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer paymentSessionId;
  @ApiModelProperty(value = "支付金额")
    private String amount;
    @ApiModelProperty(value = "过期时间")
    private String expire;
    @ApiModelProperty(value = "支付手续费")
    private String fee;
    @ApiModelProperty(value = "是否成功")
    private Boolean isSuccess;
    @ApiModelProperty(value = "第三方支付系统交易号")
    private String outTradeNo;
    @ApiModelProperty(value = "支付插件ID")
    private String paymentPluginId;
    @ApiModelProperty(value = "支付插件名称")
    private String paymentPluginName;
    @ApiModelProperty(value = "编号")
    private String sn;
    @ApiModelProperty(value = "支付方式")
    private String paymentScene;
    @ApiModelProperty(value = "",example="1")
    private Integer orderId;
    @ApiModelProperty(value = "",example="1")
    private Integer parentId;
  
}
