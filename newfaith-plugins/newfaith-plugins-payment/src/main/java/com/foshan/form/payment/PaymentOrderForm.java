package com.foshan.form.payment;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" (PaymentOrderForm)")
@JsonInclude(Include.NON_NULL)
public  class PaymentOrderForm implements IForm {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer paymentOrderId;
    @ApiModelProperty(value = "取消订单原因")
    private String cancelReason;
    @ApiModelProperty(value = "开单位发票时，单位名称")
    private String companyName;
    @ApiModelProperty(value = "优惠券优惠金额")
    private String couponDiscountAmount;
    @ApiModelProperty(value = "订单发展人")
    private String developer;
    @ApiModelProperty(value = "促销活动优惠金额")
    private String discountAmount;
    @ApiModelProperty(value = "订单过期时间")
    private String expirationTime;
    @ApiModelProperty(value = "发票抬头 0-个人 1--单位",example="1")
    private Integer invoiceTitle;
    @ApiModelProperty(value = "催单状态 0-未催单 1--已催单",example="1")
    private Integer isReminder;
    @ApiModelProperty(value = " 0-否； 1--需要，未开；2--需要，已冲红；3--需要，已开;",example="1")
    private Integer needInvoice;
    @ApiModelProperty(value = "订单编号")
    private String orderCode;
    @ApiModelProperty(value = "订单备注")
    private String orderMemo;
    @ApiModelProperty(value = "订单状态 0--已提交未付款  1--已付款  2--已发货 3--关闭未评价  4--已评价 7--已拆分 8--已取消",example="1")
    private Integer orderState;
    @ApiModelProperty(value = "订单原金额,促销优惠前金额")
    private String orderTotalAmount;
    @ApiModelProperty(value = "已付金额")
    private String paidAmount;
    @ApiModelProperty(value = "开单位发票时，单位的纳税识别号")
    private String paytaxNo;
    @ApiModelProperty(value = "发票类型 0-普通发票 1--增值税专用发票",example="1")
    private Integer productOrderType;
    @ApiModelProperty(value = "退款金额")
    private String refundAmount;
    @ApiModelProperty(value = "催单时间")
    private String reminderTime;
    @ApiModelProperty(value = "智能卡号")
    private String smartcardId;
    @ApiModelProperty(value = "",example="1")
    private Integer paymentMerchantId;
    @ApiModelProperty(value = "",example="1")
    private Integer paymentId;
  
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
