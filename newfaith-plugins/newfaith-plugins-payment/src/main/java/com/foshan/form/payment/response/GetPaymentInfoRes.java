package com.foshan.form.payment.response;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.PluginForm;
import com.foshan.form.response.BaseResponse;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** 
* @ClassName: package-info 
* @Description: TODO(获取支付信息的返回报文) 
* <AUTHOR>
* @date 2019年3月12日 下午10:42:25 
*  
*/
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class GetPaymentInfoRes extends BaseResponse {
	
	private static final long serialVersionUID = 7766600875090845238L;
	BigDecimal fee;
	BigDecimal amount;
	List<PluginForm> PaymentPlugins = new ArrayList<PluginForm>();
	
	
	
}
