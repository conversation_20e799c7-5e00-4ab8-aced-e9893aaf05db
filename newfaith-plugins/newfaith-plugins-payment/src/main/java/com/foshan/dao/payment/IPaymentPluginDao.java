package com.foshan.dao.payment;

import java.util.List;

import com.foshan.entity.payment.PaymentMerchantEntity;
import com.foshan.plugin.PaymentPlugin;

;

/** 
* @ClassName: paymentSessionDao 
* @Description: TODO(订单支付会话接口) 
* <AUTHOR>
* @date 2019年3月13日 上午11:04:56 
*  
*/
public interface IPaymentPluginDao  {

	/** 
	* @Title: getActivePaymentPlugins 
	* @Description: TODO(这里用一句话描述这个方法的作用) 
	* @param @param store
	* @param @return  参数说明 
	* @return List<PaymentPlugin>    返回类型 
	* @throws 
	*/
	List<PaymentPlugin> getActivePaymentPlugins(PaymentMerchantEntity store);

	

	
}
