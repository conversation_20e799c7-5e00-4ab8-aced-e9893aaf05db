package com.foshan.service.payment.impl;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.foshan.entity.PluginEntity;
import com.foshan.entity.payment.PaymentMerchantEntity;
import com.foshan.entity.payment.PaymentOrderEntity;
import com.foshan.form.payment.response.RefreshPaymentPluginConfigRes;

//import com.foshan.entity.PluginEntity;

import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.plugin.PaymentPlugin;
import com.foshan.service.annotation.Audit;
import com.foshan.service.payment.IPaymentPluginService;


@Transactional
@Service("paymentPluginService")
public class PaymentPluginServiceImpl extends GenericPaymentService implements IPaymentPluginService {
	private final static Logger logger = LoggerFactory.getLogger(PaymentPluginServiceImpl.class);
	private boolean onRefresh = false;

	
	private List<PaymentPlugin> paymentPlugins = new ArrayList<>();

	private Map<String, PaymentPlugin> paymentPluginMap = new HashMap<>();

	public List<PaymentPlugin> getPaymentPlugins(int forceRefresh,int type) {
		while (onRefresh); // 更插件信息时等待
		if (forceRefresh == 1 || paymentPlugins.size() == 0) {
			try {
				refreshPluginInfo(); 
			}
			catch(Exception e){
				logger.info("加载支付插件时出错:{}",e);
				this.onRefresh = false;
			}
		}
		return paymentPlugins;
	}

	/**
	 * @Title: refreshPluginInfo @Description: TODO(这里用一句话描述这个方法的作用) @param
	 * 参数说明 @return void 返回类型 @throws
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	private void refreshPluginInfo() throws Exception {
		this.onRefresh = true;
		paymentPlugins.clear();
		paymentPluginMap.clear();
		List<PluginEntity> list = pluginDao.getPluginConfigListType(0);
		for (PluginEntity pluginEntity : list) {
			try {
				Class clazz = Class.forName(pluginEntity.getSn());
				Constructor constructor = clazz.getConstructor(PluginEntity.class);
				PaymentPlugin item = (PaymentPlugin) constructor.newInstance(pluginEntity);
				item.setPluginConfig(pluginEntity);
				paymentPlugins.add(item);
				paymentPluginMap.put(item.getId(), item);

				logger.info("[" + pluginEntity.getSn() + "]支付插件加载成功");
				// logger.info("Attr["+pce.getAttr()+ "]");
			} catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
				logger.info("[" + pluginEntity.getSn() + "]支付插件加载失败，请检查部署环境是否存在对应的jar件");
				e.printStackTrace();
			} catch (NoSuchMethodException | SecurityException | IllegalArgumentException
					| InvocationTargetException ex) {
				logger.info("[" + pluginEntity.getSn() + "]支付插件加载失败");
				ex.printStackTrace();
			}
		}
		//Collections.sort(paymentPlugins);
		this.onRefresh = false;
	}

	@Override
	public List<PaymentPlugin> getPaymentPlugins() {
		return getPaymentPlugins(0,0);
	}




	@Override
	public PaymentPlugin getPaymentPlugin(PaymentMerchantEntity store,String sn) {
		PaymentPlugin result = null;
		List<PaymentPlugin>  paymentPluginList = paymentPluginDao.getActivePaymentPlugins(store);
		for (PaymentPlugin paymentPlugin : paymentPluginList) {
			if(paymentPlugin.getClass().getName().equals(sn)) {
				result = paymentPlugin;
			}
		}
		return result;
	}

	/*
	* Title: getPaymentPlugins
	*Description: 
	* @param forceRefresh
	* @return 
	* @see com.foshan.service.shop.PaymentPluginService#getPaymentPlugins(int) 
	*/
	@Override
	public List<PaymentPlugin> getPaymentPlugins(int forceRefresh) {
		return getPaymentPlugins(forceRefresh,0);
	}

	
	
//********************************************************************************终端用户部分************************	
	/*
	* Title: getActivePaymentPlugins
	*Description: 
	* @param store
	* @param request
	* @return 
	* @see com.foshan.service.shop.PaymentPluginService#getActivePaymentPlugins(com.foshan.entity.shop.PaymentMerchantEntity, javax.servlet.http.HttpServletRequest) 
	*/
	@Override
	public List<PaymentPlugin> getActivePaymentPlugins(PaymentMerchantEntity store, HttpServletRequest request) {
		Assert.notNull(store,"商铺不能为空");
		// TODO Auto-generated method stub
		//根据商铺ID获取该商铺支持的所有插件
		List<PaymentPlugin>  paymentPluginList = paymentPluginDao.getActivePaymentPlugins(store);
		List<PaymentPlugin> result = new ArrayList<>();
		for (PaymentPlugin paymentPlugin : paymentPluginList) {
			if (paymentPlugin.supports(request)) {
				result.add(paymentPlugin);
			}
		}
		return result;
	}

	/*
	* Title: getPaymentPlugin
	*Description: 
	* @param order
	* @param sn
	* @param request
	* @return 
	* @see com.foshan.service.shop.PaymentPluginService#getPaymentPlugin(com.foshan.entity.shop.ProductOrderEntity, java.lang.String, javax.servlet.http.HttpServletRequest) 
	*/
	@Override
	public PaymentPlugin getPaymentPlugin(PaymentOrderEntity order, String sn) {
		// TODO Auto-generated method stub
		Assert.notNull(order.getPaymentMerchant(),"获取订单的商户信息失败，数据不能为NULL");
		List<PaymentPlugin>  paymentPluginList = paymentPluginDao.getActivePaymentPlugins(order.getPaymentMerchant());
		for (PaymentPlugin paymentPlugin : paymentPluginList) {
			if (paymentPlugin.getId().equals(sn)) {
				return paymentPlugin;
			}
		}
		return null;
	}

	@Audit(operate = "支付插件 -刷新支付配置")
	@Override
	public IResponse refreshPaymentPluginConfig(Integer forceRefresh) {
		RefreshPaymentPluginConfigRes res = new RefreshPaymentPluginConfigRes();
		res.setCheckResult(getPaymentPlugins(1).size() > 0);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		
		return res;
	}

}