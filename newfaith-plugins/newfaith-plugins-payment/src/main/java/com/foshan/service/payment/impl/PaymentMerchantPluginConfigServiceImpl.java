/**   
* @Title: PaymentMerchantPluginConfigServiceImpl.java 
* @Package com.foshan.service.shop.impl 
* @Description: TODO(用一句话描述该文件做什么) 
* <AUTHOR>
* @date 2019年3月29日 下午6:16:07 
* @version V1.0   
*/
package com.foshan.service.payment.impl;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.dao.generic.Page;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.PluginEntity;
import com.foshan.entity.RegionEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.payment.PaymentMerchantEntity;
import com.foshan.entity.payment.PaymentMerchantPluginConfigEntity;
import com.foshan.form.UploadForm;
import com.foshan.form.payment.MerchantPaymentPluginListForm;
import com.foshan.form.payment.request.PaymentMerchantPluginReq;
import com.foshan.form.payment.request.UploadPaymentCertReq;
import com.foshan.form.payment.response.GetPaymentMerchantPluginRes;
import com.foshan.form.payment.response.GetPaymentPluginsRes;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.upload.UploadRes;
import com.foshan.plugin.PaymentPlugin;
import com.foshan.service.payment.IPaymentMerchantPluginConfigService;
import com.foshan.util.CodeUtil;
import com.foshan.util.DateUtil;
import com.foshan.util.DigestUtil;


/**
 * @ClassName: PaymentMerchantPluginConfigServiceImpl
 * @Description: TODO(这里用一句话描述这个类的作用)
 * <AUTHOR>
 * @date 2019年3月29日 下午6:16:07
 * 
 */
@Transactional
@Service("paymentMerchantPluginConfigService")
public class PaymentMerchantPluginConfigServiceImpl extends GenericPaymentService implements IPaymentMerchantPluginConfigService {
	private final static Logger logger = LoggerFactory.getLogger(PaymentMerchantPluginConfigServiceImpl.class);
	private boolean onRefresh = false;
	private List<PaymentPlugin> paymentPlugins = new ArrayList<>();
	private Map<String, PaymentPlugin> paymentPluginMap = new HashMap<>();
	
	@Override
	public List<PaymentMerchantPluginConfigEntity> getPaymentMerchantPluginConfigList(Integer paymentMerchantId, Integer pluginId, Integer type) {
		return paymentMerchantPluginConfigDao.getPaymentMerchantPluginConfigList(paymentMerchantId, pluginId, type);
	}


	@Override
	public PaymentMerchantPluginConfigEntity getPaymentMerchantPluginConfig(Integer paymentMerchantId, Integer pluginId, Integer type) {
		List<PaymentMerchantPluginConfigEntity> list = getPaymentMerchantPluginConfigList(paymentMerchantId, pluginId, type);
		if (list == null || list.size() == 0) {
			return null;
		} else {
			return list.get(0);
		}
	}
	
	@Override
	public PaymentMerchantPluginConfigEntity getPaymentMerchantPluginByMachId(String machId, String pluginSn) {
		return paymentMerchantPluginConfigDao.getPaymentMerchantPluginByMachId(machId, pluginSn);
	}
	
	@Override
	public GetPaymentPluginsRes getPaymentPlugins(PaymentMerchantPluginReq req) {
		
		GetPaymentPluginsRes res = new GetPaymentPluginsRes();
		Object userObj = getPrincipal(false);
		Integer paymentMerchantId = null;

//		if(userObj instanceof ShopUserEntity) {
//			ShopUserEntity shopUser = (ShopUserEntity)userObj;
//			paymentMerchantId = shopUser.getPaymentMerchant().getId();
//		}
//		else 
		if(userObj instanceof PlatformUserEntity) {
			if(req.getPaymentMerchantId() == null) {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "参数paymentMerchantId不能为空！");
				return res;
			}
			paymentMerchantId = req.getPaymentMerchantId();
		}
		else {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
		
		List<PaymentPlugin> list = getPaymentPlugins();
		List<MerchantPaymentPluginListForm> result = new ArrayList<MerchantPaymentPluginListForm>(list.size());
		for (PaymentPlugin p : list) {
			MerchantPaymentPluginListForm form = new MerchantPaymentPluginListForm();
			form.setPluginId(p.getPluginConfig().getId() + "");
			form.setName(p.getPluginConfig().getName());
			form.setAttrFieldDescription(p.getPluginConfig().getAttrFieldDescription());
			form.setSn(p.getPluginConfig().getSn());
			form.setLogo(p.getLogo());
			form.setPluginAttrTemplate(p.getPluginConfig().getAttr());
//			form.setCanConfig(p.getPluginConfig().canConfigForSeller());
			PaymentMerchantPluginConfigEntity spc = getPaymentMerchantPluginConfig(paymentMerchantId, p.getPluginConfig().getId(), 0);
			if (spc != null) {
				form.setAuditStatus(spc.getAuditStatus() + "");
				form.setAuditHistory(spc.getAuditHistory());
				form.setAppoint(spc.getAppoint());
				form.setAuditStatusName(getStatusNameFromCode(spc.getAuditStatus()));
				form.setLastModifyTime(DateUtil.formatLongFormat(spc.getLastModifyTime()));
				form.setPaymentMerchantPluginId(spc.getId() + "");
			}

			result.add(form);
		}
		res.setPluginList(result);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	

	@Override
	public BaseResponse addPaymentMerchantPaymentPluginConfig(PaymentMerchantPluginReq req) {
		BaseResponse res = new GenericResponse(ResponseContext.RES_SUCCESS_CODE, ResponseContext.RES_SUCCESS_CODE_INFO);
//		Object userObj = getPrincipal(false);
//		
//		if(userObj instanceof ShopUserEntity) {
//			ShopUserEntity shopUser = (ShopUserEntity)userObj;
//			req.setIsAppoint(false);
//			req.setPaymentMerchantId(shopUser.getPaymentMerchant().getId());
//		}
//		else {
//			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
//			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
//			return res;
//		}
//
//		if( req.getPluginId() == null ) {
//			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
//			res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "插件id不能为空");
//			return res;
//		}
		
		if(StringUtils.isNotEmpty(req.getAttr())) {
			try {
				JsonNode readTree = new ObjectMapper().readTree(req.getAttr());
				if(null == readTree) {
					res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
					res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "插件属性为非标准json格式");
					return res;
				}
			}
			catch(Exception e) {
				e.printStackTrace();
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "读取插件属性时出错格式");
				return res;
			}
			
		}

		// 检查是否重复创建
		if (paymentMerchantPluginConfigDao.getPaymentMerchantPluginConfigList(req.getPaymentMerchantId(), req.getPluginId(), 0).size() > 0) {
			res.setRet(ResponseContext.RES_DATA_EXIST_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_EXIST_ERROR_INFO + "当前支付方式已经创建了配置信息");
			return res;
		}

		PaymentMerchantEntity paymentMerchant = paymentMerchantDao.get(req.getPaymentMerchantId());
		PluginEntity plugin = pluginDao.get(req.getPluginId());

		if (paymentMerchant == null || plugin == null) { // 找不到对应的商户名插件
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			return res;
		}
//		if (plugin.canConfigForSeller() == false || plugin.canVisibleForSeller() == false) {
//			res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
//			res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
//			return res;
//		}

		Timestamp now = new Timestamp(new Date().getTime());
		PaymentMerchantPluginConfigEntity entity = new PaymentMerchantPluginConfigEntity();
		entity.setAttr(req.getAttr());
		entity.setCreateTime(now);
		entity.setState(1);
		entity.setLastModifyTime(now);
		entity.setAppoint(req.getIsAppoint());
		if (paymentMerchant.getAutoAudit() == null || paymentMerchant.getAutoAudit().intValue() != 1) { // 非自营商铺需要审核
			entity.setAuditStatus(EntityContext.STORE_PLUGIN_CONFIG_STATE_NOSUBMIT);
			entity.setAuditHistory("[" + DateUtil.formatLongFormat(new Date(now.getTime())) + "] 创建支付配置\n");
		} else { // 自营商铺不需要审核
			entity.setValidAttr(req.getAttr());
			entity.setAuditStatus(EntityContext.STORE_PLUGIN_CONFIG_STATE_PASS);
			entity.setAuditHistory("[" + DateUtil.formatLongFormat(new Date(now.getTime())) + "] 创建支付配置，新配置生效\n");
		}

		entity.setPlugin(plugin);
		entity.setPaymentMerchant(paymentMerchant);
		paymentMerchantPluginConfigDao.saveEntity(entity);

		return res;

	}


	@Override
	public IResponse updatePaymentMerchantPaymentPluginConfig(PaymentMerchantPluginReq req) {
		BaseResponse res = new GenericResponse(ResponseContext.RES_SUCCESS_CODE, ResponseContext.RES_SUCCESS_CODE_INFO);
//		Object userObj = getPrincipal(false);
//		
//		if(userObj instanceof ShopUserEntity) {
//			ShopUserEntity shopUser = (ShopUserEntity)userObj;
//			req.setIsAppoint(false);
//			req.setPaymentMerchantId(shopUser.getPaymentMerchant().getId());
//		}
//		else if(userObj instanceof PlatformUserEntity) {
//			if(req.getPaymentMerchantId() == null) {
//				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
//				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "参数paymentMerchantId不能为空！");
//				return res;
//			}
//		}
//		else {
//			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
//			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
//			return res;
//		}

		if(StringUtils.isNotEmpty(req.getAttr())) {
			try {
				JsonNode readTree = new ObjectMapper().readTree(req.getAttr());
				if(null == readTree) {
					res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
					res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "插件属性为非标准json格式");
					return res;
				}
			}
			catch(Exception e) {
				e.printStackTrace();
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "读取插件属性时出错格式");
				return res;
			}
			
		}


		if (req.getId() == null) { // ID不能为空
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			return res;
		}

		PaymentMerchantPluginConfigEntity entity = paymentMerchantPluginConfigDao.get(req.getId());
		if (entity == null) { // 数据找不到对应用的配置信息
			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
			res.setRetInfo("找不到对应用的支付配置信息");
			return res;
		}
		paymentMerchantPluginConfigDao.Evict(entity);
		PaymentMerchantEntity curPaymentMerchant = paymentMerchantDao.get(req.getPaymentMerchantId());
		if (curPaymentMerchant.getAutoAudit() != null && curPaymentMerchant.getAutoAudit().intValue() == 1) {// 自营商户不需要审核
			Timestamp now = new Timestamp(new Date().getTime());
			entity.setAttr(req.getAttr());
			entity.setValidAttr(req.getAttr());
			entity.setLastModifyTime(now);
			entity.setAuditHistory(entity.getAuditHistory() + "[" + DateUtil.formatLongFormat(new Date(now.getTime()))
					+ "] 修改支付配置，新配置生效\n");
			paymentMerchantPluginConfigDao.updateEntity(entity);
		} else { // 非自营商户
			if (entity.getAuditStatus() == EntityContext.STORE_PLUGIN_CONFIG_STATE_SUBMITED) { // 审核中商家不能修改
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				return res;
			} else {
				if (entity.getPaymentMerchant() == null || entity.getPlugin() == null) { // 找不到对应的商户名插件
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
					return res;
				}
//				if (entity.getPlugin().canConfigForSeller() == false
//						|| entity.getPlugin().canVisibleForSeller() == false) {
//					res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
//					res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
//					return res;
//				}
				Timestamp now = new Timestamp(new Date().getTime());
				entity.setAttr(req.getAttr());
				entity.setLastModifyTime(now);
				if (entity.getAuditStatus() == EntityContext.STORE_PLUGIN_CONFIG_STATE_NOPASS || entity.getAuditStatus() == EntityContext.STORE_PLUGIN_CONFIG_STATE_PASS) { // 审核不通过可以修改，修改完成后状态为未提交审核
					entity.setAuditStatus(EntityContext.STORE_PLUGIN_CONFIG_STATE_NOSUBMIT);
				}
				entity.setAuditHistory(entity.getAuditHistory() + "["
						+ DateUtil.formatLongFormat(new Date(now.getTime())) + "] 修改支付配置\n");
				paymentMerchantPluginConfigDao.updateEntity(entity);
			}
		}
		return res;

	}

	/*
	 * Title: commitPaymentPluginConfig Description:
	 * 
	 * @param req
	 * 
	 * @return
	 * 
	 * @see
	 * com.foshan.service.shop.PaymentMerchantPluginConfigService#commitPaymentPluginConfig(
	 * com.foshan.form.shop.request.PaymentMerchantPluginReq)
	 */
	@Override
	public IResponse submitPaymentPluginConfig(PaymentMerchantPluginReq req) {
		BaseResponse res = new GenericResponse(ResponseContext.RES_SUCCESS_CODE, ResponseContext.RES_SUCCESS_CODE_INFO);
//		Object userObj = getPrincipal(false);

//		if(userObj instanceof ShopUserEntity) {
//			ShopUserEntity shopUser = (ShopUserEntity)userObj;
//			req.setIsAppoint(false);
//			req.setPaymentMerchantId(shopUser.getPaymentMerchant().getId());
//		}
//		else {
//			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
//			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
//			return res;
//		}

		if (req.getId() == null) { // ID不能为空
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			return res;
		}

		PaymentMerchantPluginConfigEntity entity = paymentMerchantPluginConfigDao.get(req.getId());
		if (entity == null) { // 数据找不到对应用的配置信息
			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
			res.setRetInfo("找不到对应用的支付配置信息");
			return res;
		}
		paymentMerchantPluginConfigDao.Evict(entity);
		if (entity.getAuditStatus() != EntityContext.STORE_PLUGIN_CONFIG_STATE_NOSUBMIT && entity.getAuditStatus() != EntityContext.STORE_PLUGIN_CONFIG_STATE_NOPASS) { // 只有当商户支付配置项处理未提交审核或审核失败的情况下才可以提交审核
			res.setRet("1071");
			res.setRetInfo("状态错误，请确认支付插件配置当前是否处于[未提交审核]或[审核不通过]状态");
			return res;
		} else {
			entity.setAuditStatus(EntityContext.STORE_PLUGIN_CONFIG_STATE_SUBMITED);
			entity.setAuditHistory(
					entity.getAuditHistory() + "[" + DateUtil.formatLongFormat(new Date()) + "] 支付配置提交审核\n");
			paymentMerchantPluginConfigDao.updateEntity(entity);
		}
		return res;
	}

	/*
	 * Title: auditPaymentMerchantPaymentPluginConfig Description:
	 * 
	 * @param req
	 * 
	 * @return
	 * 
	 * @see com.foshan.service.shop.PaymentMerchantPluginConfigService#
	 * auditPaymentMerchantPaymentPluginConfig(com.foshan.form.shop.request.PaymentMerchantPluginReq)
	 */

	@Override
	public IResponse auditPaymentMerchantPaymentPluginConfig(PaymentMerchantPluginReq req) {

		BaseResponse res = new GenericResponse(ResponseContext.RES_SUCCESS_CODE, ResponseContext.RES_SUCCESS_CODE_INFO);
		Object userObj = getPrincipal(false);

		if(userObj instanceof PlatformUserEntity) {
			if (req.getAuditStatus() != EntityContext.STORE_PLUGIN_CONFIG_STATE_PASS && req.getAuditStatus() != EntityContext.STORE_PLUGIN_CONFIG_STATE_NOPASS) {// 审核结果只有审核通过或审核不通过，其它都有问题。
				res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
				res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO);
				return res;
			}
			if (req.getId() == null) { // ID不能为空
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				return res;
			}

			PaymentMerchantPluginConfigEntity entity = paymentMerchantPluginConfigDao.get(req.getId());
			if (entity == null) { // 数据找不到对应用的配置信息
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo("数据找不到对应用的配置信息");
				return res;
			}

			paymentMerchantPluginConfigDao.Evict(entity);

			if (entity.getAuditStatus() == EntityContext.STORE_PLUGIN_CONFIG_STATE_SUBMITED) { // 状态为审核中时才可以审核
				Timestamp now = new Timestamp(new Date().getTime());
				if (req.getAuditStatus() == EntityContext.STORE_PLUGIN_CONFIG_STATE_PASS) { // 审核通过
					entity.setValidAttr(entity.getAttr());
					entity.setLastModifyTime(now);
					entity.setAuditHistory(
							entity.getAuditHistory() + "[" + DateUtil.formatLongFormat(now) + "] 支付配置审核通过，新配置生效\n");
				} else {// 审核不通过
					entity.setAuditHistory(entity.getAuditHistory() + "[" + DateUtil.formatLongFormat(now)
							+ "] 支付配置审核不通过\n\t原因：" + req.getAuditInfo() + "\n");
				}
				entity.setAuditStatus(req.getAuditStatus());
				paymentMerchantPluginConfigDao.updateEntity(entity);
				return res;
			} else {
				res.setRet("1071");
				res.setRetInfo("状态错误，请确认支付插件配置当前是否处于[审核中]状态");
				return res;
			}

		}
		else {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}

	}

	/*
	 * Title: appointPaymentMerchantPaymentPluginConfig Description:
	 * 
	 * @param req
	 * 
	 * @return
	 * 
	 * @see com.foshan.service.shop.PaymentMerchantPluginConfigService#
	 * appointPaymentMerchantPaymentPluginConfig(com.foshan.form.shop.request.PaymentMerchantPluginReq)
	 */
	@Override
	public IResponse appointPaymentMerchantPaymentPluginConfig(PaymentMerchantPluginReq req) {
		
		BaseResponse res = new GenericResponse(ResponseContext.RES_SUCCESS_CODE, ResponseContext.RES_SUCCESS_CODE_INFO);
		Object userObj = getPrincipal(false);

		if(userObj instanceof PlatformUserEntity) {
			if (req.getPaymentMerchantId() == null || req.getPluginId() == null) { // 联合主键参数不能为空
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				return res;
			}
			
			if(StringUtils.isNotEmpty(req.getAttr())) {
				try {
					JsonNode readTree = new ObjectMapper().readTree(req.getAttr());
					if(null == readTree) {
						res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
						res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "插件属性为非标准json格式");
						return res;
					}
				}
				catch(Exception e) {
					e.printStackTrace();
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "读取插件属性时出错格式");
					return res;
				}
				
			}


			// 检查是否重复创建
			List<PaymentMerchantPluginConfigEntity> paymentMerchantPluginConfigList = paymentMerchantPluginConfigDao.getPaymentMerchantPluginConfigList(req.getPaymentMerchantId(), req.getPluginId(), 0);
			if (paymentMerchantPluginConfigList.size() > 0 && 
					paymentMerchantPluginConfigList.get(0).getAppoint() == false &&
					paymentMerchantPluginConfigList.get(0).getAuditStatus() != EntityContext.STORE_PLUGIN_CONFIG_STATE_PASS) {
				res.setRet("1072");
				res.setRetInfo("为商家指定支付配置失败，当前支付方式已经创建了配置信息");
				return res;
			}
			PaymentMerchantPluginConfigEntity entity = new PaymentMerchantPluginConfigEntity();
			entity.setAttr(req.getAttr());
			entity.setValidAttr(req.getAttr());
			// entity.setAuditHistory(req.getAuditHistory());

			Timestamp now = new Timestamp(new Date().getTime());
			entity.setCreateTime(now);
			entity.setState(1);
			entity.setLastModifyTime(now);
			entity.setAppoint(req.getIsAppoint());
			PaymentMerchantEntity paymentMerchant = paymentMerchantDao.get(req.getPaymentMerchantId());
			PluginEntity plugin = pluginDao.get(req.getPluginId());

			if (paymentMerchant == null || plugin == null) { // 找不到对应的商户名插件
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo("找不到对应的商户名插件");
				return res;
			}
//			if (plugin.canVisibleForSeller() == false) {
//				res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
//				res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
//				return res;
//			}

			// if (paymentMerchant.getIsSelfSupport() != 1) {
			entity.setAuditStatus(EntityContext.STORE_PLUGIN_CONFIG_STATE_PASS); // 管理员指定支付配置，默认为审核通过，是否权力太大
			// } else { entity.setAuditStatus(2); }
			entity.setAuditHistory((entity.getAuditHistory() == null ? "" : entity.getAuditHistory()) + "["
					+ DateUtil.formatLongFormat(new Date(now.getTime())) + "] 管理员为" + paymentMerchant.getPaymentMerchantName() + "["
					+ paymentMerchant.getId() + "]指定支付配置，新配置生效\n");

			entity.setPlugin(plugin);
			entity.setPaymentMerchant(paymentMerchant);

			paymentMerchantPluginConfigDao.saveEntity(entity);

			return res;
			
		}
		else {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
		
		
	}

	/**
	 * @Title: getStatusNameFromCode 
	 * @param  auditStatus  
	 * @return  String 返回类型 
	 * @throws
	 */
	private String getStatusNameFromCode(int auditStatus) {
		if (auditStatus == EntityContext.STORE_PLUGIN_CONFIG_STATE_NOSUBMIT) {
			return "未提交审核";
		} else if (auditStatus == EntityContext.STORE_PLUGIN_CONFIG_STATE_SUBMITED) {
			return "审核中";
		} else if (auditStatus == EntityContext.STORE_PLUGIN_CONFIG_STATE_PASS) {
			return "审核通过";
		} else {
			return "审核不通过";
		}
		
	}


	public IResponse updatePaymentMerchantPaymentPluginConfigByPlatform(PaymentMerchantPluginReq req) {
		BaseResponse res = new GenericResponse(ResponseContext.RES_SUCCESS_CODE, ResponseContext.RES_SUCCESS_CODE_INFO);
		Object userObj = getPrincipal(false);

		if(userObj instanceof PlatformUserEntity) {
			if (req.getId() == null) { // ID不能为空
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				return res;
			}
			
			if(StringUtils.isNotEmpty(req.getAttr())) {
				try {
					JsonNode readTree = new ObjectMapper().readTree(req.getAttr());
					if(null == readTree) {
						res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
						res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "插件属性为非标准json格式");
						return res;
					}
				}
				catch(Exception e) {
					e.printStackTrace();
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "读取插件属性时出错格式");
					return res;
				}
				
			}


			PaymentMerchantPluginConfigEntity entity = paymentMerchantPluginConfigDao.get(req.getId());
			if (entity == null) { // 数据找不到对应用的配置信息
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo("找不到对应用的支付配置信息");
				return res;
			}

			Timestamp now = new Timestamp(new Date().getTime());
			paymentMerchantPluginConfigDao.Evict(entity); //把对象变为游离态
			entity.setAttr(req.getAttr());
			entity.setValidAttr(req.getAttr());
			entity.setAppoint(req.getIsAppoint());
			entity.setLastModifyTime(now);
			entity.setAuditHistory(entity.getAuditHistory() + "[" + DateUtil.formatLongFormat(new Date(now.getTime()))
					+ "] 管理员修改支付配置，新配置生效\n");
			paymentMerchantPluginConfigDao.updateEntity(entity);

			return res;
		}
		else {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
	}
	
	@Override
	public BaseResponse addPaymentMerchantPaymentPluginConfigByPlatform(PaymentMerchantPluginReq req) {
		BaseResponse res = new GenericResponse(ResponseContext.RES_SUCCESS_CODE, ResponseContext.RES_SUCCESS_CODE_INFO);
		Object userObj = getPrincipal(false);
		
	    if(userObj instanceof PlatformUserEntity) {
			if(req.getPaymentMerchantId() == null) {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "参数paymentMerchantId不能为空！");
				return res;
			}
			if( req.getPluginId() == null ) {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "插件id不能为空");
				return res;
			}
			if(req.getIsAppoint() == null ) {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "插件isAppoint不能为空");
				return res;
			}
			if(StringUtils.isNotEmpty(req.getAttr())) {
				try {
					JsonNode readTree = new ObjectMapper().readTree(req.getAttr());
					if(null == readTree) {
						res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
						res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "插件属性为非标准json格式");
						return res;
					}
				}
				catch(Exception e) {
					e.printStackTrace();
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "读取插件属性时出错格式");
					return res;
				}
				
			}

			
			// 检查是否重复创建
			if (paymentMerchantPluginConfigDao.getPaymentMerchantPluginConfigList(req.getPaymentMerchantId(), req.getPluginId(), 0).size() > 0) {
				res.setRet("1072");
				res.setRetInfo("配置失败，当前支付方式已经创建了配置信息");
				return res;
			}
			PaymentMerchantEntity paymentMerchant = paymentMerchantDao.get(req.getPaymentMerchantId());
			PluginEntity plugin = pluginDao.get(req.getPluginId());

			if (paymentMerchant == null || plugin == null) { // 找不到对应的商户名插件
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				return res;
			}
			

			Timestamp now = new Timestamp(new Date().getTime());
			PaymentMerchantPluginConfigEntity entity = new PaymentMerchantPluginConfigEntity();
			entity.setAttr(req.getAttr());
			entity.setCreateTime(now);
			entity.setState(1);
			entity.setLastModifyTime(now);
			entity.setAppoint(req.getIsAppoint());
			entity.setValidAttr(req.getAttr());
			entity.setAuditStatus(EntityContext.STORE_PLUGIN_CONFIG_STATE_PASS);
			entity.setAuditHistory("[" + DateUtil.formatLongFormat(new Date(now.getTime())) + "] 创建支付配置，新配置生效\n");
			
			entity.setPlugin(plugin);
			entity.setPaymentMerchant(paymentMerchant);
			paymentMerchantPluginConfigDao.saveEntity(entity);
			
		}
		else {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
		}

		return res;

	}

	
	public IResponse deletePaymentMerchantPaymentPluginConfigByPlatform(PaymentMerchantPluginReq req) {
		// TODO Auto-generated method stub
		BaseResponse res = new GenericResponse(ResponseContext.RES_SUCCESS_CODE, ResponseContext.RES_SUCCESS_CODE_INFO);
		Object userObj = getPrincipal(false);
		if(userObj == null) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
		
		if(userObj instanceof PlatformUserEntity) {
			if (req.getId() == null) { // ID不能为空
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				return res;
			}

			PaymentMerchantPluginConfigEntity entity = paymentMerchantPluginConfigDao.get(req.getId());
			if (entity == null) { // 数据找不到对应用的配置信息
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo("找不到对应用的支付配置信息");
				return res;
			}
			
			paymentMerchantPluginConfigDao.delete(entity); //删除对象
			return res;
		}
		else {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
	

	}

	@Override
	public IResponse getPaymentPlugins(Integer curPageNum, Integer pageSize, Integer paymentMerchantId, String paymentMerchantName,
			Integer pluginId, Integer auditStatus, Integer regionId) {
		GetPaymentPluginsRes res =  new GetPaymentPluginsRes();
		Page<PaymentMerchantPluginConfigEntity> page = new Page<PaymentMerchantPluginConfigEntity>();
		Object userObj = getPrincipal(false);
		
		if(userObj instanceof PlatformUserEntity) {
			page.setPageSize(null != pageSize ? pageSize : 10);
			page.setBeginCount((null != curPageNum ? curPageNum : 1 - 1) * pageSize);
			page.setCurrentPage(null != curPageNum ? curPageNum : 1);

			if(null != regionId) {
				RegionEntity region = regionDao.get(regionId);
				String startRegionCode = region.getStartRegionCode();
				String endRegionCode = region.getEndRegionCode();
				paymentMerchantPluginConfigDao.getPaymentPlugins(page,paymentMerchantId,paymentMerchantName,pluginId,auditStatus,startRegionCode,endRegionCode);
			}else {
				paymentMerchantPluginConfigDao.getPaymentPlugins(page,paymentMerchantId,paymentMerchantName,pluginId,auditStatus);
			}
			
	        page.getResultList().forEach(s -> {
	        	MerchantPaymentPluginListForm form =  new MerchantPaymentPluginListForm();
	        	form.setAppoint(s.getAppoint());
				form.setPluginId(s.getPlugin().getId() + "");
				form.setAttr(s.getAttr());
				form.setAuditHistory(s.getAuditHistory());
				form.setAuditStatus(s.getAuditStatus() + "");
				form.setAuditStatusName(getStatusNameFromCode(s.getAuditStatus()));
//				form.setCanConfig(s.getPlugin().canConfigForSeller());
				form.setLastModifyTime(DateUtil.formatLongFormat(s.getLastModifyTime()));
				form.setName(s.getPlugin().getName());
				form.setSn(s.getPlugin().getSn());
				form.setPaymentMerchantPluginId(s.getId() + "");
				form.setPaymentMerchantName(s.getPaymentMerchant().getPaymentMerchantName());
				form.setAttrFieldDescription(s.getPlugin().getAttrFieldDescription());
				form.setValidAttr(s.getValidAttr());
				form.setPaymentMerchantId(s.getPaymentMerchant().getId()+"");
				res.getPluginList().add(form);        	
	        });
			
			res.setTotalResult(page.getTotalCount());
			res.setPageSize(page.getPageSize());
			res.setCurrentPage(page.getCurrentPage());
			res.setTotal(page.getTotalPage());
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			return res;
		}
		else {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
	
	}

	@Override
	public GetPaymentMerchantPluginRes getPaymentMerchantPluginDetail(PaymentMerchantPluginReq req) {
		GetPaymentMerchantPluginRes res = new GetPaymentMerchantPluginRes();
		
		Integer paymentMerchantId = null;
//		Object userObj = getPrincipal(false);
//		if(userObj == null) {
//			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
//			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
//			return res;
//		}
//		

		if(req.getId() == null) {
			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "商铺插件id不能为空");
			return res;
		}

		PaymentMerchantPluginConfigEntity paymentMerchantPlugin = paymentMerchantPluginConfigDao.get(req.getId());
//		if(userObj instanceof PlatformUserEntity || userObj instanceof ShopUserEntity) {
//			if(userObj instanceof ShopUserEntity) {
//				ShopUserEntity shopUser = (ShopUserEntity)userObj;
//				paymentMerchantId = shopUser.getPaymentMerchant().getId();
				paymentMerchantId = req.getPaymentMerchantId();
				if(paymentMerchantId !=null) {
					if(paymentMerchantPlugin.getPaymentMerchant().getId().intValue() != paymentMerchantId.intValue()) {
						return new GetPaymentMerchantPluginRes(ResponseContext.RES_PERM_UNAUTHORIZED_CODE, "当前商户无权限查看该数据");
					}
					if(paymentMerchantPlugin.getAppoint() == true){
						return new GetPaymentMerchantPluginRes(ResponseContext.RES_PERM_UNAUTHORIZED_CODE, "不能查看系统指定的配置信息");
					}
				}
				
//			}
//		}
//		else {
//			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
//			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
//			return res;
//		}
//

		if (paymentMerchantPlugin != null) {
			
			MerchantPaymentPluginListForm form = new MerchantPaymentPluginListForm();
			form.setAppoint(paymentMerchantPlugin.getAppoint());
			form.setPluginId(paymentMerchantPlugin.getPlugin().getId() + "");
			form.setAttr(paymentMerchantPlugin.getAttr());
			form.setAuditHistory(paymentMerchantPlugin.getAuditHistory());
			form.setAuditStatus(paymentMerchantPlugin.getAuditStatus() + "");
			form.setAuditStatusName(getStatusNameFromCode(paymentMerchantPlugin.getAuditStatus()));
//			form.setCanConfig(paymentMerchantPlugin.getPlugin().canConfigForSeller());
			form.setLastModifyTime(DateUtil.formatLongFormat(paymentMerchantPlugin.getLastModifyTime()));
			form.setName(paymentMerchantPlugin.getPlugin().getName());
			form.setLogo(paymentMerchantPlugin.getPlugin().getLogo());
			form.setSn(paymentMerchantPlugin.getPlugin().getSn());
			form.setPaymentMerchantPluginId(paymentMerchantPlugin.getId() + "");
			form.setPaymentMerchantId(paymentMerchantPlugin.getPaymentMerchant().getId() + "");
			form.setPaymentMerchantName(paymentMerchantPlugin.getPaymentMerchant().getPaymentMerchantName());
			form.setAttrFieldDescription(paymentMerchantPlugin.getPlugin().getAttrFieldDescription());
			form.setValidAttr(paymentMerchantPlugin.getValidAttr());
			res.setPaymentMerchantPaymentPluginListForm(form);
			
		}
		else {
			res = new GetPaymentMerchantPluginRes(ResponseContext.RES_DATA_NULL_CODE, "找不到对应用的支付配置信息");
			res.setPaymentMerchantPaymentPluginListForm(null);;
		}

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public GetPaymentMerchantPluginRes getPaymentPluginDetail(PaymentMerchantPluginReq req) {
		GetPaymentMerchantPluginRes res = new GetPaymentMerchantPluginRes();
//		Object userObj = getPrincipal(false);
//		if(userObj == null) {
//			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
//			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
//			return res;
//		}
//		
		//if(userObj instanceof ShopUserEntity || userObj instanceof PlatformUserEntity) {
			if(req.getPluginId() == null) {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "pluginId不能为空!");
				return res;
			}
			
			PluginEntity pluginEntity = pluginDao.get(req.getPluginId());
			
			if(pluginEntity != null) {
				MerchantPaymentPluginListForm form = new MerchantPaymentPluginListForm();
				form.setPluginId(pluginEntity.getId() + "");
				form.setName(pluginEntity.getName());
				form.setAttrFieldDescription(pluginEntity.getAttrFieldDescription());
				form.setSn(pluginEntity.getSn());
				form.setLogo(pluginEntity.getLogo());
				form.setPluginAttrTemplate(pluginEntity.getAttr());
//				form.setCanConfig(pluginEntity.canConfigForSeller());
				res.setPaymentMerchantPaymentPluginListForm(null);;
				res.setRet(ResponseContext.RES_SUCCESS_CODE);  
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				return res;
			}
			else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);  
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO+ "找不到指定的插件信息");
				return res;
			}
//		}
//		else {
//			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
//			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
//			return res;
//		}
	}
	
	@Override
	public UploadRes uploadCertFile(HttpServletRequest request,UploadPaymentCertReq req) {
		UploadRes res = new UploadRes();
		Integer paymentMerchantId = null;
//		Object userObj = getPrincipal(false);
//		if(userObj == null) {
//			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
//			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
//			return res;
//		}
		
//		if(userObj instanceof ShopUserEntity) {
//			ShopUserEntity shopUser = (ShopUserEntity)userObj;
//			paymentMerchantId = shopUser.getPaymentMerchant().getId();
//		}
//		else if(userObj instanceof PlatformUserEntity) {
		paymentMerchantId = req.getPaymentMerchantId();
		if(paymentMerchantId == null) {
			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "参数paymentMerchantId不能为空！");
			return res;
		}
//		}
//		else {
//			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
//			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
//			return res;
//		}
		
		String realDirPath = contextInfo.getCertFileRootDir() + File.separator + "payment" + File.separator + paymentMerchantId
				+ File.separator;
		UploadRes resTmp;
		try {
			resTmp = (UploadRes) uploadFile(request, realDirPath, null, false, null,
					"crt,pem,p12,der,cer,pfx");
			if(resTmp.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
				String saveFileName = "payment" + File.separator + paymentMerchantId + File.separator
						+ resTmp.getUploadFormList().get(0).getFileName();
				resTmp.getUploadFormList().get(0).setFileName(saveFileName);
				res.getUploadFormList().add(resTmp.getUploadFormList().get(0));
			}
			res.setRet(resTmp.getRet());
			res.setRetInfo(resTmp.getRetInfo());
			
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo(e.getMessage());
		}
		
		return res;
		
	}
	public List<PaymentPlugin> getPaymentPlugins() {
		return getPaymentPlugins(0,0);
	}
	public List<PaymentPlugin> getPaymentPlugins(int forceRefresh,int type) {
		while (onRefresh); // 更插件信息时等待
		if (forceRefresh == 1 || paymentPlugins.size() == 0) {
			try {
				refreshPluginInfo(); 
			}
			catch(Exception e){
				logger.info("加载支付插件时出错:{}",e);
				this.onRefresh = false;
			}
		}
		return paymentPlugins;
	}
	@SuppressWarnings({ "rawtypes", "unchecked" })
	private void refreshPluginInfo() throws Exception {
		this.onRefresh = true;
		paymentPlugins.clear();
		paymentPluginMap.clear();
		List<PluginEntity> list = pluginDao.getPluginConfigListType(0);
		for (PluginEntity pluginEntity : list) {
			try {
				Class clazz = Class.forName(pluginEntity.getSn());
				Constructor constructor = clazz.getConstructor(PluginEntity.class);
				PaymentPlugin item = (PaymentPlugin) constructor.newInstance(pluginEntity);
				item.setPluginConfig(pluginEntity);
				paymentPlugins.add(item);
				paymentPluginMap.put(item.getId(), item);

				logger.info("[" + pluginEntity.getSn() + "]支付插件加载成功");
				// logger.info("Attr["+pce.getAttr()+ "]");
			} catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
				logger.info("[" + pluginEntity.getSn() + "]支付插件加载失败，请检查部署环境是否存在对应的jar件");
				e.printStackTrace();
			} catch (NoSuchMethodException | SecurityException | IllegalArgumentException
					| InvocationTargetException ex) {
				logger.info("[" + pluginEntity.getSn() + "]支付插件加载失败");
				ex.printStackTrace();
			}
		}
		//Collections.sort(paymentPlugins);
		this.onRefresh = false;
	}
	public IResponse uploadFile(HttpServletRequest request,String realUrl ,
			String virtualUrl, boolean saveState,Integer assetType,String fileSuffix)throws IllegalStateException, IOException {

		UploadRes res = new UploadRes();
		UploadForm uploadForm = new UploadForm();
		CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
				request.getSession().getServletContext());
		if (multipartResolver.isMultipart(request)) {
			//Date date = new Date();
			MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
			Iterator<String> iter = multiRequest.getFileNames();
			while (iter.hasNext()) {
				MultipartFile file = multiRequest.getFile(iter.next().toString());
				String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".")+1).toLowerCase();

				if(!fileSuffix.contains(suffix)){
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "不支持该文件类型");
					return res;
				}
				
				createMultilayerFile(realUrl);
				if (file != null) {
					String fileName = System.currentTimeMillis() + "-" + DigestUtil.getMD5Str(file.getOriginalFilename()) + "-"
							+ CodeUtil.getId(10000)
							+ file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
					String fileUpload = realUrl + File.separator + fileName;
					file.transferTo(new File(fileUpload));
					if (suffix.equals("jpg") || suffix.equals("png") || suffix.equals("jpeg") || suffix.equals("bmp") || suffix.equals("gif")){
						modifyImageFormat(fileUpload,fileUpload,suffix);
					}
					String url =  virtualUrl+ "/"  + fileName;
					uploadForm.setFilePath(url);
					uploadForm.setFileName(fileName);
					if(saveState){
						AssetEntity asset = new AssetEntity();
						String transName = String.valueOf(DigestUtil.getMD5Str(fileName));
						asset.setAssetCode(transName);
						asset.setAssetName(new String(file.getOriginalFilename().getBytes("ISO-8859-1"),"UTF-8"));
						asset.setAssetState(EntityContext.ASSET_STATE_PENDING);
						asset.setAssetType(assetType);
						asset.setImageFile(url);
						asset.setPackageFlag(EntityContext.ASSET_PACKAGE_FLAG_INVALID);
						asset.setSmallImageFile(url);
						assetDao.save(asset);
						uploadForm.setFileId(asset.getId());
					}
				}
			}
			//res.setUploadFile(f);
			res.getUploadFormList().add(uploadForm);
		} 
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	private static boolean createMultilayerFile(String dir) {
		try {
			File dirPath = new File(dir);
			if (!dirPath.exists()) {
				dirPath.mkdirs();
			}
		} catch (Exception e) {
			System.out.println("创建多层目录操作出错: " + e.getMessage());
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	/**
	 * 修改原图的文件格式
	 * @param srcPath 原图路径
	 * @param destPath 新图路径
	 * @param formatName 图片格式，支持bmp|gif|jpg|jpeg|png
	 * @return
	 */
	public static boolean modifyImageFormat(String srcPath, String destPath, String formatName) {
		boolean isSuccess = false;
		InputStream fis = null;
		try {
			fis = new FileInputStream(srcPath); 
			BufferedImage bufferedImg = ImageIO.read(fis);
			isSuccess = ImageIO.write(bufferedImg, formatName, new File(destPath));
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (fis != null) {
				try {
					fis.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return isSuccess;
	}
}
