package com.foshan.service.payment.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.LockMode;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.query.Query;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.foshan.dao.generic.Page;
import com.foshan.domain.payment.PaymentResultNotifyMessage;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.payment.PaymentOrderEntity;
import com.foshan.entity.payment.PaymentRecordEntity;
import com.foshan.entity.payment.PaymentSessionEntity;
import com.foshan.form.payment.PaymentPaymentRecordForm;
import com.foshan.form.payment.request.PaymentPaymentRecordReq;
import com.foshan.form.payment.response.paymentPaymentRecord.AddPaymentPaymentRecordRes;
import com.foshan.form.payment.response.paymentPaymentRecord.GetPaymentPaymentRecordInfoRes;
import com.foshan.form.payment.response.paymentPaymentRecord.GetPaymentPaymentRecordListRes;
import com.foshan.form.payment.response.paymentPaymentRecord.ModifyPaymentPaymentRecordRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.payment.IPaymentPaymentRecordService;
import com.foshan.util.CodeUtil;
import com.foshan.util.DigestUtil;
import com.foshan.util.HttpClientUtil;
import com.foshan.util.SpringHandler;
import com.hazelcast.collection.IQueue;
import com.hazelcast.spring.cache.HazelcastCacheManager;

@Transactional
@Service("paymentPaymentRecordService")
public class PaymentPaymentRecordServiceImpl extends GenericPaymentService implements IPaymentPaymentRecordService {

	private final static Logger logger = LoggerFactory.getLogger(PaymentPaymentRecordServiceImpl.class);
	private static ExecutorService threadService = Executors.newFixedThreadPool(5);

	@Resource
	private HazelcastCacheManager cacheManager;

	@Override
	public IResponse getPaymentPaymentRecordList(PaymentPaymentRecordReq req) {
		GetPaymentPaymentRecordListRes res = new GetPaymentPaymentRecordListRes();
		Page<PaymentRecordEntity> page = new Page<PaymentRecordEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from PaymentRecordEntity a where 1=1 ");
		hql.append("");
		hql.append(" ORDER BY a.id desc");
		page = paymentRecordDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			PaymentPaymentRecordForm paymentPaymentRecordForm = new PaymentPaymentRecordForm();
			paymentPaymentRecordForm.setPaymentPaymentRecordId(o.getId());
			paymentPaymentRecordForm.setAccount(o.getAccount());
			paymentPaymentRecordForm.setAmount(null != o.getAmount() ? o.getAmount().toString() : "");
			paymentPaymentRecordForm.setBank(o.getBank());
			paymentPaymentRecordForm.setFee(null != o.getFee() ? o.getFee().toString() : "");
			paymentPaymentRecordForm.setMemo(o.getMemo());
//            paymentPaymentRecordForm.setMethod(o.getMethod());
			paymentPaymentRecordForm.setOutTradeNo(o.getOutTradeNo());
			paymentPaymentRecordForm.setPayer(o.getPayer());
			paymentPaymentRecordForm.setPaymentMethod(o.getPaymentMethod());
			paymentPaymentRecordForm.setPaymentPlunginSn(o.getPaymentPlunginSn());
			paymentPaymentRecordForm.setPaymentSessionSn(o.getPaymentSessionSn());
			paymentPaymentRecordForm.setSn(o.getSn());
//            paymentPaymentRecordForm.setOrderId(o.getOrderId());
//            paymentPaymentRecordForm.setParentPaymentId(o.getParentPaymentId());
			res.getPaymentPaymentRecordList().add(paymentPaymentRecordForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public IResponse addPaymentPaymentRecord(PaymentPaymentRecordReq req) {
		AddPaymentPaymentRecordRes res = new AddPaymentPaymentRecordRes();
		// if () {
		PaymentRecordEntity paymentPaymentRecord = new PaymentRecordEntity();

		paymentPaymentRecord.setAccount(req.getAccount());
		paymentPaymentRecord
				.setAmount(StringUtils.isNotEmpty(req.getAmount()) ? new BigDecimal(req.getAmount()) : null);
		paymentPaymentRecord.setBank(req.getBank());
		paymentPaymentRecord.setFee(StringUtils.isNotEmpty(req.getFee()) ? new BigDecimal(req.getFee()) : null);
		paymentPaymentRecord.setMemo(req.getMemo());
//            paymentPaymentRecord.setMethod(req.getMethod());
		paymentPaymentRecord.setOutTradeNo(req.getOutTradeNo());
		paymentPaymentRecord.setPayer(req.getPayer());
		paymentPaymentRecord.setPaymentMethod(req.getPaymentMethod());
		paymentPaymentRecord.setPaymentPlunginSn(req.getPaymentPlunginSn());
		paymentPaymentRecord.setPaymentSessionSn(req.getPaymentSessionSn());
		paymentPaymentRecord.setSn(req.getSn());
//            paymentPaymentRecord.setOrderId(req.getOrderId());
//            paymentPaymentRecord.setParentPaymentId(req.getParentPaymentId());
		paymentRecordDao.save(paymentPaymentRecord);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		} else {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//		}
		return res;
	}

	@Override
	public IResponse modifyPaymentPaymentRecord(PaymentPaymentRecordReq req) {
		ModifyPaymentPaymentRecordRes res = new ModifyPaymentPaymentRecordRes();
		if (null != req.getPaymentPaymentRecordId()) {
			PaymentRecordEntity paymentPaymentRecord = paymentRecordDao.get(req.getPaymentPaymentRecordId());
			if (null != paymentPaymentRecord) {
				paymentPaymentRecord.setAccount(req.getAccount());
				paymentPaymentRecord
						.setAmount(StringUtils.isNotEmpty(req.getAmount()) ? new BigDecimal(req.getAmount()) : null);
				paymentPaymentRecord.setBank(req.getBank());
				paymentPaymentRecord.setFee(StringUtils.isNotEmpty(req.getFee()) ? new BigDecimal(req.getFee()) : null);
				paymentPaymentRecord.setMemo(req.getMemo());
//                paymentPaymentRecord.setMethod(req.getMethod());
				paymentPaymentRecord.setOutTradeNo(req.getOutTradeNo());
				paymentPaymentRecord.setPayer(req.getPayer());
				paymentPaymentRecord.setPaymentMethod(req.getPaymentMethod());
				paymentPaymentRecord.setPaymentPlunginSn(req.getPaymentPlunginSn());
				paymentPaymentRecord.setPaymentSessionSn(req.getPaymentSessionSn());
				paymentPaymentRecord.setSn(req.getSn());
//                paymentPaymentRecord.setOrderId(req.getOrderId());
//                paymentPaymentRecord.setParentPaymentId(req.getParentPaymentId());
				res.setPaymentPaymentRecordId(paymentPaymentRecord.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse deletePaymentPaymentRecord(PaymentPaymentRecordReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getPaymentPaymentRecordId()) {
			PaymentRecordEntity paymentPaymentRecord = paymentRecordDao.get(req.getPaymentPaymentRecordId());
			if (null != paymentPaymentRecord) {
				paymentRecordDao.deleteById(req.getPaymentPaymentRecordId());
				// paymentPaymentRecord.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getPaymentPaymentRecordInfo(PaymentPaymentRecordReq req) {
		GetPaymentPaymentRecordInfoRes res = new GetPaymentPaymentRecordInfoRes();
		if (null != req.getPaymentPaymentRecordId()) {
			PaymentRecordEntity paymentPaymentRecord = paymentRecordDao.get(req.getPaymentPaymentRecordId());
			if (null != paymentPaymentRecord) {
				PaymentPaymentRecordForm paymentPaymentRecordForm = new PaymentPaymentRecordForm();
				paymentPaymentRecordForm.setPaymentPaymentRecordId(paymentPaymentRecord.getId());
				paymentPaymentRecordForm.setAccount(paymentPaymentRecord.getAccount());
				paymentPaymentRecordForm.setAmount(
						null != paymentPaymentRecord.getAmount() ? paymentPaymentRecord.getAmount().toString() : "");
				paymentPaymentRecordForm.setBank(paymentPaymentRecord.getBank());
				paymentPaymentRecordForm
						.setFee(null != paymentPaymentRecord.getFee() ? paymentPaymentRecord.getFee().toString() : "");
				paymentPaymentRecordForm.setMemo(paymentPaymentRecord.getMemo());
//                paymentPaymentRecordForm.setMethod(paymentPaymentRecord.getMethod());
				paymentPaymentRecordForm.setOutTradeNo(paymentPaymentRecord.getOutTradeNo());
				paymentPaymentRecordForm.setPayer(paymentPaymentRecord.getPayer());
				paymentPaymentRecordForm.setPaymentMethod(paymentPaymentRecord.getPaymentMethod());
				paymentPaymentRecordForm.setPaymentPlunginSn(paymentPaymentRecord.getPaymentPlunginSn());
				paymentPaymentRecordForm.setPaymentSessionSn(paymentPaymentRecord.getPaymentSessionSn());
				paymentPaymentRecordForm.setSn(paymentPaymentRecord.getSn());
//                paymentPaymentRecordForm.setOrderId(paymentPaymentRecord.getOrderId());
//                paymentPaymentRecordForm.setParentPaymentId(paymentPaymentRecord.getParentPaymentId());
				res.setPaymentPaymentRecordForm(paymentPaymentRecordForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@PostConstruct
	private void notifyPaymentResult() throws Exception {
		// 服务重启时获取已经支付成功，但未能成功通知业务系统的会话，重新通知一遍。
		for (int i = 0; i < 5; i++) {
			threadService.execute(new Runnable() {
				@SuppressWarnings("unlikely-arg-type")
				@Override
				public void run() {
					try {
						IQueue<PaymentResultNotifyMessage> queue = cacheManager.getHazelcastInstance()
								.getQueue("paymentResultNotifyQueue");

						while (true) {
							PaymentResultNotifyMessage item = queue.take();
							logger.info("异步处理付款结果消息: {}", item);
							if (item != null) {
								SessionFactory sessionFactory = (SessionFactory) SpringHandler
										.getBean("faithSessionFactory");
								Session session = sessionFactory.openSession();
								Transaction transaction = session.beginTransaction();
								@SuppressWarnings("unchecked")
								Query<PaymentSessionEntity> query = session
										.createQuery("select e from PaymentSessionEntity e where e.sn ='"
												+ item.getPaymentSessionSn() + "'");
								PaymentSessionEntity paymentSession = query.uniqueResult();
								if (paymentSession != null) {
									if (!LockMode.PESSIMISTIC_WRITE.equals(session.getLockMode(paymentSession))) {
										session.flush();
										session.refresh(paymentSession, LockMode.PESSIMISTIC_WRITE);
									}
									// 支付成功
									if (item.getResulstMap() != null
											&& "SUCCESS".equals(item.getResulstMap().get("returnCode"))) {
										// 回写订单
										PaymentOrderEntity order = paymentSession.getOrder();
										if (order != null) {
											logger.info("[{}]创建订单支付记录OrderPayment", paymentSession.getSn());
											PaymentRecordEntity orderPayment = new PaymentRecordEntity();
											orderPayment.setMethod(PaymentRecordEntity.Method.online);
											orderPayment.setPaymentMethod(paymentSession.getPaymentPluginName());
											orderPayment.setAmount(paymentSession.getAmount());
											orderPayment.setFee(paymentSession.getFee());
											orderPayment.setOrder(order);
											orderPayment.setOutTradeNo(paymentSession.getOutTradeNo());
											orderPayment.setPaymentSessionSn(paymentSession.getSn());
											orderPayment.setPaymentPlunginSn(paymentSession.getPaymentPluginId());

											// 修改支付记录
											Assert.notNull(order, "订单不能为NULL");
											Assert.notNull(orderPayment, "订单支付不能为NULL");
											Assert.notNull(orderPayment.getAmount(), "订单支付金额不能为NULL");
											Assert.state(orderPayment.getAmount().compareTo(BigDecimal.ZERO) > 0,
													"订单支付金额需要大于零");

											orderPayment.setSn(CodeUtil.getSNCode("PR"));
											orderPayment.setOrder(order);
											session.save(orderPayment);
											logger.info("[{}]保存订单支付记录", paymentSession.getSn());

											// 修改已付金额
											BigDecimal paidAmount = order.getPaidAmount();
											paidAmount = (paidAmount == null) ? BigDecimal.ZERO : paidAmount;
											order.setPaidAmount(paidAmount.add(orderPayment.getEffectiveAmount()));
											order.setPaymentRecord(orderPayment);
											// 需要配送的订单付款后状态变为已付款
											order.setOrderState(EntityContext.PRODUCT_ORDER_STATE_PAY);

										}
										else {
											logger.error("[{}]支付会话找不到对应的订单信息", paymentSession.getSn());
										}
										// 修改支付会话状态为已经成功
										paymentSession.setIsSuccess(true);
										logger.info("[{}]支付会话状态设置为成功，会话结束", paymentSession.getSn());

									}

									// 支付结果通知业务系统
									sendPaymentMessageToBuss(paymentSession);
								}
								else {
									logger.error("找不到[paymentSessionId={}]的支付会话记录！", item.getPaymentSessionSn());
								}

								if (transaction != null) {
									transaction.commit();
								}

								if (session != null && session.isOpen()) {
									session.close();
								}
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});
			//logger.debug("--启动支付结果消息通知线程" + i + "--");
		}
		logger.info("--支付结果消息通知线程启动完成！TreadsNum:5--");
	}

	/**
	 * 
	 * @Title:退款结果通知业务系统
	 * @Description:
	 * @param paymentSession PaymentSessionEntity
	 * @throws Exception
	 */
	private void sendPaymentMessageToBuss(PaymentSessionEntity paymentSession) throws Exception {
		// 通知业务系统
		if (paymentSession == null) {
			return;
		}

		Map<String, String> modelMap = new TreeMap<>();
		if (paymentSession.getIsSuccess() == true) {
			modelMap.put("returnCode", "SUCCESS");

			// nonceStr 要与下单返回结果中的一致
			modelMap.put("amount", paymentSession.getAmount().toString());
		} else {
			modelMap.put("returnCode", "FAILED");
		}
		modelMap.put("clientTradeNo", paymentSession.getClientTradeNo());
		modelMap.put("paymentSessionSn", paymentSession.getSn());
		modelMap.put("outTradeNo", paymentSession.getOutTradeNo());

		Thread.sleep(1000);
		DigestUtil.fillSignatureParam(modelMap, paymentSession.getOrder().getPaymentMerchant().getMerchantCode(),
				paymentSession.getOrder().getPaymentMerchant().getSecret());

		String jsonstr = JSONObject.valueToString(modelMap);
		String post;
		try {
			logger.info("发送支付结果报文到业务系统：");
			post = HttpClientUtil.jsonPost(paymentSession.getNotifyUrl(), "UTF-8", jsonstr, null);
			logger.info("业务系统响应支付通知报文：{}", post);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		paymentSession.setNotifyNumberOfTimes(paymentSession.getNotifyNumberOfTimes() + 1);
		paymentSession.setNotifyTime(new Timestamp(System.currentTimeMillis()));
		if (StringUtils.isNotEmpty(post) && post.toUpperCase().contains("SUCCESS")) {
			paymentSession.setNotifyResult(true);
		}
	}

}