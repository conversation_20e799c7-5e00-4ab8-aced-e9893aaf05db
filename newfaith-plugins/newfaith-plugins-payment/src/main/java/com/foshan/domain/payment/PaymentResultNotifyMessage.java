package com.foshan.domain.payment;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class PaymentResultNotifyMessage implements Serializable {

	/**
	 * 支付结果通知消息
	 */

	private static final long serialVersionUID = -1562513126911360660L;
	private String paymentSessionSn;
	private Map<String, Object> resulstMap;
	private Date enterTime;

	public PaymentResultNotifyMessage(String paymentSessionSn, Map<String, Object> resulstMap, Date enterTime) {
		super();
		this.paymentSessionSn = paymentSessionSn;
		this.resulstMap = resulstMap;
		this.enterTime = enterTime;
	}

	@Override
	public String toString() {
		return "PaymentResultNotifyMessage [paymentSessionSn=" + paymentSessionSn + ", resulstMap=" + resulstMap
				+ ", enterTime=" + enterTime + "]";
	}

}
