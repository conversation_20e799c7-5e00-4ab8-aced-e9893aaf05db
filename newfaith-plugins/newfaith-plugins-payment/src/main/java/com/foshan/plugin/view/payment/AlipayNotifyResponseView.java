package com.foshan.plugin.view.payment;

import java.io.PrintWriter;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.servlet.View;

/**
 * 
 * @ClassName: AlipayNotifyResponseView
 * @Description: TODO(支付宝支付结果通知接口返回报文)
 * <AUTHOR>
 * @date 2019年3月21日 上午11:01:47
 *
 */
public class AlipayNotifyResponseView implements View {

	@Override
	public String getContentType() {
		// TODO Auto-generated method stub
		return "text/html;charset=utf-8";
	}

	@Override
	public void render(Map<String, ?> model, HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		PrintWriter out = response.getWriter();
		out.println(model.get("message").toString());
		out.flush();
		out.close();
	}
}
