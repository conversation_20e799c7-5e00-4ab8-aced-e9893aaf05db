package com.foshan.form.sensitivewords.request;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="类型(CultureCloudSensitiveWordsReq)")
public  class SensitiveWordsReq extends BasePageRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = 8556251343082394497L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer sensitiveWordsId;
	@ApiModelProperty(value = "敏感词")
	private String sensitiveWord;
	@ApiModelProperty(value = "使用状态：0--未使用 1--使用",example="1")
	private Integer useState;

}
