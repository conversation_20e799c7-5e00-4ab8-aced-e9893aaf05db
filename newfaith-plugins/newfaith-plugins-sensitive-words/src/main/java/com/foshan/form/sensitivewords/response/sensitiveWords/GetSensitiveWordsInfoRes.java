package com.foshan.form.sensitivewords.response.sensitiveWords;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import com.foshan.form.sensitivewords.SensitiveWordsForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取敏感字详情(GetSensitiveWordsInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetSensitiveWordsInfoRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 1058062707807817606L;
	@ApiModelProperty(value = "敏感字对象")
	private SensitiveWordsForm sensitiveWordsForm ;

}
