 var totalFileLength, totalUploaded, fileCount, filesUploaded;

    function debug(s) {
        var debug = document.getElementById('debug');
        if (debug) {
            debug.innerHTML = debug.innerHTML + '<br/>' + s;
        }
    }

    function onUploadComplete(e) {
        totalUploaded += document.getElementById('files').
                files[filesUploaded].size;
        filesUploaded++;
        debug('完成 ' + filesUploaded + " of " + fileCount);
        debug('总共上传: ' + Math.round(totalUploaded/1024/1024)+"Mb");        
        if (filesUploaded < fileCount) {
            uploadNext();
        } else {
            var bar = document.getElementById('bar');
            bar.style.width = '100%';
            bar.innerHTML = '100% 完成';
            alert('完成上传！');
        }
    }
    
    function onFileSelect(e) {
        var files = e.target.files; // FileList object
        var output = [];
        fileCount = files.length;
        totalFileLength = 0;
        for (var i=0; i<fileCount; i++) {
            var file = files[i];
            output.push(file.name, ' (',
                  Math.round(file.size/1024/1024), 'MB, ',
                  file.lastModifiedDate.toLocaleDateString(), ')'
            );
            output.push('<br/>');
            debug('增加： ' + Math.round(file.size/1024/1024)+"MB");
            totalFileLength += file.size;
        }
        document.getElementById('selectedFiles').innerHTML = 
            output.join('');
        debug('文件大小共计:' + Math.round(totalFileLength/1024/1024)+"MB");
    }

    function onUploadProgress(e) {
        if (e.lengthComputable) {
            var percentComplete = parseInt(
                    (e.loaded + totalUploaded) * 100 
                    / totalFileLength);
            var bar = document.getElementById('bar');
            bar.style.width = percentComplete + '%';
            bar.innerHTML = percentComplete + ' % 完成';
        } else {
            debug('计算失败！！！');
        }
    }

    function onUploadFailed(e) {
        alert("文件上传失败！");
    }
    
    function uploadNext() {
        var xhr = new XMLHttpRequest();
        var fd = new FormData();
        var file = document.getElementById('files').
                files[filesUploaded];
        var serviceId = document.getElementById('sid').value;
        var folderId = document.getElementById('fid').value;
        var groupId = document.getElementById('gid').value;
        var categoryId = document.getElementById('cid').value;
        fd.append("multipartFile", file);
        fd.append("serviceId",serviceId);
        fd.append("folderId",folderId);
        fd.append("groupId",groupId);
        fd.append("categoryId",categoryId);
        
        xhr.upload.addEventListener(
                "progress", onUploadProgress, false);
        xhr.addEventListener("load", onUploadComplete, false);
        xhr.addEventListener("error", onUploadFailed, false);
        xhr.open("POST", "vote/uploadFile");
        debug('正在上传： ' + file.name);
        xhr.send(fd);
    }

    function startUpload() {
        totalUploaded = filesUploaded = 0;
        uploadNext();
    }
    
    
    window.onload = function() {
        document.getElementById('files').addEventListener(
                'change', onFileSelect, false);
        document.getElementById('uploadButton').
                addEventListener('click', startUpload, false);
    }
    
   