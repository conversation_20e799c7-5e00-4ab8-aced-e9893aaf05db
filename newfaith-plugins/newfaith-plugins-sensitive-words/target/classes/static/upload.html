<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>投票问卷系统文件上传</title>
<script type="text/javascript" src="js/upload.js"></script>
</head>
<body>
	<h1>文件上传</h1>
	<div id='progressBar' style='height: 20px; border: 2px solid green'>
		<div id='bar' style='height: 100%; background: #33dd33; width: 0%'>
		</div>
	</div>
	<form>

		<label style="background: #33bb00; font-size: 16px; color: red">第一步：请先填写要上传照片的上传信息(serviceId、forlderId、groupId)或题库文件的categoryId；
			==></label><br/><label style="background: #33bb00;">上传照片：投票业务ID：(ServiceId):</label>
			<select id="sid" name="serviceId" style="border: 2px solid green;" >
<option value="2">魅力三水</option>
<option value="9">测试投票</option>
</select><label style="background: #33bb00;">栏目ID：(FolderId):</label>&nbsp;
		<select id="fid" name="folderId" style="border: 2px solid green;" >
<option value="144">淼城有着数</option>	
<option value="190">乡村振兴</option>	
<option value="192">党政机关类</option>	
<option value="193">社会廉洁类</option>		
<option value="26">投票栏目1</option>
<option value="27">投票栏目2</option>
</select><label style="background: #33bb00;">分组ID：(GroupId):</label>&nbsp;
		<select id="gid" name="groupId" style="border: 2px solid green;" >
<option value="51">三水美食</option>
<option value="52">开心游淼</option>
<option value="53">培训教育</option>
<option value="54">楼市优惠</option>
<option value="55">健美养生</option>
<option value="56">金融理财</option>
<option value="67">视频类乡村振兴</option>
<option value="68">视频类党政机关</option>
<option value="69">视频类社会廉洁</option>
<option value="70">视频类微视频</option>
<option value="71">图片类乡村振兴</option>
<option value="72">图片类党政机关</option>
<option value="73">图片类社会廉洁</option>
<option value="29">测试栏目1-测试组1</option>
<option value="30">测试栏目1-测试组2</option>
<option value="31">测试栏目2-测试组1</option>
<option value="32">测试栏目2-测试组2</option>
</select>
<br/>
<label style="background: #33bb00;">上传题库：分类(CategoryId:)</label><select id="cid" name="categoryId" style="border: 2px solid green;" >
<option value="1">党员教育</option>
<option value="2">宪法考学</option>
<option value="4">南海民政考题问卷</option>
<option value="5">南海民政调查问卷</option>
<option value="6">司法局调查问卷</option>
</select>
			<br /> <label
					style="background: #33bb00; font-size: 16px; color: red">第二步：选择要上传的照片或题库文件，支持多选
			==></label>&nbsp;&nbsp;&nbsp;&nbsp;<input type="file" id="files" multiple
					style="border: 2px solid green;" /> <br />
		<output id="selectedFiles"></output>
		<label style="background: #33bb00; font-size: 16px; color: red">第三步：点击上传按钮
			==></label>&nbsp;&nbsp;&nbsp;&nbsp;<input style="font-size: 16px; color: red"
					id="uploadButton" type="button" value="开始上传" />
			</form>
	<div id='debug'
		style='height: 800px; border: 2px solid green; overflow: auto'>
	</div>
</body>
</html>