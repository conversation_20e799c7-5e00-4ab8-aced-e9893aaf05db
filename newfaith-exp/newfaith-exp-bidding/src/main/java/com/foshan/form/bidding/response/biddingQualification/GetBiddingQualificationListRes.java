package com.foshan.form.bidding.response.biddingQualification;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.bidding.BiddingQualificationForm;
import com.foshan.form.bidding.BiddingRuleForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="公司资质列表响应(GetBiddingQualificationListRes)")
@JsonInclude(Include.NON_NULL)
public class GetBiddingQualificationListRes extends BasePageResponse {/**
	 * 
	 */
	private static final long serialVersionUID = -5434549318584940808L;
	@ApiModelProperty(value = "公司资质列表")
	private List<BiddingQualificationForm> qualificationList = new ArrayList<BiddingQualificationForm>();

}
