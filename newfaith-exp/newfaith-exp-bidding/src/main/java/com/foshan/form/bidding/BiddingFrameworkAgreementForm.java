package com.foshan.form.bidding;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="框架协议对象(BiddingFrameworkAgreementForm)")
@JsonInclude(Include.NON_NULL)
public class BiddingFrameworkAgreementForm implements IForm {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -7062184776639571038L;
	
	@ApiModelProperty(value = "id")
	private Integer id;
	@ApiModelProperty(value = "名称")
	private String name;

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
