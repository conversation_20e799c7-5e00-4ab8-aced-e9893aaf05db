package com.foshan.form.bidding;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="框架协议对象(BiddingPartnerQualificationForm)")
@JsonInclude(Include.NON_NULL)
public class BiddingPartnerQualificationForm implements IForm {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -7062184716468271038L;
	
	@ApiModelProperty(value = "id")
	private Integer partnerQualificationId;
	@ApiModelProperty(value = "证书名称")
	private String partnerQualificationName;
	@ApiModelProperty(value = "有效期")
	private Date expirationDate;

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
