package com.foshan.form.bidding.response.biddingPartner;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="合作伙伴删除响应(DeleteBiddingPartnerRes)")
@JsonInclude(Include.NON_NULL)
public class DeleteBiddingPartnerRes extends BasePageResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -5749486653693640580L;
	@ApiModelProperty(value = "合作伙伴id")
	private Integer partnerId;

}
