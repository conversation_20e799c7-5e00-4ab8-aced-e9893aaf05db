package com.foshan.form.bidding.response.biddingUser;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.PlatformUserForm;
import com.foshan.form.bidding.BiddingUserForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取用户返回列表对象(BiddingUserRes)")
@JsonInclude(Include.NON_NULL)
public class GetBiddingUserListRes extends BasePageResponse {
	/**
	 * 用户列表响应对象
	 */
	private static final long serialVersionUID = -684743009880185100L;
	@ApiModelProperty(value = "用户对象列表")
	private List<BiddingUserForm> userList = new ArrayList<BiddingUserForm>(); 
	
}
