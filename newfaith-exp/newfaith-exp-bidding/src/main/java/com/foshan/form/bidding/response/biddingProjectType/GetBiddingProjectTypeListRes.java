package com.foshan.form.bidding.response.biddingProjectType;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.bidding.BiddingProjectTypeForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="项目类别列表响应(GetBiddingProjectTypeListRes)")
@JsonInclude(Include.NON_NULL)
public class GetBiddingProjectTypeListRes extends BasePageResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -536017759345018612L;
	
	@ApiModelProperty(value = "项目类别列表")
	private List<BiddingProjectTypeForm> projectTypeList = new ArrayList<BiddingProjectTypeForm>();

}
