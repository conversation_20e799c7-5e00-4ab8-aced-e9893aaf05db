package com.foshan.form.bidding.response.biddingPredownload;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.bidding.BiddingPredownloadForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="预下载列表响应(GetBiddingPredownloadListRes)")
@JsonInclude(Include.NON_NULL)
public class GetBiddingPredownloadListRes extends BasePageResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 2146788654674201217L;
	@ApiModelProperty(value = "项目和项目文件列表")
	private List<BiddingPredownloadForm> predownloadList = new ArrayList<BiddingPredownloadForm>();

}
