package com.foshan.form.bidding.request;

import com.foshan.form.request.PlatformUserReq;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="请求参数(CommunityUserReq)")
public class BiddingUserReq extends PlatformUserReq {
	/**
	 * 
	 */
	private static final long serialVersionUID = -237287638545967600L;
	@ApiModelProperty(value = "公司")
	private String company;
}
