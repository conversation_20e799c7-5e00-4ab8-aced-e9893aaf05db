package com.foshan.form.bidding.request;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "公司资质请求对象(BiddingQualificationReq)")
public class BiddingQualificationReq extends BasePageRequest {/**
	 * 
	 */
	private static final long serialVersionUID = -871313353927190062L;
	@ApiModelProperty(value = "公司资质文件id")
	private Integer assetId;
	@ApiModelProperty(value = "公司资质文件名称")
	private String assetName;

}
