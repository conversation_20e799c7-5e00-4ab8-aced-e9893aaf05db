package com.foshan.form.bidding.response.biddingPartner;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.bidding.BiddingPartnerAttachmentForm;
import com.foshan.form.bidding.BiddingPartnerContractForm;
import com.foshan.form.bidding.BiddingPartnerPerformanceForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="合作伙伴详情响应(GetBiddingPartnerInfoRes)")
@JsonInclude(Include.NON_NULL)
public class GetBiddingPartnerInfoRes extends BasePageResponse {/**
	 * 
	 */
	private static final long serialVersionUID = -861993419075071705L;
	@ApiModelProperty(value = "合作伙伴id")
	private Integer partnerId;
//	@ApiModelProperty(value = "名称")
//	private String name;
//	@ApiModelProperty(value = "行业类别")
//	private String category;
//	@ApiModelProperty(value = "联系方式")
//	private String phone;
	@ApiModelProperty(value = "公司")
	private String company;
//	@ApiModelProperty(value = "邮箱")
//	private String email;
//	@ApiModelProperty(value = "截止时间")
//	private Date endDate;
	@ApiModelProperty(value = "公司主营内容")
	private String companyIntro;
	@ApiModelProperty(value = "注册资金（万元）")
	private Integer registeredCapital;
	@ApiModelProperty(value = "实缴资金（万元）")
	private Integer realCapital;
	@ApiModelProperty(value = "公司所在地")
	private String address;
	@ApiModelProperty(value = "人员规模")
	private String employeesNum;
	@ApiModelProperty(value = "ICT")
	private String ict;
	@ApiModelProperty(value = "平安城市")
	private String safeCity;
	@ApiModelProperty(value = "通信工程")
	private String communicationEngineering;
	@ApiModelProperty(value = "IDC")
	private String idc;
	@ApiModelProperty(value = "应急广播")
	private String emergencyBroadcast;
//	@ApiModelProperty(value = "次级类别")
//	private String subCategory;
	@ApiModelProperty(value = "业绩详情")
	private List<BiddingPartnerPerformanceForm> partnerPerformanceList = new ArrayList<>();
	@ApiModelProperty(value = "附件")
	private List<BiddingPartnerAttachmentForm> partnerAttachmentList = new ArrayList<>();
	@ApiModelProperty(value = "合同")
	private List<BiddingPartnerContractForm> partnerContractList = new ArrayList<>();

}
