package com.foshan.form.bidding.request;

import com.foshan.form.request.BaseRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="导入请求参数(ImportExcelReq)")
public class ImportExcelReq extends BaseRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = -615289884882894599L;
	@ApiModelProperty(value = "文件类型", example = "1")
	private Integer docType;
	@ApiModelProperty(value = "自动填充,0：否,1：是;", example = "1")
	private Integer autoCreateUnit;

	
}
