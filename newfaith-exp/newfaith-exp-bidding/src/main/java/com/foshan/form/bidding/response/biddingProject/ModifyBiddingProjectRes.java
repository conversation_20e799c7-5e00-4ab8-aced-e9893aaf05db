package com.foshan.form.bidding.response.biddingProject;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "修改项目返回对象(ModifyBiddingProjectRes)")
@JsonInclude(Include.NON_NULL)
public class ModifyBiddingProjectRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 8585124263797411286L;
	@ApiModelProperty(value = "项目id")
	private String projectId;
	@ApiModelProperty(value = "项目名称")
	private String projectName;
	@ApiModelProperty(value = "项目建立日期")
	private String projectCreationDate;
	@ApiModelProperty(value = "项目金额")
	private String projectCash;
	@ApiModelProperty(value = "业主单位名称")
	private String ownerName;
	@ApiModelProperty(value = "业主单位联系人")
	private String ownerContact;
	@ApiModelProperty(value = "业主单位联系方式")
	private String ownerContactInfo;
	@ApiModelProperty(value = "合作单位名称")
	private String partnerName;
	@ApiModelProperty(value = "合作单位联系人")
	private String partnerContact;
	@ApiModelProperty(value = "合作单位联系方式")
	private String partnerContactInfo;
	@ApiModelProperty(value = "项目类别id")
	private String projectTypeId;

}
