package com.foshan.form.bidding.response.biddingPartnerQualification;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.bidding.BiddingPartnerQualificationForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="合作伙伴证书列表响应(GetBiddingPartnerQualificationListRes)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetBiddingPartnerQualificationListRes extends BasePageResponse {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "合作伙伴证书列表")
    private List<BiddingPartnerQualificationForm> partnerQualificationList = new ArrayList<BiddingPartnerQualificationForm>();
}
