package com.foshan.form.bidding;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import com.foshan.form.UploadedFile;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="项目文件对象(BiddingProjectDocumentForm)")
@JsonInclude(Include.NON_NULL)
public class BiddingProjectDocumentForm extends UploadedFile implements IForm{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 3976881508715451008L;
	@ApiModelProperty(value = "项目文件ID",example="1")
	private Integer docId;
	@ApiModelProperty(value = "项目文件名称")
	private String docName;
	@ApiModelProperty(value = "项目文件下载量")
	private Integer download;

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
