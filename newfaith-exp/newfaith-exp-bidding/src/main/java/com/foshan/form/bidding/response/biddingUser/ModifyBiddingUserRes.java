package com.foshan.form.bidding.response.biddingUser;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "修改用户返回对象(ModifyBiddingUserRes)")
@JsonInclude(Include.NON_NULL)
public class ModifyBiddingUserRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -157126220129430300L;
	@ApiModelProperty(value = "用户Id", example = "1")
	private Integer userId;
	@ApiModelProperty(value = "用户名称")
	private String userName;
	@ApiModelProperty(value = "用户密码")
	private String userPassword;
	@ApiModelProperty(value = "公司")
	private String company;
	@ApiModelProperty(value = "姓名")
	private String name;
	@ApiModelProperty(value = "联系方式")
	private String phone;
	@ApiModelProperty(value = "角色")
	private String role;
}
