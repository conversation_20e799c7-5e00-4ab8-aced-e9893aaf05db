package com.foshan.form.bidding.request;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "项目文件请求对象(BiddingProjectDocumentReq)")
public class BiddingAssetReq extends BasePageRequest{

	/**
	 * 项目文件请求
	 */
	private static final long serialVersionUID = 664972073188982900L;
	@ApiModelProperty(value = "项目文件id")
	private Integer assetId;
	@ApiModelProperty(value = "项目文件名称")
	private String assetName;
	@ApiModelProperty(value = "项目文件")
	private MultipartFile[] files;
	@ApiModelProperty(value = "项目id")
	private Integer projectId;
	@ApiModelProperty(value = "项目创建日期")
	private String projectCreationDate;
	@ApiModelProperty(value = "项目文件存放路径")
	private String realUrl;
	@ApiModelProperty(value = "项目类别id")
	private Integer projectTypeId;
	@ApiModelProperty(value = "文件种类 0-项目文件 1-合作伙伴文件 2-规章制度文件")
	private Integer docType;
	@ApiModelProperty(value = "用户id")
	private Integer userId;
	@ApiModelProperty(value = "assetId列表")
	private String assetIds;
}
