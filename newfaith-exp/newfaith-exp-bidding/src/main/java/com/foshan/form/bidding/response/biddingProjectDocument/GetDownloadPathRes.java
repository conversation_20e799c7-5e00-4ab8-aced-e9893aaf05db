package com.foshan.form.bidding.response.biddingProjectDocument;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.bidding.BiddingAssetForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="下载文件信息响应(GetBiddingProjectDocumentListRes)")
@JsonInclude(Include.NON_NULL)
public class GetDownloadPathRes extends BasePageResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -6503269181763471771L;
	@ApiModelProperty(value = "下载信息列表")
	private List<BiddingAssetForm> assetList = new ArrayList<BiddingAssetForm>();


}
