package com.foshan.form.bidding;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="项目类别对象(BiddingProjectForm)")
@JsonInclude(Include.NON_NULL)
public class BiddingProjectForm implements IForm{
	/**
	 * 
	 */
	private static final long serialVersionUID = -887835594218696132L;
	@ApiModelProperty(value = "项目编号id")
	private Integer id;
	@ApiModelProperty(value = "项目名称")
	private String projectName;
	@ApiModelProperty(value = "项目建立日期")
	private String projectCreationDate;
	@ApiModelProperty(value = "项目金额")
	private String projectCash;
	@ApiModelProperty(value = "业主单位名称")
	private String ownerName;
	@ApiModelProperty(value = "业主单位联系人")
	private String ownerContact;
	@ApiModelProperty(value = "业主单位联系方式")
	private String ownerContactInfo;
	@ApiModelProperty(value = "合作单位名称")
	private String partnerName;
	@ApiModelProperty(value = "合作单位联系人")
	private String partnerContact;
	@ApiModelProperty(value = "合作单位联系方式")
	private String partnerContactInfo;
	@ApiModelProperty(value = "项目类别")
	private BiddingProjectTypeForm projectType = new BiddingProjectTypeForm();
	@ApiModelProperty(value = "项目类别名称")
	private String projectTypeName;
	@ApiModelProperty(value = "用户id")
	private Integer userId;
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
