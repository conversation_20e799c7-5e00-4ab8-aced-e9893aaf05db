package com.foshan.service.bidding;

import com.foshan.form.bidding.request.BiddingPartnerQualificationReq;
import com.foshan.form.response.IResponse;

public interface IBiddingPartnerQualificationService {
	public IResponse getBiddingPartnerQualificationList(BiddingPartnerQualificationReq req);
	public IResponse getBiddingPartnerQualificationInfo(BiddingPartnerQualificationReq req);
	public IResponse addBiddingPartnerQualification(BiddingPartnerQualificationReq req);
	public IResponse modifyBiddingPartnerQualification(BiddingPartnerQualificationReq req);
	public IResponse deleteBiddingPartnerQualification(BiddingPartnerQualificationReq req);

}
