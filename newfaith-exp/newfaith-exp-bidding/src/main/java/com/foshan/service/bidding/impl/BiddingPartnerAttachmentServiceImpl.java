package com.foshan.service.bidding.impl;

import com.foshan.dao.bidding.impl.BiddingPartnerAttachmentImpl;
import com.foshan.dao.generic.Page;
import com.foshan.entity.AssetSpecEntity;
import com.foshan.entity.bidding.BiddingPartnerAttachmentEntity;
import com.foshan.entity.bidding.BiddingPartnerAttachmentEntity;
import com.foshan.entity.bidding.BiddingPartnerEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.UploadForm;
import com.foshan.form.bidding.BiddingPartnerAttachmentForm;
import com.foshan.form.bidding.request.BiddingPartnerAttachmentReq;
import com.foshan.form.bidding.response.biddingPartnerAttachment.DeleteBiddingPartnerAttachmentRes;
import com.foshan.form.bidding.response.biddingPartnerAttachment.DeleteBiddingPartnerAttachmentRes;
import com.foshan.form.bidding.response.biddingPartnerAttachment.GetBiddingPartnerAttachmentListRes;
import com.foshan.form.request.UploadReq;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.upload.UploadRes;
import com.foshan.service.bidding.IBiddingPartnerAttachmentService;
import com.foshan.util.CodeUtil;
import com.foshan.util.DigestUtil;
import com.foshan.util.PicUtil;
import com.foshan.util.bidding.BiddingDownloadInfoObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.FileSystemUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.activation.MimetypesFileTypeMap;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;

@Transactional
@Service("biddingPartnerAttachmentService")
public class BiddingPartnerAttachmentServiceImpl extends GenericBiddingService implements IBiddingPartnerAttachmentService {
    private final static Logger logger = LoggerFactory.getLogger(BiddingPartnerAttachmentServiceImpl.class);

    public BiddingPartnerAttachmentServiceImpl(BiddingPartnerAttachmentImpl biddingPartnerAttachmentDao) {
        super();
        this.biddingPartnerAttachmentDao = biddingPartnerAttachmentDao;
    }

    /*
     *
     * realUrl : 文件真实地址 virtualUrl : 虚拟地址 saveState : true需要存到t_asset表 assetType :
     * 当saveState＝true时要填写，媒类型 0--图片 1--声音 2--视频 fileSuffix : 文件后缀，如多个以逗号隔开
     */

    @SuppressWarnings("rawtypes")
    @Override
    public IResponse uploadFile(HttpServletRequest request, MultipartFile[] file, UploadReq req, Integer partnerId,
                                Integer isSynchronize) {
        UploadRes res = new UploadRes();
        req = getAssetSpecForm(req);
        if(null == file ) {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "上传文件为空！");
        }
        for (MultipartFile tempFile : file) {
            try {
                UploadForm uploadForm = new UploadForm();
                uploadForm=disposeFile(tempFile,req,uploadForm,partnerId,isSynchronize);
                res.getUploadFormList().add(uploadForm);
            } catch (IOException e) {
                res.setRet("0003");
                res.setRetInfo("服务器异常！！！");
                e.printStackTrace();
                return res;
            }

        }
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        return res;
    }

    @SuppressWarnings("unused")
    public UploadForm disposeFile(MultipartFile file,UploadReq req,UploadForm uploadForm,Integer partnerId,
                                  Integer isSynchronize) throws IOException{
        String dir = "";
        boolean isPic = false;
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        String today = sdf.format(date);
        String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".")+1).toLowerCase();
        if ((suffix.equals("jpg") || suffix.equals("png") || suffix.equals("jpeg") || suffix.equals("bmp") || suffix.equals("gif"))){
            dir = contextInfo.getAssetFilePath()+ File.separator +"images"+ File.separator + today;
            isPic=true;
        }else if(suffix.equals("doc") || suffix.equals("docx")){
            dir = contextInfo.getAssetFilePath()+ File.separator +"doc"+ File.separator + today;
        }else if(suffix.equals("xls") || suffix.equals("xlsx")){
            dir = contextInfo.getAssetFilePath()+ File.separator +"xls"+ File.separator + today;
        }else if(suffix.equals("pdf")){
            dir = contextInfo.getAssetFilePath()+ File.separator +"pdf"+ File.separator + today;
        }else if(suffix.equals("rar") || suffix.equals("zip") || suffix.equals("7z")){
            dir = contextInfo.getAssetFilePath()+ File.separator +"zip"+ File.separator + today;
        }else if(suffix.equals("ppt") || suffix.equals("pptx")){
            dir = contextInfo.getAssetFilePath()+ File.separator +"ppt"+ File.separator + today;
        }else if(suffix.equals("wps") || suffix.equals("wpt") || suffix.equals("wdb")){
            dir = contextInfo.getAssetFilePath()+ File.separator +"wps"+ File.separator + today;
        }else if(suffix.equals("txt")){
            dir = contextInfo.getAssetFilePath()+ File.separator +"txt"+ File.separator + today;
        }

        createMultilayerFile(dir);
        if (file != null) {
            String transName = DigestUtil.getMD5Str(System.currentTimeMillis()+file.getOriginalFilename()+ CodeUtil.getId(10000)) ;
            String fileName = transName+ file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));

            //避免有问题的图片造成机顶盒重启问题
            //modifyImageFormat(fileUpload,fileUpload,suffix);
            BiddingPartnerAttachmentEntity asset = new BiddingPartnerAttachmentEntity();

            if(isPic){
                //req = getAssetSpecForm(req);
                String newFileName="";
                String formatName="";
                //0:原图格式;1:转成JPG;2:转成PNG;3:转成BMP;4:转成JPEG;5:转成GIF
                if(null!=req.getVariableFormat()){
                    if(req.getVariableFormat()== EntityContext.PIC_JPG_FORMAT){
                        newFileName=fileName.split("[.]")[0]+".jpg";
                        formatName="jpg";
                    }else if(req.getVariableFormat()==EntityContext.PIC_PNG_FORMAT){
                        newFileName=fileName.split("[.]")[0]+".png";
                        formatName="png";
                    }else if(req.getVariableFormat()==EntityContext.PIC_BMP_FORMAT){
                        newFileName=fileName.split("[.]")[0]+".bmp";
                        formatName="bmp";
                    }else if(req.getVariableFormat()==EntityContext.PIC_JPEG_FORMAT){
                        newFileName=fileName.split("[.]")[0]+".jpeg";
                        formatName="jpeg";
                    }else if(req.getVariableFormat()==EntityContext.PIC_GIF_FORMAT){
                        newFileName=fileName.split("[.]")[0]+".gif";
                        formatName="gif";
                    }else{
                        newFileName=fileName;
                        formatName= suffix;
                    }
                }else {
                    newFileName=fileName;
                    formatName= suffix;
                }
                String newFile= dir + File.separator+newFileName;
                if(isOSLinux()){
                    Runtime.getRuntime().exec("chmod 644 " + newFile);
                }
                String url = "images"+ "/" + today + "/"  +newFileName;
                uploadForm.setFilePath(url);
                if(req.isOnShelves()){
                    asset.setAssetState(EntityContext.ASSET_STATE_UP);
                }else{
                    asset.setAssetState(EntityContext.ASSET_STATE_PENDING);
                }

                BufferedImage bufferedImage = ImageIO.read(file.getInputStream());
                Integer width = bufferedImage.getWidth();
                Integer height = bufferedImage.getHeight();
                if(null!=req.getImageHeight() && null!=req.getImageWidth()){
                    PicUtil.resize(file.getInputStream(),  newFile, req.getImageWidth(), req.getImageHeight(),req.isKeepAspectRatio());
                }else{
                    String[] imageWidthAndHeight =  contextInfo.getImageWidthAndHeight().split("[*]");
                    PicUtil.resize(file.getInputStream(),  newFile, Integer.valueOf(imageWidthAndHeight[0]),
                            Integer.valueOf(imageWidthAndHeight[1]),req.isKeepAspectRatio());
                }

                createMultilayerFile(dir+ File.separator+"small");
                String smallImagePath = dir + File.separator+"small"+File.separator+newFileName;
                createMultilayerFile(dir+ File.separator+"middle");
                String middleImagePath = dir + File.separator+"middle"+File.separator+newFileName;
                if(null!=req.getSmallImageWidth() && null!=req.getSmallImageHeight()){
                    PicUtil.resize(file.getInputStream(),  smallImagePath, req.getSmallImageWidth(), req.getSmallImageHeight(),req.isKeepAspectRatio());
                }else{
                    String[] smallImageWidthAndHeight =  contextInfo.getSmallImageWidthAndHeight().split("[*]");
                    PicUtil.resize(file.getInputStream(),  smallImagePath, Integer.valueOf(smallImageWidthAndHeight[0]),
                            Integer.valueOf(smallImageWidthAndHeight[1]),req.isKeepAspectRatio());
                }

                if(null!=req.getMiddleImageWidth() && null!=req.getMiddleImageHeight()){
                    PicUtil.resize(file.getInputStream(), middleImagePath, req.getMiddleImageWidth(), req.getMiddleImageHeight(),req.isKeepAspectRatio());
                }else{
                    String[] middleImageWidthAndHeight =  contextInfo.getSmallImageWidthAndHeight().split("[*]");
                    PicUtil.resize(file.getInputStream(),  middleImagePath, Integer.valueOf(middleImageWidthAndHeight[0]),
                            Integer.valueOf(middleImageWidthAndHeight[1]),req.isKeepAspectRatio());
                }

                if(null!=req.getFilesize()
                        && req.getFilesize()>0
                        && file.getSize()>req.getFilesize()){
                    //设置精度，递归压缩的比率，建议小于0.9,如果为空默认0.9
                    if(null!=req.getAccuracy() && req.getAccuracy().compareTo(new BigDecimal(0))==1){
                        PicUtil.commpressPicForSize(newFile,newFile,req.getFilesize(), req.getAccuracy().doubleValue());
                    }else{
                        PicUtil.commpressPicForSize(newFile,newFile, req.getFilesize(),0.9);
                    }
                }
                asset.setImageFile(url);
                asset.setSmallImageFile("images"+ "/" + today + "/"+"small"+ "/" +newFileName);
                asset.setMiddleImageFile("images"+ "/" + today + "/"+"middle"+ "/" +newFileName);
                asset.setAssetType(EntityContext.ASSET_TYPE_IMAGE);
            }else{
                String fileUpload = dir + File.separator + fileName;
                File tempFile = new File(fileUpload);
                file.transferTo(tempFile);
                if(isOSLinux()){
                    Runtime.getRuntime().exec("chmod 644 " + fileUpload);
                }
                String url = null;
                if(suffix.equals("doc") || suffix.equals("docx")){
                    url = "doc"+ "/" + today + "/" + fileName;
                }else if(suffix.equals("xls") || suffix.equals("xlsx")){
                    url = "xls"+ "/" + today + "/" + fileName;
                }else if(suffix.equals("pdf")){
                    url = "pdf"+ "/" + today + "/" + fileName;
                }else if(suffix.equals("rar") || suffix.equals("zip") || suffix.equals("7z")) {
                    url = "zip"+ "/" + today + "/" + fileName;
                }else if(suffix.equals("ppt") || suffix.equals("pptx")) {
                    url = "ppt"+ "/" + today + "/" + fileName;
                }else if(suffix.equals("wps") || suffix.equals("wpt") || suffix.equals("wdb")) {
                    url = "wps"+ "/" + today + "/" + fileName;
                }else if(suffix.equals("txt")) {
                    url = "txt"+ "/" + today + "/" + fileName;
                }
                uploadForm.setFilePath(url);
                asset.setAssetState(EntityContext.ASSET_STATE_PENDING);
                asset.setImageFile(url);
                asset.setSmallImageFile(url);
                asset.setAssetType(EntityContext.ASSET_TYPE_DOC);
            }

            if(null!=req.getParentAssetId()){
                BiddingPartnerAttachmentEntity bpae = biddingPartnerAttachmentDao.get(req.getParentAssetId());
                if(null!=bpae){
                    asset.setParentAsset(bpae);
                    if(isPic){
                        asset.setIsCover(EntityContext.IS_NOT_COVER);
                    }
                }else if(isPic){
                    asset.setIsCover(EntityContext.IS_COVER);
                }
            }else if(isPic){
                asset.setIsCover(EntityContext.IS_COVER);
            }

            asset.setAssetCode(transName);
            asset.setAssetName(file.getOriginalFilename());
            asset.setPackageFlag(EntityContext.ASSET_PACKAGE_FLAG_INVALID);
            asset.setDownload(0);
            if(partnerId != null) {
                BiddingPartnerEntity pne = biddingPartnerDao
                        .getUniqueByHql("from BiddingPartnerEntity where id = " + partnerId);
                asset.setPartner(pne);
            }
            biddingPartnerAttachmentDao.saveOrUpdate(asset);
            uploadForm.setFileId(asset.getId());
            uploadForm.setFileName(asset.getAssetName());

            if(isSynchronize == 1){
                //同步到dify
                try{
                    Map<String, String> fileMap = new HashMap<>();
                    fileMap.put("file", dir + '/' + fileName);
                    Map<String, String> textMap = new HashMap<>();
                    Map<String, Object> params = new HashMap<>();
                    Map<String, String> process_rule = new HashMap<>();
                    process_rule.put("rules", "");
                    process_rule.put("mode", "automatic");
                    params.put("indexing_technique", "high_quality");
                    params.put("process_rule", process_rule);
                    textMap.put("data", mapper.writeValueAsString(params));

                    String url = "http://10.75.255.22/v1/datasets/51a12f78-d9b2-477d-a4d2-826b93a72f3e/document/create_by_file";
                    logger.info("--------------------------------------调用dify知识库上传文档接口地址：" + url
                            + "-------------------------");
                    String result = formUpload(url,textMap,fileMap,null,file.getOriginalFilename());
                    logger.info("--------------------------------------调用dify知识库上传文档接口返回：" + result
                            + "----------------------------------------");
                } catch (Exception e) {
                    e.printStackTrace();
                }
                //
            }
        }
        return uploadForm;
    }

    public GetBiddingPartnerAttachmentListRes getBiddingPartnerAttachmentList(BiddingPartnerAttachmentReq req) {
        GetBiddingPartnerAttachmentListRes res = new GetBiddingPartnerAttachmentListRes();
        List<BiddingPartnerAttachmentForm> rfList = new ArrayList<BiddingPartnerAttachmentForm>();
        Page<BiddingPartnerAttachmentEntity> page = new Page<>();
        page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
        page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 0) * page.getPageSize());
        page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
        StringBuilder hql = new StringBuilder("select distinct a from BiddingPartnerAttachmentEntity a inner join a.partner b where b.id=" + req.getPartnerId());
        if(null != req.getPartnerAttachmentName() && StringUtils.isNotEmpty(req.getPartnerAttachmentName())) {
            hql.append("and a.assetName like '%" + req.getPartnerAttachmentName() + "%'");
        }
        page = biddingPartnerAttachmentDao.queryPage(page, hql.toString());
        res.setTotalResult(page.getTotalCount());
        res.setPageSize(page.getPageSize());
        res.setCurrentPage(page.getCurrentPage());
        res.setTotal(page.getTotalPage());
        page.getResultList().forEach(o -> {
            BiddingPartnerAttachmentForm rf = new BiddingPartnerAttachmentForm();
            rf.setId(o.getId());
            rf.setName(o.getAssetName());
            rfList.add(rf);
        });

        res.setPartnerAttachmentList(rfList);
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        return res;

    }

    @Override
    public IResponse deleteFile(Integer partnerAttachmentId) {
        DeleteBiddingPartnerAttachmentRes res = new DeleteBiddingPartnerAttachmentRes();

        if (null != partnerAttachmentId) {
            BiddingPartnerAttachmentEntity pae = biddingPartnerAttachmentDao.getUniqueByHql("from BiddingPartnerAttachmentEntity where id = " + partnerAttachmentId);
            if(null != pae) {
                String path = contextInfo.getAssetFilePath() + File.separator + pae.getImageFile();
                File file = new File(path);
                if(file.exists()) {
                    FileSystemUtils.deleteRecursively(file);
                }else {
                    logger.info("文件路径错误");
                }
//				FileSystemUtils.deleteRecursively(new File(contextInfo.getAssetFilePath() + File.separator + ae.getImageFile()));
                if(null != pae.getMiddleImageFile()) {
                    FileSystemUtils.deleteRecursively(new File(contextInfo.getAssetFilePath() + File.separator + pae.getMiddleImageFile()));
                }
                if(null != pae.getSmallImageFile()) {
                    FileSystemUtils.deleteRecursively(new File(contextInfo.getAssetFilePath() + File.separator + pae.getSmallImageFile()));
                }
                biddingPartnerAttachmentDao.deleteById(partnerAttachmentId);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            }
            else if(null == pae){
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
                return res;
            }

        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;
    }

    @Override
    public IResponse batchDeleteFile(String partnerAttachmentIds) {
        DeleteBiddingPartnerAttachmentRes res = new DeleteBiddingPartnerAttachmentRes();
        if(StringUtils.isNotEmpty(partnerAttachmentIds)) {
            String[] partnerAttachmentIdList = partnerAttachmentIds.split(",");
            for(String partnerAttachmentId : partnerAttachmentIdList) {
                BiddingPartnerAttachmentEntity pae = biddingPartnerAttachmentDao.get(Integer.parseInt(partnerAttachmentId));
                if(null != pae) {
                    String path = contextInfo.getAssetFilePath() + File.separator + pae.getImageFile();
                    File file = new File(path);
                    if(file.exists()) {
                        FileSystemUtils.deleteRecursively(file);
                    }else {
                        logger.info("文件路径错误");
                    }
                    biddingPartnerAttachmentDao.deleteById(Integer.parseInt(partnerAttachmentId));
                    res.setRet(ResponseContext.RES_SUCCESS_CODE);
                    res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
                }else {
                    res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
                    res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
                }
            }
        }else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;
    }

    public List<BiddingDownloadInfoObject> getDownloadPath(Integer id){
        List<BiddingDownloadInfoObject> downloadInfoList = new ArrayList<>();
        //id为assetId，获取单个文件的下载路径
        if(null != id) {
            BiddingPartnerAttachmentEntity pae = biddingPartnerAttachmentDao.getUniqueBySql("select * from t_asset where id = " + id);
            pae.setDownload(pae.getDownload() + 1);
            biddingPartnerAttachmentDao.update(pae);
            BiddingDownloadInfoObject downloadInfo = new BiddingDownloadInfoObject();
            downloadInfo.setId(pae.getId());
            downloadInfo.setDocName(pae.getAssetName());
            downloadInfo.setDocPath(pae.getImageFile());
            downloadInfoList.add(downloadInfo);
        }
        return downloadInfoList;
    }

    public List<BiddingDownloadInfoObject> getBatchDownloadPath(String ids){
        List<BiddingDownloadInfoObject> downloadInfoList = new ArrayList<BiddingDownloadInfoObject>();
        //id为assetId，获取单个文件的下载路径
        if(StringUtils.isNotEmpty(ids)) {
            String[] partnerAttachmentIdList = ids.split(",");
            for(String partnerAttachmentId : partnerAttachmentIdList) {
                BiddingPartnerAttachmentEntity pae = biddingPartnerAttachmentDao.getUniqueBySql("select * from t_asset where id = " + partnerAttachmentId);
                pae.setDownload(pae.getDownload() + 1);
                biddingPartnerAttachmentDao.update(pae);
                BiddingDownloadInfoObject downloadInfo = new BiddingDownloadInfoObject();
                downloadInfo.setDocName(pae.getAssetName());
                downloadInfo.setDocPath(pae.getImageFile());
                downloadInfoList.add(downloadInfo);
            }
        }

        return downloadInfoList;
    }

    public UploadReq getAssetSpecForm(UploadReq req){
        AssetSpecEntity assetSpec = null;
        if(null != req.getAssetSpecId() || null != req.getServiceId() || StringUtils.isNotEmpty(req.getTag())) {
            StringBuilder hql = new StringBuilder("SELECT a from AssetSpecEntity a where ");
            if(null != req.getAssetSpecId()){
                assetSpec = assetSpecDao.get(req.getAssetSpecId());
                hql.append(" a.id=" + req.getAssetSpecId() +" or");
            }
            if(null != req.getServiceId()){
                hql.append(" a.serviceId=" + req.getServiceId() +" and");
            }
            if(StringUtils.isNotEmpty(req.getTag())){
                hql.append(" a.tag=" + req.getTag());
            }
            List<AssetSpecEntity> list = assetSpecDao.getListByHql(hql.toString().endsWith("or") ?
                    hql.toString().substring(0, hql.toString().length()-2) :
                    (hql.toString().endsWith("and") ?  hql.toString().substring(0, hql.toString().length()-3) :hql.toString()));
            if(null!=list&&list.size()>0){
                assetSpec = list.get(0);
                req.setAccuracy(null!=assetSpec.getAccuracy() ? assetSpec.getAccuracy() : req.getAccuracy());
                req.setFilesize(null!=assetSpec.getFilesize() ? assetSpec.getFilesize() : req.getFilesize());
                req.setImageHeight(null!=assetSpec.getImageHeight() ? assetSpec.getImageHeight() : req.getImageHeight());
                req.setImageWidth(null!=assetSpec.getImageWidth() ? assetSpec.getImageWidth() : req.getImageWidth());
                req.setOnShelves( assetSpec.isOnShelves() );
                req.setServiceId(null!=assetSpec.getServiceId() ? assetSpec.getServiceId() : req.getServiceId());
                req.setSmallImageHeight(null!=assetSpec.getSmallImageHeight() ? assetSpec.getSmallImageHeight() : req.getSmallImageHeight());
                req.setSmallImageWidth(null!=assetSpec.getSmallImageWidth() ? assetSpec.getSmallImageWidth() : req.getSmallImageWidth());

                req.setMiddleImageHeight(null!=assetSpec.getMiddleImageHeight() ? assetSpec.getMiddleImageHeight() : req.getMiddleImageHeight());
                req.setMiddleImageWidth(null!=assetSpec.getMiddleImageWidth() ? assetSpec.getMiddleImageWidth() : req.getMiddleImageWidth());
                //req.setTag( StringUtils.isNotEmpty(assetSpec.getTag()) ? assetSpec.getTag() : req.getTag());
                req.setAssetSpecId(assetSpec.getId());
                req.setVariableFormat(null!=assetSpec.getVariableFormat() ? assetSpec.getVariableFormat() : req.getVariableFormat());
                req.setKeepAspectRatio(assetSpec.isKeepAspectRatio());
                req.setKeepAspectRatio(assetSpec.isKeepAspectRatio());
            }
        }

        req.setImageHeight(null!=req.getImageHeight() ? req.getImageHeight() :
                Integer.valueOf(contextInfo.getImageWidthAndHeight().split("\\*")[1]));
        req.setImageWidth(null!=req.getImageWidth() ? req.getImageWidth() :
                Integer.valueOf(contextInfo.getImageWidthAndHeight().split("\\*")[0]));

        req.setSmallImageHeight(null!=req.getSmallImageHeight() ? req.getSmallImageHeight() :
                Integer.valueOf(contextInfo.getSmallImageWidthAndHeight().split("\\*")[1]));
        req.setSmallImageWidth(null!=req.getSmallImageWidth() ? req.getSmallImageWidth() :
                Integer.valueOf(contextInfo.getSmallImageWidthAndHeight().split("\\*")[0]));
        req.setMiddleImageHeight(null!=req.getMiddleImageHeight() ? req.getMiddleImageHeight() :
                Integer.valueOf(contextInfo.getMiddleImageWidthAndHeight().split("\\*")[1]));
        req.setMiddleImageWidth(null!=req.getMiddleImageWidth() ? req.getMiddleImageWidth() :
                Integer.valueOf(contextInfo.getMiddleImageWidthAndHeight().split("\\*")[0]));
        return req;
    }

    private static boolean createMultilayerFile(String dir) {
        try {
            File dirPath = new File(dir);
            if (!dirPath.exists()) {
                dirPath.mkdirs();
            }
        } catch (Exception e) {
            System.out.println("创建多层目录操作出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
        return true;
    }

    public boolean isOSLinux() {
        Properties prop = System.getProperties();
        String os = prop.getProperty("os.name");
        boolean statu = false;
        if (os != null && os.toLowerCase().indexOf("linux") > -1) {
            statu = true;
        }
        System.out.println("-----------------isOSLinux:" + statu + ";name:" + os.toLowerCase().indexOf("linux"));
        return statu;
    }

    /**
     * 修改原图的文件格式
     *
     * @param srcPath    原图路径
     * @param destPath   新图路径
     * @param formatName 图片格式，支持bmp|gif|jpg|jpeg|png
     * @return
     */
    public static boolean modifyImageFormat(String srcPath, String destPath, String formatName) {
        boolean isSuccess = false;
        InputStream fis = null;
        try {
            fis = new FileInputStream(srcPath);
            BufferedImage bufferedImg = ImageIO.read(fis);
            isSuccess = ImageIO.write(bufferedImg, formatName, new File(destPath));
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return isSuccess;
    }

    /**
     * 上传
     *
     * @param urlStr
     * @param textMap
     * @param fileMap
     * @param contentType 没有传入文件类型默认采用application/octet-stream
     *                    contentType非空采用filename匹配默认的图片类型
     * @return 返回response数据
     */
    @SuppressWarnings("rawtypes")
    public static String formUpload(String urlStr, Map<String, String> textMap, Map<String, String> fileMap,
                                    String contentType, String originFileName) {
        String res = "";
        HttpURLConnection conn = null;
        // boundary就是request头和上传文件内容的分隔符
        String BOUNDARY = "---------------------------123821742118716";
        try {
            URL url = new URL(urlStr);
            conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(72000);
            conn.setReadTimeout(72000);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Connection", "Keep-Alive");
            // conn.setRequestProperty("User-Agent","Mozilla/5.0 (Windows; U; Windows NT
            // 6.1; zh-CN; rv:*******)");
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY);
            conn.setRequestProperty("Authorization", " Bearer dataset-ua9gu2G3gHt2NA5gqg09YDex");
            OutputStream out = new DataOutputStream(conn.getOutputStream());
            // text
            if (textMap != null) {
                StringBuffer strBuf = new StringBuffer();
                Iterator iter = textMap.entrySet().iterator();
                while (iter.hasNext()) {
                    Map.Entry entry = (Map.Entry) iter.next();
                    String inputName = (String) entry.getKey();
                    String inputValue = (String) entry.getValue();
                    if (inputValue == null) {
                        continue;
                    }
                    strBuf.append("\r\n").append("--").append(BOUNDARY).append("\r\n");
                    strBuf.append("Content-Disposition: form-data; name=\"" + inputName + "\"\r\n\r\n");
                    strBuf.append(inputValue);
                }
                out.write(strBuf.toString().getBytes());
            }
            // file
            if (fileMap != null) {
                Iterator iter = fileMap.entrySet().iterator();
                while (iter.hasNext()) {
                    Map.Entry entry = (Map.Entry) iter.next();
                    String inputName = (String) entry.getKey();
                    String inputValue = (String) entry.getValue();
                    if (inputValue == null) {
                        continue;
                    }
                    File file = new File(inputValue);
                    String filename = file.getName();

                    // 没有传入文件类型，同时根据文件获取不到类型，默认采用application/octet-stream
                    contentType = new MimetypesFileTypeMap().getContentType(file);
                    // contentType非空采用filename匹配默认的图片类型
                    if (!"".equals(contentType)) {
                        if (filename.endsWith(".png")) {
                            contentType = "image/png";
                        } else if (filename.endsWith(".jpg") || filename.endsWith(".jpeg")
                                || filename.endsWith(".jpe")) {
                            contentType = "image/jpeg";
                        } else if (filename.endsWith(".gif")) {
                            contentType = "image/gif";
                        } else if (filename.endsWith(".ico")) {
                            contentType = "image/image/x-icon";
                        }else if(filename.endsWith(".tar")) {
                            contentType = "application/x-tar";
                        }
                    }
                    if (contentType == null || "".equals(contentType)) {
                        contentType = "application/octet-stream";
                    }
                    StringBuffer strBuf = new StringBuffer();
                    strBuf.append("\r\n").append("--").append(BOUNDARY).append("\r\n");
                    strBuf.append("Content-Disposition: form-data; name=\"" + inputName + "\"; filename=\"" + originFileName
                            + "\"\r\n");
                    strBuf.append("Content-Type:" + contentType + "\r\n\r\n");
                    out.write(strBuf.toString().getBytes());
                    DataInputStream in = new DataInputStream(new FileInputStream(file));
                    int bytes = 0;
                    byte[] bufferOut = new byte[1024];
                    while ((bytes = in.read(bufferOut)) != -1) {
                        out.write(bufferOut, 0, bytes);
                    }
                    in.close();
                }
            }
            byte[] endData = ("\r\n--" + BOUNDARY + "--\r\n").getBytes();
            out.write(endData);
            out.flush();
            out.close();
            // 读取返回数据
            StringBuffer strBuf = new StringBuffer();
            BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String line = null;
            while ((line = reader.readLine()) != null) {
                strBuf.append(line).append("\n");
            }
            res = strBuf.toString();
            reader.close();
            reader = null;
        } catch (Exception e) {
            System.out.println("发送POST请求出错。" + urlStr);
            e.printStackTrace();
        } finally {
            if (conn != null) {
                conn.disconnect();
                conn = null;
            }
        }
        return res;
    }
}
