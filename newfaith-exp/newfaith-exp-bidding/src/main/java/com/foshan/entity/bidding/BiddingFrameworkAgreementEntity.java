package com.foshan.entity.bidding;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.DiscriminatorColumn;
import javax.persistence.DiscriminatorType;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.DictionaryDataEntity;
import com.foshan.entity.DictionaryEntity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
@DiscriminatorColumn(discriminatorType = DiscriminatorType.STRING)
@DiscriminatorValue("BiddingFrameworkAgreement")
public class BiddingFrameworkAgreementEntity extends AssetEntity {
	/**
	 * 框架文件
	 */
	private static final long serialVersionUID = 1857737342267013443L;
	@Column(columnDefinition = "int(10) default 0 comment '下载量'")
	private Integer download;
	@ManyToOne(targetEntity = BiddingPartnerEntity.class,fetch = FetchType.LAZY)
	@JoinColumn(name = "partnerId", referencedColumnName = "Id",foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
	private BiddingPartnerEntity partner;

}
