package com.foshan.entity.bidding;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.PermissionEntity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_permission")
@DiscriminatorValue("BIDDING")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class BiddingPermissionEntity extends PermissionEntity{

	/**
	 * 招投标平台权限
	 */
	private static final long serialVersionUID = -5743825032608584000L;
}
