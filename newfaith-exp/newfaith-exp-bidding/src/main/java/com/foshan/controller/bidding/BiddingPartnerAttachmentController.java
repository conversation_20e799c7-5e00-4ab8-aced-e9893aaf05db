package com.foshan.controller.bidding;

import com.foshan.form.UploadedFile;
import com.foshan.form.bidding.request.BiddingPartnerAttachmentReq;
import com.foshan.form.bidding.response.biddingPartnerAttachment.DeleteBiddingPartnerAttachmentRes;
import com.foshan.form.bidding.response.biddingPartnerAttachment.GetBiddingPartnerAttachmentListRes;
import com.foshan.form.request.UploadReq;
import com.foshan.form.response.upload.UploadRes;
import com.foshan.util.bidding.BiddingDownloadInfoObject;
import com.foshan.util.bidding.DownloadFileUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Api(tags = "合作伙伴附件模块")
@RestController
public class BiddingPartnerAttachmentController extends BaseBiddingController {
    @ApiOperation(value = "获取合作伙伴附件列表(getBiddingPartnerAttachmentList)", httpMethod = "POST", notes = "获取合作伙伴附件列表，传{}即可")
    @ResponseBody
    @RequestMapping(value = "/getBiddingPartnerAttachmentList",method = {
            RequestMethod.POST},consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public GetBiddingPartnerAttachmentListRes getBiddingPartnerAttachmentList(@RequestBody BiddingPartnerAttachmentReq req, HttpServletResponse response) {
        GetBiddingPartnerAttachmentListRes res = (GetBiddingPartnerAttachmentListRes) biddingPartnerAttachmentService.getBiddingPartnerAttachmentList(req);
        return res;
    }

    @ApiOperation(value = "上传合作伙伴附件(uploadBiddingPartnerAttachmentFile)", httpMethod = "POST", notes = "上传合作伙伴附件")
    @ResponseBody
    @RequestMapping(value = "/uploadBiddingPartnerAttachmentFile",method = { RequestMethod.POST},produces = MediaType.APPLICATION_JSON_VALUE)
    public UploadRes uploadBiddingFile(HttpServletRequest request, HttpServletResponse response,
                                       @ModelAttribute UploadedFile uploadedFile, Integer parentAssetId,
                                       Integer assetSpecId, Integer serviceId, Integer imageWidth, Integer imageHeight, Integer smallImageWidth
            , Integer smallImageHeight, Integer createSmallImage, Integer createMiddleImage, Integer onShelves,
                                       String tag, Integer variableFormat, Integer filesize, String accuracy , Integer middleImageHeight, Integer middleImageWidth,
                                       Integer partnerId, @RequestParam(value = "isSynchronize" ,required = true)Integer isSynchronize
    ) throws Exception {
        //long start = System.currentTimeMillis();
        request.setCharacterEncoding("utf-8");
        MultipartFile[] multipartFile = uploadedFile.getMultipartFile();
        UploadReq req = new UploadReq();
        partnerId = null!=partnerId ? partnerId : null;
        req.setAccuracy(StringUtils.isNotEmpty(accuracy) ? new BigDecimal(accuracy) : null);
        req.setAssetSpecId(null!=assetSpecId ? assetSpecId : null );
        req.setFilesize(null!=filesize ? filesize : null);
        req.setImageHeight(null!=smallImageHeight ? smallImageHeight : null);
        req.setImageWidth(null!=smallImageWidth ? smallImageWidth : null);
        req.setMiddleImageHeight(null!=middleImageHeight ? middleImageWidth : null);
        req.setMiddleImageWidth(null!=middleImageWidth ? middleImageWidth : null);
        req.setOnShelves(null!=onShelves ? (1==onShelves ? true :false) : false);
        req.setParentAssetId(null!=parentAssetId ? parentAssetId : null);
        req.setServiceId(null!=serviceId ? serviceId : null);
        req.setSmallImageHeight(null!=smallImageHeight ? smallImageHeight : null);
        req.setSmallImageWidth(null!=smallImageWidth ? smallImageWidth : null);
        req.setTag(StringUtils.isNotEmpty(tag) ? tag : "" );
        req.setVariableFormat(null!=variableFormat ? variableFormat: null);
        UploadRes res =(UploadRes)biddingPartnerAttachmentService.uploadFile(request,multipartFile,req,partnerId,isSynchronize);
        return res;
    }

    @ApiOperation(value = "删除合作伙伴附件(deleteBiddingPartnerAttachment)", httpMethod = "POST", notes = "删除合作伙伴附件<p>1:partnerAttachmentId不能为空")
    @ResponseBody
    @RequestMapping(value = "/deleteBiddingPartnerAttachment",method = {
            RequestMethod.POST},consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public DeleteBiddingPartnerAttachmentRes deleteBiddingPartnerAttachment(@RequestBody BiddingPartnerAttachmentReq req, HttpServletResponse response) {
        Integer partnerAttachmentId = req.getPartnerAttachmentId();
        DeleteBiddingPartnerAttachmentRes res = (DeleteBiddingPartnerAttachmentRes) biddingPartnerAttachmentService.deleteFile(partnerAttachmentId);
        return res;
    }

    @ApiOperation(value = "批量删除合作伙伴附件(batchDeleteBiddingPartnerAttachment)", httpMethod = "POST", notes = "批量删除合作伙伴附件<p>1:partnerAttachmentIds不能为空")
    @ResponseBody
    @RequestMapping(value = "/batchDeleteBiddingPartnerAttachment",method = {
            RequestMethod.POST},consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public DeleteBiddingPartnerAttachmentRes batchdeleteBiddingPartnerAttachment(@RequestBody BiddingPartnerAttachmentReq req,HttpServletResponse response) {
        String partnerAttachmentIds = req.getPartnerAttachmentIds();
        DeleteBiddingPartnerAttachmentRes res = (DeleteBiddingPartnerAttachmentRes) biddingPartnerAttachmentService.batchDeleteFile(partnerAttachmentIds);
        return res;
    }

    @ApiOperation(value = "下载合作伙伴附件(partnerAttachmentSingleDownload)", httpMethod = "POST", notes = "下载合作伙伴附件")
    @RequestMapping(value = "/partnerAttachmentSingleDownload", method = {RequestMethod.POST},consumes = MediaType.APPLICATION_JSON_VALUE)
    public void partnerAttachmentSingleDownload(@RequestBody BiddingPartnerAttachmentReq req,HttpServletResponse response) throws IOException {
        String path = null;
        List<BiddingDownloadInfoObject> downloadPathList = biddingPartnerAttachmentService.getDownloadPath(req.getPartnerAttachmentId());
        path = contextInfo.getAssetFilePath() + File.separator + downloadPathList.get(0).getDocPath();
        String fileName = downloadPathList.get(0).getDocName();
        DownloadFileUtil.downSingleFile(response, path, fileName);
    }

    @ApiOperation(value = "批量下载合作伙伴附件(partnerAttachmentBatchDownload)", httpMethod = "POST", notes = "批量下载合作伙伴附件")
    @RequestMapping(value = "/partnerAttachmentBatchDownload", method = {RequestMethod.POST},consumes = MediaType.APPLICATION_JSON_VALUE)
    public void partnerAttachmentBatchDownload(@RequestBody BiddingPartnerAttachmentReq req,HttpServletResponse response) throws IOException{
        List<File> files = new ArrayList<>();
        String path = null;
        List<BiddingDownloadInfoObject> downloadInfoList = biddingPartnerAttachmentService.getBatchDownloadPath(req.getPartnerAttachmentIds());
        for (int i = 0; i < downloadInfoList.size(); i++) {
            path = contextInfo.getAssetFilePath() + File.separator + downloadInfoList.get(i).getDocPath();
            String newName = downloadInfoList.get(i).getDocName();
            String newPath = DownloadFileUtil.createFile(newName);
            DownloadFileUtil.copyFileUsingStream(path,newPath);
            files.add(new File(newPath));
        }
        String fileName = downloadInfoList.get(0).getDocName().substring(0, downloadInfoList.get(0).getDocName().lastIndexOf('.')) + "等" + downloadInfoList.size() + "个文件.zip";
        DownloadFileUtil.zipDownload(response, fileName, files);
    }
}
