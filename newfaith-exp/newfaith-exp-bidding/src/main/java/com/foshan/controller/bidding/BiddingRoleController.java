package com.foshan.controller.bidding;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.bidding.request.BiddingRoleReq;
import com.foshan.form.bidding.response.biddingRole.GetBiddingRoleListRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "角色模块")
@RestController
public class BiddingRoleController extends BaseBiddingController{
	// 获取角色列表
		@ApiOperation(value = "角色列表(getBiddingRoleList)", httpMethod = "POST", notes = "获取角色列表，传空{}即可；")
		@ResponseBody
		@RequestMapping(value = "/getBiddingRoleList", method = {
				RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
		public GetBiddingRoleListRes getBiddingRoleList(@RequestBody BiddingRoleReq req, HttpServletRequest request)
				throws JsonProcessingException {
			GetBiddingRoleListRes res = (GetBiddingRoleListRes) biddingRoleService.getBiddingRoleList(req);
			return res;
		}
}
