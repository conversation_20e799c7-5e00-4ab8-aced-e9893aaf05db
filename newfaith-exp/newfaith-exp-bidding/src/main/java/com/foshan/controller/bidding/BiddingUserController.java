package com.foshan.controller.bidding;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.bidding.request.BiddingUserReq;
import com.foshan.form.bidding.response.biddingUser.AddBiddingUserRes;
import com.foshan.form.bidding.response.biddingUser.DeleteBiddingUserRes;
import com.foshan.form.bidding.response.biddingUser.GetBiddingUserInfoRes;
import com.foshan.form.bidding.response.biddingUser.GetBiddingUserListRes;
import com.foshan.form.bidding.response.biddingUser.ModifyBiddingUserRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "用户模块")
@RestController
public class BiddingUserController extends BaseBiddingController{
	// 获取用户列表
		@ApiOperation(value = "用户列表(getBiddingUserList)", httpMethod = "POST", notes = "获取用户列表，传空{}即可；")
		@ResponseBody
		@RequestMapping(value = "/getBiddingUserList", method = {
				RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
		public GetBiddingUserListRes getBiddingUserList(@RequestBody BiddingUserReq req, HttpServletRequest request)
				throws JsonProcessingException {
			GetBiddingUserListRes res = (GetBiddingUserListRes) biddingUserService.getBiddingUserList(req);
			return res;
		}
		
		@ApiOperation(value = "新增用户(AddBiddingUser)", httpMethod = "POST", notes = "新增用户<p>1:userName、userPassword、userCode、RoleIdList不能为空；")
		@ResponseBody
		@RequestMapping(value = "/addBiddingUser", method = {
				RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
		public AddBiddingUserRes addBiddingUser(@RequestBody BiddingUserReq req, HttpServletRequest request){

			AddBiddingUserRes res = (AddBiddingUserRes) biddingUserService.addBiddingUser(req);
			return res;
		}
		
		@ApiOperation(value = "删除用户(DeleteBiddingUser)", httpMethod = "POST", notes = "删除用户<p>1:userId不能为空；")
		@ResponseBody
		@RequestMapping(value = "/deleteBiddingUser", method = {
				RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
		public DeleteBiddingUserRes deleteBiddingUser(@RequestBody BiddingUserReq req, HttpServletRequest request){

			DeleteBiddingUserRes res = (DeleteBiddingUserRes) biddingUserService.deleteBiddingUser(req);
			return res;
		}
		
		@ApiOperation(value = "修改用户(ModifyBiddingUser)", httpMethod = "POST", notes = "修改用户<p>1:userId不能为空；")
		@ResponseBody
		@RequestMapping(value = "/modifyBiddingUser", method = {
				RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
		public ModifyBiddingUserRes modifyBiddingUser(@RequestBody BiddingUserReq req, HttpServletRequest request){

			ModifyBiddingUserRes res = (ModifyBiddingUserRes) biddingUserService.modifyBiddingUser(req);
			return res;
		}
		
		@ApiOperation(value = "用户详情(getBiddingUserInfo)", httpMethod = "POST", notes = "获取用户列表<p>1:userId不能为空；")
		@ResponseBody
		@RequestMapping(value = "/getBiddingUserInfo", method = {
				RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
		public GetBiddingUserInfoRes getBiddingUserInfo(@RequestBody BiddingUserReq req, HttpServletRequest request)
				throws JsonProcessingException {
			GetBiddingUserInfoRes res = (GetBiddingUserInfoRes) biddingUserService.getBiddingUserInfo(req);
			return res;
		}
}
