package com.foshan.controller.bidding;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.foshan.form.bidding.request.BiddingProjectTypeReq;
import com.foshan.form.bidding.response.biddingProjectType.AddBiddingProjectTypeRes;
import com.foshan.form.bidding.response.biddingProjectType.DeleteBiddingProjectTypeRes;
import com.foshan.form.bidding.response.biddingProjectType.GetBiddingProjectTypeListRes;
import com.foshan.form.bidding.response.biddingProjectType.ModifyBiddingProjectTypeRes;
import com.foshan.util.bidding.BiddingDownloadInfoObject;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "项目类别模块")
@RestController
public class BiddingProjectTypeController extends BaseBiddingController {

	@ApiOperation(value = "获取项目类别列表(getBiddingProjectTypeList)", httpMethod = "POST", notes = "获取项目类别列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getBiddingProjectTypeList",method = {
			RequestMethod.POST},consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
	public GetBiddingProjectTypeListRes getBiddingProjectTypeList(@RequestBody BiddingProjectTypeReq req,HttpServletResponse response) {
		GetBiddingProjectTypeListRes res = (GetBiddingProjectTypeListRes) biddingProjectTypeService.getBiddingProjectTypeList(req);
		return res;
	}
	
	@ApiOperation(value = "添加项目类别(addBiddingProjectType)", httpMethod = "POST", notes = "获取项目类别 <p>1:projectTypeName不能为空")
	@ResponseBody
	@RequestMapping(value = "/addBiddingProjectType",method = {
			RequestMethod.POST},consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
	public AddBiddingProjectTypeRes addBiddingProjectType(@RequestBody BiddingProjectTypeReq req,HttpServletResponse response) {
		AddBiddingProjectTypeRes res = (AddBiddingProjectTypeRes) biddingProjectTypeService.addBiddingProjectType(req);
		return res;
	}
	
	@ApiOperation(value = "删除项目类别(deleteBiddingProjectType)", httpMethod = "POST", notes = "删除项目类别 <p>1:projectTypeId不能为空")
	@ResponseBody
	@RequestMapping(value = "/deleteBiddingProjectType",method = {
			RequestMethod.POST},consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
	public DeleteBiddingProjectTypeRes deleteBiddingProjectType(@RequestBody BiddingProjectTypeReq req,HttpServletResponse response) {
		List<Integer> projectIdList = biddingProjectService.getProjectIdList(req.getProjectTypeId());
		for(int i=0;i<projectIdList.size();i++) {
			List<BiddingDownloadInfoObject> downloadPathList = biddingProjectDocumentService.getDownloadPath(projectIdList.get(i),1);
			for (int j = 0; j < downloadPathList.size(); j++) {
				biddingProjectDocumentService.deleteFile(downloadPathList.get(j).getId());
			}
			biddingProjectService.deleteBiddingProject(projectIdList.get(i));
		}
		DeleteBiddingProjectTypeRes res = (DeleteBiddingProjectTypeRes) biddingProjectTypeService.deleteBiddingProjectType(req);
		return res;
	}
	
	@ApiOperation(value = "修改项目类别(modifyBiddingProjectType)", httpMethod = "POST", notes = "删除项目类别 <p>1:projectTypeId不能为空")
	@ResponseBody
	@RequestMapping(value = "/modifyBiddingProjectType",method = {
			RequestMethod.POST},consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyBiddingProjectTypeRes modifyBiddingProjectType(@RequestBody BiddingProjectTypeReq req,HttpServletResponse response) {
		ModifyBiddingProjectTypeRes res = (ModifyBiddingProjectTypeRes) biddingProjectTypeService.modifyBiddingProjectType(req);
		return res;
	}
}
