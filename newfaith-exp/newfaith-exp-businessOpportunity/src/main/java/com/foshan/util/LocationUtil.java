package com.foshan.util;

public class LocationUtil {
    // 中国经度范围大约在73.66~135.05
    private static final double MIN_LONGITUDE = 73.66;
    private static final double MAX_LONGITUDE = 135.05;
    
    // 中国纬度范围大约在3.86~53.55
    private static final double MIN_LATITUDE = 3.86;
    private static final double MAX_LATITUDE = 53.55;

    public static boolean isValidLocation(Double longitude, Double latitude) {
        if (longitude == null || latitude == null) {
            return false;
        }
        return longitude >= MIN_LONGITUDE && longitude <= MAX_LONGITUDE
                && latitude >= MIN_LATITUDE && latitude <= MAX_LATITUDE;
    }
} 