package com.foshan.controller.businessOpportunity;

import com.foshan.form.businessOpportunity.request.opportunity.*;
import com.foshan.form.response.IResponse;
import com.foshan.service.businessOpportunity.IBusinessOpportunityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@Api(tags = "商机管理接口")
@RequestMapping("/businessOpportunity")
public class BusinessOpportunityController {

    @Autowired
    private IBusinessOpportunityService businessOpportunityService;

    @ApiOperation(value = "创建商机(addBusinessOpportunity)", httpMethod = "POST", notes = "创建新的商机信息")
    @ResponseBody
    @RequestMapping(value = "/addBusinessOpportunity", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public IResponse createBusinessOpportunity(@RequestBody BusinessOpportunityAddReq req, HttpServletRequest request) {
        return businessOpportunityService.addBusinessOpportunity(req);
    }

    @ApiOperation(value = "更新商机(updateBusinessOpportunity)", httpMethod = "POST", notes = "更新已有商机信息")
    @ResponseBody
    @RequestMapping(value = "/updateBusinessOpportunity", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public IResponse updateBusinessOpportunity(@RequestBody BusinessOpportunityUpdateReq req, HttpServletRequest request) {
        return businessOpportunityService.updateBusinessOpportunity(req);
    }

    @ApiOperation(value = "删除商机(deleteBusinessOpportunity)", httpMethod = "POST", notes = "删除指定商机")
    @ResponseBody
    @RequestMapping(value = "/deleteBusinessOpportunity", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public IResponse deleteBusinessOpportunity(@RequestBody BusinessOpportunityDeleteReq req, HttpServletRequest request) {
        return businessOpportunityService.deleteBusinessOpportunity(req);
    }

    @ApiOperation(value = "获取商机详情(getBusinessOpportunityDetail)", httpMethod = "POST", notes = "获取商机详细信息")
    @ResponseBody
    @RequestMapping(value = "/getBusinessOpportunityDetail", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public IResponse getBusinessOpportunityDetail(@RequestBody BusinessOpportunityDetailReq req, HttpServletRequest request) {
        return businessOpportunityService.getBusinessOpportunityDetail(req);
    }

    @ApiOperation(value = "获取商机列表(getBusinessOpportunityList)", httpMethod = "POST", notes = "分页查询商机列表信息")
    @ResponseBody
    @RequestMapping(value = "/getBusinessOpportunityList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public IResponse getBusinessOpportunityList(@RequestBody BusinessOpportunityQueryReq req, HttpServletRequest request) {
        return businessOpportunityService.getBusinessOpportunityList(req);
    }

    @ApiOperation(value = "查询即将到期合约", httpMethod = "GET", notes = "查询即将到期的合约信息")
    @ResponseBody
    @RequestMapping(value = "/getExpiringContracts", method = {
            RequestMethod.GET }, produces = MediaType.APPLICATION_JSON_VALUE)
    public IResponse getExpiringContracts(HttpServletRequest request) {
        return businessOpportunityService.getExpiringContracts();
    }

    @ApiOperation(value = "查询附近商机", httpMethod = "POST", notes = "根据经纬度查询附近的商机信息")
    @ResponseBody
    @RequestMapping(value = "/getNearbyBusinessOpportunities", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public IResponse getNearbyBusinessOpportunities(@RequestBody BusinessOpportunityNearbyReq req, HttpServletRequest request) {
        return businessOpportunityService.getNearbyBusinessOpportunities(req);
    }

} 