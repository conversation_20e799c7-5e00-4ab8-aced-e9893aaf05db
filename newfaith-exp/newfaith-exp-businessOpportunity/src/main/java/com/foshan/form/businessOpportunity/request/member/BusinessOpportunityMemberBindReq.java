package com.foshan.form.businessOpportunity.request.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
@ApiModel(description = "绑定商机会员手机号请求")
public class BusinessOpportunityMemberBindReq {
    
    @NotBlank(message = "openId不能为空")
    @ApiModelProperty(value = "微信openId", required = true)
    private String openId;
    
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "请输入正确的手机号码")
    @ApiModelProperty(value = "手机号", required = true)
    private String mobile;
    
    @NotBlank(message = "验证码不能为空")
    @ApiModelProperty(value = "验证码", required = true)
    private String code;
} 