package com.foshan.form.businessOpportunity.request.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(description = "获取商机会员信息请求")
public class BusinessOpportunityMemberInfoReq {
    
    @ApiModelProperty(value = "微信openId", required = true)
    private String openId;
    @ApiModelProperty(value = "用户code")
    private String userCode;
} 