package com.foshan.util.health;


import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.ParseException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;

public class YoyeeSMSUtil {
    private static final Logger logger = LoggerFactory.getLogger(YoyeeSMSUtil.class);

    static String userName = "EJJ";// 账号用户名
    static String password = "TqViHhpbaNs3";// 密码
    static String address = "http://sms.ue35.net/sms";//接口网关地址 ，例如：http://127.0.0.1:8001/sms

    // 短信批量发送接口
    public static String SENDMESSAGE_API_URL = "http://address:port/sms/api/sendMessage";
    // 短信一对一发送接口
    public static String SENDMESSAGEONE_API_URL = "http://address:port/sms/api/sendMessageOne";
    // 回执状态获取接口
    public static String GETREPORT_API_URL = "http://address:port/sms/api/getReport";
    // 上行回复获取接口
    public static String GETUPSTREAM_API_URL = "http://address:port/sms/api/getUpstream";
    // 查询余额接口
    public static String GETBALANCE_API_URL = "http://address:port/sms/api/getBalance";
    //提交短信模板接口
    public static String ADDTEMPLATE_API_URL = "http://address:port/sms/api/addTemplate";
    //查询短信模板接口
    public static String QUERYTEMPLATE_API_URL = "http://address:port/sms/api/queryTemplate";
    //报备签名接口
    public static String ADDSIGNATURE_API_URL = "http://address:port/sms/api/addSignature";
    //查询签名接口
    public static String QUERYSIGNATURE_API_URL = "http://address:port/sms/api/querySignature";


    public static void main(String[] args) {
        String userName = "EJJ";// 账号用户名
        String password = "TqViHhpbaNs3";// 密码
        String address = "http://sms.ue35.net/sms";//接口网关地址 ，例如：http://127.0.0.1:8001/sms


        sendMessage(userName, password, address,""); //批量发送
//        sendMessageOne(userName, password, address);//一对一
//        getReport(userName, password, address);//回执获取
//        getUpstream(userName, password, address);//上行获取
//        getBalance(userName, password, address);//查询余额
//        addTemplate(userName, password, address);//提交短信模板
//       queryTemplate(userName, password, address);//查询短信模板
//        addSignature(userName, password, address);//报备签名
//      querySignature(userName, password, address);//查询签名
    }

    /**
     * 短信批量发送接口
     *
     * @param userName
     * @param password
     * @param address
     */
    public static void sendMessage(String userName, String password, String address,String content) {
        // 替换地址
        String url = SENDMESSAGE_API_URL.replace("http://address:port/sms", address);


        //发送手机号码，最大数量不得超过 10000 个号码，系统将自动去除重复号码
        List<String> phoneList= Arrays.asList("");

        // 时间戳
        Date d = new Date();
        long timestamp = d.getTime();

        // sign鉴权 md5(userName + content + timestamp + md5(password))
        String sign = md5(userName + content + timestamp + md5(password));

        // 拼接JSON
        JSONObject jsons = new JSONObject();
        jsons.put("userName", userName);
        jsons.put("content", content);
        jsons.put("timestamp", timestamp);
        jsons.put("sign", sign);

        //每次发送10000个号码
        int length = phoneList.size();
        int size = 10000;
        int index = length / size;
        int r = length % size;
        if (r > 0) {
            index += 1;
        }
        for (int i = 0; i < index; i++) {
            if (r > 0 && i == index - 1) {
                jsons.put("phoneList", phoneList.subList(i * size, length));
                // 发送请求
                postUrl(url, jsons);
            } else {
                jsons.put("phoneList", phoneList.subList(i * size, (i+1)*size));
                postUrl(url, jsons);
            }
        }
    }

    /**
     * 短信一对一发送接口
     *
     * @param userName
     * @param password
     * @param address
     */
    public static String sendMessageOne(Map<String, String> sendMap ) {
        // 替换地址
        String url = SENDMESSAGEONE_API_URL.replace("http://address:port/sms", address);

        // 数组形式，每个 JSON 对象包含短信内容和号码数据，最大 1000 个号码。
        JSONArray messageList=new JSONArray();

//        String[][] list = new String[][] { { "18278810214", "123456" }, { "18278810215", "486512" }, { "18278810216", "889945" } };

//        for (int i = 0; i < list[0].length; i++)
//        {
//            JSONObject msg = new JSONObject();
//            msg.put("phone", list[i][0]);
//            msg.put("content", content.replace("{%变量%}", list[i][1]));
//            messageList.add(msg);
//        }




        // 时间戳
        Date d = new Date();
        long timestamp = d.getTime();

        // sign鉴权 md5(userName + timestamp + md5(password))
        String sign = md5(userName + timestamp + md5(password));

        JSONObject jsons = new JSONObject();
        jsons.put("userName", userName);
        jsons.put("timestamp", timestamp);
        jsons.put("sign", sign);


        for (String phone : sendMap.keySet()) {
            JSONObject msg = new JSONObject();
            msg.put("phone",phone );
            msg.put("content", "【伦教健共体】"+sendMap.get(phone));
            messageList.add(msg);

        }
        //每次最大发送1000个号码
        int length = messageList.size();
        int size = 1000;
        int index = length / size;
        int r = length % size;
        if (r > 0) {
            index += 1;
        }
        for (int i = 0; i < index; i++) {
            if (r > 0 && i == index - 1) {
                jsons.put("messageList", messageList.subList(i * size, length));
                // 发送请求
                return postUrl(url, jsons);
            } else {
                jsons.put("messageList", messageList.subList(i * size, (i+1)*size));
               return postUrl(url, jsons);
            }
        }
        return null;
    }

    /**
     * 回执状态获取接口
     *
     * @param userName
     * @param password
     * @param address
     */
    public static void getReport(String userName, String password, String address) {
        //替换地址
        String url = GETREPORT_API_URL.replace("http://address:port/sms", address);
        // 时间戳
        Date d = new Date();
        long timestamp = d.getTime();

        // sign鉴权 md5(userName + timestamp + md5(password))
        String sign = md5(userName + timestamp + md5(password));

        JSONObject jsons = new JSONObject();
        jsons.put("userName", userName);
        jsons.put("timestamp", timestamp);
        jsons.put("sign", sign);

        postUrl(url, jsons);
    }

    /**
     * 上行回复获取接口
     *
     * @param userName
     * @param password
     * @param address
     */
    public static void getUpstream(String userName, String password, String address) {
        //替换地址
        String url = GETUPSTREAM_API_URL.replace("http://address:port/sms", address);
        // 时间戳
        Date d = new Date();
        long timestamp = d.getTime();

        // sign鉴权 md5(userName + timestamp + md5(password))
        String sign = md5(userName + timestamp + md5(password));

        JSONObject jsons = new JSONObject();
        jsons.put("userName", userName);
        jsons.put("timestamp", timestamp);
        jsons.put("sign", sign);

        postUrl(url, jsons);
    }

    /**
     * 查询余额接口
     *
     * @param userName
     * @param password
     * @param address
     */
    public static void getBalance(String userName, String password, String address) {
        //替换地址
        String url = GETBALANCE_API_URL.replace("http://address:port/sms", address);
        // 时间戳
        Date d = new Date();
        long timestamp = d.getTime();

        // sign鉴权 md5(userName + timestamp + md5(password))
        String sign = md5(userName + timestamp + md5(password));

        JSONObject jsons = new JSONObject();
        jsons.put("userName", userName);
        jsons.put("timestamp", timestamp);
        jsons.put("sign", sign);

        postUrl(url, jsons);
    }

    /**
     * 提交短信模板接口
     *
     * @param userName
     * @param password
     * @param address
     */
    public static void addTemplate(String userName, String password, String address) {
        //替换地址
        String url = ADDTEMPLATE_API_URL.replace("http://address:port/sms", address);
        // 时间戳
        Date d = new Date();
        long timestamp = d.getTime();
        String content = "【小哈科技】验证码{%变量%}";

        // sign鉴权 md5(userName + timestamp + md5(password))
        String sign = md5(userName + content + timestamp + md5(password));

        JSONObject jsons = new JSONObject();
        jsons.put("userName", userName);
        jsons.put("timestamp", timestamp);
        jsons.put("sign", sign);
        jsons.put("content", content);

        postUrl(url, jsons);
    }

    /**
     * 查询短信模板接口
     *
     * @param userName
     * @param password
     * @param address
     */
    public static void queryTemplate(String userName, String password, String address) {
        //替换地址
        String url = QUERYTEMPLATE_API_URL.replace("http://address:port/sms", address);
        // 时间戳
        Date d = new Date();
        long timestamp = d.getTime();

        // sign鉴权 md5(userName + timestamp + md5(password))
        String sign = md5(userName + timestamp + md5(password));

        JSONObject jsons = new JSONObject();
        jsons.put("userName", userName);
        jsons.put("timestamp", timestamp);
        jsons.put("sign", sign);

        postUrl(url, jsons);
    }

    /**
     * 报备签名接口
     *
     * @param userName
     * @param password
     * @param address
     */
    public static void addSignature(String userName, String password, String address) {
        //替换地址
        String url = ADDSIGNATURE_API_URL.replace("http://address:port/sms", address);
        // 时间戳
        Date d = new Date();
        long timestamp = d.getTime();
        // sign鉴权 md5(userName + timestamp + md5(password))
        String sign = md5(userName + timestamp + md5(password));
        //短信签名，必须包含签名符号【】
        JSONArray signatureList = new JSONArray();
        signatureList.add("【嘻哈科技】");
        signatureList.add("【一一科技】");

        JSONObject jsons = new JSONObject();
        jsons.put("userName", userName);
        jsons.put("timestamp", timestamp);
        jsons.put("sign", sign);
        jsons.put("signatureList", signatureList);

        postUrl(url, jsons);
    }

    /**
     * 查询签名接口
     *
     * @param userName
     * @param password
     * @param address
     */
    public static void querySignature(String userName, String password, String address) {
        //替换地址
        String url = QUERYSIGNATURE_API_URL.replace("http://address:port/sms", address);
        // 时间戳
        Date d = new Date();
        long timestamp = d.getTime();

        // sign鉴权 md5(userName + timestamp + md5(password))
        String sign = md5(userName + timestamp + md5(password));

        JSONObject jsons = new JSONObject();
        jsons.put("userName", userName);
        jsons.put("timestamp", timestamp);
        jsons.put("sign", sign);

        postUrl(url, jsons);
    }


    /**
     * md5加密(Byte)
     *
     * @param text
     * @return
     */
    private static String md5(String text) {
        if (text == null || "".equals(text.trim())) {
            throw new IllegalArgumentException("请输入要加密的内容");
        }
        String encryptText = null;
        try {
            MessageDigest m = MessageDigest.getInstance("md5");
            m.update(text.getBytes(StandardCharsets.UTF_8));
            byte[] arr = m.digest();
            StringBuffer sb = new StringBuffer(32);
            for (int i = 0; i < arr.length; ++i) {
                String hex = Integer.toHexString(arr[i] & 0xFF);
                if (hex.length() < 2){
                    sb.append('0');
                }
                sb.append(hex);
            }
            return sb.toString();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return encryptText;
    }

    /**
     * 向指定地址发一个post请求，带着data数据
     * @param jsonObject
     * @param url
     * @throws ParseException
     * @throws IOException
     */
    public static String postUrl(String url,JSONObject jsonObject) {
        String body = "";
        String encoding="utf-8";
        //创建httpclient对象
        CloseableHttpClient client = HttpClients.createDefault();
        //创建post方式请求对象
        HttpPost httpPost = new HttpPost(url);
        //装填参数
        StringEntity s = new StringEntity(jsonObject.toString(), encoding);
        s.setContentEncoding(new BasicHeader("Content-Type",
                "application/json"));
        //设置参数到请求对象中
        httpPost.setEntity(s);
        httpPost.setHeader("Content-type", "application/json");
        httpPost.setHeader("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        try{
            //执行请求操作，并拿到结果（同步阻塞）
            CloseableHttpResponse response = client.execute(httpPost);
            //获取结果实体
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                //按指定编码转换结果实体为String类型
                body = EntityUtils.toString(entity, encoding);
            }
            EntityUtils.consume(entity);
            //释放链接
            response.close();
        }catch (Exception e){
            logger.info(e.toString());
        }
        logger.info("url:{}\ndata:{}\nresultJson:{}", url,jsonObject.toString(),body);

        return body;
    }

}
