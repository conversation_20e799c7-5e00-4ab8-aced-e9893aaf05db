package com.foshan.service.health;

import javax.servlet.http.HttpServletRequest;

import com.foshan.form.health.request.HealthMemberReq;
import com.foshan.form.health.request.HealthlMemberLoginReq;
import com.foshan.form.response.IResponse;
import com.foshan.service.annotation.Audit;

public interface IHealthMemberService {
	public IResponse getHealthMemberList(HealthMemberReq req);
//	public IResponse addHospitalMember(HealthMemberReq req);
	public IResponse modifyHealthMember(HealthMemberReq req);
	public IResponse deleteHealthMember(HealthMemberReq req);
	public IResponse getHealthMemberInfo(HealthMemberReq req);
	public IResponse findHealthMemberLoginState(HealthlMemberLoginReq req);

    IResponse getHealthMemberLoginState(HealthlMemberLoginReq req);

    IResponse changePassword(HealthMemberReq req);

    public IResponse memberRegister(HealthMemberReq req, HttpServletRequest request);

	//记录登录的智能卡号
	IResponse bindSmartcard(HealthlMemberLoginReq req);

	IResponse getHistoryPhone(HealthlMemberLoginReq req);

//    public IResponse getPhoneVerificationCode(HealthlMemberLoginReq req, HttpServletRequest request);

}
