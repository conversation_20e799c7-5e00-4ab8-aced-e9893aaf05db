package com.foshan.service.health.impl;

import com.foshan.dao.generic.Page;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.health.DiseaseTypeEntity;
import com.foshan.form.health.DiseaseTypeForm;
import com.foshan.form.health.request.DiseaseTypeReq;
import com.foshan.form.health.response.diseaseType.AddDiseaseTypeRes;
import com.foshan.form.health.response.diseaseType.GetDiseaseTypeInfoRes;
import com.foshan.form.health.response.diseaseType.GetDiseaseTypeListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.health.IDiseaseTypeService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Transactional
@Service("diseaseTypeService")
public class DiseaseTypeServiceImpl extends GenericHealthService implements IDiseaseTypeService {
    private final static Logger logger = LoggerFactory.getLogger(DiseaseTypeServiceImpl.class);

    @Override
    @Audit(operate = "新增病种")
    public IResponse addDiseaseType(DiseaseTypeReq req) {
        AddDiseaseTypeRes res = new AddDiseaseTypeRes();
        Object userObj = getPrincipal(true);
        if (userObj instanceof PlatformUserEntity) {
            if (StringUtils.isNotEmpty(req.getDiseaseTypeName())) {
                DiseaseTypeEntity diseaseType = new DiseaseTypeEntity();
                diseaseType.setDiseaseTypeName(req.getDiseaseTypeName());
                diseaseType.setDiseaseTypeDetail(StringUtils.isNotEmpty(req.getDiseaseTypeDetail()) ? req.getDiseaseTypeDetail() : "");
                diseaseType.setAvailable(null != req.getAvailable() ? req.getAvailable() : 0);
                diseaseType.setNotifyPhone(StringUtils.isNotEmpty(req.getNotifyPhone()) ? req.getNotifyPhone() : "");
                if (StringUtils.isNotEmpty(req.getImageIdList())) {
                    String[] assetIds = req.getImageIdList().split(",");
                    int i = 0;
                    for (String assetId : assetIds) {
                        AssetEntity image = assetDao.get(Integer.parseInt(assetId));
                        image.setAssetOrders(i);
                        diseaseType.getAssetSet().add(image);
                        i++;
                    }
                }
                diseaseTypeDao.save(diseaseType);
            } else {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
            }
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } else {
            res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_INSUFFICIENT_INFO);
        }
        return res;
    }

    @Override
    public IResponse getDiseaseTypeList(DiseaseTypeReq req) {
        GetDiseaseTypeListRes res = new GetDiseaseTypeListRes();

        Page<DiseaseTypeEntity> page = new Page<>();
        page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
        page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
        page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

        StringBuilder hql = new StringBuilder("select distinct a from DiseaseTypeEntity a where 1=1 ");
        hql.append(StringUtils.isNotEmpty(req.getDiseaseTypeName()) ? " and a.diseaseTypeName='" + req.getDiseaseTypeName() +"'": "");
//        hql.append(" ORDER BY a.orders DESC ");
        page = diseaseTypeDao.queryPage(page, hql.toString());
        res.setTotalResult(page.getTotalCount());
        res.setPageSize(page.getPageSize());
        res.setCurrentPage(page.getCurrentPage());
        res.setTotal(page.getTotalPage());
        page.getResultList().forEach(o -> {
            DiseaseTypeForm diseaseTypeForm = new DiseaseTypeForm();
            diseaseTypeForm.setDiseaseTypeId(o.getId());
            diseaseTypeForm.setDiseaseTypeName(o.getDiseaseTypeName());
            diseaseTypeForm.setDiseaseTypeDetail(o.getDiseaseTypeDetail());
            diseaseTypeForm.setAvailable(o.getAvailable());
            diseaseTypeForm.setNotifyPhone(o.getNotifyPhone());
            if (null != o.getAssetSet()) {
                for (AssetEntity subAsset : o.getAssetSet()) {
                    diseaseTypeForm.getImageList().add(getAsset(subAsset));
                }
            }
            res.getDiseaseTypeFormList().add(diseaseTypeForm);
        });
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        return res;
    }


    @Override
    @Audit(operate = "修改病种")
    public IResponse modifyDiseaseType(DiseaseTypeReq req) {
        GenericResponse res = new GenericResponse();
        Object userObj = getPrincipal(true);
        if (userObj instanceof PlatformUserEntity) {
            if (null != req.getDiseaseTypeId()) {
                DiseaseTypeEntity diseaseType = diseaseTypeDao.get(req.getDiseaseTypeId());
                if (null != diseaseType) {
                    diseaseType.setDiseaseTypeName(StringUtils.isNotEmpty(req.getDiseaseTypeName()) ? req.getDiseaseTypeName() : diseaseType.getDiseaseTypeName());
                    diseaseType.setDiseaseTypeDetail(StringUtils.isNotEmpty(req.getDiseaseTypeDetail()) ? req.getDiseaseTypeDetail() :
                            diseaseType.getDiseaseTypeDetail());
                    diseaseType.setAvailable(null != req.getAvailable() ? req.getAvailable() : diseaseType.getAvailable());
                    diseaseType.setNotifyPhone(StringUtils.isNotEmpty(req.getNotifyPhone()) ? req.getNotifyPhone() : diseaseType.getNotifyPhone());
                    if (StringUtils.isNotEmpty(req.getImageIdList())) {
                        String[] assetIds = req.getImageIdList().split(",");
                        diseaseType.getAssetSet().clear();
                        int i = 0;
                        for (String assetId : assetIds) {
                            AssetEntity image = assetDao.get(Integer.parseInt(assetId));
                            image.setAssetOrders(i);
                            diseaseType.getAssetSet().add(image);
                            i++;
                        }
                    }
                    diseaseTypeDao.update(diseaseType);
                    res.setRet(ResponseContext.RES_SUCCESS_CODE);
                    res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
                } else {
                    res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                    res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
                }
            } else {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_INSUFFICIENT_INFO);
        }
        return res;
    }

    @Override
    @Audit(operate = "删除病种")
    public IResponse deleteDiseaseType(DiseaseTypeReq req) {
        GenericResponse res = new GenericResponse();
        Object userObj = getPrincipal(true);
        if (userObj instanceof PlatformUserEntity) {
            if (null != req.getDiseaseTypeId()) {
                DiseaseTypeEntity diseaseType = diseaseTypeDao.get(req.getDiseaseTypeId());
                if (null != diseaseType) {
                    diseaseTypeDao.delete(diseaseType);
                    res.setRet(ResponseContext.RES_SUCCESS_CODE);
                    res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
                } else {
                    res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                    res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
                }
            } else {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_INSUFFICIENT_INFO);
        }
        return res;
    }

    @SuppressWarnings("unused")
    @Override
    public IResponse getDiseaseTypeInfo(DiseaseTypeReq req) {
        GetDiseaseTypeInfoRes res = new GetDiseaseTypeInfoRes();
        DiseaseTypeForm diseaseTypeForm = new DiseaseTypeForm();

        if (null != req.getDiseaseTypeId()) {
            DiseaseTypeEntity diseaseType = diseaseTypeDao.get(req.getDiseaseTypeId());
            if (null != diseaseType) {
                diseaseTypeForm.setDiseaseTypeId(diseaseType.getId());
                diseaseTypeForm.setDiseaseTypeName(diseaseType.getDiseaseTypeName());
                diseaseTypeForm.setDiseaseTypeDetail(diseaseType.getDiseaseTypeDetail());
                diseaseTypeForm.setAvailable(diseaseType.getAvailable());
                diseaseTypeForm.setNotifyPhone(diseaseType.getNotifyPhone());
                if (null != diseaseType.getAssetSet()) {
                    for (AssetEntity subAsset : diseaseType.getAssetSet()) {
                        diseaseTypeForm.getImageList().add(getAsset(subAsset));
                    }
                }
                res.setDiseaseTypeForm(diseaseTypeForm);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;
    }


}
