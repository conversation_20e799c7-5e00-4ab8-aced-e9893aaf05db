package com.foshan.service.health;

import com.foshan.form.health.request.HealthReservationPresetPeriodReq;
import com.foshan.form.response.IResponse;

public interface IHealthReservationPresetPeriodService {
    public IResponse getHealthReservationPresetPeriodList(HealthReservationPresetPeriodReq req);
    public IResponse addHealthReservationPresetPeriod(HealthReservationPresetPeriodReq req);
    public IResponse modifyHealthReservationPresetPeriod(HealthReservationPresetPeriodReq req);
    public IResponse deleteHealthReservationPresetPeriod(HealthReservationPresetPeriodReq req);
    public IResponse getHealthReservationPresetPeriodInfo(HealthReservationPresetPeriodReq req);
}
