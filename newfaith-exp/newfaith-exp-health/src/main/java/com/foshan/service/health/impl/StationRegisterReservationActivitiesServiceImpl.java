package com.foshan.service.health.impl;

import com.foshan.dao.generic.Page;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.context.EntityContext;

import com.foshan.entity.health.*;

import com.foshan.form.health.HealthReservationPresetPeriodForm;
import com.foshan.form.health.HealthReservationStrategyForm;
import com.foshan.form.health.ServiceStationForm;
import com.foshan.form.health.StationRegisterReservationActivitiesForm;
import com.foshan.form.health.request.StationRegisterReservationActivitiesReq;

import com.foshan.form.health.response.healthReservationActivities.GetHealthReservationActivitiesInfo;
import com.foshan.form.health.response.healthReservationActivities.GetHealthReservationActivitiesListRes;
import com.foshan.form.health.response.healthReservationPresetPeriod.GetHealthReservationPresetPeriodListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.health.IStationRegisterReservationActivitiesService;
import com.foshan.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static java.util.Comparator.comparingInt;

@Transactional
@Service("stationRegisterReservationActivitiesService")
public class StationRegisterReservationActivitiesServiceImpl extends HealthReservationActivitiesServiceImpl implements IStationRegisterReservationActivitiesService {

    private final static Logger logger = LoggerFactory.getLogger(StationRegisterReservationActivitiesServiceImpl.class);

    @Override
    public IResponse getStationRegisterReservationActivitiesList(StationRegisterReservationActivitiesReq req) {
        GetHealthReservationActivitiesListRes res = new GetHealthReservationActivitiesListRes();
        Page<HealthReservationActivitiesEntity> page = new Page<HealthReservationActivitiesEntity>();
        page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
        page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
        page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
        //Object userObj = getPrincipal(true);
        StringBuilder hql =
                new StringBuilder("select distinct a from HealthReservationActivitiesEntity a where ").append(null != req.getState() ? " a" +
                                ".state=" + req.getState() :
                                " a.state=" + EntityContext.RECORD_STATE_VALID)
                        .append(null != req.getStationId() ? " and a.station.id=" + req.getStationId() : "")
                        .append(null != req.getStrategyId() ? " and a.strategy.id=" + req.getStrategyId() : "")
                        .append(StringUtils.isNotEmpty(req.getActivitiesName()) ?
                                " and a.activitiesName like '%" + req.getActivitiesName() + "%'" : "")
                        .append(" and a.reservationType= 8");
        hql.append(" ORDER BY a.orders ASC");
        page = healthReservationActivitiesDao.queryPage(page, hql.toString());

        res.setTotalResult(page.getTotalCount());
        res.setPageSize(page.getPageSize());
        res.setCurrentPage(page.getCurrentPage());
        res.setTotal(page.getTotalPage());
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        page.getResultList().forEach(o -> {

            StationRegisterReservationActivitiesForm activities = new StationRegisterReservationActivitiesForm(
                    o.getId(), o.getActivitiesName(), o.getContent(), o.getAgreement(),
                    sdf.format(o.getStartTime()), sdf.format(o.getEndTime()), o.getNeedConfirmAgreement(),
                    sdf.format(o.getCreateTime()), sdf.format(o.getLastModifyTime()), o.getState(), o.getOrders(), o.getReservationType());
            activities.setEmploy(o.getEmploy());
            HealthReservationStrategyForm strategy = new HealthReservationStrategyForm(o.getStrategy().getId(),
                    o.getStrategy().getStrategyName(), o.getStrategy().getPeriodType(), o.getStrategy().getSkipDay(),
                    o.getStrategy().getAutoExtend(), o.getStrategy().getLagDay(), o.getStrategy().getSkipDate());

            ServiceStationForm stationForm = new ServiceStationForm(o.getStation().getId(), o.getStation().getStationName(),
                    o.getStation().getStationDetail(), o.getStation().getAddress(), o.getStation().getLeaderName(),
                    o.getStation().getLeaderPhone());
            activities.setStationForm(stationForm);
            if (null != o.getAssetSet()) {
                for (AssetEntity subAsset : o.getAssetSet()) {
                    activities.getImageList().add(getAsset(subAsset));
                }
            }

            activities.setStrategyForm(strategy);
            res.getReservationActivitiesList().add(activities);
        });
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        return res;
    }

    @Override
    public IResponse getStationRegisterPresetPeriodListByActivities(StationRegisterReservationActivitiesReq req) {
        GetHealthReservationPresetPeriodListRes res = new GetHealthReservationPresetPeriodListRes();
        Page<HealthReservationPresetPeriodEntity> page = new Page<HealthReservationPresetPeriodEntity>();
        page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
        page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
        page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
        //Object userObj = getPrincipal(true);
        HealthReservationActivitiesEntity activities = healthReservationActivitiesDao.get(req.getActivitiesId());

        if(null == activities){
            res.setRet(ResponseContext.RES_DATA_NULL_CODE);
            res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
            return res;
        }

        res.setTotalResult(page.getTotalCount());
        res.setPageSize(page.getPageSize());
        res.setCurrentPage(page.getCurrentPage());
        res.setTotal(page.getTotalPage());
        activities.getStrategy().getPresetPeriodList().forEach(o -> {
            HealthReservationPresetPeriodForm reservationPresetPeriodForm = new HealthReservationPresetPeriodForm(o.getId(),
                    o.getMaxNum(), o.getOrders(), o.getStartTime(), o.getEndTime());

            res.getReservationPresetPeriodFormList().add(reservationPresetPeriodForm);
        });
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        return res;
    }

    @Audit(operate = "新增预约活动")
    @Override
    public IResponse addStationRegisterReservationActivities(StationRegisterReservationActivitiesReq req) {
        GenericResponse res = new GenericResponse();
        if (null != req.getStrategyId() &&
                StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime()) &&
                StringUtils.isNotEmpty(req.getActivitiesName()) && null != req.getStationId()) {
            if (null != req.getNeedConfirmAgreement() && req.getNeedConfirmAgreement() == 1 && StringUtils.isEmpty(req.getAgreement())) {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
                return res;
            } else if (null == req.getNeedConfirmAgreement()) {
                req.setNeedConfirmAgreement(0);
            }
            HealthReservationStrategyEntity strategy =
                    healthReservationStrategyDao.get(req.getStrategyId());

            if (null == strategy) {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
                return res;
            }


            ServiceStationEntity station = serviceStationDao.get(req.getStationId());
            if (null == station) {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + " 无此站点数据");
                return res;
            }

            String hql =
                    "from HealthReservationActivitiesEntity a where a.reservationType=8 and a.station.id=" + req.getStationId();
            HealthReservationActivitiesEntity activities = healthReservationActivitiesDao.getUniqueByHql(hql);
            if (null == activities) {

                activities = new HealthReservationActivitiesEntity();
                activities.setActivitiesName(req.getActivitiesName());
                activities.setAgreement(StringUtils.isNotEmpty(req.getAgreement()) ? req.getAgreement() : "");
                activities.setContent(StringUtils.isNotEmpty(req.getContent()) ? req.getContent() : "");
                activities.setEndTime(Timestamp.valueOf(req.getEndTime()));
                activities.setNeedConfirmAgreement(req.getNeedConfirmAgreement());
                activities.setOrders(null != req.getOrders() ? req.getOrders() : 100);
                activities.setStartTime(Timestamp.valueOf(req.getStartTime()));
                activities.setStrategy(strategy);
                activities.setLastModifyTime(new Timestamp(new Date().getTime()));
                activities.setEmploy(null != req.getEmploy() ? req.getEmploy() : 0);
                activities.setReservationType(8);
                activities.setStation(station);
                activities.setState(EntityContext.RECORD_STATE_VALID);

                if (StringUtils.isNotEmpty(req.getImageIdList())) {
                    String[] assetIds = req.getImageIdList().split(",");
                    int i = 0;
                    for (String assetId : assetIds) {
                        AssetEntity image = assetDao.get(Integer.parseInt(assetId));
                        image.setAssetOrders(i);
                        activities.getAssetSet().add(image);
                        i++;
                    }
                }

                healthReservationActivitiesDao.save(activities);
            } else {
                activities.setState(EntityContext.RECORD_STATE_VALID);
            }
            generateReservationDate(activities, req.getDoctorMap());


            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }

        return res;
    }


    @Audit(operate = "修改预约活动")
    @Override
    public IResponse modifyStationRegisterReservationActivities(StationRegisterReservationActivitiesReq req) {
        GenericResponse res = new GenericResponse();
        if (null != req.getActivitiesId()) {
            if (null != req.getNeedConfirmAgreement() && req.getNeedConfirmAgreement() == 1 && StringUtils.isEmpty(req.getAgreement())) {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
                return res;
            } else if (null == req.getNeedConfirmAgreement()) {
                req.setNeedConfirmAgreement(0);
            }

            HealthReservationActivitiesEntity activities = healthReservationActivitiesDao.get(req.getActivitiesId());
            HealthReservationStrategyEntity strategy = null;
            if (null != req.getStrategyId()) {
                strategy =
                        healthReservationStrategyDao.get(req.getStrategyId());
                if (null == strategy) {
                    res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
                    res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
                    return res;
                }
            }

            if (null == activities) {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
                return res;
            }
            if (null != req.getStationId()) {
                ServiceStationEntity station = serviceStationDao.get(req.getStationId());
                if (null == station) {
                    res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                    res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + " 无此站点数据");
                    return res;
                } else {
                    activities.setStation(station);
                }
            }


            activities.setActivitiesName(StringUtils.isNotEmpty(req.getActivitiesName()) ? req.getActivitiesName() : activities.getActivitiesName());
            activities.setAgreement(StringUtils.isNotEmpty(req.getAgreement()) ? req.getAgreement() : activities.getAgreement());
            activities.setContent(StringUtils.isNotEmpty(req.getContent()) ? req.getContent() : activities.getContent());
            activities.setEndTime(StringUtils.isNotEmpty(req.getEndTime()) ? Timestamp.valueOf(req.getEndTime()) : activities.getEndTime());
            activities.setNeedConfirmAgreement(null != req.getNeedConfirmAgreement() ? req.getNeedConfirmAgreement() :
                    activities.getNeedConfirmAgreement());
            activities.setOrders(null != req.getOrders() ? req.getOrders() : activities.getOrders());
            activities.setStartTime(StringUtils.isNotEmpty(req.getStartTime()) ? Timestamp.valueOf(req.getStartTime()) : activities.getStartTime());
            activities.setStrategy(null != strategy ? strategy : activities.getStrategy());
            activities.setEmploy(null != req.getEmploy() ? req.getEmploy() : activities.getEmploy());
            activities.setLastModifyTime(new Timestamp(new Date().getTime()));


            if (StringUtils.isNotEmpty(req.getImageIdList())) {
                String[] assetIds = req.getImageIdList().split(",");
                activities.getAssetSet().clear();
                int i = 0;
                for (String assetId : assetIds) {
                    AssetEntity image = assetDao.get(Integer.parseInt(assetId));
                    image.setAssetOrders(i);
                    activities.getAssetSet().add(image);
                    i++;
                }
            }


            healthReservationActivitiesDao.update(activities);
            generateReservationDate(activities, req.getDoctorMap());

            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;
    }

    @Audit(operate = "删除预约活动")
    @Override
    public IResponse deleteStationRegisterReservationActivities(StationRegisterReservationActivitiesReq req) {
        GenericResponse res = new GenericResponse();
        if (null != req.getActivitiesId()) {
            HealthReservationActivitiesEntity activities = healthReservationActivitiesDao.get(req.getActivitiesId());
            if (null != activities) {
                activities.setState(EntityContext.RECORD_STATE_INVALID);
                activities.getReservationDateList().forEach(o -> {
                    o.setState(EntityContext.RECORD_STATE_INVALID);
                    o.getPeriodList().forEach(p -> {
                        p.setState(EntityContext.RECORD_STATE_INVALID);
                    });
                });
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }

        return res;
    }

    @Override
    public IResponse getStationRegisterReservationActivitiesInfo(StationRegisterReservationActivitiesReq req) {
        GetHealthReservationActivitiesInfo res = new GetHealthReservationActivitiesInfo();
        if (null != req.getActivitiesId()) {

            HealthReservationActivitiesEntity activities = healthReservationActivitiesDao.get(req.getActivitiesId());
            if (null != activities) {
                DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


                StationRegisterReservationActivitiesForm activitiesForm = new StationRegisterReservationActivitiesForm(
                        activities.getId(), activities.getActivitiesName(), activities.getContent(), activities.getAgreement(),
                        sdf.format(activities.getStartTime()), sdf.format(activities.getEndTime()), activities.getNeedConfirmAgreement(),
                        sdf.format(activities.getCreateTime()), sdf.format(activities.getLastModifyTime()), activities.getState(),
                        activities.getOrders(), activities.getReservationType());
                HealthReservationStrategyForm strategy = new HealthReservationStrategyForm(activities.getStrategy().getId(),
                        activities.getStrategy().getStrategyName(), activities.getStrategy().getPeriodType(), activities.getStrategy().getSkipDay(),
                        activities.getStrategy().getAutoExtend(), activities.getStrategy().getLagDay(), activities.getStrategy().getSkipDate());
                activitiesForm.setEmploy(activities.getEmploy());
                activitiesForm.setStrategyForm(strategy);

                ServiceStationForm stationForm = new ServiceStationForm(activities.getStation().getId(), activities.getStation().getStationName(),
                        activities.getStation().getStationDetail(), activities.getStation().getAddress(), activities.getStation().getLeaderName(),
                        activities.getStation().getLeaderPhone());
                activitiesForm.setStationForm(stationForm);

                if (null != activities.getAssetSet()) {
                    for (AssetEntity subAsset : activities.getAssetSet()) {
                        activitiesForm.getImageList().add(getAsset(subAsset));
                    }
                }

                Map doctorMap = new HashMap();

                activities.getReservationDateList().forEach(o -> {
//                    String doctorIds = "";
                    List temp = new ArrayList();
                    if (o.getIsStart() == EntityContext.RECORD_STATE_VALID) {
                        //如果是今天以前的isStart设0然后跳过
                        Date today = new Date();
                        try {
                            today = DateUtil.parse(DateUtil.format(today, 0), 0); // Get start of today
                            if (o.getReservationDate().before(today)) {
                                o.setIsStart(EntityContext.RECORD_STATE_INVALID);
                                healthReservationDateDao.update(o);
                            } else {
                                for (HealthReservationPeriodEntity period : healthReservationPeriodDao.getListByHql(
                                        "select distinct a from HealthReservationPeriodEntity a where a.reservationDate.id=" + o.getId() + " and a" +
                                                ".stationDoctor " +
                                                "is not null and a.state=" + EntityContext.RECORD_STATE_VALID + (null != req.getPresetPeriodId() ?
                                                " and a" +
                                                ".presetPeriodId=" + req.getPresetPeriodId() : "") + " order by a.presetPeriodId asc")) {
//                        doctorIds += period.getStationDoctor().getId() +";"+period.getStationDoctor().getName()+ ",";

                                    Date date = null;
                                    if (StringUtils.isNotEmpty(req.getReservationDate())) {
                                        try {
                                            date = DateUtil.parse(req.getReservationDate(), 0);
                                        } catch (ParseException e) {
                                            logger.error(e.getMessage());
                                        }
                                        if (period.getReservationDate().getReservationDate().equals(date)) {
                                            Map tempDoctor = new HashMap();
                                            tempDoctor.put("periodId", period.getId());
                                            tempDoctor.put("id", period.getStationDoctor().getId());
                                            tempDoctor.put("name", period.getStationDoctor().getName());
                                            tempDoctor.put("overPlusNum", period.getOverplusNum());
                                            tempDoctor.put("presetPeriodId", period.getPresetPeriodId());
                                            tempDoctor.put("startTime", period.getStartTime());
                                            tempDoctor.put("endTime", period.getEndTime());
                                            temp.add(tempDoctor);
                                        }
                                    } else {
                                        Map tempDoctor = new HashMap();
                                        tempDoctor.put("periodId", period.getId());
                                        tempDoctor.put("id", period.getStationDoctor().getId());
                                        tempDoctor.put("name", period.getStationDoctor().getName());
                                        tempDoctor.put("overPlusNum", period.getOverplusNum());
                                        tempDoctor.put("presetPeriodId", period.getPresetPeriodId());
                                        tempDoctor.put("startTime", period.getStartTime());
                                        tempDoctor.put("endTime", period.getEndTime());
                                        temp.add(tempDoctor);
                                    }

                                }
                                if (temp.size() > 0) {
                                    doctorMap.put(new SimpleDateFormat("yyyy-MM-dd").format(o.getReservationDate()), temp);
                                }
                            }
                    }catch (Exception e){
                        logger.error(e.getMessage());
                    }}
                });
                activitiesForm.setDoctorMap(doctorMap);

                res.setHealthReservationActivitiesForm(activitiesForm);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

            } else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }

        return res;
    }

    public void generateReservationDate(HealthReservationActivitiesEntity activities, Map<String, String> doctorMap) {
        if (activities.getReservationDateList().size() > 0) {
            for (HealthReservationDateEntity reservationDate : activities.getReservationDateList()) {
                if (0 < differentDaysByMillisecond(reservationDate.getReservationDate(), new Date())) {
                    reservationDate.setState(EntityContext.RECORD_STATE_INVALID);
                    reservationDate.setIsStart(EntityContext.RECORD_STATE_INVALID);
                    reservationDate.getPeriodList().forEach(o -> {
                        o.setState(EntityContext.RECORD_STATE_INVALID);
                    });
                }
            }
        }
//        if (activities.getStrategy().getPeriodType() == 0) {
        activities.getStrategy().getPresetPeriodList().sort(comparingInt(HealthReservationPresetPeriodEntity::getOrders));
        try {
            //设置旧的医生预约为无效
            healthReservationDateDao.executeUpdate("update HealthReservationDateEntity a set a.state=" + EntityContext.RECORD_STATE_INVALID +
                    " where a.activities.id=" + activities.getId());
            List<HealthReservationPeriodEntity> oldPeriodList = healthReservationPeriodDao.getListByHql("from HealthReservationPeriodEntity a" +
                    " where (a.stationDoctor is not null) and a.reservationDate.activities.id=" + activities.getId());
            oldPeriodList.forEach(o -> {
                o.setState(EntityContext.RECORD_STATE_INVALID);
                healthReservationPeriodDao.update(o);
            });


            for (Map.Entry<String, String> vo : doctorMap.entrySet()) {
                Date date = DateUtil.parse(vo.getKey(), 0);
                String hql = "from HealthReservationDateEntity a where a.activities.id=" + activities.getId() +
                        " and a.reservationDate='" + DateUtil.format(date, 0) + "'";
                HealthReservationDateEntity reservationDate = healthReservationDateDao.getUniqueByHql(hql);
                if (null != reservationDate) {
                    reservationDate.setState(EntityContext.RECORD_STATE_VALID);

//                    for (HealthReservationPresetPeriodEntity o : activities.getStrategy().getPresetPeriodList()) {
                        for (String doctorId : doctorMap.get(vo.getKey()).split(",")) {
                            String presetId = doctorId.split("\\|")[0];

                            HealthReservationPresetPeriodEntity o = healthReservationPresetPeriodDao.get(Integer.parseInt(presetId));
                            if(activities.getStrategy().getPresetPeriodList().contains(o)){

//                            if (presetId.equals(o.getId())) {

                                doctorId = doctorId.split("\\|")[1];
                                StationDoctorEntity stationDoctor = stationDoctorDao.get(Integer.parseInt(doctorId));
//                                if (null != stationDoctor){
//                                    continue;
//                                }

                                //取消旧的医生预约
//                                healthReservationPeriodDao.executeUpdate("update HealthReservationPeriodEntity a set a.state="+EntityContext
//                                .RECORD_STATE_INVALID+
//                                        " where a.reservationDate.id=" + reservationDate.getId() +
//                                        " and a.presetPeriodId='" + o.getId() + "'"+" and a.stationDoctor is not null and  a.stationDoctor.id not
//                                        " +
//                                        "in ("+doctorMap.get(vo.getKey())+")");


                                HealthReservationPeriodEntity period = healthReservationPeriodDao.findUnique(
                                        " select distinct a  from HealthReservationPeriodEntity a where a.reservationDate.id=" + reservationDate.getId() +
                                                " and a.presetPeriodId='" + o.getId() + "'" + " and a.stationDoctor.id=" + doctorId);
                                if (null != period) {
                                    period.setState(EntityContext.RECORD_STATE_VALID);
                                    healthReservationPeriodDao.update(period);
                                } else {
                                    period = new HealthReservationPeriodEntity();
                                    period.setEndTime(o.getEndTime());
                                    period.setMaxNum(o.getMaxNum());
                                    period.setOverplusNum(o.getMaxNum());
                                    period.setPresetPeriodId(o.getId());
                                    period.setReservationDate(reservationDate);
                                    period.setStartTime(o.getStartTime());
                                    period.setState(EntityContext.RECORD_STATE_VALID);
                                    period.setOrders(o.getOrders());
                                    period.setStationDoctor(stationDoctor);
                                    healthReservationPeriodDao.save(period);
                                    reservationDate.getPeriodList().add(period);
                                }
                            }
                        }
//                    }


                } else {
                    reservationDate = new HealthReservationDateEntity();
                    reservationDate.setActivities(activities);
                    reservationDate.setIsStart(1);
                    reservationDate.setReservationDate(new java.sql.Date(date.getTime()));
                    reservationDate.setState(EntityContext.RECORD_STATE_VALID);

                    healthReservationDateDao.save(reservationDate);
                    activities.getReservationDateList().add(reservationDate);
                    healthReservationActivitiesDao.update(activities);
//                    for (HealthReservationPresetPeriodEntity o : activities.getStrategy().getPresetPeriodList()) {
                        for (String doctorId : doctorMap.get(vo.getKey()).split(",")) {
                            String presetId = doctorId.split("\\|")[0];

                            HealthReservationPresetPeriodEntity o = healthReservationPresetPeriodDao.get(Integer.parseInt(presetId));
                            if(activities.getStrategy().getPresetPeriodList().contains(o)) {
                                doctorId = doctorId.split("\\|")[1];
                                StationDoctorEntity stationDoctor = stationDoctorDao.get(Integer.parseInt(doctorId));
                                if (null == stationDoctor) {
                                    continue;
                                }
                                HealthReservationPeriodEntity period = new HealthReservationPeriodEntity();
                                period.setEndTime(o.getEndTime());
                                period.setMaxNum(o.getMaxNum());
                                period.setOverplusNum(o.getMaxNum());
                                period.setPresetPeriodId(o.getId());
                                period.setReservationDate(reservationDate);
                                period.setStartTime(o.getStartTime());
                                period.setOrders(o.getOrders());
                                period.setStationDoctor(stationDoctor);
                                period.setState(EntityContext.RECORD_STATE_VALID);
                                healthReservationPeriodDao.saveOrUpdate(period);
                                //reservationDate.getPeriodList().add(period);
                            }
                        }
//                    }
                }
            }
        } catch (Exception e) {
            logger.error("生成预约日期异常", e);
            logger.error("生成预约日期异常,id:" + activities.getId());
        }
    }

}
