package com.foshan.form.health.response.healthReservationRecord;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取预约统计返回列表对象(GetHealthReservationRecordStatisticsRes)")
@JsonInclude(Include.NON_NULL)
public class GetHealthReservationRecordStatisticsRes extends BaseResponse {
	private static final long serialVersionUID = 5843549289233242176L;
	/**
	 * 
	 */

	@ApiModelProperty(value = "预约对象列表")
	private Map reservationStatisticsMap = new HashMap();

}
