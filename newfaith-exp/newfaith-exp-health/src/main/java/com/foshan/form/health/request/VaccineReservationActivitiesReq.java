package com.foshan.form.health.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="疫苗请求参数(VaccineReservationActivitiesReq)")
public class VaccineReservationActivitiesReq extends HealthReservationActivitiesReq {
	private static final long serialVersionUID = -5551963531084808939L;
	/**
	 * 
	 */

	@ApiModelProperty(value = "疫苗种类 0-儿童 1-成人", example = "1")
	private Integer isAdult;

	@Override
	public String transformAuditInfo(){
		StringBuffer sb = new StringBuffer(null != this.getActivitiesId() ? "活动id:" + this.getActivitiesId():"新建活动");
		sb.append(StringUtils.isNotEmpty(this.getActivitiesName()) ? "活动名称:" + this.getActivitiesName():"");
		sb.append(StringUtils.isNotEmpty(this.getContent()) ? ",内容:" + this.getContent():"");
		sb.append(StringUtils.isNotEmpty(this.getAgreement()) ? ",协议:" + this.getAgreement():"");
		sb.append(null != this.getNeedConfirmAgreement() ? ",需要确认协议:" + this.getNeedConfirmAgreement():"");
		sb.append(StringUtils.isNotEmpty(startTime) ? ",开始时间:" + startTime:"");
		sb.append(StringUtils.isNotEmpty(endTime) ? ",结束时间:" + endTime:"");
		sb.append(null != this.getStrategyId() ? ",策略ID:" + this.getStrategyId():"");
		sb.append(null != this.getEmploy() ? ",是否生效:" + this.getEmploy():"");
		sb.append(null != this.getStrategyId() ? ",状态:" + this.getState():"");
		sb.append(null != this.getOrders() ? ",排序值:" + this.getOrders():"");
		sb.append(null != this.getIsAdult() ? ",疫苗种类:" + this.getIsAdult():"");
		return sb.toString();
	}
}
