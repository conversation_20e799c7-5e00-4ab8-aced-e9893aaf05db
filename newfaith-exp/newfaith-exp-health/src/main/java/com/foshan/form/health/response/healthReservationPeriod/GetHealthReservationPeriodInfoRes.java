package com.foshan.form.health.response.healthReservationPeriod;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.health.HealthReservationPeriodForm;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取预约时间段返回对象(GetHealthReservationPeriodInfoRes)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetHealthReservationPeriodInfoRes extends BaseResponse {

    @ApiModelProperty(value = "预约时间段信息",example="1")
    private HealthReservationPeriodForm periodForm;
}
