package com.foshan.form.health;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="疫苗对象(VaccineReservationActivitiesForm)")
@JsonInclude(Include.NON_NULL)
public  class VaccineReservationActivitiesForm extends HealthReservationActivitiesForm{
	private static final long serialVersionUID = -6064913056614739866L;
	/**
	 *
	 */



	@ApiModelProperty(value = "疫苗种类", example = "1")
	private Integer isAdult;

	public VaccineReservationActivitiesForm(Integer activitiesId, String activitiesName, String content, String agreement,
										   String startTime, String endTime, Integer needConfirmAgreement, String createTime, String lastModifyTime
			, Integer state, Integer orders,Integer reservationType,Integer isAdult) {
		this.activitiesId = activitiesId;
		this.activitiesName = activitiesName;
		this.content = content;
		this.agreement = agreement;
		this.startTime = startTime;
		this.endTime = endTime;
		this.needConfirmAgreement = needConfirmAgreement;
		this.createTime = createTime;
		this.lastModifyTime = lastModifyTime;
		this.state = state;
		this.orders = orders;
		this.reservationType = reservationType;
		this.isAdult = isAdult;
	}


}
