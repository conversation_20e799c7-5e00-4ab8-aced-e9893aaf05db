package com.foshan.form.health.response.healthMember;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取小程序码Base返回对象(GetWxCodeBaseRes)")
@JsonInclude(Include.NON_NULL)
public class GetWxCodeBaseRes extends BaseResponse {
	private static final long serialVersionUID = -2735138995936157534L;
	/**
	 * 
	 */
	@ApiModelProperty(value = "用户对象列表")
	private String base64Code;
}
