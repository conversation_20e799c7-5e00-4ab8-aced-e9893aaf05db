package com.foshan.form.health.request;

import com.foshan.form.request.BasePageRequest;
import com.foshan.form.request.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="提示语请求参数(NotifyReq)")
public class NotifyReq extends BasePageRequest {

	private static final long serialVersionUID = 8646408535935577842L;
	/**
	 *
	 */
	@ApiModelProperty(value = "提示语id", example = "1")
	private Integer notifyId;
	@ApiModelProperty(value = "模板id", example = "1")
	private Integer templateId;
	@ApiModelProperty(value = "提示语名称", example = "1")
	private String notifyName;
	@ApiModelProperty(value = "提示语内容", example = "1")
	private String notifyContent;
	@ApiModelProperty(value = "填入内容", example = "1")
	private String fillContent;
	@ApiModelProperty(value = "上下架状态 0-下架 1-上架", example = "1")
	private Integer state;



}
