package com.foshan.form.health.response.familyDoctor;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.health.FamilyDoctorForm;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取家庭医生详情返回对象(GetFamilyDoctorInfoRes)")
@JsonInclude(Include.NON_NULL)
public class GetFamilyDoctorInfoRes extends BaseResponse {

	private static final long serialVersionUID = 2533840455236162490L;
	/**
	 * 
	 */

	@ApiModelProperty(value = "家庭医生信息",example="1")
	private FamilyDoctorForm familyDoctor;


	public GetFamilyDoctorInfoRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO 自动生成的构造函数存根
	}


}
