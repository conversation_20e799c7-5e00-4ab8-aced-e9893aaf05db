package com.foshan.form.health.response.diseaseType;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(value = "病种返回对象(DiseaseTypeRes)")
@JsonInclude(Include.NON_NULL)
@Getter
@Setter
public class DiseaseTypeRes extends BaseResponse{

	private static final long serialVersionUID = -9084848035510760252L;
	/**
	 * 病种返回对象
	 */

	@ApiModelProperty(value = "病种名称", example = "1")
	private String diseaseTypeName;
	@ApiModelProperty(value = "病种详情", example = "1")
	private String diseaseTypeDetail;

	@ApiModelProperty(value = "图片ID", example = "1")
	private String imageIdList;

}
