package com.foshan.form.health;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="社区经理对象(CommunityManagerForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityManagerForm implements IForm {

	private static final long serialVersionUID = -4542995715927416018L;
	/**
	 * 
	 */
	@ApiModelProperty(value = "社区经理id", example = "1")
	private Integer communityManagerId;
	@ApiModelProperty(value = "长者名称")
	private String name;
	@ApiModelProperty(value = "发展码")
	private String referralCode;

	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
