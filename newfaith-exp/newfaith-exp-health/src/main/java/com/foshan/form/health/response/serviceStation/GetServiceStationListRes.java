package com.foshan.form.health.response.serviceStation;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.health.ServiceStationForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取站点返回列表对象(GetServiceStationListRes)")
@JsonInclude(Include.NON_NULL)
public class GetServiceStationListRes extends BasePageResponse {
	private static final long serialVersionUID = -861961690070985285L;
	/**
	 * 
	 */

	@ApiModelProperty(value = "站点对象列表")
	private List<ServiceStationForm> serviceStationFormList = new ArrayList<ServiceStationForm>();

}
