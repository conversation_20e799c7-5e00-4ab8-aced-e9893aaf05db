package com.foshan.form.health.response.healthMember;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取历史手机返回对象(GetHistoryPhoneRes)")
@JsonInclude(Include.NON_NULL)
public class GetHistoryPhoneRes extends BaseResponse {

	private static final long serialVersionUID = 8462092703375651271L;
	/**
	 *
	 */

	@ApiModelProperty(value = "手机号列表",example="1")
	private List<String> phoneList;


	public GetHistoryPhoneRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO 自动生成的构造函数存根
	}


}
