package com.foshan.form.health;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="站点挂号预约活动对象(StationRegisterReservationActivitiesForm)")
@JsonInclude(Include.NON_NULL)
public  class StationRegisterReservationActivitiesForm extends HealthReservationActivitiesForm{

	private static final long serialVersionUID = -314959467848703123L;
	/**
	 *
	 */

	Map doctorMap = new HashMap();

	public StationRegisterReservationActivitiesForm(Integer activitiesId, String activitiesName, String content, String agreement,
                                                    String startTime, String endTime, Integer needConfirmAgreement, String createTime, String lastModifyTime
			, Integer state, Integer orders, Integer reservationType ) {
		this.activitiesId = activitiesId;
		this.activitiesName = activitiesName;
		this.content = content;
		this.agreement = agreement;
		this.startTime = startTime;
		this.endTime = endTime;
		this.needConfirmAgreement = needConfirmAgreement;
		this.createTime = createTime;
		this.lastModifyTime = lastModifyTime;
		this.state = state;
		this.orders = orders;
		this.reservationType = reservationType;
	}


}
