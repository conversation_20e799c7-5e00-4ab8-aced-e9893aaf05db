package com.foshan.form.health.response.notifyTemplate;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.health.NotifyTemplateForm;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取通知详情返回对象(GetNotifyTemplateInfoRes)")
@JsonInclude(Include.NON_NULL)
public class GetNotifyTemplateInfoRes extends BaseResponse {

	/**
	 * 
	 */

	@ApiModelProperty(value = "通知信息",example="1")
	private NotifyTemplateForm notifyTemplate;



}
