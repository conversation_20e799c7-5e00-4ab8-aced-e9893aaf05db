package com.foshan.form.health.response.diseaseType;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.health.DiseaseTypeForm;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取病种详情返回对象(GetDiseaseTypeInfoRes)")
@JsonInclude(Include.NON_NULL)
public class GetDiseaseTypeInfoRes extends BaseResponse {

	private static final long serialVersionUID = 8259311596834214913L;
	/**
	 * 
	 */

	@ApiModelProperty(value = "病种信息",example="1")
	private DiseaseTypeForm diseaseTypeForm;


	public GetDiseaseTypeInfoRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO 自动生成的构造函数存根
	}


}
