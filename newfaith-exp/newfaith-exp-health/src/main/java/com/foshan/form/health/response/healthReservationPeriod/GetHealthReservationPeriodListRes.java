package com.foshan.form.health.response.healthReservationPeriod;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.health.HealthReservationPeriodForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取预约预设时间段返回列表对象(GetHealthReservationPresetPeriodListRes)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetHealthReservationPeriodListRes extends BasePageResponse {
    /**
     *
     */
    @ApiModelProperty(value = "预约策略对象列表")
    private List<HealthReservationPeriodForm> reservationPeriodFormList = new ArrayList<HealthReservationPeriodForm>();
}
