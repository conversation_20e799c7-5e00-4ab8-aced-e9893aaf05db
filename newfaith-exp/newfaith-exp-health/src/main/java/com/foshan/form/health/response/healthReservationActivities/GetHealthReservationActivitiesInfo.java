package com.foshan.form.health.response.healthReservationActivities;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.health.HealthReservationActivitiesForm;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获活动详情对象(GetHealthReservationActivitiesInfo)")
@JsonInclude(Include.NON_NULL)
public class GetHealthReservationActivitiesInfo extends GenericResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1372762541789093461L;
	@ApiModelProperty(value = "活动对象列表")
	private HealthReservationActivitiesForm healthReservationActivitiesForm ; 


}
