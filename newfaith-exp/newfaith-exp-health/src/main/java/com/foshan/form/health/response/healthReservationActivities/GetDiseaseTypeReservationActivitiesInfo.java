package com.foshan.form.health.response.healthReservationActivities;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.health.DiseaseTypeReservationActivitiesForm;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="病种预约活动对象详情(GetDiseaseTypeReservationActivitiesInfo)")
@JsonInclude(Include.NON_NULL)
public class GetDiseaseTypeReservationActivitiesInfo extends BaseResponse {

	private static final long serialVersionUID = 2970555614693733094L;
	/**
	 * 
	 */
	
	@ApiModelProperty(value = "病种预约活动对象列表")
	private DiseaseTypeReservationActivitiesForm diseaseTypeReservationActivitiesForm ;


}
