package com.foshan.form.health.response.stationDoctor;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.health.StationDoctorForm;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取站点医生详情返回对象(GetStationDoctorInfoRes)")
@JsonInclude(Include.NON_NULL)
public class GetStationDoctorInfoRes extends BaseResponse {

	private static final long serialVersionUID = 8183611297026000172L;
	/**
	 *
	 */

	@ApiModelProperty(value = "站点医生信息",example="1")
	private StationDoctorForm stationDoctor;


	public GetStationDoctorInfoRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO 自动生成的构造函数存根
	}


}
