package com.foshan.form.health.response.diseaseType;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.health.DiseaseTypeForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取病种返回列表对象(GetDiseaseTypeListRes)")
@JsonInclude(Include.NON_NULL)
public class GetDiseaseTypeListRes extends BasePageResponse {
	private static final long serialVersionUID = -861961690070985285L;
	/**
	 * 
	 */

	@ApiModelProperty(value = "用户对象列表")
	private List<DiseaseTypeForm> diseaseTypeFormList = new ArrayList<DiseaseTypeForm>();

}
