package com.foshan.form.health;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="通知对象(NotifyTemplateForm)")
@JsonInclude(Include.NON_NULL)
public  class NotifyTemplateForm implements IForm {

	/**
	 *
	 */
	@ApiModelProperty(value = "通知Id", example = "1")
	private Integer notifyTemplateId;
	@ApiModelProperty(value = "通知名称", example = "1")
	private String notifyTemplateName;
	@ApiModelProperty(value = "通知内容", example = "1")
	private String notifyTemplateContent;

	@ApiModelProperty(value = "通知状态", example = "1")
	private Integer state;





	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
