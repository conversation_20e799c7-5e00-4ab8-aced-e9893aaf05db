package com.foshan.form.health.response.healthReservationStrategy;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.health.HealthReservationStrategyForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取预约策略返回列表对象(GetHealthReservationStrategyListRes)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetHealthReservationStrategyListRes extends BasePageResponse {
    /**
     *
     */
    @ApiModelProperty(value = "预约策略对象列表")
    private List<HealthReservationStrategyForm> reservationStrategyFormList = new ArrayList<HealthReservationStrategyForm>();

}
