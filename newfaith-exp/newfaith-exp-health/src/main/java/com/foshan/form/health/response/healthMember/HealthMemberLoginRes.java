package com.foshan.form.health.response.healthMember;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.health.HealthRoleForm;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@ApiModel(value = "医疗会员登录返回对象(MemberLoginRes)")
@JsonInclude(Include.NON_NULL)
@Getter
@Setter
public class HealthMemberLoginRes extends BaseResponse{
	
	/**
	 * 党员登陆返回对象
	 */
	private static final long serialVersionUID = -3685317499363264149L;
	@ApiModelProperty(value = "会员Id",example="1")
	private Integer memberId;
	@ApiModelProperty(value = "会员电话")
	private String phone;
	@ApiModelProperty(value = "微信")
	private String weixin;
	@ApiModelProperty(value = "CA卡号")
	private String smartcardId;
	@ApiModelProperty(value = "帐号")
	private String loginName;
	@ApiModelProperty(value = "码是否为空标志：0-非空，1空",example="1")
	private Integer isEmptyPassword;//密码是否为空标志：0-非空，1空
	@ApiModelProperty(value = "区域码")
	private String regionCode;
	@ApiModelProperty(value = "公司ID",example="1")
	private Integer companyId;
	@ApiModelProperty(value = "角色名")
	private String roleName;
	@ApiModelProperty(value = "用户真实姓名")
	private String userName;
	@ApiModelProperty(value = "角色对象列表")
	private List<HealthRoleForm> educationRoleList = new ArrayList<HealthRoleForm>();

}
