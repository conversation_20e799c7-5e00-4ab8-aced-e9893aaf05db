package com.foshan.form.health.response.FamilyDoctorTeam;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.health.FamilyDoctorTeamForm;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取家庭医生团队详情返回对象(GetFamilyDoctorTeamInfoRes)")
@JsonInclude(Include.NON_NULL)
public class GetFamilyDoctorTeamInfoRes extends BaseResponse {

	private static final long serialVersionUID = -6776894350779408434L;
	/**
	 *
	 */

	@ApiModelProperty(value = "家庭医生团队信息",example="1")
	private FamilyDoctorTeamForm familyDoctorTeam;


	public GetFamilyDoctorTeamInfoRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO 自动生成的构造函数存根
	}


}
