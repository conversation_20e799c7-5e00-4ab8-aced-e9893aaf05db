package com.foshan.controller.health;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.aop.health.ReservationNotify;
import com.foshan.form.health.request.ServiceStationReq;
import com.foshan.form.health.response.serviceStation.GetServiceStationInfoRes;
import com.foshan.form.health.response.serviceStation.GetServiceStationListRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "站点模块")
@RequestMapping()
@RestController
public class ServiceStationController extends BaseHealthController {
	

	// 获取站点列表
	@ApiOperation(value = "获取站点列表(getServiceStationList)", httpMethod = "POST", notes = "获取站点列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getServiceStationList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetServiceStationListRes getServiceStationList(@RequestBody ServiceStationReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetServiceStationListRes res = (GetServiceStationListRes) serviceStationService.getServiceStationList(req);
		return res;
	}
	
	
	@ApiOperation(value = "新增站点(addServiceStation)", httpMethod = "POST", notes = "新增站点；")
	@ResponseBody
	@RequestMapping(value = "/addServiceStation", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addServiceStation(@RequestBody ServiceStationReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) serviceStationService.addServiceStation(req);
		return res;
	}

	
	// 修改站点
	@ApiOperation(value = "修改站点(modifyServiceStation)", httpMethod = "POST", notes = "修改站点，ServiceStationId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyServiceStation", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyServiceStation(@RequestBody ServiceStationReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) serviceStationService.modifyServiceStation(req);
		return res;
	}


	// 删除站点
	@ApiOperation(value = "删除站点(deleteServiceStation)", httpMethod = "POST", notes = "删除站点，ServiceStationId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteServiceStation", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteServiceStation(@RequestBody ServiceStationReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) serviceStationService.deleteServiceStation(req);
		return res;
	}

	// 获取详情站点
	@ApiOperation(value = "获取详情站点(getServiceStationInfo)", httpMethod = "POST", notes = "获取详情站点，ServiceStationId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getServiceStationInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetServiceStationInfoRes getServiceStationInfo(@RequestBody ServiceStationReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetServiceStationInfoRes res = (GetServiceStationInfoRes) serviceStationService.getServiceStationInfo(req);
		return res;
	}


}
