package com.foshan.controller.health;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.health.request.FamilyDoctorReq;
import com.foshan.form.health.response.familyDoctor.AddFamilyDoctorRes;
import com.foshan.form.health.response.familyDoctor.GetFamilyDoctorInfoRes;
import com.foshan.form.health.response.familyDoctor.GetFamilyDoctorListRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "家庭医生模块")
@RequestMapping()
@RestController
public class FamilyDoctorController extends BaseHealthController {
	

	// 获取家庭医生列表
	@ApiOperation(value = "获取家庭医生列表(getFamilyDoctorList)", httpMethod = "POST", notes = "获取家庭医生列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getFamilyDoctorList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetFamilyDoctorListRes getFamilyDoctorList(@RequestBody FamilyDoctorReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetFamilyDoctorListRes res = (GetFamilyDoctorListRes) familyDoctorService.getFamilyDoctorList(req);
		return res;
	}
	
	
	@ApiOperation(value = "新增家庭医生(addFamilyDoctor)", httpMethod = "POST", notes = "新增家庭医生；")
	@ResponseBody
	@RequestMapping(value = "/addFamilyDoctor", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddFamilyDoctorRes addFamilyDoctor(@RequestBody FamilyDoctorReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddFamilyDoctorRes res = (AddFamilyDoctorRes) familyDoctorService.addFamilyDoctor(req);
		return res;
	}

	
	// 修改家庭医生
	@ApiOperation(value = "修改家庭医生(modifyFamilyDoctor)", httpMethod = "POST", notes = "修改家庭医生，FamilyDoctorId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyFamilyDoctor", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyFamilyDoctor(@RequestBody FamilyDoctorReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) familyDoctorService.modifyFamilyDoctor(req);
		return res;
	}


	// 删除家庭医生
	@ApiOperation(value = "删除家庭医生(deleteFamilyDoctor)", httpMethod = "POST", notes = "删除家庭医生，FamilyDoctorId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteFamilyDoctor", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteFamilyDoctor(@RequestBody FamilyDoctorReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) familyDoctorService.deleteFamilyDoctor(req);
		return res;
	}

	// 获取详情家庭医生
	@ApiOperation(value = "获取详情家庭医生(getFamilyDoctorInfo)", httpMethod = "POST", notes = "获取详情家庭医生，FamilyDoctorId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getFamilyDoctorInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetFamilyDoctorInfoRes getFamilyDoctorInfo(@RequestBody FamilyDoctorReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetFamilyDoctorInfoRes res = (GetFamilyDoctorInfoRes) familyDoctorService.getFamilyDoctorInfo(req);
		return res;
	}


}
