package com.foshan.controller.health;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.foshan.form.health.request.StationDoctorReq;
import com.foshan.form.health.response.stationDoctor.GetStationDoctorInfoRes;
import com.foshan.form.health.response.stationDoctor.GetStationDoctorListRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Api(tags = "站点医生模块")
@RequestMapping()
@RestController
public class StaionDoctorController extends BaseHealthController {
	

	// 获取站点医生列表
	@ApiOperation(value = "获取站点医生列表(getStationDoctorList)", httpMethod = "POST", notes = "获取站点医生列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getStationDoctorList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetStationDoctorListRes getStationDoctorList(@RequestBody StationDoctorReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetStationDoctorListRes res = (GetStationDoctorListRes) stationDoctorService.getStationDoctorList(req);
		return res;
	}
	
	
	@ApiOperation(value = "新增站点医生(addStationDoctor)", httpMethod = "POST", notes = "新增站点医生；")
	@ResponseBody
	@RequestMapping(value = "/addStationDoctor", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addStationDoctor(@RequestBody StationDoctorReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) stationDoctorService.addStationDoctor(req);
		return res;
	}

	
	// 修改站点医生
	@ApiOperation(value = "修改站点医生(modifyStationDoctor)", httpMethod = "POST", notes = "修改站点医生，StationDoctorId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyStationDoctor", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyStationDoctor(@RequestBody StationDoctorReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) stationDoctorService.modifyStationDoctor(req);
		return res;
	}


	// 删除站点医生
	@ApiOperation(value = "删除站点医生(deleteStationDoctor)", httpMethod = "POST", notes = "删除站点医生，StationDoctorId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteStationDoctor", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteStationDoctor(@RequestBody StationDoctorReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) stationDoctorService.deleteStationDoctor(req);
		return res;
	}

	// 获取详情站点医生
	@ApiOperation(value = "获取详情站点医生(getStationDoctorInfo)", httpMethod = "POST", notes = "获取详情站点医生，StationDoctorId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getStationDoctorInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetStationDoctorInfoRes getStationDoctorInfo(@RequestBody StationDoctorReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetStationDoctorInfoRes res = (GetStationDoctorInfoRes) stationDoctorService.getStationDoctorInfo(req);
		return res;
	}

	// 导入站点医生信息
	@ApiOperation(value = "导入站点医生信息(importStationDoctorFile)", httpMethod = "POST", notes = "导入站点医生信息；")
	@ResponseBody
	@RequestMapping(value = "/importStationDoctorFile", method = { RequestMethod.POST, RequestMethod.GET })
	public GenericResponse importStationDoctorFile( StationDoctorReq req,HttpServletRequest request, HttpServletResponse response)
			throws JsonParseException, JsonMappingException, IOException {
		GenericResponse res = (GenericResponse) stationDoctorService.importStationDoctorFile(request);
		return res;
	}

	@ApiOperation(value = "导出站点医生信息(exportStationDoctorExcel)", httpMethod = "POST", notes = "导出站点医生信息；")
	@ResponseBody
	@RequestMapping(value = "/exportStationDoctorExcel", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportStationDoctorExcel(@RequestBody StationDoctorReq req, HttpServletResponse response){
		stationDoctorService.exportStationDoctorExcel(req,response);
	}

}
