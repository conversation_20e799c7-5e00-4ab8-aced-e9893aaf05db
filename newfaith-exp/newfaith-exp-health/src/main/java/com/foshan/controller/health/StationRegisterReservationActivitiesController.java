package com.foshan.controller.health;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.health.request.StationRegisterReservationActivitiesReq;
import com.foshan.form.health.response.healthReservationActivities.GetHealthReservationActivitiesInfo;
import com.foshan.form.health.response.healthReservationActivities.GetHealthReservationActivitiesListRes;

import com.foshan.form.health.response.healthReservationPresetPeriod.GetHealthReservationPresetPeriodListRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "站点医生挂号预约活动模块")
@RestController
public class StationRegisterReservationActivitiesController extends BaseHealthController {
	// 获取站点医生挂号预约活动列表
	@ApiOperation(value = "获取站点医生挂号预约活动列表(getStationRegisterReservationActivitiesList)", httpMethod = "POST", notes = "获取站点医生挂号预约活动列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getStationRegisterReservationActivitiesList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetHealthReservationActivitiesListRes getStationRegisterReservationActivitiesList(@RequestBody StationRegisterReservationActivitiesReq req,
																							 HttpServletRequest request)
			throws JsonProcessingException {
		GetHealthReservationActivitiesListRes res = (GetHealthReservationActivitiesListRes) stationRegisterReservationActivitiesService.getStationRegisterReservationActivitiesList(req);
		return res;
	}

	// 获取站点医生挂号预约活动列表
	@ApiOperation(value = "获取站点医生挂号预约活动列表(getStationRegisterPresetPeriodListByActivities)", httpMethod = "POST", notes = "获取站点医生挂号预约活动列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getStationRegisterPresetPeriodListByActivities", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetHealthReservationPresetPeriodListRes getStationRegisterPresetPeriodListByActivities(@RequestBody StationRegisterReservationActivitiesReq req,
																							 HttpServletRequest request)
			throws JsonProcessingException {
		GetHealthReservationPresetPeriodListRes res = (GetHealthReservationPresetPeriodListRes) stationRegisterReservationActivitiesService.getStationRegisterPresetPeriodListByActivities(req);
		return res;
	}
	
	// 新增站点医生挂号预约活动
	@ApiOperation(value = "新增站点医生挂号预约活动(addStationRegisterReservationActivities)", httpMethod = "POST", notes = "新增站点医生挂号预约活动，StrategyId、PublicAreaId、StartTime和EndTime不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addStationRegisterReservationActivities", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addStationRegisterReservationActivities(@RequestBody StationRegisterReservationActivitiesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) stationRegisterReservationActivitiesService.addStationRegisterReservationActivities(req);
		return res;
	}
	
	// 修改站点医生挂号预约活动
	@ApiOperation(value = "修改站点医生挂号预约活动(modifyStationRegisterReservationActivities)", httpMethod = "POST", notes = "修改站点医生挂号预约活动，ActivitiesId、StrategyId、PublicAreaId、StartTime和EndTime不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyStationRegisterReservationActivities", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyStationRegisterReservationActivities(@RequestBody StationRegisterReservationActivitiesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) stationRegisterReservationActivitiesService.modifyStationRegisterReservationActivities(req);
		return res;
	}
	// 删除站点医生挂号预约活动
	@ApiOperation(value = "删除站点医生挂号预约活动(deleteStationRegisterReservationActivities)", httpMethod = "POST", notes = "删除站点医生挂号预约活动，ActivitiesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteStationRegisterReservationActivities", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteStationRegisterReservationActivities(@RequestBody StationRegisterReservationActivitiesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) stationRegisterReservationActivitiesService.deleteStationRegisterReservationActivities(req);
		return res;
	}
	
	// 获取站点医生挂号预约活动详情
	@ApiOperation(value = "获取站点医生挂号预约活动详情(getStationRegisterReservationActivitiesInfo)", httpMethod = "POST", notes = "获取站点医生挂号预约活动详情，ActivitiesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getStationRegisterReservationActivitiesInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetHealthReservationActivitiesInfo getStationRegisterReservationActivitiesInfo(@RequestBody StationRegisterReservationActivitiesReq req,
																						  HttpServletRequest request)
			throws JsonProcessingException {
		GetHealthReservationActivitiesInfo res = (GetHealthReservationActivitiesInfo) stationRegisterReservationActivitiesService.getStationRegisterReservationActivitiesInfo(req);
		return res;
	}
}
