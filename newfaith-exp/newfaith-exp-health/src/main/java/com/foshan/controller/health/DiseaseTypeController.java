package com.foshan.controller.health;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.health.request.DiseaseTypeReq;
import com.foshan.form.health.response.diseaseType.AddDiseaseTypeRes;
import com.foshan.form.health.response.diseaseType.GetDiseaseTypeInfoRes;
import com.foshan.form.health.response.diseaseType.GetDiseaseTypeListRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "病种模块")
@RequestMapping()
@RestController
public class DiseaseTypeController extends BaseHealthController {
	

	// 获取病种列表
	@ApiOperation(value = "获取病种列表(getDiseaseTypeList)", httpMethod = "POST", notes = "获取病种列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getDiseaseTypeList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetDiseaseTypeListRes getDiseaseTypeList(@RequestBody DiseaseTypeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetDiseaseTypeListRes res = (GetDiseaseTypeListRes) diseaseTypeService.getDiseaseTypeList(req);
		return res;
	}
	
	
	@ApiOperation(value = "新增病种(addDiseaseType)", httpMethod = "POST", notes = "新增病种；")
	@ResponseBody
	@RequestMapping(value = "/addDiseaseType", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddDiseaseTypeRes addDiseaseType(@RequestBody DiseaseTypeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddDiseaseTypeRes res = (AddDiseaseTypeRes) diseaseTypeService.addDiseaseType(req);
		return res;
	}

	
	// 修改病种
	@ApiOperation(value = "修改病种(modifyDiseaseType)", httpMethod = "POST", notes = "修改病种，DiseaseTypeId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyDiseaseType", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyDiseaseType(@RequestBody DiseaseTypeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) diseaseTypeService.modifyDiseaseType(req);
		return res;
	}


	// 删除病种
	@ApiOperation(value = "删除病种(deleteDiseaseType)", httpMethod = "POST", notes = "删除病种，DiseaseTypeId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteDiseaseType", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteDiseaseType(@RequestBody DiseaseTypeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) diseaseTypeService.deleteDiseaseType(req);
		return res;
	}

	// 获取详情病种
	@ApiOperation(value = "获取详情病种(getDiseaseTypeInfo)", httpMethod = "POST", notes = "获取详情病种，DiseaseTypeId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getDiseaseTypeInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetDiseaseTypeInfoRes getDiseaseTypeInfo(@RequestBody DiseaseTypeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetDiseaseTypeInfoRes res = (GetDiseaseTypeInfoRes) diseaseTypeService.getDiseaseTypeInfo(req);
		return res;
	}


}
