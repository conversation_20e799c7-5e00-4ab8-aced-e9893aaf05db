package com.foshan.controller.health;

import com.foshan.form.health.request.PunchInReq;
import com.foshan.form.health.response.healthMember.GetWxCodeBaseRes;
import com.foshan.form.health.response.punchIn.AddPunchInRes;
import com.foshan.form.health.response.punchIn.GetPunchInInfoRes;
import com.foshan.form.health.response.punchIn.GetPunchInListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@Api(tags = "打卡模块")
@RequestMapping()
@RestController
public class PunchInController extends BaseHealthController {
	

	// 获取打卡列表
	@ApiOperation(value = "获取打卡列表(getPunchInList)", httpMethod = "POST", notes = "获取打卡列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getPunchInList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetPunchInListRes getPunchInList(@RequestBody PunchInReq req){
		GetPunchInListRes res = (GetPunchInListRes) punchInService.getPunchInList(req);
		return res;
	}
	
	
	@ApiOperation(value = "新增打卡(addPunchIn)", httpMethod = "POST", notes = "新增打卡；")
	@ResponseBody
	@RequestMapping(value = "/addPunchIn", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddPunchInRes addPunchIn(@RequestBody PunchInReq req){
		AddPunchInRes res = (AddPunchInRes) punchInService.addPunchIn(req);
		return res;
	}

	
	// 修改打卡
	@ApiOperation(value = "修改打卡(modifyPunchIn)", httpMethod = "POST", notes = "修改打卡，PunchInId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyPunchIn", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyPunchIn(@RequestBody PunchInReq req){
		GenericResponse res = (GenericResponse) punchInService.modifyPunchIn(req);
		return res;
	}


	// 删除打卡
	@ApiOperation(value = "删除打卡(deletePunchIn)", httpMethod = "POST", notes = "删除打卡，PunchInId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deletePunchIn", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deletePunchIn(@RequestBody PunchInReq req){
		GenericResponse res = (GenericResponse) punchInService.deletePunchIn(req);
		return res;
	}

	// 获取详情打卡
	@ApiOperation(value = "获取详情打卡(getPunchInInfo)", httpMethod = "POST", notes = "获取详情打卡，PunchInId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getPunchInInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetPunchInInfoRes getPunchInInfo(@RequestBody PunchInReq req){
		GetPunchInInfoRes res = (GetPunchInInfoRes) punchInService.getPunchInInfo(req);
		return res;
	}

	// 获取详情打卡
	@ApiOperation(value = "获取智能卡号二维码(getSmartCardIdWxCode)", httpMethod = "POST", notes = "获取智能卡号二维码，smartCardId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getSmartCardIdWxCode", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void getSmartCardIdWxCode(@RequestBody PunchInReq req, HttpServletResponse response){
		punchInService.getSmartCardIdWxCode(req,response);

	}

	@ApiOperation(value = "获取智能卡号二维码BASE64(getSmartCardIdWxCodeBase)", httpMethod = "POST", notes = "获取智能卡号二维码，smartCardId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getSmartCardIdWxCodeBase", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetWxCodeBaseRes getSmartCardIdWxCodeBase(@RequestBody PunchInReq req){
		return (GetWxCodeBaseRes)punchInService.getSmartCardIdWxCodeBase(req);

	}

}
