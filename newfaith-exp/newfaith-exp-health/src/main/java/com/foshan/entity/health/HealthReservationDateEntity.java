package com.foshan.entity.health;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Date;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_health_reservation_date")
@org.hibernate.annotations.Table(appliesTo = "t_health_reservation_date",comment="预约日期")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class HealthReservationDateEntity implements IEntityBean {

	/**
	 * 预约日期
	 */
	private static final long serialVersionUID = 9141194091448145398L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "date comment '日期'")
	private Date reservationDate;
	@Column(columnDefinition = "int(2) comment '是否开始预约 0否；1是'")
	protected Integer isStart;
	@Column(columnDefinition = "int(1) default 1 comment '状态 0--无效数据  1--有效数据'")
	protected Integer state;
	
	@OneToMany(targetEntity = HealthReservationPeriodEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "dateId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<HealthReservationPeriodEntity> periodList = new ArrayList<HealthReservationPeriodEntity>();
	
	@ManyToOne(targetEntity = HealthReservationActivitiesEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "activitiesId", referencedColumnName = "id", nullable = true)
	private HealthReservationActivitiesEntity activities;

}
