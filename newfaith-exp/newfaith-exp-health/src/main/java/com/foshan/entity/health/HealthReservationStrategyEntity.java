package com.foshan.entity.health;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_health_reservation_strategy")
@org.hibernate.annotations.Table(appliesTo = "t_health_reservation_strategy",comment="预约策略")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class HealthReservationStrategyEntity extends Health implements IEntityBean {

	/**
	 * 预约策略
	 */
	private static final long serialVersionUID = -8836071710048081844L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "int(2) comment '1法定节假日 2工作日 0不限'")
	protected Integer periodType;
	@Column(columnDefinition = "varchar(64) comment '策略名称'")
	private String strategyName;
	@Column(columnDefinition = "varchar(16) comment '跳过日 星期天-0 以此类推至星期六-6' default ''")
	private String skipDay;
	@Column(columnDefinition = "int(2) comment '延迟预约日 0-不延迟 1-延迟一天（当天不能预约） 2-延迟两天（今明不能预约）'")
	protected Integer lagDay;
	@Column(columnDefinition = "int(1) comment '是否自动延期 0-否 1-是'")
	protected Integer autoExtend;
	@Column(columnDefinition = "varchar(16) comment '跳过日期 1-1号 2-2号 以此类推至31号，多个日期用逗号隔开' default ''")
	private String skipDate;

	@OneToMany(targetEntity = HealthReservationActivitiesEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "strategyId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<HealthReservationActivitiesEntity> activitiesList = new ArrayList<HealthReservationActivitiesEntity>();
	
	@ManyToMany(targetEntity = HealthReservationPresetPeriodEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_Health_strategy_preset_period", joinColumns = @JoinColumn(name = "strategyId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "presetPeriodId", referencedColumnName = "id"))
	private List<HealthReservationPresetPeriodEntity> presetPeriodList = new ArrayList<HealthReservationPresetPeriodEntity>();
}
