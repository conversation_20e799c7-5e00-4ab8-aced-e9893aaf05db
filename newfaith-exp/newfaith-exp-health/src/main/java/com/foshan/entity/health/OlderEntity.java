package com.foshan.entity.health;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;


@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_older")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class OlderEntity implements IEntityBean {


    private static final long serialVersionUID = -8659081312441381786L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(columnDefinition = "varchar(10) comment '长者姓名'")
    private String name;
    @Column(columnDefinition = "varchar(18) comment '身份证号'")
    private String idCard;
    @Column(columnDefinition = "varchar(11) comment '手机号'")
    private String phone;
    @Column(columnDefinition = "varchar(20) comment '诊疗卡号'")
    private String medicalCard;
    @Column(columnDefinition = "varchar(64) comment '健康标识 （65岁以上、高血压、高血糖）'")
    private String healthFlag;
    @Column(columnDefinition = "varchar(256) comment '地址'")
    private String address;
    @Column(columnDefinition = "varchar(64) comment '签约团队'")
    private String signedTeam;
    @Column(columnDefinition = "int(1) comment '性别 1：男 0：女'")
    private Integer sex;
    @Column(columnDefinition = "int(1) comment '状态 1：正常 0：禁用'")
    private Integer state;

//    @ManyToOne(targetEntity = FamilyDoctorEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
//    @JoinColumn(name = "parentDoctorId", referencedColumnName = "id", nullable = true)
//    @JsonIgnore
//    private FamilyDoctorEntity parentDoctor;
//    @OneToMany(targetEntity = FamilyDoctorEntity.class, fetch = FetchType.LAZY)
//    @JoinColumn(name = "parentDoctorId", referencedColumnName = "id")
//    @JsonIgnore
//    private Set<FamilyDoctorEntity> subDoctorList = new HashSet<FamilyDoctorEntity>();

//
//    @OneToMany(targetEntity = AssetEntity.class, fetch = FetchType.LAZY)
//    @JoinColumn(name = "familyDoctorId", referencedColumnName = "id", nullable = true)
//    @JsonIgnore
//    private Set<AssetEntity> AssetSet = new HashSet<AssetEntity>();
//
//    @ManyToOne(targetEntity = ServiceStationEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
//    @JoinColumn(name = "stationId", referencedColumnName = "id", nullable = true)
//    private ServiceStationEntity station;

}
