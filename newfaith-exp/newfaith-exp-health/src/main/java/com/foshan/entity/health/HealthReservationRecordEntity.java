package com.foshan.entity.health;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
//import com.foshan.encrypt.annotation.EncryptField;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_reservation")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class HealthReservationRecordEntity extends Health {

    private static final long serialVersionUID = 7686945409413758354L;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(columnDefinition = "int(2) default 0 comment '预约状态 0-未处理 1-已转介 2-已处理 3-已取消 4-无效信息 5-用户取消'")
    private Integer status;
    @Column(columnDefinition = "varchar(64) comment '智能卡号'")
    private String smartCardId;
    @Column(columnDefinition = "varchar(11) comment '手机号'", nullable = false)
//    @EncryptField
    private String phone;
    @Column(columnDefinition = "varchar(64) comment '身份证号'")
//    @EncryptField
    private String idCard;
    @Column(columnDefinition = "varchar(256) comment '备注'")
    private String remarks;
    @Column(columnDefinition = "text comment '保留备注'")
    private String reservedData;
    @Column(columnDefinition = "varchar(128) comment '预约码'")
    private String serialNum;
    @Column(columnDefinition = "varchar(4) comment '发展码'")
    private String referralCode;

//    @Column(columnDefinition = "Timestamp default current_timestamp comment '预约时间'")
//    private Timestamp createTime;

    @ManyToOne(targetEntity = HealthMemberEntity.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
    @JsonIgnore
    private HealthMemberEntity healthMember;

    @ManyToOne(targetEntity = HealthReservationPeriodEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "periodId", referencedColumnName = "id", nullable = true)
    private HealthReservationPeriodEntity reservatPeriod;

    @ManyToOne(targetEntity = HealthReservationActivitiesEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "activitiesId", referencedColumnName = "id", nullable = true)
    private HealthReservationActivitiesEntity activities;
}
