package com.foshan.entity.health;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;

@Getter
@Setter
@NoArgsConstructor
@Entity
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
@DiscriminatorValue("VACCINE")
public class VaccineReservationActivitiesEntity extends HealthReservationActivitiesEntity {

	/**
	 * 疫苗预约活动
	 */
	private static final long serialVersionUID = 2905229461095798215L;
	@Column(columnDefinition = "int(1) default 1 comment '状态 0--儿童疫苗  1--成人疫苗'")
	protected Integer isAdult;
	

}
