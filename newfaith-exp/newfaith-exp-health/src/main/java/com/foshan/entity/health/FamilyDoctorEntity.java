package com.foshan.entity.health;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.IEntityBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_family_doctor")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class FamilyDoctorEntity implements IEntityBean {


    private static final long serialVersionUID = -4033424010498270054L;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(columnDefinition = "varchar(64) comment '医生名称'")
    private String familyDoctorName;
    @Column(columnDefinition = "varchar(512) comment '医生详情'")
    private String familyDoctorDetail;
    @Column(columnDefinition = "int(11) comment '排序值'")
    private Integer orders;

//    @ManyToOne(targetEntity = FamilyDoctorEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
//    @JoinColumn(name = "parentDoctorId", referencedColumnName = "id", nullable = true)
//    @JsonIgnore
//    private FamilyDoctorEntity parentDoctor;
//    @OneToMany(targetEntity = FamilyDoctorEntity.class, fetch = FetchType.LAZY)
//    @JoinColumn(name = "parentDoctorId", referencedColumnName = "id")
//    @JsonIgnore
//    private Set<FamilyDoctorEntity> subDoctorList = new HashSet<FamilyDoctorEntity>();


    @OneToMany(targetEntity = AssetEntity.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "familyDoctorId", referencedColumnName = "id", nullable = true)
    @JsonIgnore
    private Set<AssetEntity> AssetSet = new HashSet<AssetEntity>();

    @ManyToOne(targetEntity = ServiceStationEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "stationId", referencedColumnName = "id", nullable = true)
    private ServiceStationEntity station;

}
