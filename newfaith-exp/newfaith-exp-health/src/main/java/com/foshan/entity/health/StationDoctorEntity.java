package com.foshan.entity.health;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AssetEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@Entity
@DiscriminatorValue("StationDoctor")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class StationDoctorEntity extends HealthUserEntity {


    private static final long serialVersionUID = 7973591167124043646L;

    @Column(columnDefinition = "varchar(18) comment '医生身份证号'")
    private String idCard;
    @Column(columnDefinition = "int(1) comment '性别 1：男 2：女'")
    private Integer sex;
    @Column(columnDefinition = "varchar(11) comment '职务'")
    private String job;
    @Column(columnDefinition = "varchar(11) comment '职称'")
    private String title;
    @Column(columnDefinition = "varchar(64) comment '简介'")
    private String description;
    @Column(columnDefinition = "varchar(11) comment '科目 默认全科'")
    private String subject;

//    @ManyToOne(targetEntity = FamilyDoctorEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
//    @JoinColumn(name = "parentDoctorId", referencedColumnName = "id", nullable = true)
//    @JsonIgnore
//    private FamilyDoctorEntity parentDoctor;
//    @OneToMany(targetEntity = FamilyDoctorEntity.class, fetch = FetchType.LAZY)
//    @JoinColumn(name = "parentDoctorId", referencedColumnName = "id")
//    @JsonIgnore
//    private Set<FamilyDoctorEntity> subDoctorList = new HashSet<FamilyDoctorEntity>();


    @OneToMany(targetEntity = AssetEntity.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "stationDoctorId", referencedColumnName = "id", nullable = true)
    @JsonIgnore
    private Set<AssetEntity> AssetSet = new HashSet<AssetEntity>();

    @ManyToOne(targetEntity = ServiceStationEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "stationId", referencedColumnName = "id", nullable = true)
    private ServiceStationEntity station;

}
