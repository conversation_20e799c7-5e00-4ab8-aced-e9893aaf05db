package com.foshan.entity.health;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.IEntityBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_vaccine")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class VaccineEntity implements IEntityBean{


    private static final long serialVersionUID = 8615669270753284829L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(columnDefinition = "varchar(64) comment '疫苗名称'")
    private String vaccineName;
    @Column(columnDefinition = "varchar(512) comment '疫苗详情'")
    private String vaccineDetail;

}
