package com.foshan.aop.health;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.foshan.dao.health.INotifyTemplateDao;
import com.foshan.entity.health.NotifyTemplateEntity;
import com.foshan.form.health.request.HealthReservationRecordReq;
import com.foshan.form.health.response.healthReservationRecord.AddHealthReservationRecordRes;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.util.ContextInfo;
import com.foshan.util.HttpClientUtil;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Aspect
@Component
public class ReservationNotifyAOP {
    @Autowired
    protected ContextInfo contextInfo;
    @Autowired
    protected INotifyTemplateDao notifyDao;

    private final static Logger logger = LoggerFactory.getLogger(ReservationNotifyAOP.class);

    //切入点：增强标有MyLogAnnotation注解的方法
    @Pointcut(value = "@annotation(com.foshan.aop.health.ReservationNotify)")
    //切入点签名
    public void notifyAnnotation() {
        System.out.println("pointCut签名。。。");
    }
    //前置通知
    //注意：获取注解中的属性时，@annotation()中的参数要和deBefore方法中的参数写法一样，即myLogAnnotation
//    @Before("logAnnotation() && @annotation(reservationNotify)")
//    public void deBefore(JoinPoint jp, ReservationNotify reservationNotify) throws Throwable {
//        System.out.println("前置通知：方法执行前执行...");
//        // 接收到请求，记录请求内容
//        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
//        HttpServletRequest request = attributes.getRequest();
//        // 获取注解中的属性
//        System.out.println("deBefore===========>" + reservationNotify.desc());
//        // 记录下请求内容
//        System.out.println("URL : " + request.getRequestURL().toString());
//    }


//    @Around(value = "logAnnotation()")
//    public Object around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
//        if (true) {
//
//
//            Object[] args = proceedingJoinPoint.getArgs();
//            // 将获取的值进行修改
//            args[1] = "and status = 2";
//            // 然后继续执行该接口（这里修改参数）
//            Object ret = proceedingJoinPoint.proceed(args);  //将新的参数传入
//
//            // 这里定义一个新的对象用来返回，
//            // 这里是用来测试的，所以数据是随机的，但是返回的类型要与刚开始的类型相同
//        }
//        return proceedingJoinPoint.proceed();
//    }

    //返回通知
    @AfterReturning(returning = "ret", pointcut = "notifyAnnotation() && @annotation(reservationNotify)")
    public void doAfterReturning(JoinPoint jp, Object ret, ReservationNotify reservationNotify) {
        try { // 获取注解中的属性
            System.out.println("doAfterReturning===========>");
            // 处理完请求，返回内容
//            System.out.println(jp.getArgs()[0]);
//            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
//            HttpServletRequest request = attributes.getRequest();
            HealthReservationRecordReq req = (HealthReservationRecordReq) jp.getArgs()[0];
//            ServiceStationReq req = (ServiceStationReq)jp.getArgs()[0];
            System.out.println(req.getPhone());
            AddHealthReservationRecordRes res = (AddHealthReservationRecordRes) ret;
//            System.out.println(res.getReservation().getReservationDate());
//            System.out.println(res.getReservation().getReservationPeriod());
            System.out.println("返回通知：方法的返回值 : " + ret);
            if (ResponseContext.RES_SUCCESS_CODE.equals(res.getRet())) {
                List<String> typeList = new ArrayList<>(Arrays.asList("病种短信", "家庭医生短信", "基卫短信", "疫苗短信", "儿童保健短信", "优惠体检短信", "就诊短信", "站点医生短信"));

                String hql =
                        "select distinct a from NotifyTemplateEntity a where a.notifyName = '" + typeList.get(res.getReservation().getReservationType())+"'";
                NotifyTemplateEntity notify = notifyDao.getUniqueByHql(hql);
                ObjectMapper objectMapper = new ObjectMapper();
                ObjectNode json = objectMapper.createObjectNode();

                String content = notify.getNotifyContent().replaceAll("date", res.getReservation().getReservationDate()).replaceAll("timePeriod",
                        res.getReservation().getReservationPeriod()).replaceAll("station",res.getReservation().getStationName());

                if (StringUtils.isNotEmpty(res.getReservation().getReservedData())){
                    ObjectMapper mapper = new ObjectMapper();
                    Map<String, String> map = mapper.readValue(res.getReservation().getReservedData(), Map.class);
                    String business = StringUtils.isNotEmpty(map.get("business"))?map.get("business"):"";
                    String meal = StringUtils.isNotEmpty(map.get("meal"))?map.get("meal"):"";
                    content = content.replaceAll("business",business).replaceAll("meal",meal);
                }


                // 发送短信
                json.put("messageContent", content);
                json.put("userNumber", req.getPhone());

                Map<String, String> result = null;

                objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
                logger.info("--------------------------------------调用短信接口地址：" + contextInfo.smsInterfaceUrl + ";   参数："
                        + json.toString() + "-------------------------");
                result = HttpClientUtil.post(contextInfo.smsInterfaceUrl, objectMapper.writeValueAsString(json), "utf-8");
                logger.info("--------------------------------------调用短信接口返回：" + result
                        + "----------------------------------------");




            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //异常通知
//    @AfterThrowing(throwing = "ex", pointcut = "logAnnotation() && @annotation(myLogAnnotation)")
//    public void throwss(JoinPoint jp,Exception ex, MyLogAnnotation myLogAnnotation){
//        // 获取注解中的属性
//        System.out.println("throwss===========>" + myLogAnnotation.desc());
//        System.out.println("异常通知：方法异常时执行.....");
//        System.out.println("产生异常的方法："+jp);
//        System.out.println("异常种类："+ex);
//    }

//    //后置通知
//    @After("logAnnotation() && @annotation(reservationNotify)")
//    public void after(JoinPoint jp, ReservationNotify reservationNotify){
//        // 获取注解中的属性
//        System.out.println("after===========>");
//        System.out.println(jp.getArgs().toString());
//        List<Object> argList = new ArrayList<>();
//        for (Object arg : jp.getArgs()) {
//            // request/response无法使用toJSON
//            if (arg instanceof HttpServletRequest) {
//                argList.add("request");
//            } else if (arg instanceof HttpServletResponse) {
//                argList.add("response");
//            } else {
////                argList.add(JSON.toJSON(arg));
//            }
//        }
//
//        System.out.println("后置通知：最后且一定执行.....");
//    }
}
