package com.foshan.form.party.request;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="操作日志求参数(OperationLogReq)")
public class OperationLogReq extends BasePageRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = -971848560861063212L;
	@ApiModelProperty(value = "日志Id",example="1")
	private Integer operationLogId;
	@ApiModelProperty(value = "主题Id",example="1")
	private Integer topicId;
	@ApiModelProperty(value = "文章Id",example="1")
	private Integer assetId;
	
	
}
