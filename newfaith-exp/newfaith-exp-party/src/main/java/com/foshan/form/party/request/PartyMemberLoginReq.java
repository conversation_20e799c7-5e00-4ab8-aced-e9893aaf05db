package com.foshan.form.party.request;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "党员登录请求对象(MemberLoginReq)")
public class PartyMemberLoginReq extends BasePageRequest {
	/**
	 * 党员登陆请求
	 */
	private static final long serialVersionUID = 32636125721847096L;

	@ApiModelProperty(value = "手机号，密码、短信验证码、第三方平台获取token、电视客户端登陆方式必填")
	private String phone;
	@ApiModelProperty(value = "手机验证码,短信登录必填")
	private String messageCode;
	@ApiModelProperty(value = "图形验证码")
	private String verifyCode;
	@ApiModelProperty(value = "密码，密码登录类型必填")
	private String password;
	@ApiModelProperty(value = "性别：0--女 1--男 2--保密", example = "1")
	private Integer sex;
	@ApiModelProperty(value = "帐号")
	private String loginName;
	@ApiModelProperty(value = "智能卡号")
	private String smartcardId;
	@ApiModelProperty(value = "保留字段", example = "1")
	private Integer binding;
	@ApiModelProperty(value = "必填，验证方式，1-密码，5-微信小程序")
	private String type;
	@ApiModelProperty(value = "微信/小程序授权登陆的验证码，传入微信/小程序获取到的的code,微信/小程序登陆必填")
	private String wechatCode;
	@ApiModelProperty(value = "分配给第三方平台的令牌，客户端传入token校验,第三方平台获取token登陆必填")
	private String appToken;
	@ApiModelProperty(value = "微信小程序加密数据,微信小程序注册且登陆时必填")
	private String encryptedData;
	@ApiModelProperty(value = "用于解密微信小程序加密数据的初始向量,微信小程序注册且登陆时必填")
	private String iv;
	@ApiModelProperty(value = "用于校验微信小程序开放数据签名")
	private String signature;
	@ApiModelProperty(value = "登陆票据")
	private String loginTicket;

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getMessageCode() {
		return messageCode;
	}

	public void setMessageCode(String messageCode) {
		this.messageCode = messageCode;
	}

	public String getVerifyCode() {
		return verifyCode;
	}

	public void setVerifyCode(String verifyCode) {
		this.verifyCode = verifyCode;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getSmartcardId() {
		return smartcardId;
	}

	public void setSmartcardId(String smartcardId) {
		this.smartcardId = smartcardId;
	}

	public Integer getSex() {
		return sex;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getLoginName() {
		return loginName;
	}

	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}

	public Integer getBinding() {
		return binding;
	}

	public void setBinding(Integer binding) {
		this.binding = binding;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getWechatCode() {
		return wechatCode;
	}

	public void setWechatCode(String wechatCode) {
		this.wechatCode = wechatCode;
	}

	public String getAppToken() {
		return appToken;
	}

	public void setAppToken(String appToken) {
		this.appToken = appToken;
	}

	public String getEncryptedData() {
		return encryptedData;
	}

	public void setEncryptedData(String encryptedData) {
		this.encryptedData = encryptedData;
	}

	public String getIv() {
		return iv;
	}

	public void setIv(String iv) {
		this.iv = iv;
	}

	public String getSignature() {
		return signature;
	}

	public void setSignature(String signature) {
		this.signature = signature;
	}

	public String getLoginTicket() {
		return loginTicket;
	}

	public void setLoginTicket(String loginTicket) {
		this.loginTicket = loginTicket;
	}

}
