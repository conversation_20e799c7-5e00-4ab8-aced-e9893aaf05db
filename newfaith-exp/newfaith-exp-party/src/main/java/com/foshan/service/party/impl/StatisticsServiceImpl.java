package com.foshan.service.party.impl;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.jdbc.Work;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foshan.entity.party.PartyOrganizationEntity;
import com.foshan.form.party.request.StatisticsReq;
import com.foshan.form.party.response.statistics.GetStatisticsRes;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.party.IStatisticsService;
import com.foshan.util.SpringHandler;

@Transactional
@Service("statisticsService")
public class StatisticsServiceImpl extends GenericPartyService implements IStatisticsService {

	@Override
	public IResponse subPartySubmitGeneralSituation(StatisticsReq req) {
		GetStatisticsRes res = new GetStatisticsRes();
		if (null != req.getPartyId() && 
				StringUtils.isNotEmpty(req.getStartTime()) && 
				StringUtils.isNotEmpty(req.getEndTime())) {
			PartyOrganizationEntity partyOrganization = partyOrganizationDao.get(req.getPartyId());
			if (null != partyOrganization) {
				List<String> processList = new ArrayList<String>();
				processList.add("process");
				processList.add("审核通过");
				processList.add("驳回");
				processList.add("归档");
				res.getDataList().add(processList);

				SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
				Session session = sessionFactory.openSession();
				session.doWork(new Work() {
					@Override
					public void execute(Connection connection) throws SQLException {
						// TODO 自动生成的方法存根
						Statement st = (Statement) connection.createStatement();
						//String time =req.getTime();
						String processIds="";
						processIds = getProcessIds(partyOrganization, processIds);
						StringBuilder sql = new StringBuilder("SELECT p.processName , COUNT(1) AS `totalNumber`," + 
								"  SUM((CASE `o`.`auditState` WHEN '2' THEN 1 ELSE 0 END)) AS `pass`," + 
								"  SUM((CASE `o`.`auditState` WHEN '3' THEN 1 ELSE 0 END)) AS `archive`," + 
								"  SUM((CASE `o`.`auditState` WHEN '10' THEN 1 ELSE 0 END)) AS `rejected` " + 
								"  FROM `t_operation_log` o INNER JOIN  t_asset a ON o.assetId=a.id  JOIN t_party_audit_process p "
								+ "  WHERE p.id=o.auditProcessId AND p.id IN("+processIds+") AND a.partyId="+
								req.getPartyId()
								+" AND a.createTime<='"+req.getEndTime()+"' AND a.createTime>='"+req.getStartTime()
								+"' GROUP BY o.auditProcessId");
						
						ResultSet rs = st.executeQuery(sql.toString());
						while (rs.next()) {
							List<Object> dataList = new ArrayList<Object>();
							dataList.add(rs.getString("processName"));
							//dataList.add(rs.getInt("totalNumber"));
							dataList.add(rs.getInt("pass"));
							dataList.add(rs.getInt("archive"));
							dataList.add(rs.getInt("rejected"));
							res.getDataList().add(dataList);
						}
						rs.close();
						st.close();
						connection.close();
					}
				});
				// tx.commit();
				if (session != null) {
					session.close();
				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	
	public String getProcessIds(PartyOrganizationEntity partyOrganization,String processIds) {
		if(null!=partyOrganization && partyOrganization.getNeedAudit()==1) {
			if(null!=partyOrganization.getPartyAuditProcessList()) {
				for(int i=0;i<partyOrganization.getPartyAuditProcessList().size();i++) {
					String id = partyOrganization.getPartyAuditProcessList().get(i).getId().toString();
					processIds = processIds.equals("") ? id : processIds+","+id;
				}
			}
			processIds = getProcessIds(partyOrganization.getParentPartyOrganization(),processIds);
		}
		return processIds;
	}
	
	public List<String> getProcessName(PartyOrganizationEntity partyOrganization,List<String> list) {
		if(null!=partyOrganization && partyOrganization.getNeedAudit()==1) {
			if(null!=partyOrganization.getPartyAuditProcessList()) {
				for(int i=0;i<partyOrganization.getPartyAuditProcessList().size();i++) {
					String ProcessName = partyOrganization.getPartyAuditProcessList().get(i).getProcessName();
					list.add(ProcessName);
				}
			}
			list = getProcessName(partyOrganization.getParentPartyOrganization(),list);
		}
		return list;
	}

	@Override
	public IResponse partyMemberSubmitGeneralSituation(StatisticsReq req) {
		GetStatisticsRes res = new GetStatisticsRes();
		if (null != req.getPartyId() && 
				StringUtils.isNotEmpty(req.getStartTime()) && 
				StringUtils.isNotEmpty(req.getEndTime())) {
			PartyOrganizationEntity partyOrganization = partyOrganizationDao.get(req.getPartyId());
			if (null != partyOrganization) {
				SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
				Session session = sessionFactory.openSession();
				session.doWork(new Work() {
					@Override
					public void execute(Connection connection) throws SQLException {
						// TODO 自动生成的方法存根
						Statement st = (Statement) connection.createStatement();
						//String time =req.getTime();
						List<String> nameList = getProcessName(partyOrganization, new ArrayList<String>());
						
						List<String> processList = new ArrayList<String>();
						StringBuilder sumStr = new StringBuilder();
						processList.add("processName");
						for(int i=0 ; i<nameList.size();i++){
							processList.add(nameList.get(i));
							sumStr.append(i+1!= nameList.size() ? 
								"SUM((CASE `p`.`processName` WHEN '"+nameList.get(i)+"' THEN 1 ELSE 0 END)) AS `name"+i+"`," :
								"SUM((CASE `p`.`processName` WHEN '"+nameList.get(i)+"' THEN 1 ELSE 0 END)) AS `name"+i+"`");
						};
						res.getDataList().add(processList);
						StringBuilder sql = new StringBuilder("  SELECT c.userName , COUNT(1)        AS `totalNumber`," + 
								sumStr.toString()+
								"  FROM `t_operation_log` o INNER JOIN  t_asset a ON o.assetId=a.id  JOIN t_party_audit_process p " + 
								"  INNER JOIN `t_account` c ON c.id=a.memberId  " + 
								"  WHERE p.id=o.auditProcessId  ");
						sql.append(null!=req.getMemberId() ? " and a.memberId= "+req.getMemberId():" AND a.partyId="+req.getPartyId())
							.append(" AND a.createTime<='"+req.getEndTime()+"' AND a.createTime>='"+req.getStartTime()
								+"' AND o.auditState=2 GROUP BY a.memberId  ");
						
						ResultSet rs = st.executeQuery(sql.toString());
						while (rs.next()) {
							List<Object> dataList = new ArrayList<Object>();
							dataList.add(rs.getString("userName"));
							for(int i=0; i<nameList.size();i++){
								dataList.add(rs.getInt("name"+i));
							}
							res.getDataList().add(dataList);
						}
						rs.close();
						st.close();
						connection.close();
					}
				});
				// tx.commit();
				if (session != null) {
					session.close();
				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse subordinateSubmitGeneralSituation(StatisticsReq req) {
		GetStatisticsRes res = new GetStatisticsRes();
		if (null != req.getPartyId() && 
				StringUtils.isNotEmpty(req.getStartTime()) && 
				StringUtils.isNotEmpty(req.getEndTime())) {
			PartyOrganizationEntity partyOrganization = partyOrganizationDao.get(req.getPartyId());
			if (null != partyOrganization) {
				List<String> processList = new ArrayList<String>();
				processList.add("process");
				processList.add("审核通过");
				processList.add("驳回");
				processList.add("归档");
				res.getDataList().add(processList);

				SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
				Session session = sessionFactory.openSession();
				session.doWork(new Work() {
					@Override
					public void execute(Connection connection) throws SQLException {
						// TODO 自动生成的方法存根
						Statement st = (Statement) connection.createStatement();
						//String time =req.getTime();
						String processIds="";
						processIds = getProcessIds(partyOrganization, processIds);
						StringBuilder sql = new StringBuilder("SELECT p.partyName , COUNT(1) AS `totalNumber`," + 
								"  SUM((CASE `o`.`auditState` WHEN '2' THEN 1 ELSE 0 END)) AS `pass`," + 
								"  SUM((CASE `o`.`auditState` WHEN '3' THEN 1 ELSE 0 END)) AS `archive`," + 
								"  SUM((CASE `o`.`auditState` WHEN '10' THEN 1 ELSE 0 END)) AS `rejected` " + 
								"  FROM `t_operation_log` o INNER JOIN  t_asset a ON o.assetId=a.id  "
								+ " INNER JOIN t_party_organization p ON p.id=a.partyId  WHERE p.parentPartyOrganizationId="+
								req.getPartyId()+" AND a.createTime<='"+req.getEndTime()+"' AND a.createTime>='"+req.getStartTime()
								+"' GROUP BY p.partyName");
						
						ResultSet rs = st.executeQuery(sql.toString());
						while (rs.next()) {
							List<Object> dataList = new ArrayList<Object>();
							dataList.add(rs.getString("partyName"));
							//dataList.add(rs.getInt("totalNumber"));
							dataList.add(rs.getInt("pass"));
							dataList.add(rs.getInt("archive"));
							dataList.add(rs.getInt("rejected"));
							res.getDataList().add(dataList);
						}
						rs.close();
						st.close();
						connection.close();
					}
				});
				// tx.commit();
				if (session != null) {
					session.close();
				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse thisPartSubmitGeneralSituation(StatisticsReq req) {
		GetStatisticsRes res = new GetStatisticsRes();
		if (null != req.getPartyId() && 
				StringUtils.isNotEmpty(req.getStartTime()) && 
				StringUtils.isNotEmpty(req.getEndTime())) {
			PartyOrganizationEntity partyOrganization = partyOrganizationDao.get(req.getPartyId());
			if (null != partyOrganization) {
				SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
				Session session = sessionFactory.openSession();
				session.doWork(new Work() {
					@Override
					public void execute(Connection connection) throws SQLException {
						// TODO 自动生成的方法存根
						Statement st = (Statement) connection.createStatement();
						//String time =req.getTime();
						String processIds="";
						processIds = getProcessIds(partyOrganization, processIds);
						StringBuilder sql = new StringBuilder("SELECT COUNT(1) AS `totalNumber`," + 
								"  SUM((CASE `o`.`auditState` WHEN '2' THEN 1 ELSE 0 END)) AS `pass`," + 
								"  SUM((CASE `o`.`auditState` WHEN '3' THEN 1 ELSE 0 END)) AS `archive`," + 
								"  SUM((CASE `o`.`auditState` WHEN '10' THEN 1 ELSE 0 END)) AS `rejected` " + 
								"  FROM `t_operation_log` o INNER JOIN  t_asset a ON o.assetId=a.id  "
								+ " INNER JOIN t_party_organization p ON p.id=a.partyId  WHERE p.id="+
								req.getPartyId() +" AND a.createTime<='"+req.getEndTime()+"' AND a.createTime>='"+req.getStartTime()+"'");
						
						ResultSet rs = st.executeQuery(sql.toString());
						while (rs.next()) {
							Map<String,String> passMap = new HashMap<String,String>();
							passMap.put("name", "审核通过");
							passMap.put("value", rs.getInt("pass")+"");
							
							Map<String,String> archiveMap = new HashMap<String,String>();
							archiveMap.put("name", "存档");
							archiveMap.put("value", rs.getInt("archive")+"");
							
							Map<String,String> rejectedMap = new HashMap<String,String>();
							rejectedMap.put("name", "驳回");
							rejectedMap.put("value", rs.getInt("rejected")+"");
							res.getDataList().add(passMap);
							res.getDataList().add(archiveMap);
							res.getDataList().add(rejectedMap);
						}
						rs.close();
						st.close();
						connection.close();
					}
				});
				// tx.commit();
				if (session != null) {
					session.close();
				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

}
