package com.foshan.service.party.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.ColumnEntity;
import com.foshan.entity.RegionEntity;
import com.foshan.entity.ServiceEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.party.PartyAssetEntity;
import com.foshan.entity.party.PartyColumnEntity;
import com.foshan.entity.party.PartyMemberEntity;
import com.foshan.entity.party.PartyOrganizationEntity;
import com.foshan.entity.party.PartyTopicEntity;
import com.foshan.form.RegionForm;
import com.foshan.form.party.PartyColumnForm;
import com.foshan.form.party.PartyTopicForm;
import com.foshan.form.party.request.PartyColumnReq;
import com.foshan.form.party.PartyAssetForm;
import com.foshan.form.party.response.partyColumn.GetPartyColumnAssetList;
import com.foshan.form.party.response.partyColumn.GetPartyColumnListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.column.AddColumnRes;
import com.foshan.form.response.column.ModifyColumnRes;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.party.IPartyColumnService;
import com.foshan.util.CodeUtil;
import com.foshan.util.DateUtil;

@Transactional
@Service("partyColumnService")
public class PartyColumnServiceImpl extends GenericPartyService implements IPartyColumnService {


	@Override
	public IResponse addPartyColumn(PartyColumnReq req) {
		// TODO Auto-generated method stub
		AddColumnRes res = new AddColumnRes();

		PartyColumnEntity column = new PartyColumnEntity();
		if (null != req.getServiceId() && StringUtils.isNotEmpty(req.getColumnName())) {
			ServiceEntity service = serviceDao.get(req.getServiceId());
			if (null != service) {
				Integer columnId = null;
				Integer columnCode = (Integer) partyColumnDao
						.createQuery("select max(substring(a.columnCode,5)+0)+1 from ColumnEntity a").uniqueResult();
				column.setColumnCode(StringUtils.isNotEmpty(req.getColumnCode()) ? req.getColumnCode()
						: CodeUtil.codeGenerator(null != columnCode ? columnCode : 1, 10, "MANU"));
				column.setColumnName(req.getColumnName());
				column.setColumnInfo(req.getColumnInfo());
				column.setColumnType(
						null != req.getColumnType() ? req.getColumnType() : EntityContext.COLUMN_TYPE_IMAGE);
				column.setColumnState(
						null != req.getColumnState() ? req.getColumnState() : EntityContext.RECORD_STATE_VALID);
				column.setCommendFlag(
						null != req.getCommendFlag() ? req.getCommendFlag() : EntityContext.COMMENT_FLAG_INVALID);
				column.setTargetType(
						null != req.getTargetType() ? req.getTargetType() : EntityContext.COLUMN_TARGET_TYPE_COMMON);
				column.setMappingFolderId(req.getMappingFolderId());
				column.setMappingSystem(
						null != req.getMappingSystem() ? req.getMappingSystem() : EntityContext.COLUMN_MAPPING_BO);
				column.setMappingFolderName(req.getMappingFolderName());
				column.setIsGlobal(null != req.getIsGlobal() ? req.getIsGlobal() : EntityContext.RECORD_STATE_VALID);
				column.setState(null != req.getState() ? req.getState() : EntityContext.RECORD_STATE_VALID);
				column.setService(service);
				if (null != req.getColumnImageId()) {
					column.setColumnImage(assetDao.get(req.getColumnImageId()));
				}

				if (null != req.getColumnPhoneImageId()) {
					column.setColumnPhoneImage(assetDao.get(req.getColumnPhoneImageId()));
				}

				// 设置栏目绑定区域，默认省节点区域
				if (StringUtils.isNotEmpty(req.getRegionIds())) {
					String[] regionIds = req.getRegionIds().split(",");
					List<RegionEntity> regionList = new ArrayList<RegionEntity>();
					for (String regionId : regionIds) {
						regionList.add(regionDao.get(Integer.parseInt(regionId)));
					}
					column.setRegionList(regionList);
				} else {
					RegionEntity region = regionDao.getUniqueByHql("from RegionEntity a where a.regionLevel=1");
					column.getRegionList().add(region);
				}
				
				if (StringUtils.isNotEmpty(req.getPartyOrganizationIdList())) {
					String[] partyIds = req.getPartyOrganizationIdList().split(",");
					List<PartyOrganizationEntity> partyList = new ArrayList<PartyOrganizationEntity>();
					for (String partyId : partyIds) {
						partyList.add(partyOrganizationDao.get(Integer.parseInt(partyId)));
					}
					column.setPartyOrganizationList(partyList);
				}

				if (null != req.getParentColumnId()) {
					ColumnEntity parentColumn = partyColumnDao.get(req.getParentColumnId());
					if (null != parentColumn) {
						Integer columnOrder = (Integer) partyColumnDao.createQuery(
								"select max(a.orders)+1 from ColumnEntity a inner join a.parentColumn b where b.id="
										+ parentColumn.getId())
								.uniqueResult();
						column.setColumnLevel(parentColumn.getColumnLevel() + 1);
						column.setColumnPath(parentColumn.getColumnPath() + "/" + req.getColumnName());
						column.setOrders(
								null != req.getOrders() ? req.getOrders() : null != columnOrder ? columnOrder : 1);
						column.setParentColumn(parentColumn);
						// 保存新增栏目，返回栏目ID
						columnId = (Integer) partyColumnDao.save(column);
					} else {
						res.setRet("0001");
						res.setRetInfo("新增栏目的父栏目不存在！！！");
						return res;
					}
				} else {
					if (null == service.getColumn()) {
						Integer columnOrder = (Integer) partyColumnDao
								.createQuery("select max(a.orders)+1 from ColumnEntity a where a.columnLevel=1")
								.uniqueResult();
						column.setOrders(
								null != req.getOrders() ? req.getOrders() : null != columnOrder ? columnOrder : 1);
						column.setColumnLevel(1);
						column.setColumnPath(req.getColumnName());
						columnId = (Integer) partyColumnDao.save(column);
						service.setColumn(column);
					} else {
						res.setRet("0001");
						res.setRetInfo("当前业务已经存在根栏目，确认后再添加！！！");
						return res;
					}
				}

				// 构造返回值
				res.setColumnId(columnId);
				res.setServiceId(req.getServiceId());
				res.setColumnCode(req.getColumnCode());
				res.setColumnName(req.getColumnName());
				res.setParentColumnId(req.getParentColumnId());
				column.getRegionList().forEach(o -> {
					res.getRegionList()
							.add(new RegionForm(o.getId(), o.getRegionCode(), o.getRegionName(),
									o.getParentRegion().getId(), o.getParentRegion().getRegionName(),
									o.getStartRegionCode(), o.getEndRegionCode(), o.getRegionLevel()));
				});
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setServiceId(req.getServiceId());
				res.setRet("0001");
				res.setRetInfo("业务不存在，不能增加栏目！！！");
				return res;
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse modifyPartyColumn(PartyColumnReq req) {
		// TODO Auto-generated method stub
		ModifyColumnRes res = new ModifyColumnRes();

		if (null != req.getColumnId()) {
			PartyColumnEntity column = partyColumnDao.get(req.getColumnId());
			if (null != column) {
				String oldColumnName = column.getColumnName();
				column.setColumnCode(
						StringUtils.isNotEmpty(req.getColumnCode()) ? req.getColumnCode() : column.getColumnCode());
				column.setColumnPath(StringUtils.isNotEmpty(req.getColumnName())
						? column.getColumnPath().replaceAll(oldColumnName, req.getColumnName())
						: column.getColumnPath());
				column.setColumnName(
						StringUtils.isNotEmpty(req.getColumnName()) ? req.getColumnName() : column.getColumnName());
				column.setColumnInfo(
						StringUtils.isNotEmpty(req.getColumnInfo()) ? req.getColumnInfo() : column.getColumnInfo());
				column.setColumnType(null != req.getColumnType() ? req.getColumnType() : column.getColumnType());
				column.setColumnState(null != req.getColumnState() ? req.getColumnState() : column.getColumnState());
				column.setState(null != req.getState() ? req.getState() : column.getState());
				column.setCommendFlag(null != req.getCommendFlag() ? req.getCommendFlag() : column.getCommendFlag());
				column.setOrders(null != req.getOrders() ? req.getOrders() : 0);
				column.setTargetType(
						null != req.getTargetType() ? req.getTargetType() : EntityContext.COLUMN_TARGET_TYPE_COMMON);
				column.setMappingFolderId(StringUtils.isNotEmpty(req.getMappingFolderId()) ? req.getMappingFolderId()
						: column.getMappingFolderId());
				column.setMappingSystem(
						null != req.getMappingSystem() ? req.getMappingSystem() : EntityContext.COLUMN_MAPPING_BO);
				column.setMappingFolderName(
						StringUtils.isNotEmpty(req.getMappingFolderName()) ? req.getMappingFolderName()
								: column.getMappingFolderName());
				if (null != req.getColumnImageId()
						&& (null == column.getColumnImage() || (null != column.getColumnImage()
								&& column.getColumnImage().getId() != req.getColumnImageId()))) {
					column.setColumnImage(assetDao.get(req.getColumnImageId()));
				}

				if (null != req.getColumnPhoneImageId()
						&& ((null == column.getColumnPhoneImage()) || (null != column.getColumnPhoneImage()
								&& column.getColumnPhoneImage().getId() != req.getColumnPhoneImageId()))) {
					column.setColumnPhoneImage(assetDao.get(req.getColumnPhoneImageId()));
				}

				if (null != req.getParentColumnId()) {
					ColumnEntity parentColumn = partyColumnDao.get(req.getParentColumnId());
					if (null != parentColumn) {
						if ((null != column.getParentColumn()
								&& req.getParentColumnId() != column.getParentColumn().getId())
								|| null == column.getParentColumn()) {
							column.setParentColumn(parentColumn);
							column.setColumnPath(parentColumn.getColumnPath() + "|"
									+ (StringUtils.isNotEmpty(req.getColumnName()) ? req.getColumnName()
											: column.getColumnName()));
							column.setColumnLevel(parentColumn.getColumnLevel() + 1);
						}
					} else {
						res.setRet("0001");
						res.setRetInfo("要修改栏目的父栏目不存在！！！");
						return res;
					}
				} else {
					if (null != column.getParentColumn()) {
						column.setParentColumn(null);
						column.setColumnLevel(1);
						column.setColumnPath(StringUtils.isNotEmpty(req.getColumnName()) ? req.getColumnName()
								: column.getColumnName());
					}
				}

				// 修改栏目绑定区域
				if (StringUtils.isNotEmpty(req.getRegionIds())) {
					column.setRegionList(null);
					String[] regionIds = req.getRegionIds().split(",");
					List<RegionEntity> regionList = new ArrayList<RegionEntity>();
					for (String regionId : regionIds) {
						regionList.add(regionDao.get(Integer.parseInt(regionId)));
					}
					column.setRegionList(regionList);
				}
				if (StringUtils.isNotEmpty(req.getPartyOrganizationIdList())) {
					column.setPartyOrganizationList(null);
					String[] partyIds = req.getPartyOrganizationIdList().split(",");
					List<PartyOrganizationEntity> partyList = new ArrayList<PartyOrganizationEntity>();
					for (String partyId : partyIds) {
						partyList.add(partyOrganizationDao.get(Integer.parseInt(partyId)));
					}
					column.setPartyOrganizationList(partyList);
				}

				partyColumnDao.saveOrUpdate(column);
				res.setColumnCode(req.getColumnCode());
				res.setColumnName(req.getColumnName());
				res.setParentColumnId(req.getParentColumnId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet("0001");
				res.setRetInfo("要修改的栏目不存在！！！");
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse deletePartyColumn(PartyColumnReq req) {
		// TODO Auto-generated method stub
		GenericResponse res = new GenericResponse();

		if (null != req.getColumnId()) {
			PartyColumnEntity column = partyColumnDao.get(req.getColumnId());
			if (null != column) {
				column.getParentColumn().getSubColumnList().remove(column);
				column.setParentColumn(null);
				column.setPartyTopicList(null);
				column.setPartyOrganizationList(null);
				partyColumnDao.delete(column);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet("0001");
				res.setRetInfo("栏目不存在！！！");
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@SuppressWarnings("unchecked")
	@Override
	public IResponse getPartyColumnList(PartyColumnReq req) {
		GetPartyColumnListRes res = new GetPartyColumnListRes();

		if (null != req.getColumnId()) {
			PartyColumnEntity column = partyColumnDao.get(req.getColumnId());
			if (null != column) {
				res.setColumnId(column.getId());
				res.setParentColumnId(column.getParentColumn() == null ? 0 : column.getParentColumn().getId());
				res.setServiceId(column.getService().getId());
				res.setColumnCode(column.getColumnCode());
				res.setColumnName(column.getColumnName());
				res.setColumnType(column.getColumnType());
				res.setColumnState(column.getColumnState());
				res.setCommendFlag(column.getCommendFlag());
				res.setMappingFolderId(column.getMappingFolderId());
				res.setMappingFolderName(column.getMappingFolderName());
				res.setMappingSystem(column.getMappingSystem());
				res.setTargetType(column.getTargetType());
				res.setColumnImage(getAsset(column.getColumnImage()));
				res.setColumnPhoneImage(getAsset(column.getColumnPhoneImage()));
				res.setIsGlobal(column.getIsGlobal());
				res.setPartyOrganizationList(getPartyOrganizationFormList(column.getPartyOrganizationList()));
				res.setState(column.getState());
				column.getPartyTopicList().forEach(t->{
					PartyTopicForm topicForm = getPartyTopicForm(t,0,EntityContext.RECORD_STATE_VALID,1);
					if(null != topicForm) {
						res.getPartyTopicFormList().add(topicForm);
					}
				});
				Integer serviceId = column.getService().getId();
				if (null != req.getDepth() && req.getDepth() > 1) {
					// 查找子栏目
					res.getSubColumnList()
							.addAll((List<PartyColumnForm>) partyColumnList(column.getSubColumnList(), serviceId, column.getId(),
									req.getDepth(), EntityContext.COUNT_VISIT_OFF, null, null,
									req.getTargetType(),req.getColumnState()).get("columnList"));
				} else if (null == req.getDepth()) {
					// 查找子栏目
					res.getSubColumnList()
							.addAll((List<PartyColumnForm>) partyColumnList(column.getSubColumnList(), serviceId, column.getId(),
									contextInfo.getDefaultGetDataDepth(), EntityContext.COUNT_VISIT_OFF, null, null,
									req.getTargetType(),req.getColumnState())
											.get("columnList"));
				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet("0001");
				res.setRetInfo("要获取列表的栏目不存在！！！");
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public IResponse getPartyColumnInfo(PartyColumnReq req) {//页面需要一个详情接口
		GetPartyColumnListRes res = new GetPartyColumnListRes();

		if (null != req.getColumnId()) {
			PartyColumnEntity column = partyColumnDao.get(req.getColumnId());
			if (null != column) {
				res.setColumnId(column.getId());
				res.setParentColumnId(column.getParentColumn() == null ? 0 : column.getParentColumn().getId());
				res.setServiceId(column.getService().getId());
				res.setColumnCode(column.getColumnCode());
				res.setColumnName(column.getColumnName());
				res.setColumnType(column.getColumnType());
				res.setColumnState(column.getColumnState());
				res.setCommendFlag(column.getCommendFlag());
				res.setMappingFolderId(column.getMappingFolderId());
				res.setMappingFolderName(column.getMappingFolderName());
				res.setMappingSystem(column.getMappingSystem());
				res.setTargetType(column.getTargetType());
				res.setColumnImage(getAsset(column.getColumnImage()));
				res.setColumnPhoneImage(getAsset(column.getColumnPhoneImage()));
				res.setOrders(column.getOrders());
				res.setColumnInfo(column.getColumnInfo());
				res.setIsGlobal(column.getIsGlobal());
				res.setPartyOrganizationList(getPartyOrganizationFormList(column.getPartyOrganizationList()));
				res.setState(column.getState());
				column.getPartyTopicList().forEach(t->{
					PartyTopicForm topicForm = getPartyTopicForm(t,0,EntityContext.RECORD_STATE_VALID,1);
					if(null != topicForm) {
						res.getPartyTopicFormList().add(topicForm);
					}
				});
				Integer serviceId = column.getService().getId();
				req.setDepth(1);
				// 查找子栏目
				res.getSubColumnList()
						.addAll((List<PartyColumnForm>) partyColumnList(column.getSubColumnList(), serviceId, column.getId(),
								req.getDepth(), EntityContext.COUNT_VISIT_OFF, null, null,
								req.getTargetType(),req.getColumnState()).get("columnList"));

				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet("0001");
				res.setRetInfo("要获取列表的栏目不存在！！！");
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getServicePartyRootColumn(PartyColumnReq req) {
		// TODO Auto-generated method stub
		return null;
	}
	
	@Override
	public IResponse removeColumnTopicRelevance(PartyColumnReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getColumnId() && null != req.getTopicId()) {
			PartyColumnEntity column = partyColumnDao.get(req.getColumnId());
			PartyTopicEntity topic = partyTopicDao.get(req.getTopicId());
			if (null != column && null!= topic && column.getPartyTopicList().contains(topic)) {
				column.getPartyTopicList().remove(topic);
				topic.getPartyColumnList().remove(column);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	@Override
	public IResponse addColumnTopicRelevance(PartyColumnReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getColumnId() && StringUtils.isNotEmpty(req.getTopicIdList())) {
			PartyColumnEntity column = partyColumnDao.get(req.getColumnId());
			
			if (null != column) {
				String[] topicIds = req.getTopicIdList().split(",");
				for (String topicId : topicIds) {
					PartyTopicEntity topic = partyTopicDao.get(Integer.valueOf(topicId));
					if(null!=topic && !column.getPartyTopicList().contains(topic)) {
						column.getPartyTopicList().add(topic);
					}
				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	
	@Override
	public IResponse removeColumnPartyOrganizationRelevance(PartyColumnReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getColumnId() && null != req.getPartyOrganizationId()) {
			PartyColumnEntity column = partyColumnDao.get(req.getColumnId());
			PartyOrganizationEntity party = partyOrganizationDao.get(req.getPartyOrganizationId());
			if (null != column && null!= party && column.getPartyOrganizationList().contains(party)) {
				column.getPartyOrganizationList().remove(party);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	@Override
	public IResponse addColumnPartyOrganizationRelevance(PartyColumnReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getColumnId() && StringUtils.isNotEmpty(req.getPartyOrganizationIdList())) {
			PartyColumnEntity column = partyColumnDao.get(req.getColumnId());
			
			if (null != column) {
				String[] partyIds = req.getPartyOrganizationIdList().split(",");
				for (String partyId : partyIds) {
					PartyOrganizationEntity party = partyOrganizationDao.get(Integer.valueOf(partyId));
					if(null!=party && !column.getPartyOrganizationList().contains(party)) {
						column.getPartyOrganizationList().add(party);
					}
				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	@Override
	public IResponse getPartyColumnAssetList(PartyColumnReq req) {
		GetPartyColumnAssetList res = new GetPartyColumnAssetList();
		Object userObj = getPrincipal(true);
		Page<PartyAssetEntity> page = new Page<PartyAssetEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		String time = DateUtil.formatLongFormat(new Date());
		StringBuilder hql = new StringBuilder("select distinct a from PartyAssetEntity a "
				+ "inner join a.topicList b inner join b.partyColumnList c ");
		
		
		if(null!=userObj && userObj instanceof PartyMemberEntity) {
			StringBuilder pathHql = new StringBuilder("");
			String[] path = {};
			Integer level =0; 
			PartyMemberEntity member = (PartyMemberEntity) userObj;
			for(int i=0;i<member.getPartyOrganizationList().size();i++){
				String[] partyPath = member.getPartyOrganizationList().get(i).getPartyPath().split("/");
				int length = partyPath.length;
				if(length>level) {
					path = partyPath;
					level = length;
				}
			}
			StringBuilder pathStr = new StringBuilder("");
			for(int i=0;i<path.length-1;i++) {
				String str = path[i];
				if(pathHql.length()>0) {
					pathHql.append(" or o.partyPath='"+pathStr.toString()+"/"+str+"'");
					pathStr.append("/"+str);
				}else {
					pathHql.append(" o.partyPath='"+str+"'");
					pathStr.append(str);
				}
			}
			
			hql.append(" inner join a.auditProcess p inner join p.partOrganization o where")
				.append(" ((a.auditState=2  and a.endProcess=1 ")
				.append(" and a.isPublic in(0,1) and b.isPublic in(2,1)) ")
				.append(pathHql.length()>0 ? "or ( a.auditState IN (1,2) AND a.endProcess=0 and ("
				+pathHql.toString()+") and a.isPublic in(0,1)) and b.isPublic in(0,1))" :"");
		}else {
			hql.append(" where ")
			.append(" a.auditState=2 ")
			.append(" and a.endProcess=1 and a.isPublic in(2,1) and b.isPublic in(2,1)");
		}
		
		
		
		hql.append(" and a.assetState=2 ")
			.append(" and b.state="+EntityContext.RECORD_STATE_VALID +
					" and b.publishState=1 and b.auditState=2")
			.append(" and b.startTime<='"+time +"' and b.endTime>='"+time+"'")
			.append(" and c.state="+EntityContext.RECORD_STATE_VALID +" and c.columnState=1");
		
		hql.append(null!=req.getTargetType()? " and c.targetType="+req.getTargetType():"")
			.append(null!=req.getColumnId() ? " and c.id="+req.getColumnId():"")
			.append(StringUtils.isNotEmpty(req.getAssetName()) ?
			" and a.assetName like '%" + req.getAssetName() + "%'" :"");
		page = partyAssetDao.queryPage(page, hql.toString());
		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			PartyAssetForm asset = getPartyAssetForm(o);
			res.getAssetList().add(asset);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
}
