package com.foshan.entity.party;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;
import com.foshan.entity.RegionEntity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_party_organization")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class PartyOrganizationEntity  implements IEntityBean  {

	/**
	 * 支部组织
	 */
	private static final long serialVersionUID = -2813035636838770578L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64)  comment '支部编码'")
	private String partyCode;
	@Column(columnDefinition = "varchar(64)  comment '支部名称'")
	private String partyName;
	@Column(columnDefinition = "varchar(256)  comment '支部路径'")
	private String partyPath;
	@Column(columnDefinition = "varchar(256)  comment '支部地址'")
	private String partyAddress;
	@Column(columnDefinition = "varchar(64)  comment '支部电话'")
	private String partyPhone;
	@Column(columnDefinition = "longtext  comment '简介'")
	private String birfe;
	@Column(columnDefinition = "int(2) comment '排序'")
	private Integer orderNum;
	@Column(columnDefinition = "int(2) comment '支部类型 0：政府机关；1：企业；2：学校；'")
	private Integer partyType;
	@Column(columnDefinition = "int(2) comment '1：党支部；2：党总支；3：党委；'")
	private Integer organizationLevel;
	@Column(columnDefinition = "longtext  comment '更新日志（json）'")
	private String updateLog;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '时间'")
	private Timestamp createTime;
	@Column(columnDefinition = "Timestamp  comment '最后修改时间'")
	private Timestamp lastModifyTime;
	@Column(columnDefinition = "int(1) default 1 comment '状态 0--无效数据  1--有效数据'")
	private Integer state;
	@Column(columnDefinition = "int(1) default 1 comment '是否需要审核 0--否  1--是'")
	private Integer needAudit;
	@Column(columnDefinition = "int(1) default 1 comment '审核状态'")
	private Integer auditState;

	
	
	@ManyToMany(targetEntity = PartyColumnEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_column_party_organization", joinColumns = @JoinColumn(name = "partyId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "columnId", referencedColumnName = "id"))
	@JsonIgnore
	private List<PartyColumnEntity> partyColumnList = new ArrayList<PartyColumnEntity>();
	
	@ManyToOne(targetEntity = PartyOrganizationEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentPartyOrganizationId", referencedColumnName = "id", nullable = true)
	private PartyOrganizationEntity parentPartyOrganization;
	
	@OneToMany(targetEntity = PartyOrganizationEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentPartyOrganizationId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<PartyOrganizationEntity> subPartyOrganizationList = new ArrayList<PartyOrganizationEntity>();

	@ManyToMany(targetEntity = PartyMemberEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_member_organization", joinColumns = @JoinColumn(name = "partyId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "memberId", referencedColumnName = "id"))
	private List<PartyMemberEntity> partyMemberList = new ArrayList<PartyMemberEntity>();
	
	@OneToMany(targetEntity = PartyTopicEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "partyId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<PartyTopicEntity> partyTopicList = new ArrayList<PartyTopicEntity>();
	
	@ManyToOne(targetEntity = RegionEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "regionId", referencedColumnName = "id", nullable = true)
	private RegionEntity region;
	
	@OneToMany(targetEntity = PartyTopicEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "processId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<PartyTopicEntity> pendingTopicList = new ArrayList<PartyTopicEntity>();
	
	@OneToMany(targetEntity = PartyAuditProcessEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "partyId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<PartyAuditProcessEntity> partyAuditProcessList = new ArrayList<PartyAuditProcessEntity>();
	@OneToMany(targetEntity = PartyAssetEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "partyId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<PartyAssetEntity> assetList = new ArrayList<PartyAssetEntity>();
}
