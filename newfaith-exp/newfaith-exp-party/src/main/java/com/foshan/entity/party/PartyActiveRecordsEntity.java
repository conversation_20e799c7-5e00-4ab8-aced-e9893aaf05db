package com.foshan.entity.party;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_party_active_records")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class PartyActiveRecordsEntity implements IEntityBean {

	/**
	 * 活动记录
	 */
	private static final long serialVersionUID = -615655874121265037L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "Timestamp comment '记录时间'")
	private Timestamp recordTime;
	@Column(columnDefinition = "int(2) comment '记录类型 0:学习材料；1：活动学习；'")
	private Integer recordType;
	@Column(columnDefinition = "int(2) comment '是否有效学习  0:否；1：是（活动类型的学习，需要签到后才为有效学习）；'")
	private Integer isValid;
	
	
	@ManyToOne(targetEntity = PartyTopicEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "topicId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private PartyTopicEntity partyTopic;
	
	@ManyToMany(targetEntity = AssetEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_active_records_asset", joinColumns = @JoinColumn(name = "activeRecordsId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "assetId", referencedColumnName = "id"))
	@JsonIgnore
	private List<AssetEntity> assetList = new ArrayList<AssetEntity>();
	
	@ManyToOne(targetEntity = PartyMemberEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private PartyMemberEntity partyMember;
}
