package com.foshan.entity.party;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_party_audit_process")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class PartyAuditProcessEntity implements IEntityBean {
	/**
	 * 
	 */
	private static final long serialVersionUID = 8096925931789187474L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(254)  comment '流程名称'")
	private String processName;
	@Column(columnDefinition = "int(2) comment '指定用户  0:指定角色；1：指定用户；'")
	private Integer appointUsuer;
	@Column(columnDefinition = "int(2) comment '流程顺序，从低到高'")
	private Integer orders;
	@OneToMany(targetEntity = PartyAuditorEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "auditProcessId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<PartyAuditorEntity> partyAuditorList = new ArrayList<PartyAuditorEntity>();
	@ManyToOne(targetEntity = PartyOrganizationEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "partyId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private PartyOrganizationEntity partOrganization;
	
	@OneToMany(targetEntity = PartyAssetEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "auditProcessId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<PartyAssetEntity> assetList = new ArrayList<PartyAssetEntity>();
}
