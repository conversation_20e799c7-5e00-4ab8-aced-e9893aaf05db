package com.foshan.util.community;

/*import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

import javax.servlet.http.HttpServletResponse;*/

public class PoiUtils {

/*
    public static void excelToPdf(HSSFWorkbook workbook,int sheetAt,HttpServletResponse response) throws IOException, DocumentException {
        Document document = new Document(PageSize.A4.rotate());

        BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        Font font = new Font(baseFont);
        font.setSize(10);

        PdfWriter writer = PdfWriter.getInstance(document,new FileOutputStream("E:\\22.pdf"));

        document.open();
        //HSSFWorkbook workbook = new HSSFWorkbook(new FileInputStream(new File("E:\\11.xls")));
        for(int s=0;s<sheetAt;s++) {
            HSSFSheet sheet = workbook.getSheetAt(s);
            
            int column = sheet.getRow(0).getLastCellNum();
            int row = sheet.getPhysicalNumberOfRows();

            PdfPTable table = new PdfPTable(column-sheet.getRow(0).getFirstCellNum());

            String str = null;
            for (int i = sheet.getFirstRowNum(); i < row; i++) {
                for (int j = sheet.getRow(0).getFirstCellNum(); j < column; j++) {
                    //得到excel单元格的内容
                    HSSFCell cell = sheet.getRow(i).getCell(j);
                    //if(cell.getCellType() == HSSFCell.CELL_TYPE_NUMERIC) {
                    if(null != cell) {
                        if(cell.getCellType() == CellType.NUMERIC) {
                            str = (int)cell.getNumericCellValue() + "";
                        }else if(cell.getCellType() == CellType.FORMULA) {
                        	str = cell.getCellFormula();
                        }else{
                            str = cell.getStringCellValue();
                        }
                    }

                    //创建pdf单元格对象，并往pdf单元格对象赋值。
                    PdfPCell cells = new PdfPCell(new Paragraph(str, font));
                    //pdf单元格对象添加到table对象
                    table.addCell(cells);
                }
            }
            document.add(table);
        }

        //   2.设置请求头，encode文件名
        response.setContentType("application/pdf;charset=UTF-8");
        response.setHeader("Content-Disposition", 
        "attachment; filename=" + java.net.URLEncoder.encode("" + 
         "11.pdf", "UTF-8"));
     //   3.通过流将pdf实例写出到浏览器
      //  PdfWriter writer = PdfWriter.getInstance(document, response.getOutputStream());
        
        document.close();
        writer.close();
    }
    
    */

    

}

