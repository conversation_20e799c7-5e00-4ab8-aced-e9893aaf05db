package com.foshan.util.community;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import org.apache.commons.lang3.StringUtils;

import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityMeterEntity;
import com.foshan.entity.community.CommunityPayItemsEntity;
import com.foshan.entity.community.CommunityReceivablesEntity;
import com.foshan.entity.community.vo.CarBillingDateVo;
import com.foshan.entity.community.vo.MeterRecordVo;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CommunityCache {
	/*
	 * 初始化缓存数据
	 */
	public static Map<Integer, CommunityEstateEntity> estateList = new HashMap<>();
	public static Map<Integer, CommunityPayItemsEntity> payItemsList = new HashMap<>();
	public static Map<String, Integer> payItemsNameList = new HashMap<>();
	public static Map<Integer, CommunityMeterEntity> meterList = new HashMap<>();
	public static Map<Integer, List<CarBillingDateVo>> carBillingDate = new HashMap<>();
	@SuppressWarnings("rawtypes")
	public static Map<String, Map<String, Collection>> cache = new HashMap<>();

	/*
	 * 报表缓存对象 Map<报表名称,Map<key,Map<项目(title/data),内容(String[]/Collection>>>
	 */
	public static Map<String, Map<String, Map<String, Object>>> statisticsCache = new HashMap<>();

	/*
	 * 应收费用计算缓存数据
	 */
	// 单元应收款对象<单元id,Map<应收时间(yyyy年MM月),Map<费用名称PayItemsName,应收款数据对象>>>,按单元id排序
	public static Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> receivableCalEstateList = new TreeMap<>();

	// 单元应收款对象<应收时间(yyyy年MM月),Map<单元id,Map<费用名称payItemsName,应收款数据对象>>>,按应收时间排序
	public static Map<String, Map<Integer, Map<String, Set<CommunityReceivablesEntity>>>> receivableCalDateList = new TreeMap<>();

	// 单元应收款对象<单元id,Map<费用名称PayItemsName,Map<应收时间(yyyy年MM月),应收款数据对象>>>,按单元id，费用名称及时间排序
	public static Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> receivableCalNameList = new TreeMap<>();

	// 公用分摊数据缓存<总表id,总表分摊记录>
	public static Map<Integer, MeterRecordVo> allocationList = new TreeMap<>();
	// 单元公用分摊数据缓存<总表id,总表分摊记录>
//	public static List<AllocationItemVo> itemAllocationList  = new ArrayList<>();

	/*
	 * 检查缓存中是否存在应收款数据对象
	 */
	public static Boolean checkReceivableCache(CommunityReceivablesEntity receivables,
			Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> target) {
		Boolean result = false;
		try {
			if (null != receivables) {
				Integer estateId = receivables.getEstate().getId();
				String payItemsName = receivables.getPayItemsName();
				LocalDate receivableDate = receivables.getReceivableDate().toInstant().atZone(ZoneId.systemDefault())
						.toLocalDate();
				result = checkReceivableCache(estateId, receivableDate, payItemsName, target);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return result;
	}

	/*
	 * 检查缓存中是否存在应收款数据对象
	 */
	public static Boolean checkReceivableCache(Integer estateId, LocalDate receivableDate, String payItemsName,
			Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> target) {
		Boolean result = false;
		try {
			String dateStr = receivableDate.format(DateTimeFormatter.ofPattern("yyyy年MM月"));
			if (target.size() > 0 && target.containsKey(estateId) && target.get(estateId).containsKey(dateStr)
					&& target.get(estateId).get(dateStr).containsKey(payItemsName)) {
				result = true;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return result;
	}

	/*
	 * 向缓存中增加应收款对象
	 */
	public static Boolean putReceivableCache(CommunityReceivablesEntity receivables,
			Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> target) {
		Boolean result = false;
		try {
			CommunityEstateEntity estate = receivables.getEstate();
			Integer estateId = estate.getId();
			String payItemsName = receivables.getPayItemsName();
			LocalDate receivableDate = receivables.getReceivableDate().toInstant().atZone(ZoneId.systemDefault())
					.toLocalDate();
			String dateStr = "";
			if (receivables.getChargeCategory().equals("分摊费")) {
				dateStr = receivableDate.plus(1, ChronoUnit.MONTHS).format(DateTimeFormatter.ofPattern("yyyy年MM月"));
			} else if (receivables.getChargeCategory().equals("车位费") && receivableDate.getDayOfMonth() == 1) {
				dateStr = receivableDate.plus(-1, ChronoUnit.MONTHS).format(DateTimeFormatter.ofPattern("yyyy年MM月"));
			} else {
				dateStr = receivableDate.format(DateTimeFormatter.ofPattern("yyyy年MM月"));
			}
			Map<String, Set<CommunityReceivablesEntity>> temp1 = null;
			Map<String, Map<String, Set<CommunityReceivablesEntity>>> temp2 = null;
			if (target.size() > 0) {
				if (target.containsKey(estateId)) {
					temp2 = target.get(estateId);
				} else {
					temp2 = new TreeMap<>();
				}

				if (temp2.containsKey(dateStr)) {
					temp1 = temp2.get(dateStr);
				} else {
					temp1 = new HashMap<>();
				}

				Set<CommunityReceivablesEntity> ss = null;
				if (temp1.containsKey(payItemsName)) {
					ss = temp1.get(payItemsName);
				} else {
					ss = new HashSet<>();
				}

				if (ss.contains(receivables)) {
					ss.remove(receivables);
				}
				ss.add(receivables);
				temp1.put(payItemsName, ss);
				temp2.put(dateStr, temp1);
			} else {
				Set<CommunityReceivablesEntity> ss = new HashSet<>();
				ss.add(receivables);
				temp1 = new HashMap<>();
				temp1.put(payItemsName, ss);
				temp2 = new TreeMap<>();
				temp2.put(dateStr, temp1);
			}
			target.put(estateId, temp2);
			result = true;
		} catch (Exception ex) {
			ex.printStackTrace();
			log.info("应收缓存增加数据失败！！！" + receivables.getPayItemsName() + ":::" + receivables.getReceivableAmount());
		}
		return result;
	}

	/*
	 * 获取应收款数据队列：根据estateId、receivableDate、payItemsName的各种组合获取队列结果
	 */
	public static List<CommunityReceivablesEntity> getReceivableCacheList(Integer estateId, LocalDate receivableDate,
			String payItemsName, Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> target) {
		List<CommunityReceivablesEntity> receivablesList = new ArrayList<>();
		if (target.size() > 0) {
			if (null != estateId && null != receivableDate && StringUtils.isNotEmpty(payItemsName)) {
				String dateStr = receivableDate.format(DateTimeFormatter.ofPattern("yyyy年MM月"));
				if (checkReceivableCache(estateId, receivableDate, payItemsName, target)) {
					receivablesList.addAll(target.get(estateId).get(dateStr).get(payItemsName));
				}
			} else if (null != estateId && null != receivableDate && StringUtils.isEmpty(payItemsName)) {
				String dateStr = receivableDate.format(DateTimeFormatter.ofPattern("yyyy年MM月"));
				if (target.containsKey(estateId) && target.get(estateId).containsKey(dateStr)) {
					Map<String, Set<CommunityReceivablesEntity>> temp = target.get(estateId).get(dateStr);
					temp.keySet().forEach(o -> {
						receivablesList.addAll(temp.get(o));
					});
				}
			} else if (null != estateId && null == receivableDate && StringUtils.isNotEmpty(payItemsName)) {
				if (target.containsKey(estateId)) {
					target.get(estateId).keySet().forEach(o -> {
						if (target.get(estateId).get(o).containsKey(payItemsName)) {
							receivablesList.addAll(target.get(estateId).get(o).get(payItemsName));
						}
					});
				}
			} else if (null != estateId && null == receivableDate && StringUtils.isEmpty(payItemsName)) {
				if (target.containsKey(estateId)) {
					target.get(estateId).keySet().forEach(o -> {
						target.get(estateId).get(o).keySet().forEach(b -> {
							receivablesList.addAll(target.get(estateId).get(o).get(b));
						});
					});
				}
			} else if (null == estateId && null != receivableDate && StringUtils.isNotEmpty(payItemsName)) {
				target.keySet().forEach(o -> {
					String dateStr = receivableDate.format(DateTimeFormatter.ofPattern("yyyy年MM月"));
					if (target.get(o).containsKey(dateStr)) {
						target.get(o).get(dateStr).keySet().forEach(b -> {
							if (target.get(o).get(dateStr).containsKey(payItemsName)) {
								receivablesList.addAll(target.get(o).get(dateStr).get(payItemsName));
							}
						});
					}
				});
			} else if (null == estateId && null != receivableDate && StringUtils.isEmpty(payItemsName)) {
				target.keySet().forEach(o -> {
					String dateStr = receivableDate.format(DateTimeFormatter.ofPattern("yyyy年MM月"));
					if (target.get(o).containsKey(dateStr)) {
						target.get(o).get(dateStr).keySet().forEach(b -> {
							receivablesList.addAll(target.get(o).get(dateStr).get(b));
						});
					}
				});
			} else if (null == estateId && null == receivableDate && StringUtils.isNotEmpty(payItemsName)) {
				target.keySet().forEach(o -> {
					target.get(o).keySet().forEach(b -> {
						if (target.get(o).get(b).containsKey(payItemsName)) {
							receivablesList.addAll(target.get(o).get(b).get(payItemsName));
						}
					});
				});
			} else {
				target.keySet().forEach(o -> {
					target.get(o).keySet().forEach(b -> {
						target.get(o).get(b).keySet().forEach(c -> {
							receivablesList.addAll(target.get(o).get(b).get(c));
						});
					});
				});
			}
		}

		return receivablesList;
	}

	/*
	 * 获取应收款数据队列：根据estateId、dateStr(yyyy年MM月)、payItemsName的各种组合获取队列结果
	 */
	public static List<CommunityReceivablesEntity> getReceivableCacheList(Integer estateId, String dateStr,
			String payItemsName, Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> target) {
		List<CommunityReceivablesEntity> receivablesList = new ArrayList<>();
		LocalDate receivableDate = null;
		if (StringUtils.isNotEmpty(dateStr)) {
			receivableDate = LocalDate.parse((dateStr + "01").replaceAll("年|月", "-"));
		}
		receivablesList = getReceivableCacheList(estateId, receivableDate, payItemsName, target);
		return receivablesList;
	}

	/*
	 * 获取应收款数据队列：根据estateId、Set<dateStr(yyyy年MM月)>、payItemsName的各种组合获取队列结果
	 */
	public static List<CommunityReceivablesEntity> getReceivableCacheList(Integer estateId, Set<String> dateStrSet,
			String payItemsName, Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> target) {
		List<CommunityReceivablesEntity> receivablesList = new ArrayList<>();
		dateStrSet.forEach(o -> {
			LocalDate receivableDate = LocalDate.parse((o + "01").replaceAll("年|月", "-"));
			receivablesList.addAll(getReceivableCacheList(estateId, receivableDate, payItemsName, target));
		});
		return receivablesList;
	}

	/*
	 * 删除缓存内容
	 */
	public static Boolean removeReceivableCache(CommunityReceivablesEntity receivables,
			Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> target) {
		Boolean result = true;

		try {
			if (checkReceivableCache(receivables, target)) {
				Integer estateId = receivables.getEstate().getId();
				String dateStr = receivables.getReceivableDate().toInstant().atZone(ZoneId.systemDefault())
						.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy年MM月"));
				String payItemsName = receivables.getPayItemsName();
				target.get(estateId).get(dateStr).get(receivables.getPayItemsName()).remove(receivables);

				if (target.get(estateId).get(dateStr).get(payItemsName).size() == 0) {
					target.get(estateId).get(dateStr).remove(payItemsName);
				}

				if (target.get(estateId).get(dateStr).size() == 0) {
					target.get(estateId).remove(dateStr);
				}

				if (target.get(estateId).size() == 0) {
					target.remove(estateId);
				}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
			result = false;
		}
		return result;
	}

	/*
	 * 删除缓存内容
	 */
	public static Boolean removeReceivableCache(Integer estateId, LocalDate receivableDate, String payItemsName,
			Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> target) {
		Boolean result = true;
		try {
			if (null != estateId && null != receivableDate && StringUtils.isNotEmpty(payItemsName)) {
				String dateStr = receivableDate.format(DateTimeFormatter.ofPattern("yyyy年MM月"));
				if (checkReceivableCache(estateId, receivableDate, payItemsName, target)) {
					target.get(estateId).get(dateStr).remove(payItemsName);

					if (target.get(estateId).get(dateStr).get(payItemsName).size() == 0) {
						target.get(estateId).get(dateStr).remove(payItemsName);
					}

					if (target.get(estateId).get(dateStr).size() == 0) {
						target.get(estateId).remove(dateStr);
					}

					if (target.get(estateId).size() == 0) {
						target.remove(estateId);
					}
				}
			} else if (null != estateId && null != receivableDate && StringUtils.isEmpty(payItemsName)) {
				String dateStr = receivableDate.format(DateTimeFormatter.ofPattern("yyyy年MM月"));
				if (target.containsKey(estateId) && target.get(estateId).containsKey(dateStr)) {
					target.get(estateId).remove(dateStr);

					if (target.get(estateId).size() == 0) {
						target.remove(estateId);
					}
				}

			} else if (null != estateId && null == receivableDate && StringUtils.isNotEmpty(payItemsName)) {
				if (target.containsKey(estateId)) {
					target.get(estateId).keySet().forEach(o -> {
						target.get(estateId).get(o).remove(payItemsName);

						if (target.get(estateId).get(o).size() == 0) {
							target.get(estateId).remove(o);
						}
					});

					if (target.get(estateId).size() == 0) {
						target.remove(estateId);
					}
				}

			} else if (null != estateId && null == receivableDate && StringUtils.isEmpty(payItemsName)) {
				if (target.containsKey(estateId)) {
					target.remove(estateId);
				}
			} else if (null == estateId && null != receivableDate && StringUtils.isNotEmpty(payItemsName)) {
				target.keySet().forEach(o -> {
					String dateStr = receivableDate.format(DateTimeFormatter.ofPattern("yyyy年MM月"));
					if (checkReceivableCache(o, receivableDate, payItemsName, target)) {
						target.get(o).get(dateStr).remove(payItemsName);
						if (target.get(o).get(dateStr).size() == 0) {
							target.get(o).remove(dateStr);
						}
						if (target.get(o).size() == 0) {
							target.remove(o);
						}
					}
				});
			} else if (null == estateId && null != receivableDate && StringUtils.isEmpty(payItemsName)) {
				target.keySet().forEach(o -> {
					String dateStr = receivableDate.format(DateTimeFormatter.ofPattern("yyyy年MM月"));
					if (target.containsKey(o) && target.get(o).containsKey(dateStr)) {
						target.get(o).remove(dateStr);

						if (target.get(o).size() == 0) {
							target.remove(o);
						}
					}
				});
			} else if (null == estateId && null == receivableDate && StringUtils.isNotEmpty(payItemsName)) {
				target.keySet().forEach(o -> {
					target.get(o).keySet().forEach(b -> {
						target.get(o).get(b).remove(payItemsName);
						if (target.get(o).get(b).size() == 0) {
							target.get(o).remove(b);
						}
					});

					if (target.get(o).size() == 0) {
						target.remove(o);
					}
				});
			}

		} catch (Exception ex) {
			ex.printStackTrace();
			result = false;
		}
		return result;
	}

	/*
	 * 合并两个Map的内容(source1+source2=target)
	 */
	public static Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> addReceivableCache(
			Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> source1,
			Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> source2) {
		getReceivableCacheList(null, "", "", source2).forEach(receivables -> {
			// 生成预收的应收项，把ID置为随机负数
			if (receivables.getId() == null) {
				if (receivables.hashCode() < 0) {
					receivables.setId(receivables.hashCode());
				} else {
					receivables.setId(0 - receivables.hashCode());
				}
			}
			putReceivableCache(receivables, source1);
		});

		return source1;

	}

	/*
	 * 单元应收款对象列表格式转换 receivableCalEstateList-->receivableCalDataList
	 */
	public static Map<String, Map<Integer, Map<String, Set<CommunityReceivablesEntity>>>> parseReceivableEstateList2Date(
			Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> receivableEstateList) {
		Map<String, Map<Integer, Map<String, Set<CommunityReceivablesEntity>>>> result = new HashMap<>();
		for (Integer estateId : receivableEstateList.keySet()) {
			Map<String, Map<String, Set<CommunityReceivablesEntity>>> temp = receivableEstateList.get(estateId);
			for (String dateStr : temp.keySet()) {
				Map<String, Set<CommunityReceivablesEntity>> d1 = temp.get(dateStr);
				Map<Integer, Map<String, Set<CommunityReceivablesEntity>>> d2 = null;
				if (result.size() > 0 && result.containsKey(dateStr)) {
					d2 = result.get(dateStr);
				} else {
					d2 = new HashMap<>();
				}
				d2.put(estateId, d1);
				result.put(dateStr, d2);
			}
		}
		return result;
	}

	/*
	 * 单元应收款对象列表格式转换 receivableCalEstateList-->receivableCalNameList
	 */
	public static Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> parseReceivableEstateList2Name(
			Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> receivableEstateList) {
		Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> result = new HashMap<>();
		for (Integer estateId : receivableEstateList.keySet()) {
			Map<String, Map<String, Set<CommunityReceivablesEntity>>> temp = receivableEstateList.get(estateId);

			Map<String, Map<String, Set<CommunityReceivablesEntity>>> r1 = null;
			if (result.containsKey(estateId)) {
				r1 = result.get(estateId);
			} else {
				r1 = new TreeMap<>();
			}
			for (String dateStr : temp.keySet()) {
				Map<String, Set<CommunityReceivablesEntity>> d1 = temp.get(dateStr);
				Map<String, Set<CommunityReceivablesEntity>> p1 = null;
				for (String payItemName : d1.keySet()) {
					if (r1.containsKey(payItemName)) {
						p1 = r1.get(payItemName);
					} else {
						p1 = new TreeMap<>();
					}
					p1.put(dateStr, d1.get(payItemName));
					r1.put(payItemName, p1);
				}
			}
			result.put(estateId, r1);
		}
		return result;
	}

	/*
	 * 单元应收款对象列表格式转换 receivableCalDateList-->receivableCalEstateList
	 */
	public static Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> parseReceivableDateList2Estate(
			Map<String, Map<Integer, Map<String, Set<CommunityReceivablesEntity>>>> receivableDateList) {
		Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> result = new HashMap<>();

		return result;
	}

	/*
	 * 单元应收款对象列表格式转换 receivableCalDateList-->receivableCalNameList
	 */
	public static Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> parseReceivableDateList2Name(
			Map<String, Map<Integer, Map<String, Set<CommunityReceivablesEntity>>>> receivableDateList) {
		Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> result = new HashMap<>();

		return result;
	}

	/*
	 * 单元应收款对象列表格式转换 receivableCalNameList-->receivableCalEstateList
	 */
	public static Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> parseReceivableNameList2Estate(
			Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> receivableNameList) {
		Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> result = new HashMap<>();

		return result;
	}

	/*
	 * 单元应收款对象列表格式转换 receivableCalNameList-->receivableCalDateList
	 */
	public static Map<String, Map<Integer, Map<String, Set<CommunityReceivablesEntity>>>> parseReceivableNameList2Date(
			Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> receivableNameList) {
		Map<String, Map<Integer, Map<String, Set<CommunityReceivablesEntity>>>> result = new HashMap<>();

		return result;
	}

	/*
	 * 集合框架分页方法--针对List、Set分页 输入参数： source：需要分页的源队列，可以为List，也可以为Set
	 * pageSize：每个分页队列的大小，默认每页100条记录 requestPage：当前请求页面，默认为1 返回参数：
	 * CommunityPage对象，包括resultList对象为List类型，其余参数为currentPage、pageSize、totalCount(
	 * 总记录数)、totalPage
	 */
	@SuppressWarnings("unchecked")
	public static <E, T extends Collection<E>> CommunityPage<T> getPage(T source, Integer pageSize,
			Integer requestPage) {
		CommunityPage<T> page = new CommunityPage<>();
		pageSize = null != pageSize && pageSize > 0 ? pageSize : 100;
		page.setPageSize(pageSize);
		page.setTotalCount(null != source ? source.size() : 0);
		page.setTotalPage((page.getTotalCount() - 1) / pageSize + 1);

		Map<Integer, Set<E>> result = new LinkedHashMap<>();
		Set<E> sub = new LinkedHashSet<>();
		Integer idx = 1;
		Integer batch = 1;

		for (E a : source) {
			sub.add(a);
			if (idx == pageSize) {
				idx = 0;
				result.put(batch, sub);
				sub = new LinkedHashSet<>();
				batch++;
			}
			idx++;
		}

		result.put(batch, sub);

		requestPage = null == requestPage ? 1 : requestPage > batch ? batch : requestPage;
		page.setCurrentPage(requestPage);
		if (source instanceof Set) {
			page.setResultList((T) new LinkedHashSet<E>(result.get(requestPage)));
		} else {
			page.setResultList((T) new LinkedList<E>(result.get(requestPage)));
		}
		page.setResultList((T) result.get(requestPage));
//		page.getResultList().add((T) result.get(requestPage));
		return page;
	}

	/*
	 * 集合框架分页方法--针对Map分页 输入参数： source：需要分页的源队列，可以为List，也可以为Set
	 * pageSize：每个分页队列的大小，默认每页100条记录 requestPage：当前请求页面，默认为1 返回参数：
	 * CommunityPage对象，包括resultList对象为List<Map>类型，其余参数为currentPage、pageSize、
	 * totalCount(总记录数)、totalPage
	 */
	@SuppressWarnings("unchecked")
	public static <k, v, T extends Map<k, v>> CommunityPage<T> getPage(T source, Integer pageSize,
			Integer requestPage) {
		CommunityPage<T> page = new CommunityPage<>();
		pageSize = null != pageSize && pageSize > 0 ? pageSize : 100;
		page.setPageSize(pageSize);
		page.setTotalCount(null != source ? source.size() : 0);
		page.setTotalPage((page.getTotalCount() - 1) / pageSize + 1);

		Map<Integer, Map<k, v>> result = new TreeMap<>();
		Map<k, v> sub = new TreeMap<>(Collections.reverseOrder());
		Iterator<k> itr = source.keySet().iterator();
		Integer idx = 1;
		Integer batch = 1;
		while (itr.hasNext()) {
			k id = itr.next();
			sub.put(id, source.get(id));
			if (idx == pageSize) {
				idx = 0;
				result.put(batch, sub);
				sub = new TreeMap<>(Collections.reverseOrder());
				batch++;
			}
			idx++;
		}
		result.put(batch, sub);

		requestPage = null == requestPage ? 1 : requestPage > batch ? batch : requestPage;
		page.setCurrentPage(requestPage);
		page.setResultList((T) result.get(requestPage));
//		page.getResultList().add((T) result.get(requestPage));
		return page;
	}

	public static Integer getChargeCategory(String payItemsName) {
		Integer chargeCategory = 0;

		for (CommunityPayItemsEntity o : payItemsList.values()) {
			if (o.getItemsName().equals(payItemsName)) {
				chargeCategory = o.getChargeCategory();
			}
		}
		return chargeCategory;
	}

}
