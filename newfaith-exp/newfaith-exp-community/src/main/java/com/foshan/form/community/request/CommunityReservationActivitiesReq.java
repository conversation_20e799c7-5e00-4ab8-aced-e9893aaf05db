package com.foshan.form.community.request;


import javax.persistence.Column;
import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.StringJoiner;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="活动请求参数(CommunityReservationActivitiesReq)")
public class CommunityReservationActivitiesReq extends BasePageRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = 3595264918592396622L;
	@ApiModelProperty(value = "活动ID", example = "1")
	private Integer activitiesId;
	@ApiModelProperty(value = "预约活动名称")
	private String activitiesName;
	@ApiModelProperty(value = "介绍")
	private String centent;
	@Column(columnDefinition = "text comment '协议'")
	@ApiModelProperty(value = "协议")
	private String agreement;
	@Column(columnDefinition = "int(2) comment '需要确认协议 0否1是'")
	private Integer needConfirmAgreement;
	@ApiModelProperty(value = "开始时间")
	protected String startTime;
	@ApiModelProperty(value = "结束时间")
	protected String endTime;
	@ApiModelProperty(value = "策略ID", example = "1")
	private Integer strategyId;
	@ApiModelProperty(value = "区域ID", example = "1")
	private Integer publicAreaId;
	@ApiModelProperty(value = "是否生效 0否1是", example = "1")
	private Integer employ;
	@ApiModelProperty(value = "状态 0--无效数据  1--有效数据", example = "1")
	private Integer state;
	@ApiModelProperty(value = "排序值", example = "1")
	private Integer orders;

	@Override
	public String toString() {
		return new StringJoiner(", ", CommunityReservationActivitiesReq.class.getSimpleName() + "[", "]")
				.add("smartcardId='" + smartcardId + "'")
				.add("userCode='" + userCode + "'")
				.add("regionCode='" + regionCode + "'")
				.add("ipAddr='" + ipAddr + "'")
				.add("phone='" + phone + "'")
				.add("macAddr='" + macAddr + "'")
				.add("activitiesId=" + activitiesId)
				.add("activitiesName='" + activitiesName + "'")
				.add("centent='" + centent + "'")
				.add("agreement='" + agreement + "'")
				.add("needConfirmAgreement=" + needConfirmAgreement)
				.add("startTime='" + startTime + "'")
				.add("endTime='" + endTime + "'")
				.add("strategyId=" + strategyId)
				.add("publicAreaId=" + publicAreaId)
				.add("employ=" + employ)
				.add("state=" + state)
				.add("orders=" + orders)
				.toString();
	}
}
