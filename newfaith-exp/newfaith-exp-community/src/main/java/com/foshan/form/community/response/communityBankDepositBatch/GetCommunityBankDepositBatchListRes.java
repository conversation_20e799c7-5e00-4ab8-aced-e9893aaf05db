package com.foshan.form.community.response.communityBankDepositBatch;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityBankDepositBatchForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="银行划账批次(GetCommunityBankDepositBatchListReq)")
public class GetCommunityBankDepositBatchListRes extends BasePageResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "银行划账批次列表")
	private List<CommunityBankDepositBatchForm> communityBankDepositBatchList = new ArrayList<CommunityBankDepositBatchForm>(); 

}
