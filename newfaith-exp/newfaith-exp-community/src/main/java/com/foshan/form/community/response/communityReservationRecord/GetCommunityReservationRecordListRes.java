package com.foshan.form.community.response.communityReservationRecord;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityReservationRecordForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取预约记录返回列表对象(GetCommunityReservationRecordListRes)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityReservationRecordListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -7288951495183551132L;
	@ApiModelProperty(value = "预约记录列表")
	private List<CommunityReservationRecordForm> reservationRecordList = new ArrayList<CommunityReservationRecordForm>(); 

}
