package com.foshan.form.community.response.communityRole;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityRoleForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="社区角色列表响应(GetCommunityRoleListRes)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityRoleListRes extends BasePageResponse {

	/**
	 * 社区角色列表响应对象
	 */
	private static final long serialVersionUID = 1026288215689048283L;
	
	@ApiModelProperty(value = "社区角色列表")
	private List<CommunityRoleForm> communityRoleList = new ArrayList<CommunityRoleForm>();

}
