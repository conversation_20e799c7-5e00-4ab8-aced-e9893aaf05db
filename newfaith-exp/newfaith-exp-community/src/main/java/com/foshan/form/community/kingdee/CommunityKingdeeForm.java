package com.foshan.form.community.kingdee;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
@ApiModel(value = "金蝶入帐对象(CommunityKingdeeForm)")
public class CommunityKingdeeForm implements IForm {
	/**
	* 
	*/
	private static final long serialVersionUID = -7620429743190524866L;
	private Integer importId;
	private String importDate;
	private String startDate;
	private String endDate;
	private String fileName;
	private String fileType;
	private Integer importState;

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

	public CommunityKingdeeForm(Integer importId, String importDate, String startDate, String endDate, String fileName,
			String fileType, Integer importState) {
		super();
		this.importId = importId;
		this.importDate = importDate;
		this.startDate = startDate;
		this.endDate = endDate;
		this.fileName = fileName;
		this.fileType = fileType;
		this.importState = importState;
	}

}
