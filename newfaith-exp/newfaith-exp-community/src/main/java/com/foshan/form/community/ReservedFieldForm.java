package com.foshan.form.community;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class ReservedFieldForm {
	@ApiModelProperty(value = "产权停车位面积")
	private String propertyParkingArea;
	@ApiModelProperty(value = "产权停车位个数")
	private String propertyParkingNum;
	@ApiModelProperty(value = "人防停车位面积")
	private String defenceParkingArea;
	@ApiModelProperty(value = "人防停车位个数")
	private String defenceParkingNum;
	@ApiModelProperty(value = "水表底数")
	private String waterMeterBase;
	@ApiModelProperty(value = "电表底数")
	private String electricMeterBase;
	@ApiModelProperty(value = "银行划扣全国平台客户编号")
	private String accountCustomerId;
	@ApiModelProperty(value = "银行划扣全国平台协议号")
	private String accountContractNo;
	@ApiModelProperty(value = "协议时间")
	private String accountContractTime;
	@ApiModelProperty(value = "存量协议")
	private String accountContractComment;
	@ApiModelProperty(value = "应缴费是否需要短信提醒 0：否；1是")
	private String isSendMessage;
}
