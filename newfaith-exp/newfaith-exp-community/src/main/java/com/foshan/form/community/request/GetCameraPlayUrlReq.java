package com.foshan.form.community.request;

import com.foshan.form.request.BaseRequest;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "请求播放摄像头视频流(GetPlayUrlRequest)")
public class GetCameraPlayUrlReq extends BaseRequest{
	/**
	 * 
	 */
	private static final long serialVersionUID = -95775313865419882L;
	// userId
	private Integer userId;
	//srs streamId 通常是融合视频平台的guid
	private String roomId;
//	private String serviceId;
//    private String regionCode;
}
