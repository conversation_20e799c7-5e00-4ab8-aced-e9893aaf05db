package com.foshan.form.community.response.communityEventCategoryItems;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="修改事件类型项目返回对象(ModifyCommunityEventCategoryItemsRes)")
@JsonInclude(Include.NON_NULL)
public class ModifyCommunityEventCategoryItemsRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2618586117373805767L;
	@ApiModelProperty(value = "事件类型项目Id",example="1")
	private Integer eventCategoryItemsId;

}