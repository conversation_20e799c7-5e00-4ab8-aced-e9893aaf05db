package com.foshan.form.community.request;

import java.util.Arrays;
import java.util.Objects;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "统计请求参数(CommunityStatisticsReq)")
public class CommunityStatisticsReq extends BasePageRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1249373903023344240L;
	@ApiModelProperty(value = "月份 格式如：2021-08,不传默认当月")
	private String month;
	@ApiModelProperty(value = "楼盘id列表")
	private Integer[] districtIds;
	@ApiModelProperty(value = "楼阁id列表")
	private Integer[] buildingIds;
	@ApiModelProperty(value = "统计开始时间")
	private String startDate;
	@ApiModelProperty(value = "统计结束时间")
	private String endDate;
	@ApiModelProperty(value = "应收金额显示标识，默认为1")
	private Integer receivableAmountFlag = 1;
	@ApiModelProperty(value = "已收金额显示标识,默认为0")
	private Integer receivedAmountFlag = 0;
	@ApiModelProperty(value = "欠款金额显示标识,默认为0")
	private Integer arrearsFlag = 0;
	@ApiModelProperty(value = "刷新标识,默认为0")
	private Integer onlyArrears = 0;
	@ApiModelProperty(value = "刷新标识,默认为0")
	private Integer refreshFlag = 0;
	@ApiModelProperty(value = "单元状态列表")
	private Integer[] estateStates;
	@ApiModelProperty(value = "收费项目列表")
	private String[] payItemsNames;
	@ApiModelProperty(value = "收费类别列表")
	private String[] chargeCategorys;
	@ApiModelProperty(value = "文件名")
	private String fileName;
	@ApiModelProperty(value = "房产类型")
	private String estateTypeList;
	
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + Arrays.hashCode(chargeCategorys);
		result = prime * result + Arrays.hashCode(districtIds);
		result = prime * result + Arrays.hashCode(payItemsNames);
		result = prime * result + Objects.hash(arrearsFlag, endDate, onlyArrears, receivableAmountFlag,
				receivedAmountFlag, refreshFlag, startDate);
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CommunityStatisticsReq other = (CommunityStatisticsReq) obj;
		return Objects.equals(arrearsFlag, other.arrearsFlag) && Arrays.equals(chargeCategorys, other.chargeCategorys)
				&& Arrays.equals(districtIds, other.districtIds) && Objects.equals(endDate, other.endDate)
				&& Objects.equals(onlyArrears, other.onlyArrears) && Arrays.equals(payItemsNames, other.payItemsNames)
				&& Objects.equals(receivableAmountFlag, other.receivableAmountFlag)
				&& Objects.equals(receivedAmountFlag, other.receivedAmountFlag)
				&& Objects.equals(refreshFlag, other.refreshFlag) && Objects.equals(startDate, other.startDate);
	}

}
