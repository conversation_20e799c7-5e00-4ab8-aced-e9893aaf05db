package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import com.foshan.form.RegionForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

	
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="社区对象(CommunityForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityForm implements IForm {
		
	/**
	 * 
	 */
	private static final long serialVersionUID = 4117022259152113401L;
	@ApiModelProperty(value = "事件ID",example="1")
	private Integer communityId;
	@ApiModelProperty(value = "社区名称")
	private String communityName;
	@ApiModelProperty(value = "社区地址")
	private String communityAddress;
	@ApiModelProperty(value = "管辖面积")
	private String areaSize;
	@ApiModelProperty(value = "区域")
	private RegionForm regionForm;
	
	public CommunityForm(Integer communityId,String communityName,String communityAddress,String areaSize) {
		this.communityId  = communityId;
		this.communityName = communityName;
		this.communityAddress  = communityAddress;
		this.areaSize  = areaSize;
	}
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
