package com.foshan.form.community.request;

import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "总表(CommunityMeterReq)")
public class CommunityMeterReq extends BasePageRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5766849234968782570L;
	@ApiModelProperty(value = "ID", example = "1")
	private Integer communityMeterId;
	@ApiModelProperty(value = "分摊类别：1按面积分摊，2自定义公式、3按个数", example = "1")
	private Integer allocationMethod;
	@ApiModelProperty(value = "备注")
	private String comment;
	@ApiModelProperty(value = "表坏日期")
	private String expirationDates;
	@ApiModelProperty(value = "底数")
	private String initialData;
	@ApiModelProperty(value = "安装地点")
	private String installationsite;
	@ApiModelProperty(value = "是否共用表 0-不共用（用户表），1-共用（总表）")
	private Integer isCommon;
	@ApiModelProperty(value = "是否分摊 0-否，1-是")
	private Integer isApportioned;
	@ApiModelProperty(value = "分摊周期 单位:月")
	private Integer allocationPeriod;
	@ApiModelProperty(value = "层次（树形结构层次）", example = "1")
	private Integer level;
	@ApiModelProperty(value = "表编号")
	private String meterCode;
	@ApiModelProperty(value = "表名称")
	private String meterName;
	@ApiModelProperty(value = "旧表数据")
	private String oldData;
	@ApiModelProperty(value = "旧表ID")
	private String oldId;
	@ApiModelProperty(value = "表属性Id", example = "1")
	private Integer meterAttributesId;
	@ApiModelProperty(value = "自定义分摊公式ID", example = "1")
	private Integer formulaId;
	@ApiModelProperty(value = "", example = "1")
	private Integer parentMeterId;
	@ApiModelProperty(value = "收费项目类别（冗余）")
	private String chargeCategory;
	@ApiModelProperty(value = "是否坏表 0-否，1-是", example = "1")
	private Integer isDisabled;
	@ApiModelProperty(value = "收费项目")
	private String payItemsName;
	@ApiModelProperty(value = "上次分摊日期")
	private String lastAllocateDate;
	@ApiModelProperty(value = "表层级，查询深度，默认10层", example = "1")
	private Integer depth;
	@ApiModelProperty(value = "资产ID,多个以英文逗号隔开")
	private String propertyIdList;
	@ApiModelProperty(value = "小区ID,多个以英文逗号隔开")
	private String districtIdList;
	@ApiModelProperty(value = "楼栋ID,多个以英文逗号隔开")
	private String buildingIdList;
	@ApiModelProperty(value = "表ID,多个以英文逗号隔开")
	private String communityMeterIdList;
	@ApiModelProperty(value = "收费项目ID", example = "1")
	private Integer payItemId;
	@ApiModelProperty(value = "类别（1:电表、2:水表、3:临时表、4:代收水表、5:代收电表）")
	private String categoryList;
	@ApiModelProperty(value = "状态 0--无效数据 1--有效数据",example = "1")
	private Integer state;
	@ApiModelProperty(value = "数据异常标识，0：正常；1：异常；",example = "1")
	private Integer exceptionState;
	@ApiModelProperty(value = "是否是租赁,1:是;",example = "1")
	private Integer isRent;
	@ApiModelProperty(value = "被复制表ID,多个以英文逗号隔开")
	private String beCopiedMeterIdList;

}
