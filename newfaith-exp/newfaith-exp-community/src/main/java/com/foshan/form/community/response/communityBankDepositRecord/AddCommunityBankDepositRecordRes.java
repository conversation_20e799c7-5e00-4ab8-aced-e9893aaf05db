package com.foshan.form.community.response.communityBankDepositRecord;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="银行划账记录(CommunityBankDepositRecordRes)")
@JsonInclude(Include.NON_NULL)
public  class AddCommunityBankDepositRecordRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer communityBankDepositRecordId;
  @ApiModelProperty(value = "时间")
    private String createTime;
    @ApiModelProperty(value = "最后修改时间")
    private String lastModifyTime;
    @ApiModelProperty(value = "状态 0--无效数据  1--有效数据",example="1")
    private Integer state;
    @ApiModelProperty(value = "银行帐户")
    private String bankAccount;
    @ApiModelProperty(value = "应划金额")
    private String depositAmount;
    @ApiModelProperty(value = "划扣备注（导出送盘文件的附言，一般为单元的编号）")
    private String depositComment;
    @ApiModelProperty(value = "回盘备注（一般记录划账失败原因）")
    private String feedbackComment;
    @ApiModelProperty(value = "划帐标志（0-未回盘，1-回盘，划账成功，2-回盘，划账失败）",example="1")
    private Integer isDeposited;
    @ApiModelProperty(value = "应收未收金额")
    private String outstandingAmount;
  
}
