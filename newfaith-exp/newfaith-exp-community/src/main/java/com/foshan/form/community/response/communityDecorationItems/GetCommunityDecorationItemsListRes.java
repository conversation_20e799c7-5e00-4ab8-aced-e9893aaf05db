package com.foshan.form.community.response.communityDecorationItems;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityDecorationItemsForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="装修项目项目(GetCommunityDecorationItemsListReq)")
public class GetCommunityDecorationItemsListRes extends BasePageResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "装修项目项目列表")
	private List<CommunityDecorationItemsForm> communityDecorationItemsList = new ArrayList<CommunityDecorationItemsForm>(); 

}
