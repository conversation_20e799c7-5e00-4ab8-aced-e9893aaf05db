package com.foshan.form.community.response.communityPaymentAccount;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="账户信息(CommunityPaymentAccountRes)")
@JsonInclude(Include.NON_NULL)
public  class AddCommunityPaymentAccountRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -6856284584367833916L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityPaymentAccountId;
    @ApiModelProperty(value = "时间",example="1")
    private String createTime;
    @ApiModelProperty(value = "最后修改时间",example="1")
    private String lastModifyTime;
    @ApiModelProperty(value = "状态 0--无效数据  1--有效数据",example="1")
    private Integer state;
    @ApiModelProperty(value = "银行帐户",example="1")
    private String bankAccount;
    @ApiModelProperty(value = "划帐银行",example="1")
    private String bankName;
    @ApiModelProperty(value = "是否默认",example="1")
    private Integer isDefault;
    @ApiModelProperty(value = "帐号名",example="1")
    private String userName;
    @ApiModelProperty(value = "",example="1")
    private Integer memberId;

}
