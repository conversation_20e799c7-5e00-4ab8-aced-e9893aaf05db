package com.foshan.form.community.response.communityBuilding;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="楼栋(CommunityBuildingRes)")
@JsonInclude(Include.NON_NULL)
public  class AddCommunityBuildingRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -6440451012947588215L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityBuildingId;
    @ApiModelProperty(value = "楼栋地址",example="1")
    private String address;
    @ApiModelProperty(value = "楼栋编号",example="1")
    private String buildingCode;
    @ApiModelProperty(value = "楼栋名称",example="1")
    private String buildingName;
    @ApiModelProperty(value = "建筑许可证号",example="1")
    private String buildingPermitNum;
    @ApiModelProperty(value = "楼栋类别(功能) 0:住宅、1:别墅、2:商铺、3:停车场、4:公共区域",example="1")
    private Integer buildingType;
    @ApiModelProperty(value = "封顶日期",example="1")
    private String cappingDate;
    @ApiModelProperty(value = "地性质分类 0住宅用地 1商业用地 2工业用地 3综合用地 4 其他用地",example="1")
    private Integer classification;
    @ApiModelProperty(value = "竣工日期",example="1")
    private String completionDate;
    @ApiModelProperty(value = "结构类型",example="1")
    private String construction;
    @ApiModelProperty(value = "完损等级",example="1")
    private String damagedLevel;
    @ApiModelProperty(value = "装修标准",example="1")
    private String decoration;
    @ApiModelProperty(value = "层数",example="1")
    private Integer layers;
    @ApiModelProperty(value = "预售许可证号",example="1")
    private String permitLicenceNum;
    @ApiModelProperty(value = "",example="1")
    private Integer districtId;

}
