package com.foshan.form.community.response.communityStatistics;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="社区基础统计返回对象(GetCommunityBaseStatisticsRes)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetCommunityBaseStatisticsRes extends BaseResponse {
    /**
     *
     */
    private static final long serialVersionUID = 6060850119011003034L;

    @ApiModelProperty(value = "用户数")
    private Integer member;
    @ApiModelProperty(value = "当日新增用户")
    private Integer memberToday;
    @ApiModelProperty(value = "住户数")
    private Integer household;
    @ApiModelProperty(value = "业主")
    private Integer owner;
    @ApiModelProperty(value = "租客")
    private Integer tenant;
    @ApiModelProperty(value = "家属")
    private Integer dependents;

    @ApiModelProperty(value = "住宅")
    private Integer housing;
    @ApiModelProperty(value = "别墅")
    private Integer villa;
    @ApiModelProperty(value = "商铺")
    private Integer shops;
    @ApiModelProperty(value = "停车场")
    private Integer parking;
    @ApiModelProperty(value = "公共区域")
    private Integer publicAreas;

    public GetCommunityBaseStatisticsRes(String ret, String retInfo){
        super(ret,retInfo);
    }


}
