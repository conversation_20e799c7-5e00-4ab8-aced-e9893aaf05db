package com.foshan.form.community.response.warningMessage;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="告警消息(WarningMessageRes)")
@JsonInclude(Include.NON_NULL)
public  class AddWarningMessageRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer warningMessageId;
  @ApiModelProperty(value = "发生时间")
    private String alarmTime;
    @ApiModelProperty(value = "设备ID")
    private String deviceId;
    @ApiModelProperty(value = "设备名称")
    private String deviceName;
    @ApiModelProperty(value = "告警事件ID")
    private String eventRecordId;
    @ApiModelProperty(value = "告警事件")
    private String eventType;
    @ApiModelProperty(value = "备用")
    private String jsonData;
    @ApiModelProperty(value = "所在分组")
    private String levelName;
  
}
