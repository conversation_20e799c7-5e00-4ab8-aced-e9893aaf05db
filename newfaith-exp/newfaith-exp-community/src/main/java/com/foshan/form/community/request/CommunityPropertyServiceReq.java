package com.foshan.form.community.request;

import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="报修服务请求参数(CommunityPropertyServiceReq)")
public class CommunityPropertyServiceReq extends BasePageRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = -4985535185121123505L;
	@ApiModelProperty(value = "报修服务ID", example = "1")
	private Integer propertyServiceId;
	@ApiModelProperty(value = "创建时间")
	protected String createTime;
	@ApiModelProperty(value = "报修号")
	private String eventsCode;
	@ApiModelProperty(value = "标题")
	private String title;
    @ApiModelProperty(value = "报修时间")
    private String reportTime;
	@ApiModelProperty(value = "服务状态（新增、跟进、维修、完成）")
	private String serviceState;
	@ApiModelProperty(value = "说明（备注）")
	private String centent;
	@ApiModelProperty(value = "经办人（接待员）")
	private String agent;
	@ApiModelProperty(value = "申请人")
	private String client;
	@ApiModelProperty(value = "联系电话")
	private String phone;
	@ApiModelProperty(value = "资产Id", example = "1")
	private Integer propertyId;
	@ApiModelProperty(value = "维修地点")
	private String address;
	@ApiModelProperty(value = "报修类别")
	private String serviceType;
	@ApiModelProperty(value = "报修内容")
	private String  reportContent;
	@ApiModelProperty(value = "主管部门")
	private String competentDepartment;
	@ApiModelProperty(value = "维修员")
	private String handler;
    @ApiModelProperty(value = "派工时间")
    private String dispatchingTime;
    @ApiModelProperty(value = "完成时间")
    private String completionTime;
	@ApiModelProperty(value = "维修内容")
	private String serviceContent;
	@ApiModelProperty(value = "验收员")
	private String receiver;
	@ApiModelProperty(value = "服务评价（不满意、基本满意、满意、很满意、非常满意）")
	private String  serviceEvaluation;
	@ApiModelProperty(value = "人工费")
	private String laborCost;
	@ApiModelProperty(value = "材料费")
	private String materialCost;
	@ApiModelProperty(value = "是否入账 0否 1是", example = "1")
	private Integer isGeneratedBills;
	@ApiModelProperty(value = "收费项目ID", example = "1")
	private Integer payItemsId;
    @ApiModelProperty(value = "旧表数据")
    private String oldData;
    @ApiModelProperty(value = "旧表ID")
    private String oldId;
	@ApiModelProperty(value = "回访意见")
	private String feedback;
	@ApiModelProperty(value = "回访人")
	private String visitCommissione;
	@ApiModelProperty(value = "开始时间")
	private String startTime;
	@ApiModelProperty(value = "结束时间")
	private	String endTime;
	@ApiModelProperty(value = "订单类别：0-有偿单，1-外发单", example = "1")
	private Integer orderCategory;
}
