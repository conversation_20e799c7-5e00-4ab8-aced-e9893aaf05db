package com.foshan.form.community.response.communityMeterAttributes;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityMeterAttributesForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="表属性(GetCommunityMeterAttributesInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityMeterAttributesInfoRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 3211900180584795419L;
	@ApiModelProperty(value = "表属性对象")
	private CommunityMeterAttributesForm communityMeterAttributesForm ; 

}
