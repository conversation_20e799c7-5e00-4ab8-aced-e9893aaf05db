package com.foshan.form.community.response.communityEventCategoryItems;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityEventCategoryItemsForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取事件类型项目详情返回对象(GetCommunityEventCategoryItemsInfo)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityEventCategoryItemsInfo extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7995205337034820775L;
	@ApiModelProperty(value = "事件类型项目")
	private CommunityEventCategoryItemsForm communityEventCategoryItemsForm;

}
