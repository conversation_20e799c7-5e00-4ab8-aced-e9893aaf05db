package com.foshan.form.community.response.messageAccount;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.MessageAccountForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="消息账号表(GetMessageAccountListReq)")
public class GetMessageAccountListRes extends BasePageResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "消息账号表列表")
	private List<MessageAccountForm> messageAccountList = new ArrayList<MessageAccountForm>(); 

}
