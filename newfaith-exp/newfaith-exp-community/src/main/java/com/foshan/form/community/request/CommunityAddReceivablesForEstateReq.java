package com.foshan.form.community.request;

import java.util.Date;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;



@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="根据小区、楼栋、单元Id，收费项目ID生成当月生成的管理费、违约金请求报文，影片单元结果为前三个条件字段的并集，发生过收款的应收不受影响(CommunityAddReceivablesForEstateReq)")
public  class CommunityAddReceivablesForEstateReq extends BasePageRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8916895160676516149L;
	@ApiModelProperty(value = "费项目Id字符串,多个收费项目ID要用英文逗号‘,’隔开")
	private String payItemIds;
	@ApiModelProperty(value = "对应的小区ID，如果多个小区，ID要用英文逗号‘,’隔开，非必填")
	private String districtIds;
    @ApiModelProperty(value = "对应的单元ID，如果个单元，ID要用英文逗号‘,’隔开，非必填")
    private String estateIds;
    @ApiModelProperty(value = "对应的楼栋ID，如果多个楼栋，ID要用英文逗号‘,’隔开，非必填")
    private String buildingIds;
    @ApiModelProperty(value = "开始时间，必填")
    private Date startDate;
    @ApiModelProperty(value = "结束时间，必填")
    private Date EndDate;
    @ApiModelProperty(value = "类别（1:电表、2:水表、3:临时表、4:代收水表、5:代收电表）,多可用英文逗号隔开",example="1")
    private String categoryList;
	@Override
	public String toString() {
		return "CommunityAddReceivablesForEstateReq [payItemIds=" + payItemIds + ", districtIds=" + districtIds
				+ ", estateIds=" + estateIds + ", buildingIds=" + buildingIds + ", startDate=" + startDate
				+ ", EndDate=" + EndDate + "]";
	}
	


}
