package com.foshan.form.community.response.communityPropertyService;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityPropertyServiceForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="报修服务详情(GetCommunityPropertyServiceInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityPropertyServiceInfoRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -9032531073988893101L;
	@ApiModelProperty(value = "报修服务对象")
	private CommunityPropertyServiceForm communityPropertyServiceForm ; 

}
