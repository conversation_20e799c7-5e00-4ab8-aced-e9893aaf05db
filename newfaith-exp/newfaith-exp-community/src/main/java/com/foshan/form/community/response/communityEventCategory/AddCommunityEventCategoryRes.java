package com.foshan.form.community.response.communityEventCategory;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="新增事件类型返回对象(AddCommunityEventCategoryRes)")
@JsonInclude(Include.NON_NULL)
public class AddCommunityEventCategoryRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3495796596807296062L;
	@ApiModelProperty(value = "事件类型Id",example="1")
	private Integer eventCategoryId;

}
