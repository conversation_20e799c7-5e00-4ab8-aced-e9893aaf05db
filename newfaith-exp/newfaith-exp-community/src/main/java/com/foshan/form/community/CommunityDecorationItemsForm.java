package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" 装修项目项目(CommunityDecorationItemsForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityDecorationItemsForm implements IForm {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer communityDecorationItemsId;
  @ApiModelProperty(value = "时间")
    private String createTime;
    @ApiModelProperty(value = "最后修改时间")
    private String lastModifyTime;
    @ApiModelProperty(value = "状态 0--无效数据  1--有效数据",example="1")
    private Integer state;
    @ApiModelProperty(value = "使用状态 0--未使用 1--已使用（业主可以申请）",example="1")
    private Integer beginUse;
    @ApiModelProperty(value = "项目名称")
    private String itemName;
    @ApiModelProperty(value = "排序",example="1")
    private Integer orders;
  
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
