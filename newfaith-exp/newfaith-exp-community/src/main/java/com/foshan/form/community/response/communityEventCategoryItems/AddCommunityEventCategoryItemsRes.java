package com.foshan.form.community.response.communityEventCategoryItems;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="新增事件类型项目返回对象(AddCommunityEventCategoryItemsRes)")
@JsonInclude(Include.NON_NULL)
public class AddCommunityEventCategoryItemsRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 4760399516733031929L;
	@ApiModelProperty(value = "事件类型项目Id",example="1")
	private Integer eventCategoryItemsId;

}
