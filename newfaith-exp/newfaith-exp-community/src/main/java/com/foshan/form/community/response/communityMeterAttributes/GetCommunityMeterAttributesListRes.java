package com.foshan.form.community.response.communityMeterAttributes;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityMeterAttributesForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="表属性(GetCommunityMeterAttributesListReq)")
public class GetCommunityMeterAttributesListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -8846788129775782530L;
	@ApiModelProperty(value = "表属性列表")
	private List<CommunityMeterAttributesForm> communityMeterAttributesList = new ArrayList<CommunityMeterAttributesForm>(); 

}
