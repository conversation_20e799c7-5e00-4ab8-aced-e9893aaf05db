package com.foshan.form.community.response.communityEstate;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityEstateTreeForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

	
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取单元树返回列表对象(GetCommunityEstateTreeRes)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityEstateTreeRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 3363101288879287002L;
	@ApiModelProperty(value = "单元树对象列表")
	private List<CommunityEstateTreeForm> EstateTree = new ArrayList<CommunityEstateTreeForm>();
	

	public GetCommunityEstateTreeRes(String ret, String retInfo) {
		super(ret, retInfo);
	}

}
