package com.foshan.form.community.request;

import java.util.ArrayList;
import java.util.List;

import com.foshan.form.community.CommunityGoodsForm;
import com.foshan.form.invoice.GoodsForm;
import com.foshan.form.request.AuditlogInfo;
import com.foshan.form.request.IRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="申请红票请求参数(RedInvoiceReq)")
public class RedInvoiceReq implements IRequest,AuditlogInfo{
	/**
	 * 
	 */
	private static final long serialVersionUID = 3238646954938930841L;
	@ApiModelProperty(value = "接口名")
	private String apiName;
	@ApiModelProperty(value = "销货单位税号,纳税人识别号")
	private String sellerTaxCode;
	@ApiModelProperty(value = "开票终端编号")
	private String terminalCode;
	@ApiModelProperty(value = "发票来源")
	private String invoiceSource;
	@ApiModelProperty(value = "购销方性质 0：销方 1：购方")
	private String invoiceUserType;
	@ApiModelProperty(value = "确认单申请类型 0：销方申请  1：购方申请")
	private String applyUserType;
	@ApiModelProperty(value = "购方税号,纳税人识别号")
	private String buyerTaxCode;
	@ApiModelProperty(value = "原数电发票号码")
	private String originalInvoiceNo;
	@ApiModelProperty(value = "原纸质发票代码")
	private String paperInvoiceCode;
	@ApiModelProperty(value = "原发票号码")
	private String paperInvoiceNo;
	@ApiModelProperty(value = "原开票日期")
	private String originalInvoiceDate;
	@ApiModelProperty(value = "红冲类型，0.部分红冲，1.全额红冲")
	private String redApplyType;
	@ApiModelProperty(value = "红字冲销金额")
	private String redOffsetAmount;
	@ApiModelProperty(value = "红字冲销税额")
	private String redOffsetTaxAmount;
	@ApiModelProperty(value = "冲红原因代码 01：开票有误 02：销货退回 03：服务中止 04：销售折让")
	private String redReasonCode;
	@ApiModelProperty(value = "商品列表")
	private List<CommunityGoodsForm> goodsList = new ArrayList<>();
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
