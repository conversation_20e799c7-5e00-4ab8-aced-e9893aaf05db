package com.foshan.form.community.request;



import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.StringJoiner;

import javax.persistence.Column;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="事件类型项目请求参数(CommunityEventCategoryItemsReq)")
public class CommunityEventCategoryItemsReq extends BasePageRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8646116687716235023L;
	@ApiModelProperty(value = "事件类型Id",example="1")
	private Integer eventCategoryItemsId;
	@ApiModelProperty(value = "事件类型Id",example="1")
	private Integer eventCategoryId;
	@ApiModelProperty(value = "排序",example="1")
	private Integer orders;
	@ApiModelProperty(value = "项目名称对应key")
	private String itemkey;
	@ApiModelProperty(value = "项目名称")
	private String itemName;
	@ApiModelProperty(value = "数据类型 0：字符串；1：数字；2：时间；3：文件；4：单选；5：多选；6：日期；7：日期范围；8:时间范围；",example="1")
	private Integer dataType;
	@ApiModelProperty(value = "当dataType=4或5时，对应的选项数据")
	private String options;
	@ApiModelProperty(value = "是否必填，0：否；1是；",example="1")
	private Integer isRequiredng;
	@ApiModelProperty(value = "标签，区别于其他事件项目，用于装修项目或其他特别项目")
	private String itemTag;
	@ApiModelProperty(value = "映射字段（云之家）")
	private String reflectionField;
	@ApiModelProperty(value = "装修项目ID")
	private String decorationItemsIdList;
	@ApiModelProperty(value = "是否级联，0：否；1：是；",example="1")
	private Integer isCascade;
	@ApiModelProperty(value = "级联值")
	private String cascadeValue;
	@ApiModelProperty(value = "装修项目名称")
	private String decorationItemsNameList;

	@Override
	public String toString() {
		return new StringJoiner(", ", CommunityEventCategoryItemsReq.class.getSimpleName() + "[", "]")
				.add("eventCategoryItemsId=" + eventCategoryItemsId)
				.add("eventCategoryId=" + eventCategoryId)
				.add("orders=" + orders)
				.add("itemkey='" + itemkey + "'")
				.add("itemName='" + itemName + "'")
				.add("dataType=" + dataType)
				.add("options='" + options + "'")
				.toString();
	}
}
