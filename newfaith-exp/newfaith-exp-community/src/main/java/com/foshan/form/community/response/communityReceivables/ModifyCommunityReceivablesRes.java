package com.foshan.form.community.response.communityReceivables;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="应收款(ModifyCommunityReceivablesRes)")
@JsonInclude(Include.NON_NULL)
public  class ModifyCommunityReceivablesRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -8536744181167634192L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityReceivablesId;
  @ApiModelProperty(value = "费用类别 1:管理费；2：车位费；3：分摊费；4：商管部；5：违约金；6：其他；7：押金类；8:有偿服务费；9:出租租金")
    private String chargeCategory;
    @ApiModelProperty(value = "项目来源")
    private String chargeSource;
    @ApiModelProperty(value = "备注")
    private String comment;
    @ApiModelProperty(value = "截止日期")
    private String endTime;
    @ApiModelProperty(value = "旧表数据")
    private String oldData;
    @ApiModelProperty(value = "表ID")
    private String oldId;
    @ApiModelProperty(value = "收费项目")
    private String payItemsName;
    @ApiModelProperty(value = "应收属期")
    private String paymentPeriod;
    @ApiModelProperty(value = "应收金额")
    private String receivableAmount;
    @ApiModelProperty(value = "应收日期")
    private String receivableDate;
    @ApiModelProperty(value = "应收款类型")
    private String receivablesType;
    @ApiModelProperty(value = "已收金额")
    private String receivedAmount;
    @ApiModelProperty(value = "来源备注")
    private String sourceNotes;
    @ApiModelProperty(value = "起始日期（分摊金额要写上）")
    private String startTime;
  
}
