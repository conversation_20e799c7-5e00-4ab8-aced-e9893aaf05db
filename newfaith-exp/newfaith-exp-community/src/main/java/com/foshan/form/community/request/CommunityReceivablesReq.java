package com.foshan.form.community.request;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;



@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="应收款(CommunityReceivablesReq)")
public  class CommunityReceivablesReq extends BasePageRequest {

	private static final long serialVersionUID = -3214344978882744947L;
	@ApiModelProperty(value = "ID", example = "1")
	private Integer communityReceivablesId;
	@ApiModelProperty(value = "应收编号")
	private String receivablesNO;
	@ApiModelProperty(value = "应收IDS")
	private String communityReceivablesIdList;
	@ApiModelProperty(value = "项目类别")
	private String chargeCategory;
	@ApiModelProperty(value = "项目类别ID:费用类别 1:管理费；2：车位费；3：分摊费；4：商管部；5：违约金；6：其它类；7：押金类；8:有偿服务费；9:出租租金")
	private String chargeCategoryIds;
	@ApiModelProperty(value = "项目来源")
	private String chargeSource;
	@ApiModelProperty(value = "备注")
	private String comment;
	@ApiModelProperty(value = "截止日期")
	private String endTime;
	@ApiModelProperty(value = "旧表数据")
	private String oldData;
	@ApiModelProperty(value = "表ID")
	private String oldId;
	@ApiModelProperty(value = "收费项目")
	private String payItemsName;
	@ApiModelProperty(value = "收费项目ID")
	private Integer payItemId;
	@ApiModelProperty(value = "费用类别 0:周期性收费；1：一次性收费；")
	private Integer feeType;
	@ApiModelProperty(value = "应收属期")
	private String paymentPeriod;
	@ApiModelProperty(value = "应收金额")
	private String receivableAmount;
	@ApiModelProperty(value = "应收日期")
	private String receivableDate;
	@ApiModelProperty(value = "应收款类型")
	private String receivablesType;
	@ApiModelProperty(value = "已收金额")
	private String receivedAmount;
	@ApiModelProperty(value = "来源备注")
	private String sourceNotes;
	@ApiModelProperty(value = "起始日期（分摊金额要写上）")
	private String startTime;
    @ApiModelProperty(value = "对应的单元Id")
    private Integer estateId;
    @ApiModelProperty(value = "对应的单元Id")
    private String estateIdList;
    @ApiModelProperty(value = "楼栋Id")
    private String buildingIdList;
    @ApiModelProperty(value = "小区Id")
    private String districtIdList;
    @ApiModelProperty(value = "对应的单元编号")
    private String unitCode;
    @ApiModelProperty(value = "返回结果：0--只返回未完成付款的应付（默认），1--返回全部记录,2--返回已收款记录")
    private Integer fetchAll;
	@ApiModelProperty(value = "数量", example = "1")
	private Integer quantity;
	@ApiModelProperty(value = "天（在哪天划扣）")
	private String day;
//	@ApiModelProperty(value = "手机端费用清单是否返回预收请求标识  0：不返回预收数据 1：返回预收数据 ", example = "1")
//	private Integer preFlag;
//	@ApiModelProperty(value = "手机端查询费用时，当preFlag=1时，手机端费预收月份数，如果不传按系统默认设置生成预收月份数返回", example = "1")
//	private Integer preMonths;
	@ApiModelProperty(value = "数据返回类型：0--默认(单元、时间、项目)  2--项目(单元、项目、时间)", example = "1")
	private Integer resultType=0;
	@ApiModelProperty(value = "送盘导出格式 ;bankNameList:银行名称,可传多个;mergeState:合并状态,0否,1合并;templateType:模板,0工行模板,1农行(超平)模板,2农村信用社模板;"
			+ "如:[{\"bankNameList\":\"\",\"mergeState\":\"\",\"templateType\":\"\"},"
			+ "{\"bankNameList\":\"\",\"mergeState\":\"\",\"templateType\":\"\"}]" )
	private List<Map<String,String>> formatList=new ArrayList<Map<String,String>>();
	@ApiModelProperty(value = "模板类型,0工行模板,1农行(超平)模板,2农村信用社模板", example = "1")
	private Integer templateType;
	@ApiModelProperty(value = "根据银行名称导出,可传多个")
	private  String bankNameList;
	@ApiModelProperty(value = "银行划账批次ID", example = "1")
	private Integer bankDepositBatchId;
	@ApiModelProperty(value = "0:未上锁;1:系统锁定;2:人工锁定；", example = "1")
	private Integer lockMark;
	@ApiModelProperty(value = "收费项目Id字符串,多个收费项目ID要用英文逗号‘,’隔开")
	private String payItemIds;
	@ApiModelProperty(value = "消息设置 0:不按设置发(剔除也发);1:按设置的发送（剔除的不发）;", example = "1")
	private Integer smsStatus;
	@ApiModelProperty(value = "解锁，应收ID")
	private List<String> unlockList=new ArrayList<String>();
	@ApiModelProperty(value = "系统锁盘，应收ID")
	private List<String> lockList=new ArrayList<String>();
	
	@Override
	public String toString() {
		return "CommunityReceivablesReq [communityReceivablesId=" + communityReceivablesId + ", receivablesNO="
				+ receivablesNO + ", communityReceivablesIdList=" + communityReceivablesIdList + ", chargeCategory="
				+ chargeCategory + ", chargeCategoryIds=" + chargeCategoryIds + ", chargeSource=" + chargeSource
				+ ", comment=" + comment + ", endTime=" + endTime + ", oldData=" + oldData + ", oldId=" + oldId
				+ ", payItemsName=" + payItemsName + ", payItemId=" + payItemId + ", feeType=" + feeType
				+ ", paymentPeriod=" + paymentPeriod + ", receivableAmount=" + receivableAmount + ", receivableDate="
				+ receivableDate + ", receivablesType=" + receivablesType + ", receivedAmount=" + receivedAmount
				+ ", sourceNotes=" + sourceNotes + ", startTime=" + startTime + ", estateId=" + estateId + ", unitCode="
				+ unitCode + ", fetchAll=" + fetchAll + ", quantity=" + quantity + ", resultType=" + resultType
				+ ", formatList=" + formatList + ", templateType=" + templateType + ", bankNameList=" + bankNameList
				+ ", bankDepositBatchId=" + bankDepositBatchId + ", lockMark=" + lockMark + ", payItemIds=" + payItemIds
				+ "]";
	}
	
	
	
}
