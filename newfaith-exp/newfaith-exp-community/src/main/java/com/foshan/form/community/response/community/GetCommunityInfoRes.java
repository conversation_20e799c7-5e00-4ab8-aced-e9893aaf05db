package com.foshan.form.community.response.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="社区(GetCommunityInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityInfoRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -2726971986796185271L;
	@ApiModelProperty(value = "社区对象")
	private CommunityForm communityForm ; 

}
