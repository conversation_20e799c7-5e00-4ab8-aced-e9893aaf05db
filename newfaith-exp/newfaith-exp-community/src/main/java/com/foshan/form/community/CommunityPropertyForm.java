package com.foshan.form.community;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.AssetForm;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="资产对象(CommunityPropertyForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityPropertyForm implements IForm {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -7922117975884771577L;
	@ApiModelProperty(value = "资产ID", example = "1")
	private Integer propertyId;
	@ApiModelProperty(value = "名称")
	private String propertyName;
//	@ApiModelProperty(value = "资产类型 0:门禁卡；1：住宅；2：商铺；3车位；4公共区域；5:房产;", example = "1")
//	private Integer propertyType;
	@ApiModelProperty(value = "创建时间")
	private String createTime;
	@ApiModelProperty(value = "修改时间")
	private String lastModifyTime;
	@ApiModelProperty(value = "状态 0--无效数据  1--有效数据", example = "1")
	private Integer state;
	@ApiModelProperty(value = "楼栋")
	private CommunityBuilding buildingForm;
	@ApiModelProperty(value = "图片")
	private AssetForm assetForm;
	@ApiModelProperty(value = "备注")
	protected String comment;
	@ApiModelProperty(value = "单元编号")
	protected String unitCode;
    @ApiModelProperty(value = "房屋状态：0:未收楼;1:已入住;2:装修中;3:出租;4:离退;5:自主", example = "1")
    private Integer estateState;
	@ApiModelProperty(value = "是否验收 0否；1是", example = "1")
	private Integer isAcceptanceReceive;
	@ApiModelProperty(value = "建筑面积")
	private String buildingArea;
	@ApiModelProperty(value = "使用期限")
	private String usageTerm;
	@ApiModelProperty(value = "朝向")
	protected String orientation;
    @ApiModelProperty(value = "所属楼层", example = "1")
    private Integer floor;
    @ApiModelProperty(value = "房号")
    private String roomNumber;
    @ApiModelProperty(value = "使用面积")
    private String usableArea;
    @ApiModelProperty(value = "收费面积")
    private String chargingArea;
    @ApiModelProperty(value = "附加面积")
    private String additionalArea;
    @ApiModelProperty(value = "装修标准")
    private String decoration;
    @ApiModelProperty(value = "房产类型  如：住宅 、商铺 、车位 、公共区域", example = "1")
    private String estateType;
    @ApiModelProperty(value = "图片ID", example = "1")
    private Integer assetId;
	@ApiModelProperty(value = "unitCode(单元编号),页面树状结构用")
	private String label;
    @ApiModelProperty(value = "是否可预约的 0否 1是", example = "1")
    private Integer isReserved;
    @ApiModelProperty(value = "租用状态，0-不可租用；1-可租用（未出租）；2-已出租；", example = "1")
    private Integer rentState;
	@ApiModelProperty(value = "社区地址")
	protected String communityAddress;
	@ApiModelProperty(value = "特殊分摊标识 0：正常计费 1、不分电表 2、不分水表 3、全部不分", example = "1")
	private Integer specialAllocationFlag;
/*	@ApiModelProperty(value = "条形码")
	private String barcodes;
	@ApiModelProperty(value = "0未启用,1在用，3报废", example = "1")
	private Integer facilitiesState;
	@ApiModelProperty(value = "设备类型 0门禁", example = "1")
	private Integer facilitiesTyp;
	@ApiModelProperty(value = "生产商")
	private String manufacturers;
	@ApiModelProperty(value = "型号")
	private String model;
	@ApiModelProperty(value = "供应商")
	private String supplier;
	@ApiModelProperty(value = "售后电话")
	private String supportPhone;
	@ApiModelProperty(value = "位置")
	private String location;
	@ApiModelProperty(value = "内网IP")
	private String networkIP;
	@ApiModelProperty(value = "", example = "1")
	private Integer types;
	@ApiModelProperty(value = "门禁卡类型(业主、临时施工、一次性、访客、物品放行)", example = "1")
	private Integer cardType;
	@ApiModelProperty(value = "结束时间")
	private String endTime;
	@ApiModelProperty(value = "开始时间")
	private String startTime;
	@ApiModelProperty(value = "土地性质分类 0住宅用地 1商业用地 2工业用地 3综合用地 4 其他用地", example = "1")
	private Integer classification;
	@ApiModelProperty(value = "房产类型 0住宅 1商铺 2车位 3公共区域", example = "1")
	private Integer estateType;
	@ApiModelProperty(value = "使用期限")
	private String usageTerm;
	@ApiModelProperty(value = "区域")
	private String area;
	@ApiModelProperty(value = "是否可预约的 0否 1是", example = "1")
	private Integer isReserved;
	@ApiModelProperty(value = "户型")
	private String layout;
	@ApiModelProperty(value = "栋号")
	private String buildingNum;
	@ApiModelProperty(value = "朝向")
	private String orientation;
	@ApiModelProperty(value = "单元号")
	private String unitNum;
	
	@ApiModelProperty(value = "销售状态 0:已租;1:已售;2:待租;3:待售", example = "1")
	private Integer saleState;
	@ApiModelProperty(value = "0:业主使用;1:租客使用;2:未使用", example = "1")
	private Integer userState;
	@ApiModelProperty(value = "是否验收 0否；1是", example = "1")
	private Integer isAcceptanceReceive;
	@ApiModelProperty(value = "建筑面积")
	private String builtUpArea;
	@ApiModelProperty(value = "投入使用日期")
	private String useDate;
	@ApiModelProperty(value = "小区")
	private CommunityDistrictForm districtForm;*/
	@ApiModelProperty(value = "会员资产列表")
	private List<CommunityMemberPropertyForm> memberPropertyList = new ArrayList<CommunityMemberPropertyForm>();
	@ApiModelProperty(value = "账户信息")
	private CommunityPaymentAccountForm paymentAccountForm;
	@ApiModelProperty(value = "收费项目列表")
	private List<CommunityPayItemsForm> payItemsList = new ArrayList<CommunityPayItemsForm>();

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
