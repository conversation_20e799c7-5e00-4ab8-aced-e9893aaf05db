package com.foshan.form.community.kingdee;

import java.math.BigDecimal;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "收据")
@JsonInclude(Include.NON_NULL)
public class ReceiptOrderForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = 943664364868930873L;

	@ApiModelProperty(value = "收据编号")
	private String fBillNo;
	@ApiModelProperty(value = "收款日期")
	private String fDate;
	@ApiModelProperty(value = "业主")
	private String fyzxm;
	@ApiModelProperty(value = "单元")
	private String fdymc;
	@ApiModelProperty(value = "备注")
	private String fText;
	@ApiModelProperty(value = "收款方式")
	private String fText1;
	@ApiModelProperty(value = "科耐制单人")
	private String fText2;	
	@ApiModelProperty(value = "区域")
	private String fText3;
	@ApiModelProperty(value = "现金/内部转账")
	private BigDecimal fAmount;
	@ApiModelProperty(value = "POS/微信/工行")
	private BigDecimal fAmount1;
	@ApiModelProperty(value = "农村信用社")
	private BigDecimal fAmount2;
	@ApiModelProperty(value = "支付宝")
	private BigDecimal fAmount3;
	@ApiModelProperty(value = "建行")
	private BigDecimal fAmount4;
	@ApiModelProperty(value = "农村信用社代收")
	private BigDecimal fAmount5;
	@ApiModelProperty(value = "微信支付")
	private BigDecimal fAmount175;
	@ApiModelProperty(value = "工商银行代收")
	private BigDecimal fAmount6;
	@ApiModelProperty(value = "转款")
	private BigDecimal fAmount176;
	@ApiModelProperty(value = "建设银行代收")
	private BigDecimal fAmount7;
	@ApiModelProperty(value = "客户")
	private String fText64;
	@ApiModelProperty(value = "房间号")
	private String fText65;
	@ApiModelProperty(value = "单据编号")
	private String fText55;
	@ApiModelProperty(value = "农业银行代收")
	private BigDecimal fAmount180;
	@ApiModelProperty(value = "三水农业银行")
	private BigDecimal fAmount181;
	@ApiModelProperty(value = "佛山农商银行")
	private BigDecimal fAmount182;
	@ApiModelProperty(value = "收据编号")
	private String fBillNo1;
	@ApiModelProperty(value = "收费项目")
	private String fText4;
	@ApiModelProperty(value = "属期")
	private String fText5;
	@ApiModelProperty(value = "金额")
	private BigDecimal fAmount8;
	@ApiModelProperty(value = "别墅管理费+别墅管理费项目")
	private String fText6;
	@ApiModelProperty(value = "别墅管理费+金额")
	private BigDecimal fAmount10;
	@ApiModelProperty(value = "物业管理费+物业管理费项目")
	private String fText7;
	@ApiModelProperty(value = "物业管理费+金额")
	private BigDecimal fAmount13;
	@ApiModelProperty(value = "花园管理费+花园管理费项目")
	private String fText8;
	@ApiModelProperty(value = "花园管理费+金额")
	private BigDecimal fAmount16;
	@ApiModelProperty(value = "车位管理费+车位管理费项目")
	private String fText9;
	@ApiModelProperty(value = "车位管理费+金额")
	private BigDecimal fAmount19;
	@ApiModelProperty(value = "施工服务费+施工服务费项目")
	private String fText10;
	@ApiModelProperty(value = "施工服务费+金额")
	private BigDecimal fAmount22;
	@ApiModelProperty(value = "花园及停车位管理费+花园及停车位管理费项目")
	private String fText11;
	@ApiModelProperty(value = "花园及停车位管理费+金额")
	private BigDecimal fAmount25;
	@ApiModelProperty(value = "景观大道路灯电费+景观大道路灯电费项目")
	private String fText12;
	@ApiModelProperty(value = "景观大道路灯电费+金额")
	private BigDecimal fAmount28;
	@ApiModelProperty(value = "公共电费分摊+公共电费分摊项目")
	private String fText13;
	@ApiModelProperty(value = "公共电费分摊+金额")
	private BigDecimal fAmount31;
	@ApiModelProperty(value = "二区车库公共电费+二区车库公共电费项目")
	private String fText14;
	@ApiModelProperty(value = "二区车库公共电费+金额")
	private BigDecimal fAmount34;
	@ApiModelProperty(value = "三区车库公共电费+三区车库公共电费项目")
	private String fText15;
	@ApiModelProperty(value = "三区车库公共电费+金额")
	private BigDecimal fAmount37;
	@ApiModelProperty(value = "空中花园公共电费+空中花园公共电费项目")
	private String fText16;
	@ApiModelProperty(value = "空中花园公共电费+金额")
	private BigDecimal fAmount40;
	@ApiModelProperty(value = "车库公共电费+车库公共电费项目")
	private String fText17;
	@ApiModelProperty(value = "车库公共电费+金额")
	private BigDecimal fAmount43;
	@ApiModelProperty(value = "露天车位管理服务费+露天车位管理服务费项目")
	private String fText18;
	@ApiModelProperty(value = "露天车位管理服务费+金额")
	private BigDecimal fAmount46;
	@ApiModelProperty(value = "场地费+场地费项目")
	private String fText19;
	@ApiModelProperty(value = "场地费+金额")
	private BigDecimal fAmount49;
	@ApiModelProperty(value = "出租车位管理服务费+出租车位管理服务费项目")
	private String fText20;
	@ApiModelProperty(value = "出租车位管理服务费+金额")
	private BigDecimal fAmount52;
	@ApiModelProperty(value = "商铺物业管理费+商铺物业管理费项目")
	private String fText21;
	@ApiModelProperty(value = "商铺物业管理费+金额")
	private BigDecimal fAmount55;
	@ApiModelProperty(value = "工程维修+工程维修项目")
	private String fText22;
	@ApiModelProperty(value = "工程维修+金额")
	private BigDecimal fAmount58;
	@ApiModelProperty(value = "小区临时停车收费+小区临时停车收费项目")
	private String fText23;
	@ApiModelProperty(value = "小区临时停车收费+金额")
	private BigDecimal fAmount61;
	@ApiModelProperty(value = "车库公共水费分摊+车库公共水费分摊项目")
	private String fText24;
	@ApiModelProperty(value = "车库公共水费分摊+金额")
	private BigDecimal fAmount64;
	@ApiModelProperty(value = "日常办公费+日常办公费项目")
	private String fText25;
	@ApiModelProperty(value = "日常办公费+金额")
	private BigDecimal fAmount67;
	@ApiModelProperty(value = "砂石等费用+砂石等费用项目")
	private String fText26;
	@ApiModelProperty(value = "砂石等费用+金额")
	private BigDecimal fAmount70;
	@ApiModelProperty(value = "损害财产赔偿款+损害财产赔偿款项目")
	private String fText27;
	@ApiModelProperty(value = "损害财产赔偿款+含税金额")
	private BigDecimal fAmount73;
	@ApiModelProperty(value = "有偿绿化服务费+有偿绿化服务费项目")
	private String fText28;
	@ApiModelProperty(value = "有偿绿化服务费+金额")
	private BigDecimal fAmount76;
	@ApiModelProperty(value = "有偿清洁服务费+有偿清洁服务费项目")
	private String fText29;
	@ApiModelProperty(value = "有偿清洁服务费+金额")
	private BigDecimal fAmount79;
	@ApiModelProperty(value = "IC卡工本费+IC卡工本费项目")
	private String fText30;
	@ApiModelProperty(value = "IC卡工本费+金额")
	private BigDecimal fAmount82;
	@ApiModelProperty(value = "社区活动收入+社区活动收入项目")
	private String fText31;
	@ApiModelProperty(value = "社区活动收入+金额")
	private BigDecimal fAmount85;
	@ApiModelProperty(value = "证件工本费+证件工本费项目")
	private String fText32;
	@ApiModelProperty(value = "证件工本费+金额")
	private BigDecimal fAmount88;
	@ApiModelProperty(value = "违约金+违约金项目")
	private String fText33;
	@ApiModelProperty(value = "违约金+金额")
	private BigDecimal fAmount91;
	@ApiModelProperty(value = "代收车位租金+代收车位租金项目")
	private String fText34;
	@ApiModelProperty(value = "代收车位租金+金额")
	private BigDecimal fAmount94;
	@ApiModelProperty(value = "代收代缴房屋办证费+代收代缴房屋办证费项目")
	private String fText35;
	@ApiModelProperty(value = "代收代缴房屋办证费+金额")
	private BigDecimal fAmount97;
	@ApiModelProperty(value = "员工餐费+员工餐费项目")
	private String fText36;
	@ApiModelProperty(value = "员工餐费+金额")
	private BigDecimal fAmount100;

	@ApiModelProperty(value = "代收电费+代收电费项目")
	private String fText37;
	@ApiModelProperty(value = "代收电费+金额")
	private BigDecimal fAmount103;

	@ApiModelProperty(value = "宿舍物业管理费+宿舍物业管理费项目")
	private String fText38;
	@ApiModelProperty(value = "宿舍物业管理费+金额")
	private BigDecimal fAmount106;

	@ApiModelProperty(value = "垃圾清运费+垃圾清运费项目") 
	private String fText39;
	@ApiModelProperty(value = "垃圾清运费+金额") 
	private BigDecimal fAmount109;

	@ApiModelProperty(value = "代收水费+代收水费项目") 
	private String fText40;
	@ApiModelProperty(value = "代收水费+金额") 
	private BigDecimal fAmount112;

	@ApiModelProperty(value = "物业管理费保证金+物业管理费保证金项目") 
	private String fText41;
	@ApiModelProperty(value = "物业管理费保证金+金额") 
	private BigDecimal fAmount115;

	@ApiModelProperty(value = "重型机械进场押金+重型机械进场押金项目") 
	private String fText42;
	@ApiModelProperty(value = "重型机械进场押金+金额") 
	private BigDecimal fAmount118;

	@ApiModelProperty(value = "租金保证金+租金保证金项目") 
	private String fText43;
	@ApiModelProperty(value = "租金保证金+金额") 
	private BigDecimal fAmount121;

	@ApiModelProperty(value = "装修保证金+装修保证金项目") 
	private String fText44;
	@ApiModelProperty(value = "装修保证金+金额") 
	private BigDecimal fAmount124;

	@ApiModelProperty(value = "宿舍租金+宿舍租金") 
	private String fText45;
	@ApiModelProperty(value = "宿舍租金+金额") 
	private BigDecimal fAmount127;

	@ApiModelProperty(value = "公共设施保证金+公共设施保证金项目") 
	private String fText46;
	@ApiModelProperty(value = "公共设施保证金+金额") 
	private BigDecimal fAmount130;

	@ApiModelProperty(value = "穿梭车车费+穿梭车车费项目") 
	private String fText47;
	@ApiModelProperty(value = "穿梭车车费+金额") 
	private BigDecimal fAmount133;

	@ApiModelProperty(value = "游泳池门票+游泳池门票项目") 
	private String fText48;
	@ApiModelProperty(value = "游泳池门票+金额") 
	private BigDecimal fAmount136;

	@ApiModelProperty(value = "水电周转保证金+水电周转保证金项目") 
	private String fText49;
	@ApiModelProperty(value = "水电周转保证金+金额") 
	private BigDecimal fAmount139;

	@ApiModelProperty(value = "商铺租金+商铺租金项目") 
	private String fText50;
	@ApiModelProperty(value = "商铺租金+金额") 
	private BigDecimal fAmount142;

	@ApiModelProperty(value = "公共水费分摊+公共水费分摊项目") 
	private String fText51;
	@ApiModelProperty(value = "公共水费分摊+金额") 
	private BigDecimal fAmount145;

	@ApiModelProperty(value = "小卖部食品+小卖部食品项目") 
	private String fText52;
	@ApiModelProperty(value = "小卖部食品+金额") 
	private BigDecimal fAmount148;

	@ApiModelProperty(value = "花园及停车位管理费违约金+项目") 
	private String fText56;
	@ApiModelProperty(value = "花园及停车位管理费违约金+金额") 
	private BigDecimal fAmount9;

	@ApiModelProperty(value = "露天车位管理服务费违约金+项目") 
	private String fText57;
	@ApiModelProperty(value = "露天车位管理服务费违约金+金额") 
	private BigDecimal fAmount154;

	@ApiModelProperty(value = "车位管理费违约金+项目") 
	private String fText58;
	@ApiModelProperty(value = "车位管理费违约金+金额") 
	private BigDecimal fAmount157;

	@ApiModelProperty(value = "有偿服务费+有偿服务费项目") 
	private String fText59;
	@ApiModelProperty(value = "有偿服务费+金额") 
	private BigDecimal fAmount160;

	@ApiModelProperty(value = "露天车位管理服务费2+项目") 
	private String fText60;
	@ApiModelProperty(value = "露天车位管理服务费2+含税金额") 
	private BigDecimal fAmount163;

	@ApiModelProperty(value = "露天车位管理服务费3+项目") 
	private String fText61;
	@ApiModelProperty(value = "露天车位管理服务费3+含税金额") 
	private BigDecimal fAmount166;

	@ApiModelProperty(value = "露天车位管理服务费4+项目") 
	private String fText62;
	@ApiModelProperty(value = "露天车位管理服务费4+含税金额") 
	private BigDecimal fAmount169;

	@ApiModelProperty(value = "商业街临时停车费+项目") 
	private String fText67;
	@ApiModelProperty(value = "商业街临时停车费+金额") 
	private BigDecimal fAmount177;

	@ApiModelProperty(value = "儿童公园及精灵屋门票款+项目") 
	private String fText63;
	@ApiModelProperty(value = "儿童公园及精灵屋门票款+含税金额") 
	private BigDecimal fAmount172;

	@ApiModelProperty(value = "电费+电费项目") 
	private String fText68;
	@ApiModelProperty(value = "电费+金额") 
	private BigDecimal fAmount183;

	@ApiModelProperty(value = "水费+水费项目") 
	private String fText69;
	@ApiModelProperty(value = "水费+水费金额") 
	private BigDecimal fAmount186;

	@ApiModelProperty(value = "往来款+往来款项目") 
	private String fText70;
	@ApiModelProperty(value = "往来款+往来款金额") 
	private BigDecimal fAmount189;

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
	
	
	public static ReceiptOrderForm getShouju(Object[] o) {
		ReceiptOrderForm vo = null;
		if(null!=o) {
			try {
				vo=new ReceiptOrderForm();
				vo.setFBillNo(null!=o[0]?o[0].toString():"");
				vo.setFDate(null!=o[1]?o[1].toString():"");
				vo.setFyzxm(null!=o[2]?o[2].toString():"");
				vo.setFdymc(null!=o[3]?o[3].toString():"");
				vo.setFText(null!=o[4]?o[4].toString():"");
				vo.setFText1(null!=o[5]?o[5].toString():"");
				vo.setFText2(null!=o[6]?o[6].toString():"");
				vo.setFText3(null!=o[7]?o[7].toString():"");
				vo.setFAmount(null!=o[8]&&StringUtils.isNotEmpty(o[8].toString())?new BigDecimal(o[8].toString()):new BigDecimal("0"));
				vo.setFAmount1(null!=o[9]&&StringUtils.isNotEmpty(o[9].toString())?new BigDecimal(o[9].toString()):new BigDecimal("0"));
				vo.setFAmount2(null!=o[10]&&StringUtils.isNotEmpty(o[10].toString())?new BigDecimal(o[10].toString()):new BigDecimal("0"));
				vo.setFAmount3(null!=o[11]&&StringUtils.isNotEmpty(o[11].toString())?new BigDecimal(o[11].toString()):new BigDecimal("0"));
				vo.setFAmount4(null!=o[12]&&StringUtils.isNotEmpty(o[12].toString())?new BigDecimal(o[12].toString()):new BigDecimal("0"));
				vo.setFAmount5(null!=o[13]&&StringUtils.isNotEmpty(o[13].toString())?new BigDecimal(o[13].toString()):new BigDecimal("0"));
				vo.setFAmount175(null!=o[14]&&StringUtils.isNotEmpty(o[14].toString())?new BigDecimal(o[14].toString()):new BigDecimal("0"));
				vo.setFAmount6(null!=o[15]&&StringUtils.isNotEmpty(o[15].toString())?new BigDecimal(o[15].toString()):new BigDecimal("0"));
				vo.setFAmount176(null!=o[16]&&StringUtils.isNotEmpty(o[16].toString())?new BigDecimal(o[16].toString()):new BigDecimal("0"));
				vo.setFAmount7(null!=o[17]&&StringUtils.isNotEmpty(o[17].toString())?new BigDecimal(o[17].toString()):new BigDecimal("0"));
				vo.setFText64(null!=o[18]?o[18].toString():"");
				vo.setFText65(null!=o[19]?o[19].toString():"");
				vo.setFText55(null!=o[20]?o[20].toString():"");
				vo.setFAmount180(null!=o[21]&&StringUtils.isNotEmpty(o[21].toString())?new BigDecimal(o[21].toString()):new BigDecimal("0"));
				vo.setFAmount181(null!=o[22]&&StringUtils.isNotEmpty(o[22].toString())?new BigDecimal(o[22].toString()):new BigDecimal("0"));
				vo.setFAmount182(null!=o[23]&&StringUtils.isNotEmpty(o[23].toString())?new BigDecimal(o[23].toString()):new BigDecimal("0"));
				
				vo.setFBillNo1(null!=o[24]?o[24].toString():"");
				vo.setFText4(null!=o[25]?o[25].toString():"");
				vo.setFText5(null!=o[26]?o[26].toString():"");
				vo.setFAmount8(null!=o[27]&&StringUtils.isNotEmpty(o[27].toString())?new BigDecimal(o[27].toString()):new BigDecimal("0"));
				vo.setFText6(null!=o[28]?o[28].toString():"");
				vo.setFAmount10(null!=o[29]&&StringUtils.isNotEmpty(o[29].toString())?new BigDecimal(o[29].toString()):new BigDecimal("0"));
				vo.setFText7(null!=o[30]?o[30].toString():"");
				vo.setFAmount13(null!=o[31]&&StringUtils.isNotEmpty(o[31].toString())?new BigDecimal(o[31].toString()):new BigDecimal("0"));
				vo.setFText8(null!=o[32]?o[32].toString():"");
				vo.setFAmount16(null!=o[33]&&StringUtils.isNotEmpty(o[33].toString())?new BigDecimal(o[33].toString()):new BigDecimal("0"));
				vo.setFText9(null!=o[34]?o[34].toString():"");
				vo.setFAmount19(null!=o[35]&&StringUtils.isNotEmpty(o[35].toString())?new BigDecimal(o[35].toString()):new BigDecimal("0"));
				vo.setFText10(null!=o[36]?o[36].toString():"");
				vo.setFAmount22(null!=o[37]&&StringUtils.isNotEmpty(o[37].toString())?new BigDecimal(o[37].toString()):new BigDecimal("0"));
				vo.setFText11(null!=o[38]?o[38].toString():"");
				vo.setFAmount25(null!=o[39]&&StringUtils.isNotEmpty(o[39].toString())?new BigDecimal(o[39].toString()):new BigDecimal("0"));
				vo.setFText12(null!=o[40]?o[40].toString():"");
				vo.setFAmount28(null!=o[41]&&StringUtils.isNotEmpty(o[41].toString())?new BigDecimal(o[41].toString()):new BigDecimal("0"));
				vo.setFText13(null!=o[42]?o[42].toString():"");
				vo.setFAmount31(null!=o[43]&&StringUtils.isNotEmpty(o[43].toString())?new BigDecimal(o[43].toString()):new BigDecimal("0"));
				vo.setFText14(null!=o[44]?o[44].toString():"");
				vo.setFAmount34(null!=o[45]&&StringUtils.isNotEmpty(o[45].toString())?new BigDecimal(o[45].toString()):new BigDecimal("0"));
				vo.setFText15(null!=o[46]?o[46].toString():"");
				vo.setFAmount37(null!=o[47]&&StringUtils.isNotEmpty(o[47].toString())?new BigDecimal(o[47].toString()):new BigDecimal("0"));
				vo.setFText16(null!=o[48]?o[48].toString():"");
				vo.setFAmount40(null!=o[49]&&StringUtils.isNotEmpty(o[49].toString())?new BigDecimal(o[49].toString()):new BigDecimal("0"));
				vo.setFText17(null!=o[50]?o[50].toString():"");
				vo.setFAmount43(null!=o[51]&&StringUtils.isNotEmpty(o[51].toString())?new BigDecimal(o[51].toString()):new BigDecimal("0"));
				vo.setFText18(null!=o[52]?o[52].toString():"");
				vo.setFAmount46(null!=o[53]&&StringUtils.isNotEmpty(o[53].toString())?new BigDecimal(o[53].toString()):new BigDecimal("0"));
				vo.setFText19(null!=o[54]?o[54].toString():"");
				vo.setFAmount49(null!=o[55]&&StringUtils.isNotEmpty(o[55].toString())?new BigDecimal(o[55].toString()):new BigDecimal("0"));
				vo.setFText20(null!=o[56]?o[56].toString():"");
				vo.setFAmount52(null!=o[57]&&StringUtils.isNotEmpty(o[57].toString())?new BigDecimal(o[57].toString()):new BigDecimal("0"));
				vo.setFText21(null!=o[58]?o[58].toString():"");
				vo.setFAmount55(null!=o[59]&&StringUtils.isNotEmpty(o[59].toString())?new BigDecimal(o[59].toString()):new BigDecimal("0"));
				vo.setFText22(null!=o[60]?o[60].toString():"");
				vo.setFAmount58(null!=o[61]&&StringUtils.isNotEmpty(o[61].toString())?new BigDecimal(o[61].toString()):new BigDecimal("0"));
				vo.setFText23(null!=o[62]?o[62].toString():"");
				vo.setFAmount61(null!=o[63]&&StringUtils.isNotEmpty(o[63].toString())?new BigDecimal(o[63].toString()):new BigDecimal("0"));
				vo.setFText24(null!=o[64]?o[64].toString():"");
				vo.setFAmount64(null!=o[65]&&StringUtils.isNotEmpty(o[65].toString())?new BigDecimal(o[65].toString()):new BigDecimal("0"));
				vo.setFText25(null!=o[66]?o[66].toString():"");
				vo.setFAmount67(null!=o[67]&&StringUtils.isNotEmpty(o[67].toString())?new BigDecimal(o[67].toString()):new BigDecimal("0"));
				vo.setFText26(null!=o[68]?o[68].toString():"");
				vo.setFAmount70(null!=o[69]&&StringUtils.isNotEmpty(o[69].toString())?new BigDecimal(o[69].toString()):new BigDecimal("0"));
				vo.setFText27(null!=o[70]?o[70].toString():"");
				vo.setFAmount73(null!=o[71]&&StringUtils.isNotEmpty(o[71].toString())?new BigDecimal(o[71].toString()):new BigDecimal("0"));
				vo.setFText28(null!=o[72]?o[72].toString():"");
				vo.setFAmount76(null!=o[73]&&StringUtils.isNotEmpty(o[73].toString())?new BigDecimal(o[73].toString()):new BigDecimal("0"));
				vo.setFText29(null!=o[74]?o[74].toString():"");
				vo.setFAmount79(null!=o[75]&&StringUtils.isNotEmpty(o[75].toString())?new BigDecimal(o[75].toString()):new BigDecimal("0"));
				vo.setFText30(null!=o[76]?o[76].toString():"");
				vo.setFAmount82(null!=o[77]&&StringUtils.isNotEmpty(o[77].toString())?new BigDecimal(o[77].toString()):new BigDecimal("0"));
				vo.setFText31(null!=o[78]?o[78].toString():"");
				vo.setFAmount85(null!=o[79]&&StringUtils.isNotEmpty(o[79].toString())?new BigDecimal(o[79].toString()):new BigDecimal("0"));
				vo.setFText32(null!=o[80]?o[80].toString():"");
				vo.setFAmount88(null!=o[81]&&StringUtils.isNotEmpty(o[81].toString())?new BigDecimal(o[81].toString()):new BigDecimal("0"));
				vo.setFText33(null!=o[82]?o[82].toString():"");
				vo.setFAmount91(null!=o[83]&&StringUtils.isNotEmpty(o[83].toString())?new BigDecimal(o[83].toString()):new BigDecimal("0"));
				vo.setFText34(null!=o[84]?o[84].toString():"");
				vo.setFAmount94(null!=o[85]&&StringUtils.isNotEmpty(o[85].toString())?new BigDecimal(o[85].toString()):new BigDecimal("0"));
				vo.setFText35(null!=o[86]?o[86].toString():"");
				vo.setFAmount97(null!=o[87]&&StringUtils.isNotEmpty(o[87].toString())?new BigDecimal(o[87].toString()):new BigDecimal("0"));
				vo.setFText36(null!=o[88]?o[88].toString():"");
				vo.setFAmount100(null!=o[89]&&StringUtils.isNotEmpty(o[89].toString())?new BigDecimal(o[89].toString()):new BigDecimal("0"));
				vo.setFText37(null!=o[90]?o[90].toString():"");
				vo.setFAmount103(null!=o[91]&&StringUtils.isNotEmpty(o[91].toString())?new BigDecimal(o[91].toString()):new BigDecimal("0"));
				vo.setFText38(null!=o[92]?o[92].toString():"");
				vo.setFAmount106(null!=o[93]&&StringUtils.isNotEmpty(o[93].toString())?new BigDecimal(o[93].toString()):new BigDecimal("0"));
				vo.setFText39(null!=o[94]?o[94].toString():"");
				vo.setFAmount109(null!=o[95]&&StringUtils.isNotEmpty(o[95].toString())?new BigDecimal(o[95].toString()):new BigDecimal("0"));
				vo.setFText40(null!=o[96]?o[96].toString():"");
				vo.setFAmount112(null!=o[97]&&StringUtils.isNotEmpty(o[97].toString())?new BigDecimal(o[97].toString()):new BigDecimal("0"));
				vo.setFText41(null!=o[98]?o[98].toString():"");
				vo.setFAmount115(null!=o[99]&&StringUtils.isNotEmpty(o[99].toString())?new BigDecimal(o[99].toString()):new BigDecimal("0"));
				vo.setFText42(null!=o[100]?o[100].toString():"");
				vo.setFAmount118(null!=o[101]&&StringUtils.isNotEmpty(o[101].toString())?new BigDecimal(o[101].toString()):new BigDecimal("0"));
				vo.setFText43(null!=o[102]?o[102].toString():"");
				vo.setFAmount121(null!=o[103]&&StringUtils.isNotEmpty(o[103].toString())?new BigDecimal(o[103].toString()):new BigDecimal("0"));
				vo.setFText44(null!=o[104]?o[104].toString():"");
				vo.setFAmount124(null!=o[105]&&StringUtils.isNotEmpty(o[105].toString())?new BigDecimal(o[105].toString()):new BigDecimal("0"));
				vo.setFText45(null!=o[106]?o[106].toString():"");
				vo.setFAmount127(null!=o[107]&&StringUtils.isNotEmpty(o[107].toString())?new BigDecimal(o[107].toString()):new BigDecimal("0"));
				vo.setFText46(null!=o[108]?o[108].toString():"");
				vo.setFAmount130(null!=o[109]&&StringUtils.isNotEmpty(o[109].toString())?new BigDecimal(o[109].toString()):new BigDecimal("0"));
				vo.setFText47(null!=o[110]?o[110].toString():"");
				vo.setFAmount133(null!=o[111]&&StringUtils.isNotEmpty(o[111].toString())?new BigDecimal(o[111].toString()):new BigDecimal("0"));
				vo.setFText48(null!=o[112]?o[112].toString():"");
				vo.setFAmount136(null!=o[113]&&StringUtils.isNotEmpty(o[113].toString())?new BigDecimal(o[113].toString()):new BigDecimal("0"));
				vo.setFText49(null!=o[114]?o[114].toString():"");
				vo.setFAmount139(null!=o[115]&&StringUtils.isNotEmpty(o[115].toString())?new BigDecimal(o[115].toString()):new BigDecimal("0"));
				vo.setFText50(null!=o[116]?o[116].toString():"");
				vo.setFAmount142(null!=o[117]&&StringUtils.isNotEmpty(o[117].toString())?new BigDecimal(o[117].toString()):new BigDecimal("0"));
				vo.setFText51(null!=o[118]?o[118].toString():"");
				vo.setFAmount145(null!=o[119]&&StringUtils.isNotEmpty(o[119].toString())?new BigDecimal(o[119].toString()):new BigDecimal("0"));
				vo.setFText52(null!=o[120]?o[120].toString():"");
				vo.setFAmount148(null!=o[121]&&StringUtils.isNotEmpty(o[121].toString())?new BigDecimal(o[121].toString()):new BigDecimal("0"));
				vo.setFText56(null!=o[122]?o[122].toString():"");
				vo.setFAmount9(null!=o[123]&&StringUtils.isNotEmpty(o[123].toString())?new BigDecimal(o[123].toString()):new BigDecimal("0"));
				vo.setFText57(null!=o[124]?o[124].toString():"");
				vo.setFAmount154(null!=o[125]&&StringUtils.isNotEmpty(o[125].toString())?new BigDecimal(o[125].toString()):new BigDecimal("0"));
				vo.setFText58(null!=o[126]?o[126].toString():"");
				vo.setFAmount157(null!=o[127]&&StringUtils.isNotEmpty(o[127].toString())?new BigDecimal(o[127].toString()):new BigDecimal("0"));
				vo.setFText59(null!=o[128]?o[128].toString():"");
				vo.setFAmount160(null!=o[129]&&StringUtils.isNotEmpty(o[129].toString())?new BigDecimal(o[129].toString()):new BigDecimal("0"));
				vo.setFText60(null!=o[130]?o[130].toString():"");
				vo.setFAmount163(null!=o[131]&&StringUtils.isNotEmpty(o[131].toString())?new BigDecimal(o[131].toString()):new BigDecimal("0"));
				vo.setFText61(null!=o[132]?o[132].toString():"");
				vo.setFAmount166(null!=o[133]&&StringUtils.isNotEmpty(o[133].toString())?new BigDecimal(o[133].toString()):new BigDecimal("0"));
				vo.setFText62(null!=o[134]?o[134].toString():"");
				vo.setFAmount169(null!=o[135]&&StringUtils.isNotEmpty(o[135].toString())?new BigDecimal(o[135].toString()):new BigDecimal("0"));
				vo.setFText67(null!=o[136]?o[136].toString():"");
				vo.setFAmount177(null!=o[137]&&StringUtils.isNotEmpty(o[137].toString())?new BigDecimal(o[137].toString()):new BigDecimal("0"));
				vo.setFText63(null!=o[138]?o[138].toString():"");
				vo.setFAmount172(null!=o[139]&&StringUtils.isNotEmpty(o[139].toString())?new BigDecimal(o[139].toString()):new BigDecimal("0"));
				vo.setFText68(null!=o[140]?o[140].toString():"");
				vo.setFAmount183(null!=o[141]&&StringUtils.isNotEmpty(o[141].toString())?new BigDecimal(o[141].toString()):new BigDecimal("0"));
				vo.setFText69(null!=o[142]?o[142].toString():"");
				vo.setFAmount186(null!=o[143]&&StringUtils.isNotEmpty(o[143].toString())?new BigDecimal(o[143].toString()):new BigDecimal("0"));
				vo.setFText70(null!=o[144]?o[144].toString():"");
				vo.setFAmount189(null!=o[145]&&StringUtils.isNotEmpty(o[145].toString())?new BigDecimal(o[145].toString()):new BigDecimal("0"));
			}catch (Exception ex) {
				ex.printStackTrace();
				vo = null;
			}
		}
		return vo;
	}

}
