package com.foshan.form.community.response.contract;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

	
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="合同返回对象(AddCommunityContractRes)")
@JsonInclude(Include.NON_NULL)
public  class AddCommunityContractRes extends BaseResponse {/**
	 * 
	 */
	private static final long serialVersionUID = -7189839016649583955L;

}
