package com.foshan.form.community.statistics;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "楼盘入住率数据")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BuildingOccupancyVo implements IForm {
	/**
	* 
	*/
	private static final long serialVersionUID = 4987560954013832731L;
	@ApiModelProperty(value = "楼盘编码")
	private String districtCode;
	@ApiModelProperty(value = "楼盘名称")
	private String districtName;
	@ApiModelProperty(value = "楼阁")
	private String buildingName;
	@ApiModelProperty(value = "总面积")
	private String totalBuildingArea;
	@ApiModelProperty(value = "总入住面积")
	private String totalCheckInArea;
	@ApiModelProperty(value = "总单元数")
	private String totalEstateCount;
	@ApiModelProperty(value = "未售单元数")
	private String unsoldEstateCount;
	@ApiModelProperty(value = "入住单元数")
	private String checkInEstateCount;
	@ApiModelProperty(value = "入住率")
	private String occcupancyRate;
	@ApiModelProperty(value = "装修单元数")
	private String fitmentEstateCount;
	@ApiModelProperty(value = "装修率")
	private String fitmentRate;
	@ApiModelProperty(value = "自住单元数")
	private String selfOccupationEstateCount;
	@ApiModelProperty(value = "自住率")
	private String selfOccupationRate;
	@ApiModelProperty(value = "未收楼单元数")
	private String uncollectedEstateCount;
	@ApiModelProperty(value = "未收楼率")
	private String uncollectedRate;
	@ApiModelProperty(value = "空置单元数")
	private String vacancyEstateCount;
	@ApiModelProperty(value = "空置率")
	private String vacancyRate;

	public String getNewDistrictCode() {
		return (districtCode.length() == 1 ? "00" + districtCode
				: districtCode.length() == 2 ? "0" + districtCode : districtCode) + districtName;
	}

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
