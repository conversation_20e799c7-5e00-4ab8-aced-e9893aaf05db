package com.foshan.form.community.response.communityPayItems;

import java.util.ArrayList;
import java.util.List;

import com.foshan.form.community.CommunityEstateForm;
import com.foshan.form.community.CommunityPayItemsForm;
import com.foshan.form.community.CommunityPayItemsPriceForm;
import com.foshan.form.community.CommunityPropertyForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="根据单元获取收费项目返回列表(GetCommunityPayItemsListByPropertyRes)")
public class GetCommunityPayItemsListByPropertyRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -3283838301982288154L;
	@ApiModelProperty(value = "单元收费项目列表")
	private List<CommunityPayItemsForm> communityPayItemsList = new ArrayList<CommunityPayItemsForm>(); 
	@ApiModelProperty(value = "停车位列表")
	private List<CommunityPropertyForm> parkingList = new ArrayList<CommunityPropertyForm>(); 
	@ApiModelProperty(value = "单元")
	private CommunityEstateForm estateForm;

}
