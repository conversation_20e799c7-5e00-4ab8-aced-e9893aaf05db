package com.foshan.form.community.response.optionsVo;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.OptionsVoForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取选项返回列表对象(GetOptionsVoList)")
@JsonInclude(Include.NON_NULL)
public class GetOptionsVoList extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1338593095205759025L;
	@ApiModelProperty(value = "选项对象列表")
	private List<OptionsVoForm> optionsList = new ArrayList<OptionsVoForm>(); 

}
