package com.foshan.form.community.request;

import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="应收的付款记录(CommunityPaymentRecordReceivablesReq)")
public  class CommunityPaymentRecordReceivablesReq extends BasePageRequest {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer communityPaymentRecordReceivablesId;
  @ApiModelProperty(value = "",example="1")
    private Integer paymentRecordId;
    @ApiModelProperty(value = "",example="1")
    private Integer receivablesId;
  
}
