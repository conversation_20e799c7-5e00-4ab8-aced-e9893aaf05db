package com.foshan.form.community.response.communityBuilder;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="开发商(ModifyCommunityBuilderRes)")
@JsonInclude(Include.NON_NULL)
public  class ModifyCommunityBuilderRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 8638614461987077692L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityBuilderId;
    @ApiModelProperty(value = "名称",example="1")
    private String name;

}
