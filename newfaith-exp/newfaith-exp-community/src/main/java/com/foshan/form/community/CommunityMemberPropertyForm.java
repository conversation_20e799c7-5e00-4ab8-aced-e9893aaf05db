package com.foshan.form.community;


import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="会员资产对象(CommunityMemberPropertyForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityMemberPropertyForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = -7829726556978278184L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer memberPropertyId;
  @ApiModelProperty(value = "计费日期")
    private String billingDate;
    @ApiModelProperty(value = "创建时间")
    private String createTime;
    @ApiModelProperty(value = "是否当前业主 0-否，1-是",example="1")
    private Integer isCurrentOwner;
    @ApiModelProperty(value = "入住日期")
    private String recordDate;
    @ApiModelProperty(value = "离退日期")
    private String terminationDate;	
	@ApiModelProperty(value = "会员")
	private CommunityMemberForm memberForm;
	@ApiModelProperty(value = "资产")
	private CommunityPropertyForm propertyForm;
//	@ApiModelProperty(value = "收楼日期")
//	private String acceptanceDate;
	@ApiModelProperty(value = "0业主 1住户成员（住户成员不需要填写收楼日期与计费日期）",example="1")
	private Integer memberType;
	@ApiModelProperty(value = "审核状态 0：申请中；1：通过；2：拒绝；",example="1")
	private Integer auditState;
	@ApiModelProperty(value = "是否该单元当前有效的用户；0-否,1-是",example="1")
	private Integer isCurrentMember;
	@ApiModelProperty(value = "车辆信息（json格式存放）")
	private String carInfo;
	@ApiModelProperty(value = "备注")
	private String comment;
	@ApiModelProperty(value = "计费结束日期")
	private String endDate;
	@ApiModelProperty(value = "购方名称")
	protected String buyersName;
	@ApiModelProperty(value = "购方地址")
	protected String buyersAddress;
	@ApiModelProperty(value = "企业类型")
	protected String businessType;
	@ApiModelProperty(value = "购方税号")
	protected String paytaxNo;
	@ApiModelProperty(value = "购方银行账号")
	protected String buyersBankAccount;
	@ApiModelProperty(value = "购方邮箱，用于电子发票")
	protected String buyerEmail;
	@ApiModelProperty(value = "租用类型 0-长租；1-短租；",example="1")
	private Integer rentType;
	@ApiModelProperty(value = "合同信息")
	protected String contractInfo;
	@ApiModelProperty(value = "计费日期最后修改时间")
	protected String billingDateModify;
	@ApiModelProperty(value = "车位关联单元")
	private CommunityPropertyForm parentPropertyForm;
	@ApiModelProperty(value = "子收费项目")
	private List<CommunityPayItemsPriceForm> payItemsPriceForm = new ArrayList<>();
	@ApiModelProperty(value = "合同")
	private CommunityMemberContractForm contract;


	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
