package com.foshan.form.community.response.communityBankDepositBatch;

import java.util.ArrayList;
import java.util.List;

import com.foshan.form.community.BankDepositEstatePayItemsForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取银行划账-特殊划扣单元项目列表(GetBankDepositEstatePayItemsListRes)")
public class GetBankDepositEstatePayItemsListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 8618074856508081062L;
	@ApiModelProperty(value = "银行划账批次列表")
	private List<BankDepositEstatePayItemsForm> bankDepositEstatePayItemsList = new ArrayList<BankDepositEstatePayItemsForm>(); 

}
