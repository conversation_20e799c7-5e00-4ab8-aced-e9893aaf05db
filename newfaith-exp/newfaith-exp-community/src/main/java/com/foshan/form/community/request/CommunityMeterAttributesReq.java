package com.foshan.form.community.request;

import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;



@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="表属性(CommunityMeterAttributesReq)")
public  class CommunityMeterAttributesReq extends BasePageRequest {


	/**
	 * 
	 */
	private static final long serialVersionUID = -4695886499449440650L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityMeterAttributesId;
    @ApiModelProperty(value = "属性名称",example="1")
    private String attributeName;
    @ApiModelProperty(value = "类别（1:电表、2:水表、3:临时表、4:代收水表、5:代收电表）",example="1")
    private Integer category;
    @ApiModelProperty(value = "备注",example="1")
    private String comment;
    @ApiModelProperty(value = "单位",example="1")
    private String measureUnit;
    @ApiModelProperty(value = "旧表数据",example="1")
    private String oldData;
    @ApiModelProperty(value = "旧表ID",example="1")
    private String oldId;
    @ApiModelProperty(value = "量程",example="1")
    private Integer ranges;
    @ApiModelProperty(value = "倍率",example="1")
    private String rate;
    @ApiModelProperty(value = "单价",example="1")
    private String unitPrice;
    @ApiModelProperty(value = "表ID")
    private String meterIdList;

}
