package com.foshan.form.community.response.communityDecorationAttachment;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityDecorationAttachmentForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="装修项目附件(GetCommunityDecorationAttachmentInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityDecorationAttachmentInfoRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "装修项目附件对象")
	private CommunityDecorationAttachmentForm communityDecorationAttachmentForm ; 

}
