package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="时间段对象(CommunityReservatPeriodForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityReservatPeriodForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = 3476324180626675242L;
	@ApiModelProperty(value = "时间段ID", example = "1")
	private Integer periodId;
	@ApiModelProperty(value = "总预约数", example = "1")
	protected Integer maxNum;
	@ApiModelProperty(value = "排序值", example = "1")
	protected Integer orders;
	@ApiModelProperty(value = "开始时间")
	private String startTime;
	@ApiModelProperty(value = "结束时间")
	private String endTime;
	@ApiModelProperty(value = "状态 0--无效数据 1--有效数据", example = "1")
	private Integer state;
	@ApiModelProperty(value = "剩余可预约人数", example = "1")
	protected Integer overplusNum;
	

	
	public CommunityReservatPeriodForm(Integer periodId,Integer maxNum,Integer orders,String startTime,
			String endTime,Integer state,Integer overplusNum) {
		this.periodId = periodId;
		this.maxNum = maxNum;
		this.orders = orders;
		this.startTime = startTime;
		this.endTime = endTime;
		this.state = state;
		this.overplusNum=overplusNum;
	}

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
