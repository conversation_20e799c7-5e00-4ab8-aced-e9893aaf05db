package com.foshan.form.community.request;

import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="自定义分摊公式(CommunityMeterFormulaReq)")
public  class CommunityMeterFormulaReq extends BasePageRequest {


	/**
	 * 
	 */
	private static final long serialVersionUID = 8688750795153111802L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityMeterFormulaId;
    @ApiModelProperty(value = "备注")
    private String comment;
    @ApiModelProperty(value = "公式名称")
    private String formulaName;
    @ApiModelProperty(value = "动态参数")
    private String dynamicParameter;
    @ApiModelProperty(value = "公式模版id",example="1")
    private Integer templeteId;
    @ApiModelProperty(value = "表ID")
    private String meterIdList;
    @ApiModelProperty(value = "类别（1:电表、2:水表、3:临时表、4:代收水表、5:代收电表）")
    private Integer category;

}
