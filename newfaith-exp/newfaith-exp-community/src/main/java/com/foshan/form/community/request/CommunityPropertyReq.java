package com.foshan.form.community.request;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="请求参数(CommunityPropertyReq)")
public class CommunityPropertyReq extends BasePageRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = -4234019214319980731L;
	@ApiModelProperty(value = "资产ID", example = "1")
	private Integer propertyId;
	@ApiModelProperty(value = "会员ID", example = "1")
	private Integer memberId;
	@ApiModelProperty(value = "0:业主;1:住户成员（住户成员不需要填写收楼日期与计费日期）;2:租户(计费日期填写起租日期，离退日期填写退租日期)", example = "1")
	private Integer memberType;
	@ApiModelProperty(value = "会员姓名", example = "1")
	private String userName;
	@ApiModelProperty(value = "名称")
	private String propertyName;
	@ApiModelProperty(value = "资产类型 0:门禁卡；1：住宅；2：商铺；3车位；4公共区域；5:房产;", example = "1")
	private Integer propertyType;
	@ApiModelProperty(value = "资产类型 0:门禁卡；1：住宅；2：商铺；3车位；4公共区域；5:房产;")
	private String propertyTypeList;
	@ApiModelProperty(value = "创建时间")
	private String createTime;
	@ApiModelProperty(value = "修改时间")
	private String lastModifyTime;
	@ApiModelProperty(value = "状态 0--无效数据  1--有效数据", example = "1")
	private Integer state;
	@ApiModelProperty(value = "楼栋ID", example = "1")
	private Integer buildingId;
	@ApiModelProperty(value = "获取会员未绑定资产 (配合memberId来用)  1:未绑定", example = "1")
	private Integer unbindingMember;
	@ApiModelProperty(value = "获取绑定资产 (配合meterId来用)  0:未绑定；1：绑定；", example = "1")
	private Integer bindingMeter;
	@ApiModelProperty(value = "社区ID",example="1")   
	private Integer communityId;
	@ApiModelProperty(value = "小区ID",example="1")   
	private Integer districtdId;
	@ApiModelProperty(value = "表ID",example="1")  
	private Integer meterId;
	@ApiModelProperty(value = "收费项目ID",example="1")  
	private Integer payItemsId;
	/*@ApiModelProperty(value = "房产类型 0住宅 1商铺 2车位 3公共区域",example="1")   
	private Integer estateType;
	@ApiModelProperty(value = "所属区域")   
	private String area;
	@ApiModelProperty(value = "栋号")  
	private String buildingNum;
	@ApiModelProperty(value = "单元")  
	private String unitNum;*/
	@ApiModelProperty(value = "审核状态，多个以英文逗号隔开")  
	private String auditStateList;
	@ApiModelProperty(value = "会员和资产的关系 0业主1家属2租客（承租人）3租客")  
	private String relationList;
	@ApiModelProperty(value = "备注") 
	private String comment;
	@ApiModelProperty(value = "父ID",example="1")   
	private Integer parentPropertyId;
	
}
