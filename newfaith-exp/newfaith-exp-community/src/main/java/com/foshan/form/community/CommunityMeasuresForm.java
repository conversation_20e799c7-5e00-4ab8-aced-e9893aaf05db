package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" 单元欠费采取措施表(CommunityMeasuresForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityMeasuresForm implements IForm {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer communityMeasuresId;
  @ApiModelProperty(value = "采取措施时间")
    private String adoptTime;
    @ApiModelProperty(value = "创建时间")
    private String createTime;
    @ApiModelProperty(value = "详情")
    private String details;
    @ApiModelProperty(value = "最后修改时间")
    private String lastModifyTime;
    @ApiModelProperty(value = "状态 0--进行中  1--结束",example="1")
    private Integer measuresState;
    @ApiModelProperty(value = "措施类型（0:催缴费通知书、1：电话催缴、2:发律师信、3:准备起诉材料、4:其他类型）",example="1")
    private Integer measuresType;
    @ApiModelProperty(value = "经手人/录入员")
    private String operator;
    @ApiModelProperty(value = "",example="1")
    private Integer propertyId;
    @ApiModelProperty(value = "单元编号")
    private String unitCode;
  
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
