package com.foshan.form.community.response.communityMember;


import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityMemberForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获支社区会员返回列表对象(GetCommunityMemberListRes)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityMemberListRes extends BasePageResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -8276545283113165503L;
	@ApiModelProperty(value = "社区会员对象列表")
	private List<CommunityMemberForm> memberFormList = new ArrayList<CommunityMemberForm>(); 

}
