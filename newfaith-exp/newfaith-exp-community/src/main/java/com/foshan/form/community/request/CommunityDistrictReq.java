package com.foshan.form.community.request;


import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="小区(CommunityDistrictReq)")
public  class CommunityDistrictReq extends BasePageRequest {


	/**
	 * 
	 */
	private static final long serialVersionUID = -8129772234154306131L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityDistrictId;
    @ApiModelProperty(value = "地址")
    private String address;
    @ApiModelProperty(value = "占地面积")
    private String areaSize;
    @ApiModelProperty(value = "户数")
    private String housingNumber;
    @ApiModelProperty(value = "小区名称")
    private String districtName;
    @ApiModelProperty(value = "土地年限")
    private String period;
    @ApiModelProperty(value = "容积率")
    private String ratio;
    @ApiModelProperty(value = "社区ID",example="1")
    private Integer communityId;
    @ApiModelProperty(value = "幢数",example="1")
    private Integer buildingNumber;
    @ApiModelProperty(value = "备注")
    private String comment;
    @ApiModelProperty(value = "竣工日期")
    private String completionDate;
    @ApiModelProperty(value = "小区编号")
    private String districtCode;
    @ApiModelProperty(value = "移交日期")
    private String handoverDate;
    @ApiModelProperty(value = "人口数")
    private String population;
    @ApiModelProperty(value = "拿地时间")
    private String takeTime;
    @ApiModelProperty(value = "开发商ID，多个以英文逗号隔开")
    private String builderIdList;
    @ApiModelProperty(value = "数据状态 0--无效数据  1--有效数据",example="1")
	protected Integer state;
	@ApiModelProperty(value = "小区排序号",example="1")
	private Integer districtOrder;


}
