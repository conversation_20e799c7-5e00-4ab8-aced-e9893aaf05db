package com.foshan.form.community.request;

import javax.persistence.Column;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="会员产资请求参数(CommunityMemberPropertyReq)")
public class CommunityMemberPropertyReq extends CommunityMemberReq {
	/**
	 * 
	 */
	private static final long serialVersionUID = -3020342646269522612L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer memberPropertyId;
  @ApiModelProperty(value = "计费日期")
    private String billingDate;
    @ApiModelProperty(value = "创建时间")
    private String createTime;
    @ApiModelProperty(value = "是否当前业主 0-否，1-是",example="1")
    private Integer isCurrentOwner;
    @ApiModelProperty(value = "入住日期")
    private String recordDate;
    @ApiModelProperty(value = "离退日期")
    private String terminationDate;
    @ApiModelProperty(value = "会员ID",example="1")
    private Integer memberId;
    @ApiModelProperty(value = "账户信息ID",example="1")
    private Integer paymentAccountId;
    @ApiModelProperty(value = "资产ID",example="1")
    private Integer propertyId;
    @ApiModelProperty(value = "资产ID列表")
    private String propertyIdList;
    @ApiModelProperty(value = "",example="1")
    private Integer taxInformationId;
    @ApiModelProperty(value = "会员姓名")
    private String userName;
    @ApiModelProperty(value = "审核状态 0：申请中；1：通过；2：拒绝；",example="1")
	private Integer auditState;
    @ApiModelProperty(value = "收楼日期")
    private String acceptanceDate;

	@ApiModelProperty(value = "会员ID列表")
	private String memberIdList;
	@ApiModelProperty(value = "0业主 1住户成员（住户成员不需要填写收楼日期与计费日期）",example="1")
	private Integer memberType;
	@ApiModelProperty(value = "是否该单元当前有效的用户；0-否,1-是",example="1")
	private Integer isCurrentMember;
	@ApiModelProperty(value = "开始时间")
	private String startTime;
	@ApiModelProperty(value = "结束时间")
	private String endTime;
	@ApiModelProperty(value = "车辆信息（json格式存放）")
	private String carInfo;
	@ApiModelProperty(value = "车位关联单元ID ",example="1")
	private Integer parentPropertyId;
	@ApiModelProperty(value = "计费结束日期")
	private String endDate;
	@ApiModelProperty(value = "设置关联银行账号为空 1设置为空;其他值不作设置",example="1")
	private Integer setPaymentAccountIsNull;
	@ApiModelProperty(value = "购方名称")
	protected String buyersName;
	@ApiModelProperty(value = "购方地址")
	protected String buyersAddress;
	@ApiModelProperty(value = "企业类型")
	protected String businessType;
	@ApiModelProperty(value = "购方税号")
	protected String paytaxNo;
	@ApiModelProperty(value = "购方银行账号")
	protected String buyersBankAccount;
	@ApiModelProperty(value = "购方邮箱，用于电子发票")
	protected String buyerEmail;
	@ApiModelProperty(value = "租用类型 0-长租；1-短租；",example="1")
	private Integer rentType;
	@ApiModelProperty(value = "合同信息")
	protected String contractInfo;
	@ApiModelProperty(value = "开始修改计费日期时间")
	private String startModifyTime;
	@ApiModelProperty(value = "结束修改计费日期时间")
	private String endModifyTime;
	@ApiModelProperty(value = "合同ID",example="1")
	private Integer contractId;
	@Column(columnDefinition = "varchar(64) comment '合同编号'")
	private String contractCode;
	@Column(columnDefinition = "varchar(128) comment '合同名称'")
	private String contractName;
	@Column(columnDefinition = "varchar(512) comment '租金说明'")
	private String rentInfo;
	@Column(columnDefinition = "varchar(2000) comment '合同简要说明'")
	private String contractBrief;
	@Column(columnDefinition = "varchar(512) comment '保证金说明'")
	private String depositInfo;
	@Column(columnDefinition = "varchar(32) comment '经营类别'")
	private String businessCategory;
	@ApiModelProperty(value = "租金付款方式：月付/季度付/半年付/年付'")
	private String rentPayType;
	@ApiModelProperty(value = "合同结束日期")
	private String endData;
	@ApiModelProperty(value = "开票说明")
	private String invoiceDescription;
    @ApiModelProperty(value = "文件ID", example = "1")
    private Integer assetId;
    @ApiModelProperty(value = "房屋状态：0:未收楼;1:已入住;2:装修中;3:出租;4:离退", example = "1")
    private Integer estateState;
}
