package com.foshan.form.community.response.deviceCorrelation;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.DeviceCorrelationForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取设备关联详情返回(GetDeviceCorrelationInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetDeviceCorrelationInfoRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "账号设备表对象")
	private DeviceCorrelationForm deviceCorrelation ; 

}
