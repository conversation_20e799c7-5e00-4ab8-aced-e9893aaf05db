package com.foshan.form.community.request;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="表调价历史(CommunityMeterPriceHistoryReq)")
public  class CommunityMeterPriceHistoryReq extends BasePageRequest {


	/**
	 * 
	 */
	private static final long serialVersionUID = -5790162508753621662L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityMeterPriceHistoryId;
	@ApiModelProperty(value = "调整单价")
    private String unitPrice;
	@ApiModelProperty(value = "调价时间")
    private String changeDate;
    @ApiModelProperty(value = "操作类型 0：新增；1：修改；",example="1")
    private Integer operationType;
    @ApiModelProperty(value = "表属性",example="1")
    private Integer meterAttributesId;
}
