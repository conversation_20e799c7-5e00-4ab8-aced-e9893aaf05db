package com.foshan.form.community.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="公共区域请求参数(CommunityPublicAreaReq)")
public class CommunityPublicAreaReq extends CommunityEstateReq {
	/**
	 * 
	 */
	private static final long serialVersionUID = -2098430610030286344L;
	@ApiModelProperty(value = "公共区域ID", example = "1")
	private Integer publicAreaId;
	@ApiModelProperty(value = "是否可预约的 0否 1是", example = "1")
	private Integer isReserved;
}
