package com.foshan.form.community.response.exportExcel;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.poi.ss.formula.functions.T;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="综合一览返回对象(GetReceiptViewRes)")
@JsonInclude(Include.NON_NULL)
public class GetReceiptViewRes extends BasePageResponse {	
	/**
	 * 
	 */
	private static final long serialVersionUID = -2250950528441086971L;
	@ApiModelProperty(value = "表头数据")
	private List<Map<String,String>> keyList = new ArrayList<>();
	@ApiModelProperty(value = "表格预览数据")
	//List<Map<String, String>> viewList = new ArrayList<>();
	Set<Map<String, String>>  viewList = new LinkedHashSet<>();
	//private Map<String,String> viewList = new LinkedHashMap<>();
}
