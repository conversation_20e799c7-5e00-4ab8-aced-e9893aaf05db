package com.foshan.form.community.response.communityReceipt;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="收款单据(ModifyCommunityReceiptRes)")
@JsonInclude(Include.NON_NULL)
public  class ModifyCommunityReceiptRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 2814146880772779023L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityReceiptId;
  @ApiModelProperty(value = "经办人")
    private String agent;
    @ApiModelProperty(value = "金额")
    private String amount;
    @ApiModelProperty(value = "交易银行账号")
    private String bankAccount;
    @ApiModelProperty(value = "出纳")
    private String cashier;
    @ApiModelProperty(value = "备注")
    private String comment;
    @ApiModelProperty(value = "本位币金额")
    private String currencyAmount;
    @ApiModelProperty(value = "汇率")
    private String exchangeRate;
    @ApiModelProperty(value = "币种")
    private String moneytype;
    @ApiModelProperty(value = "旧表数据")
    private String oldData;
    @ApiModelProperty(value = "表ID")
    private String oldId;
    @ApiModelProperty(value = "姓名")
    private String payerName;
    @ApiModelProperty(value = "收款方式")
    private String paymentMethod;
    @ApiModelProperty(value = "收款类型 （0：应收款；1：退款； 2：预收；）",example="1")
    private Integer paymentType;
    @ApiModelProperty(value = "打印次数",example="1")
    private Integer printNum;
    @ApiModelProperty(value = "单据编号")
    private String receiptCode;
    @ApiModelProperty(value = "收款日期")
    private String receiptDate;
    @ApiModelProperty(value = "单据类别")
    private String receiptType;
    @ApiModelProperty(value = "主管")
    private String supervisor;
    @ApiModelProperty(value = "微信交易流水号")
    private String wxTradeNo;
    @ApiModelProperty(value = "微信商户订单号")
    private String wxorderNo;
  
}
