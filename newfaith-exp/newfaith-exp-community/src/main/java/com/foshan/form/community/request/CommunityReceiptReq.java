package com.foshan.form.community.request;

import java.util.ArrayList;
import java.util.List;


import com.foshan.form.community.CommunityReceivablesForm;
import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;



@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="收款单据(CommunityReceiptReq)")
public  class CommunityReceiptReq extends BasePageRequest {


	/**
	 * 
	 */
	private static final long serialVersionUID = 2807627846069240604L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityReceiptId;
  @ApiModelProperty(value = "经办人")
    private String agent;
    @ApiModelProperty(value = "金额")
    private String amount;
    @ApiModelProperty(value = "交易银行账号")
    private String bankAccount;
    @ApiModelProperty(value = "出纳")
    private String cashier;
    @ApiModelProperty(value = "备注")
    private String comment;
    @ApiModelProperty(value = "本位币金额")
    private String currencyAmount;
    @ApiModelProperty(value = "汇率")
    private String exchangeRate;
    @ApiModelProperty(value = "币种")
    private String moneytype;
    @ApiModelProperty(value = "旧表数据")
    private String oldData;
    @ApiModelProperty(value = "表ID")
    private String oldId;
    @ApiModelProperty(value = "姓名")
    private String payerName;
    @ApiModelProperty(value = "收款方式:1、线上支付：微信：WX_PUBLIC--公众号支付;WX_NAVITE_QR--微信二维码支付;WX_MOBILE_WEBSITE--手机网站支付;WX_MINIPROGRAM--微信小程序;WX_PAYCODE--微信付款码支付;AL_NAVITE_QR--支付宝二维码支付;AL_PAYCODE--支付宝付款码支付.2、线下（标试必须是以OFFLINE_开头）：OFFLINE_POS--线下pos机;OFFLINE_CASH--线下现金;OFFLINE_TRANSFER--线下转账;银行代收--OFFLINE_BANKAGENT")
    private String paymentMethod;
    @ApiModelProperty(value = "收款类型 （0：应收款；1：退款； 2：预收；）",example="1")
    private Integer paymentType;
    @ApiModelProperty(value = "打印次数",example="1")
    private Integer printNum;
    @ApiModelProperty(value = "单据编号")
    private String receiptCode;
    @ApiModelProperty(value = "收款日期")
    private String receiptDate;
//    @ApiModelProperty(value = "单据类别")
//    private String receiptType;
    @ApiModelProperty(value = "单据编号前缀，传对应的中文名称，后转成对应的前缀：系统收据：XTSJ_，其它收据：QTSJ_，银行划账：YHSK_，微信支付：WXPAY_，支付宝支付：ALPAY_，默认为：XTSJ_，")
    private String receiptCodePrefix;
    @ApiModelProperty(value = "主管")
    private String supervisor;
    @ApiModelProperty(value = "微信交易流水号")
    private String wxTradeNo;
    @ApiModelProperty(value = "微信商户订单号")
    private String wxorderNo;
	@ApiModelProperty(value = "单元地址（小区名称+栋号+单元号层数+房号）例如：御江南十六区5座2403")
	private String estateAddress;
	@ApiModelProperty(value = "单元ID",example="1")
	private Integer propertyId;
    @ApiModelProperty(value = "开始时间")
    private String startTime;
    @ApiModelProperty(value = "结束时间")
    private String endTime;
	@ApiModelProperty(value = "是否已开发票 0:否；1：是；",example="1")
	private Integer haveInvoice;
	@ApiModelProperty(value = "费用类别 0:周期性收费；1：一次性收费；",example="1")
	private Integer feeType;
	@ApiModelProperty(value = "收费项目ID",example="1")
	private Integer payItemId;
	@ApiModelProperty(value = "状态 0--无效数据 1--有效数据",example="1")
	private Integer state;
	@ApiModelProperty(value = "重新加载数据 0--否； 1--是；",example="1")
	private Integer reloadData;
    @ApiModelProperty(value = "单元编号")
    private String unitCode;
    @ApiModelProperty(value = "小区ID；")
    private String districtIdList;
    @ApiModelProperty(value = "楼栋ID；")
    private String buildingIdList;
    @ApiModelProperty(value = "收款方式:1、线上支付：微信：WX_PUBLIC--公众号支付;WX_NAVITE_QR--微信二维码支付;WX_MOBILE_WEBSITE--手机网站支付;WX_MINIPROGRAM--微信小程序;WX_PAYCODE--微信付款码支付;AL_NAVITE_QR--支付宝二维码支付;AL_PAYCODE--支付宝付款码支付.2、线下（标试必须是以OFFLINE_开头）：OFFLINE_POS--线下pos机;OFFLINE_CASH--线下现金;OFFLINE_TRANSFER--线下转账;银行代收--OFFLINE_BANKAGENT")
    private String paymentMethodList;
	@ApiModelProperty(value = "费用类别 1:管理费；2：车位费；3：分摊费；4：商管部；5：违约金；6：其他；7：押金类；8:有偿服务费；9:出租租金", example = "1")
	private String chargeCategoryList;
	@ApiModelProperty(value = "房产类型,支持传多个")
	private String estateTypeList;
	@ApiModelProperty(value = "应收款")
	private List<CommunityReceivablesForm> receivablesList = new ArrayList<CommunityReceivablesForm>();
}
