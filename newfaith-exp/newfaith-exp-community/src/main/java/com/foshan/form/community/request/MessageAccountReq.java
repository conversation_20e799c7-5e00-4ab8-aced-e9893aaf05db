package com.foshan.form.community.request;

import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="消息账号表(MessageAccountReq)")
public  class MessageAccountReq extends BasePageRequest {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer messageAccountId;
  @ApiModelProperty(value = "账号名称")
    private String accountName;
    @ApiModelProperty(value = "密要")
    private String secretKey;
  
}
