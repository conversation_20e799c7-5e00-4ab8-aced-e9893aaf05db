package com.foshan.form.community;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="小区对象(CommunityDistrict)")
@JsonInclude(Include.NON_NULL)
public  class CommunityDistrict implements IForm {
		

	/**
	 * 小区对象
	 */
	private static final long serialVersionUID = -1175224248257566015L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityDistrictId;
    @ApiModelProperty(value = "地址")
    private String address;
    @ApiModelProperty(value = "占地面积")
    private String areaSize;
    @ApiModelProperty(value = "户数")
    private String housingNumber;
    @ApiModelProperty(value = "小区名称")
    private String districtName;
    @ApiModelProperty(value = "土地年限")
    private String period;
    @ApiModelProperty(value = "容积率")
    private String ratio;
    @ApiModelProperty(value = "幢数",example="1")
    private Integer buildingNumber;
    @ApiModelProperty(value = "备注")
    private String comment;
    @ApiModelProperty(value = "竣工日期")
    private String completionDate;
    @ApiModelProperty(value = "小区编号")
    private String districtCode;
    @ApiModelProperty(value = "移交日期")
    private String handoverDate;
    @ApiModelProperty(value = "人口数")
    private String population;
    @ApiModelProperty(value = "拿地时间")
    private String takeTime;
    @ApiModelProperty(value = "社区")
    private CommunityForm communityForm;
    @ApiModelProperty(value = "创建时间")
	protected String createTime;
    @ApiModelProperty(value = "修改时间")
	protected String lastModifyTime;
    @ApiModelProperty(value = "数据状态 0--无效数据  1--有效数据",example="1")
	protected Integer state;
	@ApiModelProperty(value = "小区排序号",example="1")
	private Integer districtOrder;

    
    public CommunityDistrict (Integer communityDistrictId,String districtName) {
    	this.communityDistrictId = communityDistrictId;
    	this.districtName = districtName;
    }
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
