package com.foshan.form.community.response.communityReceivablesChanges;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityReceivablesChangesForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="应收款变更（减加）(GetCommunityReceivablesChangesListReq)")
public class GetCommunityReceivablesChangesListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 5746882844426969376L;
	@ApiModelProperty(value = "应收款变更（减加）列表")
	private List<CommunityReceivablesChangesForm> communityReceivablesChangesList = new ArrayList<CommunityReceivablesChangesForm>(); 

}
