package com.foshan.form.community.response.communityStatistics;

import java.util.LinkedList;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取楼盘入住率返回对象(GetBuildingOccupancyRes)")
@JsonInclude(Include.NON_NULL)
public class GetBuildingOccupancyRes<T extends IForm> extends BasePageResponse {

	/**
		 * 
		 */
	private static final long serialVersionUID = -4654257250162022723L;
	private String fileName;
	private String[] titles;
	private LinkedList<T> dataList = new LinkedList<>();
}
