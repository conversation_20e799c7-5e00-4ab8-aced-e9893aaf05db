package com.foshan.form.community.response.communityReceivables;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.RentMeterAllocationForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取租赁单元试运算列表(GetRentTrialMeterOperationListRes)")
public class GetRentTrialMeterOperationListRes extends BasePageResponse {/**
	 * 
	 */
	private static final long serialVersionUID = 8150229694760079108L;
	@ApiModelProperty(value = "租赁单元结果列表")
	private List<RentMeterAllocationForm> rentMeterAllocationList = new ArrayList<>(); 
}
