package com.foshan.form.community.response.communityPropertyService;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityPropertyServiceForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取报修服务列表(GetCommunityPropertyServiceListReq)")
public class GetCommunityPropertyServiceListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 8373931450496972218L;
	@ApiModelProperty(value = "报修服务列表")
	private List<CommunityPropertyServiceForm> communityPropertyServiceList = new ArrayList<CommunityPropertyServiceForm>(); 

}
