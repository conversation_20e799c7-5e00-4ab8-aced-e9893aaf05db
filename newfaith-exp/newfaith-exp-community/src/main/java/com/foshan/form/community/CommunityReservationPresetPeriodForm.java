package com.foshan.form.community;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="预设日间段对象(CommunityReservationPresetPeriodForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityReservationPresetPeriodForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = 6379231396542710407L;
	@ApiModelProperty(value = "预设日间段ID", example = "1")
	private Integer presetPeriodId;
	@ApiModelProperty(value = "可预约人数", example = "1")
	protected Integer maxNum;
	@ApiModelProperty(value = "排序值", example = "1")
	protected Integer orders;
	@ApiModelProperty(value = "开始时间")
	private String startTime;
	@ApiModelProperty(value = "结束时间")
	private String endTime;
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
