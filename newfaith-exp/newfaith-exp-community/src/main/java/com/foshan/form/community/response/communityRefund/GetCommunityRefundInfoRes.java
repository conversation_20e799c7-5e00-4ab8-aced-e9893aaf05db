package com.foshan.form.community.response.communityRefund;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityRefundForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="退款记录(GetCommunityRefundInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityRefundInfoRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "退款记录对象")
	private CommunityRefundForm communityRefundForm ; 

}
