package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="活动对象(CommunityReservationActivitiesForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityReservationActivitiesForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = 3985018705495499964L;
	
	@ApiModelProperty(value = "活动ID", example = "1")
	private Integer activitiesId;
	@ApiModelProperty(value = "预约活动名称")
	private String activitiesName;
	@ApiModelProperty(value = "介绍")
	private String centent;
	@ApiModelProperty(value = "协议")
	private String agreement;
	@ApiModelProperty(value = "开始时间")
	protected String startTime;
	@ApiModelProperty(value = "结束时间")
	protected String endTime;
	@ApiModelProperty(value = "需要确认协议 0否1是", example = "1")
	private Integer needConfirmAgreement;
	@ApiModelProperty(value = "创建时间")
	private String createTime;
	@ApiModelProperty(value = "修改时间")
	private String lastModifyTime;
	@ApiModelProperty(value = "状态 0--无效数据 1--有效数据", example = "1")
	private Integer state;
	@ApiModelProperty(value = "排序值", example = "1")
	private Integer orders;
	@ApiModelProperty(value = "使用 0否 1是", example = "1")
	private Integer employ;
	@ApiModelProperty(value = "策略")
	private CommunityReservationStrategyForm strategyForm;
	@ApiModelProperty(value = "会员")
	private CommunityMemberForm member;
	@ApiModelProperty(value = "公共区域")
	private CommunityEstateForm publicArea;
	
	
	public CommunityReservationActivitiesForm(Integer activitiesId,String activitiesName,String centent,String agreement,
			String startTime,String endTime,Integer needConfirmAgreement,String createTime,String lastModifyTime,Integer state,Integer orders) {
		this.activitiesId = activitiesId;
		this.activitiesName = activitiesName;
		this.centent = centent;
		this.agreement = agreement;
		this.startTime = startTime;
		this.endTime = endTime;
		this.needConfirmAgreement = needConfirmAgreement;
		this.createTime = createTime;
		this.lastModifyTime = lastModifyTime;
		this.state = state;
		this.orders = orders;
	}
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
