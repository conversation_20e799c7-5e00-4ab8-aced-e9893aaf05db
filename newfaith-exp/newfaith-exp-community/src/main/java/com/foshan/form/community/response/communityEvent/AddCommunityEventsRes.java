package com.foshan.form.community.response.communityEvent;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="新增事件返回对象(AddCommunityEventsRes)")
@JsonInclude(Include.NON_NULL)
public class AddCommunityEventsRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5632513657694778692L;
	@ApiModelProperty(value = "事件Id",example="1")
	private Integer eventId;

}
