package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = " 收费项目设定(CommunityPayItemsPriceForm)")
@JsonInclude(Include.NON_NULL)
public class CommunityPayItemsPriceForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6740419271024202680L;
	@ApiModelProperty(value = "ID", example = "1")
	private Integer payItemsPriceId;
	@ApiModelProperty(value = "备注")
	private String comment;
	@ApiModelProperty(value = "项目名称")
	private String itemsName;
	@ApiModelProperty(value = "'费用类别 1:管理费；2：车位费；3：分摊费；4：商管部；5：违约金；6：其他；", example = "1")
	private Integer chargeCategory;
	@ApiModelProperty(value = "价格单位（如：元/平方米）")
	private String priceUnit;
	@ApiModelProperty(value = "管理费计算类型：1、按收费面积计算--管理费；2、按附加面积计算--花园管理费;3、按个数计算--车位租用")
	private Integer feeCalType;
	@ApiModelProperty(value = "交款日")
	private Integer payDate;
	@ApiModelProperty(value = "是否产生违约金:0--不产生违约金 1--产生违约金")
	private Integer isBreach;
	@ApiModelProperty(value = "结束时间")
	private String endTime;
	@ApiModelProperty(value = "单价")
	private String price;
	@ApiModelProperty(value = "开始时间")
	private String startTime;
	@ApiModelProperty(value = "创建时间")
	private String createTime;
	@ApiModelProperty(value = "修改时间")
	private String lastModifyTime;
    @ApiModelProperty(value = "周期 0-年；1：月；2：日；3：小时；")
    private Integer period;
    @ApiModelProperty(value = "收费项目id")
    private Integer payItemId;
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
