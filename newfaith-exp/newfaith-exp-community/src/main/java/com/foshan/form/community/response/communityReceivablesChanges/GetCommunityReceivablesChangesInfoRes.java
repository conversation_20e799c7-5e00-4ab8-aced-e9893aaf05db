package com.foshan.form.community.response.communityReceivablesChanges;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityReceivablesChangesForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="应收款变更（减加）(GetCommunityReceivablesChangesInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityReceivablesChangesInfoRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 1478321670401304290L;
	@ApiModelProperty(value = "应收款变更（减加）对象")
	private CommunityReceivablesChangesForm communityReceivablesChangesForm ; 

}
