package com.foshan.form.community.response.communityBuilder;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityBuilderForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="开发商(GetCommunityBuilderListReq)")
public class GetCommunityBuilderListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -6754959288935878432L;
	@ApiModelProperty(value = "开发商列表")
	private List<CommunityBuilderForm> communityBuilderList = new ArrayList<CommunityBuilderForm>(); 

}
