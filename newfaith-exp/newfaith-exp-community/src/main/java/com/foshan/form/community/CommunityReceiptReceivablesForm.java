package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = " 应收款-收款单据(CommunityReceiptReceivablesForm)")
@JsonInclude(Include.NON_NULL)
public class CommunityReceiptReceivablesForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4149129838002999234L;
	@ApiModelProperty(value = "ID", example = "1")
	private Integer communityReceiptReceivablesId;
	@ApiModelProperty(value = "本次收款")
	private String currentAmount;
	@ApiModelProperty(value = "已收金额")
	private String receivedAmount;
	@ApiModelProperty(value = "应收款")
	private CommunityReceivablesForm receivablesForm;
	@ApiModelProperty(value = "收款单据")
	private CommunityReceiptForm receiptForm;
	@ApiModelProperty(value = "0未开，1已开，2正在开", example = "1")
	private Integer invoiceState;
	//payItemsName/receivableDate主要用手工于排序
	@ApiModelProperty(value = "收费项目")
	private Integer payItemsId;
	@ApiModelProperty(value = "应收日期")
	private long receivableDate;
	

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
