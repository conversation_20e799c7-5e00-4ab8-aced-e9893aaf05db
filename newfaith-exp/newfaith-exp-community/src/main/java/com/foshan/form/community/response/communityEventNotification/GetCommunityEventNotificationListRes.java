package com.foshan.form.community.response.communityEventNotification;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityEventNotificationForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取事件通知(GetCommunityEventNotificationListRes)")
public class GetCommunityEventNotificationListRes extends BasePageResponse {

	private static final long serialVersionUID = 3066597026879782862L;
	@ApiModelProperty(value = "事件通知列表")
	private List<CommunityEventNotificationForm> eventNotificationList = new ArrayList<CommunityEventNotificationForm>(); 

}
