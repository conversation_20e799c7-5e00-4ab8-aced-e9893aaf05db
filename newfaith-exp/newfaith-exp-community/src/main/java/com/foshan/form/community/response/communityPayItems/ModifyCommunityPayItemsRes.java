package com.foshan.form.community.response.communityPayItems;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="收费项目设定(ModifyCommunityPayItemsRes)")
@JsonInclude(Include.NON_NULL)
public  class ModifyCommunityPayItemsRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 7560839945231216794L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityPayItemsId;
  @ApiModelProperty(value = "费用类别 1:管理费；2：车位费；3：分摊费；4：商管部；5：违约金；6：其他；7：押金类；8:有偿服务费；9:出租租金",example="1")
    private Integer chargeCategory;
    @ApiModelProperty(value = "备注")
    private String comment;
    @ApiModelProperty(value = "结束时间")
    private String endTime;
    @ApiModelProperty(value = "价格单位（如：元/平方米）")
    private String priceUnit;
    @ApiModelProperty(value = "是否生成应收款")
    private Boolean isReceivables;
    @ApiModelProperty(value = "项目名称")
    private String itemsName;
    @ApiModelProperty(value = "旧表数据")
    private String oldData;
    @ApiModelProperty(value = "表ID")
    private String oldId;
    @ApiModelProperty(value = "交款日")
    private String payDate;
    @ApiModelProperty(value = "单价")
    private String price;
    @ApiModelProperty(value = "开始时间")
    private String startTime;
	@ApiModelProperty(value = "是否产生违约金:0--不产生违约金 1--产生违约金")
	private Integer isBreach;
	@ApiModelProperty(value = "违约金名称")
	private String breachName;
    @ApiModelProperty(value = "违约金系数")
    private String breachRatio;
  
}
