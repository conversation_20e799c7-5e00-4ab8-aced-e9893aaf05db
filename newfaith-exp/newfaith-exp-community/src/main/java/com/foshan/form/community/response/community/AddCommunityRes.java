package com.foshan.form.community.response.community;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="社区(CommunityRes)")
@JsonInclude(Include.NON_NULL)
public  class AddCommunityRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 3799175463915365462L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityId;
    @ApiModelProperty(value = "社区地址",example="1")
    private String address;
    @ApiModelProperty(value = "管辖面积",example="1")
    private String areaSize;
    @ApiModelProperty(value = "社区名称",example="1")
    private String name;
    @ApiModelProperty(value = "",example="1")
    private Integer regionId;

}
