package com.foshan.form.community.request;

import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

import javax.persistence.Column;

import com.foshan.form.request.BasePageRequest;
import com.foshan.form.request.IResourcePermitRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="事件类型项目请求参数(CommunityEventCategoryItemsReq)")
public class CommunityEventCategoryReq extends BasePageRequest implements IResourcePermitRequest{

	/**
	 * 
	 */
	private static final long serialVersionUID = 7952225821167531418L;
	@ApiModelProperty(value = "事件类型项目Id",example="1")
	private Integer eventCategoryId;
	@ApiModelProperty(value = "1一级，2-二级",example="1")
	private Integer categoryLevel;
	@ApiModelProperty(value = "是否派单:0否1是",example="1")
	private Integer isDispatching;
	@ApiModelProperty(value = "事件类型项目名称")
	private String categoryName;
	@ApiModelProperty(value = "父ID",example="1")
	private Integer parentEventCategoryId;
	@ApiModelProperty(value = "数据状态",example="1")
	private Integer state;
	@ApiModelProperty(value = "图标（小程序端显示的图标）")
	private String icon;
	@ApiModelProperty(value = "排序",example="1")
	private Integer orders;
	@ApiModelProperty(value = "栏目查询深度，默认2层",example="1")
	private Integer depth;
	@ApiModelProperty(value = "类型，1:装修申请",example="1")
	private Integer categoryType;
	@ApiModelProperty(value = "是发送云之家:0否 1是",example="1")
	private Integer isSend;
	@ApiModelProperty(value = "默认参数（云之家）",example="1")
	private String defaultParameter;
	
	@Override
	public List<String> getResourcePermitTags(String url) {
		List<String> resourcePermitTags = new ArrayList<String>();
		if(url.equals("/community/getCommunityEventCategoryInfo")){
			resourcePermitTags.add("CommunityEventCategory:" + getEventCategoryId());
			return resourcePermitTags;
		}
		return resourcePermitTags;
	}

	@Override
	public String toString() {
		return new StringJoiner(", ", CommunityEventCategoryReq.class.getSimpleName() + "[", "]")
				.add("eventCategoryId=" + eventCategoryId)
				.add("categoryLevel=" + categoryLevel)
				.add("isDispatching=" + isDispatching)
				.add("categoryName='" + categoryName + "'")
				.add("parentEventCategoryId=" + parentEventCategoryId)
				.add("state=" + state)
				.toString();
	}
}
