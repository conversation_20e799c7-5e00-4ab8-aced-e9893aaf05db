package com.foshan.form.community.response.communityRole;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.PermissionForm;
import com.foshan.form.PermissionGroupForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取社区角色对象列表(GetCommunityRoleListRes)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityRoleInfotRes extends BasePageResponse {

	/**
	 * 社区角色详情
	 */
	private static final long serialVersionUID = 4372843897361031985L;
	
	@ApiModelProperty(value = "角色Id",example="1")
	private Integer roleId;
	@ApiModelProperty(value = "角色名称")
	private String roleName;
	@ApiModelProperty(value = "是否内置角色（1:内置角色，不能删除）")
	private boolean isBuiltIn;
	@ApiModelProperty(value = "名称,前端显示用")
	private String displayName;
	@ApiModelProperty(value = "角色权限列表")
	private List<PermissionForm> permissionList = new ArrayList<PermissionForm>();
	@ApiModelProperty(value = "分组角色权限列表")
	private List<PermissionGroupForm> permissionGroupList = new ArrayList<PermissionGroupForm>();
}
