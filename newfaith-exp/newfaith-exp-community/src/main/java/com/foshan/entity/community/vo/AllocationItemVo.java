package com.foshan.entity.community.vo;

import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.Data;

@JsonInclude(Include.NON_EMPTY)
@Data
public class AllocationItemVo {

	/**
	 * 公用单元分摊清单对象
	 */
	private BigDecimal allocationNum;
	private BigDecimal allocationAmount;
	private Integer allocationId;
	private String payItemsName;
	private Integer estateId;
	private String unitCode;
	private Integer meterId;
	private Integer allocationStartYear;
	private Integer allocationStartMonth;
	private Integer allocationEndYear;
	private Integer allocationEndMonth;
	private Integer allocationPeriod;
	private Integer templeteId;
	private Integer specialAllocationFlag;

	public AllocationItemVo() {
		super();
		// TODO Auto-generated constructor stub
	}

	public AllocationItemVo(BigDecimal allocationNum, BigDecimal allocationAmount, String payItemsName,
			Integer estateId, Integer allocationStartYear, Integer allocationStartMonth, Integer allocationEndYear,
			Integer allocationEndMonth, Integer allocationPeriod, Integer meterId,Integer specialAllocationFlag) {
		super();
		this.allocationNum = allocationNum;
		this.allocationAmount = allocationAmount;
		this.payItemsName = payItemsName;
		this.estateId = estateId;
		this.allocationStartYear = allocationStartYear;
		this.allocationStartMonth = allocationStartMonth;
		this.allocationEndYear = allocationEndYear;
		this.allocationEndMonth = allocationEndMonth;
		this.allocationPeriod = allocationPeriod;
		this.meterId = meterId;
		this.specialAllocationFlag = specialAllocationFlag;
	}

}
