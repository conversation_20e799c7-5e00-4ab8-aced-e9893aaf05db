package com.foshan.entity.community;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_reservation_date")
@org.hibernate.annotations.Table(appliesTo = "t_community_reservation_date",comment="预约日期")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityReservationDateEntity implements IEntityBean {

	/**
	 * 预约日期
	 */
	private static final long serialVersionUID = 9141194091448145398L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "date comment '日期'")
	private Date reservationDate;
	@Column(columnDefinition = "int(2) comment '是否开始预约 0否；1是'")
	protected Integer isStart;
	@Column(columnDefinition = "int(1) default 1 comment '状态 0--无效数据  1--有效数据'")
	protected Integer state;
	
	@OneToMany(targetEntity = CommunityReservatPeriodEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "dateId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityReservatPeriodEntity> periodList = new ArrayList<CommunityReservatPeriodEntity>();
	
	@ManyToOne(targetEntity = CommunityReservationActivitiesEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "activitiesId", referencedColumnName = "id", nullable = true)
	private CommunityReservationActivitiesEntity activities;

}
