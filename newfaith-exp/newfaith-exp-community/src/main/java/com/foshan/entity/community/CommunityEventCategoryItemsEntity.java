package com.foshan.entity.community;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_event_category_items")
@org.hibernate.annotations.Table(appliesTo = "t_community_event_category_items",comment="事件类型项目") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityEventCategoryItemsEntity implements IEntityBean {

	private static final long serialVersionUID = 9166875074684494307L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '项目名称'")
	private String itemName;
	@Column(columnDefinition = "varchar(64) comment '项目名称对应key'")
	private String itemkey;
	@Column(columnDefinition = "int(10) comment '排序'")
	private Integer orders;
	@Column(columnDefinition = "int(2) comment '数据类型 0：字符串；1：数字；2：时间；3：文件;4：单选；5：多选；6：日期；7：日期范围；8:时间范围；'")
	private Integer dataType;
	@Column(columnDefinition = "varchar(512) comment '项目名称对应key'")
	private String options;
	@Column(columnDefinition = "int(2) comment '是否必填，0：否；1是；'")
	private Integer isRequiredng;
	@Column(columnDefinition = "varchar(64) comment '项目标签，区别于其他事件项目，用于装修项目或其他特别项目'")
	private String itemTag;
	@Column(columnDefinition = "varchar(64) comment '映射字段（云之家）'")
	private String reflectionField;
	@Column(columnDefinition = "int(2) default 0  comment '是否级联，0：否；1：是；'")
	private Integer isCascade;
	@Column(columnDefinition = "varchar(64) comment '级联值'")
	private String cascadeValue;
	
	@ManyToOne(targetEntity = CommunityEventCategoryItemsEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentId", referencedColumnName = "id", nullable = true)
	private CommunityEventCategoryItemsEntity parentEventCategoryItems;
	
	@OneToMany(targetEntity = CommunityEventCategoryItemsEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityEventCategoryItemsEntity> subEventCategoryItemsList = new ArrayList<CommunityEventCategoryItemsEntity>();
	
	
	@ManyToOne(targetEntity = CommunityEventCategoryEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "eventCategoryId", referencedColumnName = "id", nullable = true)
	private CommunityEventCategoryEntity eventCategory;
	
	@ManyToMany(targetEntity = CommunityDecorationItemsEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_decoration_items_event_items", joinColumns = @JoinColumn(name = "eventItemsId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "decorationItemsId", referencedColumnName = "id"))
	@JsonIgnore
	private List<CommunityDecorationItemsEntity> decorationItemsList = new ArrayList<CommunityDecorationItemsEntity>();
}
