package com.foshan.entity.community.vo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@JsonInclude(Include.NON_EMPTY)
@Data
@Slf4j
public class MeterRecordVo {
	private Integer meterId;
	private String meterCode;
	private String meterName;
	private Integer allocationMethod;
	private String payItemsName;
	private BigDecimal additionalAmount;
	private String additionalInstructions;
	private Date expirationDates;
	private Integer meterAttributesId;
	private Integer formulaId;
	private Integer allocationPeriod;
	private Integer templeteId;
	private BigDecimal rate;
	private BigDecimal unitPrice;
	private String dynamicParameter;
	private String formulaName;
	private String templeteInfo;
	private String templeteName;
	private BigDecimal lastNum;
	private BigDecimal recordNum;
	private Integer allocationStartYear;
	private Integer allocationStartMonth;
	private Integer allocationEndYear;
	private Integer allocationEndMonth;
	private Integer estateCount;
	private BigDecimal totalChargeArea;
	private BigDecimal totalAddArea;
	private BigDecimal allocationNum;
	private BigDecimal allocationAmount;
	private Integer allocationId;


	public static MeterRecordVo getMeterRecordVo(Object[] o) {
		MeterRecordVo vo = null;
		if (null != o) {
			try {
				
				vo = new MeterRecordVo();
				vo.setMeterId(Integer.parseInt(o[0].toString()));
				vo.setMeterCode(null!=o[1]?o[1].toString():null);
				vo.setMeterName(null!=o[2]?o[2].toString():null);
				vo.setAllocationMethod(Integer.parseInt(o[3].toString()));
				vo.setPayItemsName(null!=o[4]?o[4].toString():null);
				vo.setAdditionalAmount(null!=o[5]?new BigDecimal(o[5].toString()).setScale(2, RoundingMode.HALF_UP):new BigDecimal("0"));
				vo.setAdditionalInstructions(null!=o[6]?o[6].toString():null);
				vo.setExpirationDates(null!=o[7]?Date.from(LocalDate.parse(o[7].toString().subSequence(0, 10))
						.atStartOfDay(ZoneId.systemDefault()).toInstant()):null);
				vo.setMeterAttributesId(Integer.parseInt(o[8].toString()));
				vo.setFormulaId(null!=o[9]?Integer.parseInt(o[9].toString()):null);
				vo.setAllocationPeriod(null!=o[10]?Integer.parseInt(o[10].toString()):null);
				vo.setTempleteId(null!=o[11]?Integer.parseInt(o[11].toString()):null);
				vo.setRate(null!=o[12]?new BigDecimal(o[12].toString()):new BigDecimal("1"));
				vo.setUnitPrice(null!=o[13]?new BigDecimal(o[13].toString()).setScale(8, RoundingMode.HALF_UP):new BigDecimal("0"));
				vo.setDynamicParameter(null != o[14] ? o[14].toString() : null);
				vo.setFormulaName(null != o[15] ? o[15].toString() : null);
				vo.setTempleteInfo(null != o[16] ? o[16].toString() : null);
				vo.setTempleteName(null != o[17] ? o[17].toString() : null);
				vo.setLastNum(new BigDecimal(o[18].toString()).setScale(2, RoundingMode.HALF_UP));
				vo.setRecordNum(new BigDecimal(o[19].toString()).setScale(2, RoundingMode.HALF_UP));
				vo.setAllocationStartYear(null!=o[20] ? Integer.parseInt(o[20].toString()): null);
				vo.setAllocationStartMonth(null!=o[21]? Integer.parseInt(o[21].toString()):null);
				vo.setAllocationEndYear(Integer.parseInt(o[22].toString()));
				vo.setAllocationEndMonth(Integer.parseInt(o[23].toString()));
				vo.setEstateCount(Integer.parseInt(o[24].toString()));
				vo.setTotalChargeArea(new BigDecimal(o[25].toString()).setScale(4, RoundingMode.HALF_UP));
				vo.setTotalAddArea(new BigDecimal(o[26].toString()).setScale(4, RoundingMode.HALF_UP));
				vo.setAllocationNum(new BigDecimal(o[27].toString()).setScale(2, RoundingMode.HALF_UP));
				vo.setAllocationAmount(new BigDecimal(o[28].toString()).setScale(4, RoundingMode.HALF_UP));
				vo.setAllocationId(null != o[29] ? Integer.parseInt(o[29].toString()) : null);

				

			} catch (Exception ex) {
				ex.printStackTrace();
				log.error(ex.getMessage() + ":MeterRecordVo(" + o[0].toString() + ")数据转换失败");
				vo = null;
			}
		}
		return vo;
	}


	@Override
	public String toString() {
		return "MeterRecordVo [meterId=" + meterId + ", dynamicParameter=" + dynamicParameter + ", allocationId="
				+ allocationId + "]";
	}


	

	
}
