package com.foshan.entity.community;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_reservation_record")
@org.hibernate.annotations.Table(appliesTo = "t_community_reservation_record",comment="预约记录")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityReservationRecordEntity extends Community {
	/**
	 * 预约记录
	 */
	private static final long serialVersionUID = 6281430928588039897L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(1024) comment '说明'")
	private String centent;
	@Column(columnDefinition = "int(10) comment '预约人数'")
	protected Integer reservationNum;	
	@Column(columnDefinition = "int(2) comment '预约状态 0预约成功 1已核销 2取消 3过期'")
	protected Integer reservationState;
	
	@ManyToOne(targetEntity = CommunityMemberEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
	private CommunityMemberEntity member;
	
	@ManyToOne(targetEntity = CommunityReservatPeriodEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "periodId", referencedColumnName = "id", nullable = true)
	private CommunityReservatPeriodEntity reservatPeriod;
	
	@ManyToOne(targetEntity = CommunityReservationActivitiesEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "activitiesId", referencedColumnName = "id", nullable = true)
	private CommunityReservationActivitiesEntity activities;
}
