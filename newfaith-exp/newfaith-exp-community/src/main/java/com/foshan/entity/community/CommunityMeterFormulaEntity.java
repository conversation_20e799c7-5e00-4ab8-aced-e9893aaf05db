package com.foshan.entity.community;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_meter_formula" ,uniqueConstraints = {
		@UniqueConstraint(columnNames = {"formulaName"}) })
@org.hibernate.annotations.Table(appliesTo = "t_community_meter_formula", comment = "自定义分摊公式")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityMeterFormulaEntity implements IEntityBean {
	/**
	 *
	 */
	private static final long serialVersionUID = -7129448843939024743L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '公式名称'")
	private String formulaName;
	@Column(columnDefinition = "datetime comment '上次调整公式时间'")
	private Date lastDate;
	@Column(columnDefinition = "varchar(100) comment '动态参数'")
	private String dynamicParameter;
	@Column(columnDefinition = "varchar(512) comment '备注'")
	private String comment;
	@Column(columnDefinition = "varchar(512) comment '操作日志'")
	private String operateLog;

	@Column(columnDefinition = "varchar(64) comment '表ID'")
	private String oldId;
	@Column(columnDefinition = "varchar(512) comment '旧表数据'")
	private String oldData;

	@OneToMany(targetEntity = CommunityMeterEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "formulaId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityMeterEntity> meterList = new ArrayList<CommunityMeterEntity>();

	@ManyToOne(targetEntity = CommunityFormulaTempleteEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "templeteId", referencedColumnName = "id", nullable = true)
	private CommunityFormulaTempleteEntity templete;

}
