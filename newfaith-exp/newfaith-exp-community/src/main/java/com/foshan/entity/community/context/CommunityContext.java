package com.foshan.entity.community.context;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

import com.foshan.entity.context.EntityContext;

public class CommunityContext extends EntityContext {

	/*
	 * 楼栋功能类型
	 */
	public final static Integer COMMUNITY_BUILDING_TYPE_HOURSE=0; //住宅
	public final static Integer COMMUNITY_BUILDING_TYPE_VILLA=1; //别墅
	public final static Integer COMMUNITY_BUILDING_TYPE_SHOP=2; //商场
	public final static Integer COMMUNITY_BUILDING_TYPE_PARK=3; //停车场
	public final static Integer COMMUNITY_BUILDING_TYPE_PUBLIC=4; //公共区域
	
	/*
	 * 费用项目类别
	 */
	public final static Integer COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_MANAGEMENT=1;//管理费
	public final static Integer COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_CAR=2;//车位费
	public final static Integer COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_ALLOCATION=3;//分摊费
	public final static Integer COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_SHOP=4;//商管部
	public final static Integer COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_BREAK=5;//违约金
	public final static Integer COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_OTHER=6;//其它类
	public final static Integer COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_DEPOSIT=7;//押金类
	public final static Integer COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_COST=8;//有偿服务类

	
	/*
	 * 管理费计算类型
	 */
	public final static Integer COMMUNITY_PAY_ITEMS_CAL_TYPE_CHARGE=1;//收费面积
	public final static Integer COMMUNITY_PAY_ITEMS_CAL_TYPE_ADD=2;//附加面积
	public final static Integer COMMUNITY_PAY_ITEMS_CAL_TYPE_ONCE=3;//按个数计算
	public final static Integer COMMUNITY_PAY_ITEMS_CAL_TYPE_USED=4;//按实际使用情况
	
	/*
	 * 管理费计算类型
	 */
	public final static Integer COMMUNITY_PAY_ITEMS_PRICE_PERIOD_TYPE_YEAR=0;//计费周期为年
	public final static Integer COMMUNITY_PAY_ITEMS_PRICE_PERIOD_TYPE_MONTH=1;//计费周期为月
	public final static Integer COMMUNITY_PAY_ITEMS_PRICE_PERIOD_TYPE_DAY=2;//计费周期为日
	public final static Integer COMMUNITY_PAY_ITEMS_PRICE_PERIOD_TYPE_HOUR=3;//计费周期为小时
	
	
	/*
	 * 公摊费类型
	 */
	public final static Integer COMMUNITY_ALLOCATION_CAL_TYPE_AREA=1;//按收费面积计算分摊费用
	public final static Integer COMMUNITY_ALLOCATION_CAL_TYPE_CUSTOM=2;//按自定义公式计算分摊费用
	
	/*
	 * 房间(业主)状态
	 */
	public final static Integer COMMUNITY_HOUSE_HOLD_STATE_BEFORE=0;//未收楼
	public final static Integer COMMUNITY_HOUSE_HOLD_STATE_IN=1;//已入住
	public final static Integer COMMUNITY_HOUSE_HOLD_STATE_DECORATE=2;//装修
	public final static Integer COMMUNITY_HOUSE_HOLD_STATE_RENT=3;//出租
	public final static Integer COMMUNITY_HOUSE_HOLD_STATE_OUT=4;//离退
	public final static Integer COMMUNITY_HOUSE_HOLD_STATE_OWN=5;//自主(已收楼 未入住)
	
	
	/*
	 * 业主
	 */
	public final static Integer IS_CURRENT_OWNER=1;//当前业主
	public final static Integer IS_NOT_CURRENT_OWNER=0;//不是当前业主
	
	/*
	 * 会员类型
	 */
	public final static Integer MEMBER_TYPE_IS_OWNER=0;//业主
	public final static Integer MEMBER_TYPE_IS_RESIDENT=1;//住户
	public final static Integer MEMBER_TYPE_IS_TENANT=2;//租户
	
	
	/*
	 * 金蝶对账文件导入状态
	 */
	public final static Integer KINGDEE_IMPORT_STATE_CREATE=0;//初始生成
	public final static Integer KINGDEE_IMPORT_STATE_SUCCESS=1;//成功
	public final static Integer KINGDEE_IMPORT_STATE_FAIL=2;//失败
	
	/*
	 * 公寓出租类型
	 */
	public final static Integer ESTATE_RENT_TYPE_LONG=0;//长租
	public final static Integer ESTATE_RENT_TYPE_SHORT=1;//短租
	
	//割接日期
//	public final static Date CUTOVERDATE =  Date.from(LocalDate.of(2023, 1, 28).atStartOfDay(ZoneId.systemDefault()).toInstant());
//	
}
