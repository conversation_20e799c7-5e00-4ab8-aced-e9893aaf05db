package com.foshan.entity.community.vo;



import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
/**
 * 
 * <AUTHOR>
 * 十六区优惠取消后，补收差额管理费数据
 */
@JsonInclude(Include.NON_EMPTY)
@Data
@Getter
@Setter
public class CancelBenefitAmountChangeDetailItem implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = -792358240158116139L;
	/**
	 * 实收或加减记录ID
	 */
	private Integer sourceId;
	/*
	 * 动账时间
	 */
	private Date changeDate;
	/*
	 * 动账的金额
	 */
	private BigDecimal changeAmount;
	/*
	 * 动账类型:0--收款，1--加减
	 */
	private Integer changeType = 0;
	

	
	@Override
	public int compareTo(Object o) {
		if (this == o)
			return 0;
		if (o == null)
			return -1;
		CancelBenefitAmountChangeDetailItem other = (CancelBenefitAmountChangeDetailItem) o;
		if(null == other.getChangeDate())
			return -1;
		int result = other.changeDate.compareTo(this.changeDate);
		if(result == 0) {
			return other.sourceId.compareTo(this.sourceId);
		}
		return result;
	}

	public CancelBenefitAmountChangeDetailItem() {
		super();
	}
	
	public CancelBenefitAmountChangeDetailItem(Integer sourceId, Date changeDate, BigDecimal changeAmount,
			Integer changeType) {
		super();
		this.sourceId = sourceId;
		this.changeDate = changeDate;
		this.changeAmount = changeAmount;
		this.changeType = changeType;
	}

	@Override
	public String toString() {
		return "CancelBenefitAmountChangeDetailItem [sourceId=" + sourceId + ", changeDate=" + changeDate
				+ ", changeAmount=" + changeAmount + ", changeType=" + changeType + "]";
	}

	

}
