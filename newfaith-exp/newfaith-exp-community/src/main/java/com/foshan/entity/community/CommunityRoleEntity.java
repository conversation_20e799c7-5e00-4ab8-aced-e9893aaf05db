package com.foshan.entity.community;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.RoleEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_role")
@org.hibernate.annotations.Table(appliesTo = "t_role",comment="社区平台角色")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
@DiscriminatorValue("C")
public class CommunityRoleEntity extends RoleEntity {
	/**
	 * 社区平台角色
	 */
	private static final long serialVersionUID = 6515079269641218501L;


}
