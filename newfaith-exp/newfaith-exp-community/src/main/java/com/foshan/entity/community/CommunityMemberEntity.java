package com.foshan.entity.community;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AccountEntity;
import com.foshan.entity.RoleEntity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@Entity
@DiscriminatorValue("C")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityMemberEntity extends AccountEntity {
	/**
	 * 用户
	 */
	private static final long serialVersionUID = -5402899772980823273L;

	@Column(columnDefinition = "varchar(512) comment '备注'")
	private String comment;
	@Column(columnDefinition = "int(2) comment '0业主 1住户成员（住户成员不需要填写收楼日期与计费日期）2、租户(计费日期填写起租日期，离退日期填写退租日期)'")
	private Integer memberType;
	@Column(columnDefinition = "varchar(32) comment '紧急联系人姓名'")
	private String contactPerson;
	@Column(columnDefinition = "varchar(64) comment '紧急联系方式'")
	private String emergencyContact;
	@Column(columnDefinition = "varchar(32) comment '与户主关系'")
	private String relation;
//	@Column(columnDefinition = "varchar(100) comment '购方名称'")
//	private String buyersName;
//	@Column(columnDefinition = "varchar(100) comment '购方地址电话'")
//	private String buyersAddress;
//	@Column(columnDefinition = "varchar(100) comment '企业类型'")
//	private String businessType;
//	@Column(columnDefinition = "varchar(10) comment '开票类型'")
//	private String invoiceType;
//	@Column(columnDefinition = "varchar(100) comment '购方税号'")
//	private String paytaxNo;
//	@Column(columnDefinition = "varchar(64) comment '购方银行账号'")
//	private String buyersBankAccount;
	@Column(columnDefinition = "varchar(64) comment '表ID'")
	private String oldId;
	@Column(columnDefinition = "varchar(512) comment '旧表数据'")
	private String oldData;
	@Column(columnDefinition = "varchar(1000) comment '拓展字段'")
	private String expandField;
	@Column(columnDefinition = "varchar(5000) comment '操作日志'")
	private String  operationLog;
	
	@ManyToMany(targetEntity = RoleEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_member_role", joinColumns = @JoinColumn(name = "memberId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "roleId", referencedColumnName = "id"))
	private List<RoleEntity> communityRoleList = new ArrayList<RoleEntity>();
	
	@OneToMany(targetEntity = CommunityMemberPropertyEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityMemberPropertyEntity> memberPropertyList = new ArrayList<CommunityMemberPropertyEntity>();
	
	@OneToMany(targetEntity = CommunityEventsEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityEventsEntity> userList = new ArrayList<CommunityEventsEntity>();
	
	@OneToMany(targetEntity = CommunityReservationRecordEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityReservationRecordEntity> reservationRecordList = new ArrayList<CommunityReservationRecordEntity>();
	
	@ManyToOne(targetEntity = CommunityMemberEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentMemberId", referencedColumnName = "id", nullable = true)
	private CommunityMemberEntity parentMember;
	@OneToMany(targetEntity = CommunityMemberEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentMemberId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityMemberEntity> subMemberList = new ArrayList<CommunityMemberEntity>();
	
	@OneToMany(targetEntity = CommunityPaymentAccountEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityPaymentAccountEntity> paymentAccountList = new ArrayList<CommunityPaymentAccountEntity>();
	
	@OneToMany(targetEntity = CommunityContractEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityContractEntity> contractList = new ArrayList<CommunityContractEntity>();
	
}
