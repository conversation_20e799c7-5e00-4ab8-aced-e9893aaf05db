package com.foshan.entity.community;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_invoice")
@org.hibernate.annotations.Table(appliesTo = "t_community_invoice",comment="社区发票") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityInvoiceEntity extends Community {
	/**
	 * 
	 */
	private static final long serialVersionUID = -1023894495297395030L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(32) comment '开票人'")
	private String agent;
	@Column(columnDefinition = "varchar(64) comment '购方名称'")
	private String buyerName;
	@Column(columnDefinition = "varchar(128) comment '购方地址'")
	private String buyerAddre;
	@Column(columnDefinition = "varchar(32) comment '购方银行账号'")
	private String buyerBankAccount;
	@Column(columnDefinition = "varchar(32) comment '购方识别号'")
	private String buyerCode;
	@Column(columnDefinition = "int(2) comment '操作类型：0－开蓝票；1－开红票，2申请红票，3 撤销申请红票'")
	private Integer invoiceType;
	@Column(columnDefinition = "int(2) comment '正票是否红冲,0-否，1是'")
	private Integer isRedDashed;
	@Column(columnDefinition = "varchar(32) comment '发票代码'")
	private String invoiceCode;
	@Column(columnDefinition = "varchar(32) comment '发票编号'")
	private String invoiceNum;
	@Column(columnDefinition = "varchar(32) comment '发票请求流水号'")
	private String invoiceSn;
	@Column(columnDefinition = "varchar(256) comment '红冲原因'")
	private String redDashedReason;
	@Column(columnDefinition = "decimal(10,4) comment '发票总金额'")
	private BigDecimal totalAmount;
	@Column(columnDefinition = "decimal(10,4) comment '税额'")
	private BigDecimal taxAmount;
	@Column(columnDefinition = "varchar(256) comment '平台下载地址'")
	private String platUrl;
	@Column(columnDefinition = "varchar(256) comment '本地下载地址'")
	private String localUrl;
	@Column(columnDefinition = "varchar(32) comment '请求返回码'")
	private String returnCode;
	@Column(columnDefinition = "varchar(512) comment '备用字段'")
	private String expand;
	@Column(columnDefinition = "varchar(512) comment '请求返回信息'")
	private String returnMessage;
	@Column(columnDefinition = "int(2) comment '操作状态 0 不成功，1成功，2正在开（操作类型为 0,1的时候用）'")
	private Integer invoiceState;
	
	@ManyToOne(targetEntity = CommunityInvoiceEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentInvoiceId", referencedColumnName = "id", nullable = true)
	private CommunityInvoiceEntity parentInvoice;
	@OneToMany(targetEntity = CommunityInvoiceEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentInvoiceId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityInvoiceEntity> subInvoiceList = new ArrayList<CommunityInvoiceEntity>();
	
	@ManyToOne(targetEntity = CommunityReceiptEntity.class, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "receiptId", referencedColumnName = "id", nullable = true)
	private CommunityReceiptEntity receipt;
	
	@ManyToMany(targetEntity = CommunityReceiptReceivablesEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_receipt_receivables_invoice", joinColumns = @JoinColumn(name = "invoiceId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "receiptReceivablesId", referencedColumnName = "id"))
	@JsonIgnore
	private List<CommunityReceiptReceivablesEntity> receiptReceivablesList = new ArrayList<CommunityReceiptReceivablesEntity>();

}
