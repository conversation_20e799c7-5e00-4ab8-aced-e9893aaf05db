package com.foshan.entity.community.vo;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@JsonInclude(Include.NON_EMPTY)
@Data
@Slf4j
public class EstateReceivablesVo {
	private Integer id;
	private String unitCode;
	private BigDecimal additionalArea;
	private BigDecimal chargingArea;
	private Integer parentPropertyId;
	private Integer paymentAccountId;
	private Integer payItemsId;
	private Integer chargeCategory;
	private String comment;
	private LocalDate endTime;
	private Integer feeCalType;
	private String itemsName;
	private BigDecimal price;
	private String priceUnit;
	private LocalDate startTime;
	private LocalDate acceptanceDate;
	private LocalDate billingDate;
	private Integer payDate;
	private Integer isCurrentMember;
	private Integer isCurrentOwner;
	private Integer memberId;
	private LocalDate terminationDate;
	private LocalDate endDate;
	private Integer rentState;
	private Integer memberPropertyId;

	public static EstateReceivablesVo getEstateReceivablesVo(Object[] o) {
		EstateReceivablesVo vo = null;
		if (null != o) {
			try {
				vo = new EstateReceivablesVo();
				vo.setId(Integer.parseInt(o[0].toString()));
				vo.setUnitCode(null != o[1] ? o[1].toString() : "");
				vo.setAdditionalArea(null != o[2] ? new BigDecimal(o[2].toString()) : null);
				vo.setChargingArea(null != o[3] ? new BigDecimal(o[3].toString()) : null);
				vo.setParentPropertyId(null != o[4] ? Integer.parseInt(o[4].toString()) : null);
				vo.setPaymentAccountId(null != o[5] ? Integer.parseInt(o[5].toString()) : null);
				vo.setPayItemsId(null != o[6] ? Integer.parseInt(o[6].toString()) : null);
				vo.setChargeCategory(null != o[7] ? Integer.parseInt(o[7].toString()) : null);
				vo.setComment(null != o[8] ? o[8].toString() : "");
				vo.setEndTime(null != o[9] ? LocalDate.parse(o[9].toString().substring(0, 10)) : null);
				vo.setFeeCalType(null != o[10] ? Integer.parseInt(o[10].toString()) : null);
				vo.setItemsName(null != o[11] ? o[11].toString() : "");
				vo.setPrice(null != o[12] ? new BigDecimal(o[12].toString()) : null);
				vo.setPriceUnit(null != o[13] ? o[13].toString() : "");
				vo.setStartTime(null != o[14] ? LocalDate.parse(o[14].toString().substring(0, 10)) : null);
				vo.setAcceptanceDate(null != o[15] ? LocalDate.parse(o[15].toString().substring(0, 10)) : null);
				vo.setBillingDate(null != o[16] ? LocalDate.parse(o[16].toString().substring(0, 10)) : null);
				vo.setPayDate(null != o[17] ? Integer.parseInt(o[17].toString()) : null);
				vo.setIsCurrentMember(null != o[18] ? Integer.parseInt(o[18].toString()) : null);
				vo.setIsCurrentOwner(null != o[19] ? Integer.parseInt(o[19].toString()) : null);
				vo.setMemberId(null != o[20] ? Integer.parseInt(o[20].toString()) : null);
				vo.setTerminationDate(null != o[21] ? LocalDate.parse(o[21].toString().substring(0, 10)) : null);
				vo.setEndDate(null != o[22] ? LocalDate.parse(o[22].toString().substring(0, 10)) : null);
				vo.setRentState(null != o[23] ? Integer.parseInt(o[23].toString()) : null);
				vo.setMemberPropertyId(null != o[24] ? Integer.parseInt(o[24].toString()) : null);

			} catch (Exception ex) {
				ex.printStackTrace();
				log.error(ex.getMessage() + ":estateReceivablesVo(" + o[0].toString() + ")数据转换失败");
				vo = null;
			}
		}
		return vo;
	}


	
}
