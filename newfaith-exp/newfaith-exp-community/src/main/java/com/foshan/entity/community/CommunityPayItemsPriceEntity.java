package com.foshan.entity.community;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_payItems_price")
@org.hibernate.annotations.Table(appliesTo = "t_community_payItems_price",comment="子表") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityPayItemsPriceEntity implements IEntityBean {
	/**
	 * 
	 */
	private static final long serialVersionUID = 4268980688901128086L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '时间'")
	protected Timestamp createTime;
	@Column(columnDefinition = "Timestamp  comment '最后修改时间'")
	protected Timestamp lastModifyTime;
    @Column(columnDefinition = "decimal(10,2) comment '单价'")
    private BigDecimal price;
    @Column(columnDefinition = "datetime comment '开始时间'")
    private Date startTime;
    @Column(columnDefinition = "datetime comment '结束时间'")
    private Date endTime;
    @Column(columnDefinition = "varchar(512) comment '备注'")
    private String comment;
    @Column(columnDefinition = "int(1) default 0 comment '周期 0-年；1：月；2：日；3：小时；'")
    private Integer period;
    
	@ManyToOne(targetEntity = CommunityMemberPropertyEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberPropertyId", referencedColumnName = "id", nullable = true)
	private CommunityMemberPropertyEntity memberProperty;
	
	@ManyToOne(targetEntity = CommunityPayItemsEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "payItemsId", referencedColumnName = "id", nullable = true)
	private CommunityPayItemsEntity payItems;
	
//	@ManyToMany(targetEntity = CommunityContractEntity.class, fetch = FetchType.LAZY)
//	@JoinTable(name = "t_community_payItems_price_contract", joinColumns = @JoinColumn(name = "itemsPriceId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "contractId", referencedColumnName = "id"))
//	@JsonIgnore
//	private List<CommunityContractEntity> contractList = new ArrayList<CommunityContractEntity>();

}
