package com.foshan.entity.community;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_reservation_strategy")
@org.hibernate.annotations.Table(appliesTo = "t_community_reservation_strategy",comment="预约策略")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityReservationStrategyEntity implements IEntityBean {

	/**
	 * 预约策略
	 */
	private static final long serialVersionUID = -8836071710048081844L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "int(2) comment '1法定节假日 2工作日 0不限'")
	protected Integer periodType;
	@Column(columnDefinition = "varchar(64) comment '策略名称'")
	private String strategyName;

	@OneToMany(targetEntity = CommunityReservationActivitiesEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "strategyId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityReservationActivitiesEntity> activitiesList = new ArrayList<CommunityReservationActivitiesEntity>();
	
	@ManyToMany(targetEntity = CommunityReservationPresetPeriodEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_strategy_preset_period", joinColumns = @JoinColumn(name = "strategyId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "presetPeriodId", referencedColumnName = "id"))
	private List<CommunityReservationPresetPeriodEntity> presetPeriodList = new ArrayList<CommunityReservationPresetPeriodEntity>();
}
