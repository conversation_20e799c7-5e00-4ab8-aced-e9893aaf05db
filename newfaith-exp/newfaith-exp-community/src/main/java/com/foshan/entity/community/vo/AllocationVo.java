package com.foshan.entity.community.vo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@JsonInclude(Include.NON_EMPTY)
@Data
@Slf4j
public class AllocationVo {
	private Integer estateId;
	private String payItemsName;
	private Integer templeteId;
	private BigDecimal unitPrice;
	private String dynamicParameter;
	private String templeteInfo;
	private Integer allocationStartYear;
	private Integer allocationStartMonth;
	private Integer allocationEndYear;
	private Integer allocationEndMonth;
	private BigDecimal totalChargeArea;
	private BigDecimal allocationNum;
	private Integer allocationId;
	private BigDecimal chargingArea;
	private BigDecimal usableArea;
	private Integer meterId;
	private Integer memberId;
	private Date billingDate;
	private BigDecimal propertyParkingArea;
	private BigDecimal propertyParkingNum;
	private BigDecimal defenceParkingArea;
	private BigDecimal defenceParkingNum;
	private String unitCode;
    private Integer allocationPeriod;
    private Integer specialAllocationFlag;

	public static AllocationVo getAllocationItemVo(Object[] o) {
		AllocationVo vo = null;
		if (null != o) {
			try {
				vo = new AllocationVo();
				vo.setEstateId(Integer.parseInt(o[0].toString()));
				vo.setPayItemsName(o[1].toString());
				vo.setTempleteId(null != o[2] ? Integer.parseInt(o[2].toString()) : null);
				vo.setUnitPrice(null != o[3] ? new BigDecimal(o[3].toString()) : BigDecimal.ZERO);
				vo.setDynamicParameter(null != o[4] ? o[4].toString() : "");
				vo.setTempleteInfo(null != o[5] ? o[5].toString() : "");
				vo.setAllocationStartYear(Integer.parseInt(o[6].toString()));
				vo.setAllocationStartMonth(Integer.parseInt(o[7].toString()));
				vo.setAllocationEndYear(Integer.parseInt(o[8].toString()));
				vo.setAllocationEndMonth(Integer.parseInt(o[9].toString()));
				vo.setTotalChargeArea(null != o[10] ? new BigDecimal(o[10].toString()) : BigDecimal.ZERO);
				vo.setAllocationNum(null != o[11] ? new BigDecimal(o[11].toString()) : BigDecimal.ZERO);
				vo.setAllocationId(null != o[12] ? Integer.parseInt(o[12].toString()) : null);
				vo.setChargingArea(null != o[13] ? new BigDecimal(o[13].toString()) : BigDecimal.ZERO);
				vo.setUsableArea(null != o[14] ? new BigDecimal(o[14].toString()) : BigDecimal.ZERO);
				vo.setMeterId(null != o[15] ? Integer.parseInt(o[15].toString()) : null);
				vo.setMemberId(null != o[16] ? Integer.parseInt(o[16].toString()) : null);
				vo.setBillingDate(null != o[17] ? Date.from(LocalDate.parse(o[17].toString().subSequence(0, 10))
						.atStartOfDay(ZoneId.systemDefault()).toInstant()) : null);
				vo.setPropertyParkingArea(null != o[18] ? new BigDecimal(o[18].toString()) : new BigDecimal("0"));
				vo.setPropertyParkingNum(null != o[19] ? new BigDecimal(o[19].toString()) : new BigDecimal("0"));
				vo.setDefenceParkingArea(null != o[20] ? new BigDecimal(o[20].toString()) : new BigDecimal("0"));
				vo.setDefenceParkingNum(null != o[21] ? new BigDecimal(o[21].toString()) : new BigDecimal("0"));
				vo.setUnitCode(null!=o[22]?o[22].toString():"");
				vo.setAllocationPeriod(null!=o[23]?Integer.parseInt(o[23].toString()):1);
				vo.setSpecialAllocationFlag(Integer.parseInt(o[24].toString()));

			} catch (Exception ex) {
				ex.printStackTrace();
				log.error(ex.getMessage() + ":AllocationVo(" + o[0].toString() + ")数据转换失败");
				vo = null;
			}
		}
		return vo;
	}
	

}


