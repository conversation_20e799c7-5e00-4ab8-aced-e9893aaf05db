package com.foshan.entity.community;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.UserEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;

@Getter
@Setter
@NoArgsConstructor
@Entity
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
@DiscriminatorValue("C")
public class CommunityAssetEntity extends AssetEntity{
    private static final long serialVersionUID = -7100214839735458139L;
    /**
     * 社区邻里
     */
    @Column(columnDefinition = "int(2) comment '审核状态  0:初始化；1:初审通过；2：初审不通过；3：终审通过；4：终审不通过'")
    private Integer auditState;
    @Column(columnDefinition = "int(2) comment '内容类别 0:社区微视频；1：社区互助；2:闲置转让；3:小区活动；4：公告类；5：回帖'")
    private Integer contentType;


    @ManyToOne(targetEntity = CommunityMemberEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
    @JsonIgnore
    private CommunityMemberEntity communityMember;

    @ManyToOne(targetEntity = UserEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "userId", referencedColumnName = "id", nullable = true)
    @JsonIgnore
    private UserEntity user;

//    @ManyToMany(targetEntity = CommunityColumnEntity.class, fetch = FetchType.LAZY)
//    @JoinTable(name = "t_upshelf_column", joinColumns ={@JoinColumn(name = "assetId", referencedColumnName = "id"),@JoinColumn(name = "type",referencedColumnName = "contentType"),@JoinColumn(name = "orderNumber", referencedColumnName = "assetOrders")}, inverseJoinColumns = @JoinColumn(name = "columnId", referencedColumnName = "id") )
//    @JsonIgnore
//    private List<CommunityColumnEntity> columnList = new ArrayList<CommunityColumnEntity>();
}
