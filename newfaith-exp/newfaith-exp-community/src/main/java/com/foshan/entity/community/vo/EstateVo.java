package com.foshan.entity.community.vo;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_EMPTY)
public class EstateVo {
	private BigDecimal buildingAreaTotal;

	public EstateVo(BigDecimal buildingAreaTotal) {
		this.buildingAreaTotal = buildingAreaTotal;
	}
}
