package com.foshan.entity.community;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_receive_message_type")
@org.hibernate.annotations.Table(appliesTo = "t_receive_message_type",comment="接收消息类型表") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class ReceiveMessageTypeEntity implements IEntityBean {


	/**
	 * 
	 */
	private static final long serialVersionUID = 9070586847354423947L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '对象名称'")
	private String objectName;
	@Column(columnDefinition = "int(2) comment '类型值，0：应急广播；1：小程序；2：短信；'")
	private Integer typeValue;
	
	@ManyToMany(targetEntity = MessageAccountEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_message_account_message_type", joinColumns = @JoinColumn(name = "messageTypeId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "messageAccountId", referencedColumnName = "id"))
	@JsonIgnore
	private List<MessageAccountEntity> messageAccountList = new ArrayList<MessageAccountEntity>();
}
