package com.foshan.entity.community;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_district")
@org.hibernate.annotations.Table(appliesTo = "t_community_district",comment="小区") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityDistrictEntity extends Community {

	private static final long serialVersionUID = 3806456502232345648L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '小区名称'")
	private String districtName;
	@Column(columnDefinition = "varchar(64) comment '小区编号'")
	private String districtCode;
	@Column(columnDefinition = "varchar(256) comment '地址'")
	private String address;
	@Column(columnDefinition = "varchar(64) comment '占地面积'")
	private String areaSize;
	@Column(columnDefinition = "varchar(64) comment '土地年限'")
	private String period;
	@Column(columnDefinition = "varchar(64) comment '拿地时间'")
	private String  takeTime;
	@Column(columnDefinition = "datetime comment '竣工日期'")
	private Date  completionDate;
	@Column(columnDefinition = "datetime comment '移交日期'")
	private Date handoverDate;
	@Column(columnDefinition = "int(10) comment '栋数'")
	private Integer buildingNumber;
	@Column(columnDefinition = "varchar(64) comment '容积率'")
	private String ratio;
	@Column(columnDefinition = "varchar(64) comment '户数'")
	private String housingNumber;
	@Column(columnDefinition = "varchar(64) comment '人口数'")
	private String population;
	@Column(columnDefinition = "varchar(512) comment '备注'")
	private String comment;
	@Column(columnDefinition = "int(10) comment '小区排序号'")
	private Integer districtOrder;
	
	@Column(columnDefinition = "varchar(64) comment '表ID'")
	private String oldId;
	@Column(columnDefinition = "varchar(512) comment '旧表数据'")
	private String oldData;
	
	@ManyToOne(targetEntity = CommunityEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "communityId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityEntity community;
	
	@ManyToMany(targetEntity = CommunityBuilderEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_builder_district", joinColumns = @JoinColumn(name = "districtId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "builderId", referencedColumnName = "id"))
	private List<CommunityBuilderEntity> builderList = new ArrayList<CommunityBuilderEntity>();
	
	@OneToMany(targetEntity = CommunityDepartmentEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "districtId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityDepartmentEntity> departmentList = new ArrayList<CommunityDepartmentEntity>();

	@ManyToMany(targetEntity = CommunityColumnEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_column_district",joinColumns = @JoinColumn(name = "districtId",referencedColumnName = "id"),inverseJoinColumns = @JoinColumn(name = "columnId",referencedColumnName = "id"))
	@JsonIgnore
	private List<CommunityColumnEntity> columnList = new ArrayList<CommunityColumnEntity>();
	
	@OneToMany(targetEntity = CommunityBuildingEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "districtId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityBuildingEntity> buildingList = new ArrayList<CommunityBuildingEntity>();
	
	@OneToMany(targetEntity = CommunityCecorationItemsAttachmentEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "districtId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityCecorationItemsAttachmentEntity> itemsAttachmentList = new ArrayList<CommunityCecorationItemsAttachmentEntity>();
}
