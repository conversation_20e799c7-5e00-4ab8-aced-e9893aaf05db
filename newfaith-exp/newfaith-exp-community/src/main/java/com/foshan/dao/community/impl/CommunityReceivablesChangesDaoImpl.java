package com.foshan.dao.community.impl;

import org.springframework.stereotype.Repository;
import com.foshan.dao.generic.HibernateDao;
import com.foshan.entity.community.CommunityReceivablesChangesEntity;
import com.foshan.dao.community.ICommunityReceivablesChangesDao;

@Repository("communityReceivablesChangesDao")
public class CommunityReceivablesChangesDaoImpl extends HibernateDao<CommunityReceivablesChangesEntity,Integer> implements ICommunityReceivablesChangesDao{

	public void flush(){
		 getSession().flush();
	}
}