package com.foshan.dao.community;

import com.foshan.entity.community.CommunityBankDepositRecordEntity;

import org.hibernate.LockMode;

import com.foshan.dao.generic.GenericDao;


public interface ICommunityBankDepositRecordDao  extends GenericDao<CommunityBankDepositRecordEntity,Integer> {
	void flush();
	void refresh(CommunityBankDepositRecordEntity bankDepositRecord, LockMode pessimisticWrite);
	public void clear();
	
}
