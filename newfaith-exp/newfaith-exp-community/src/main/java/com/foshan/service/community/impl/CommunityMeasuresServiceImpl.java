package com.foshan.service.community.impl;

import java.sql.Timestamp;
import java.text.ParseException;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityMeasuresEntity;
import com.foshan.entity.community.CommunityPropertyEntity;
import com.foshan.form.community.CommunityMeasuresForm;
import com.foshan.form.community.request.CommunityMeasuresReq;
import com.foshan.form.community.response.communityMeasures.AddCommunityMeasuresRes;
import com.foshan.form.community.response.communityMeasures.GetCommunityMeasuresInfoRes;
import com.foshan.form.community.response.communityMeasures.GetCommunityMeasuresListRes;
import com.foshan.form.community.response.communityMeasures.ModifyCommunityMeasuresRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.community.ICommunityMeasuresService;
import com.foshan.util.DateUtil;

@Transactional
@Service("communityMeasuresService")
public class CommunityMeasuresServiceImpl extends GenericCommunityService implements ICommunityMeasuresService{

	@Override
	public IResponse getCommunityMeasuresList(CommunityMeasuresReq req) {
		GetCommunityMeasuresListRes res = new GetCommunityMeasuresListRes();
		Page<CommunityMeasuresEntity> page = new Page<CommunityMeasuresEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityMeasuresEntity a inner join a.property b  where 1=1 ");
		hql.append(StringUtils.isNotEmpty(req.getUnitCode()) ? " and b.unitCode like'%"+req.getUnitCode()+"%'" :"")
			.append(null!=req.getMeasuresType() ? " and a.measuresType="+req.getMeasuresType():"")
			.append(StringUtils.isNotEmpty(req.getStartTime()) ? " and a.adoptTime>='"+req.getStartTime()+"'" :"")
			.append(StringUtils.isNotEmpty(req.getEndTime()) ? " and  a.adoptTime<='"+req.getEndTime()+"'" :"")
			.append(null!=req.getMeasuresState() ? " and a.measuresState="+req.getMeasuresState():"");
		hql.append(" ORDER BY a.id desc");
		page = communityMeasuresDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityMeasuresForm communityMeasuresForm = new CommunityMeasuresForm();
			communityMeasuresForm.setCommunityMeasuresId(o.getId());
            communityMeasuresForm.setAdoptTime(null != o.getAdoptTime()? DateUtil.formatLongFormat(o.getAdoptTime()) : "");
            communityMeasuresForm.setCreateTime(null != o.getCreateTime()? DateUtil.formatLongFormat(o.getCreateTime()) : "");
            communityMeasuresForm.setDetails(o.getDetails());
            communityMeasuresForm.setLastModifyTime(null != o.getLastModifyTime()? DateUtil.formatLongFormat(o.getLastModifyTime()) : "");
            communityMeasuresForm.setMeasuresState(o.getMeasuresState());
            communityMeasuresForm.setMeasuresType(o.getMeasuresType());
            communityMeasuresForm.setOperator(o.getOperator());
            if(null!=o.getProperty()) {
            	CommunityEstateEntity estate= (CommunityEstateEntity) o.getProperty();
            	communityMeasuresForm.setUnitCode(estate.getUnitCode());
            }else {
            	communityMeasuresForm.setUnitCode("");
            }
            
			res.getCommunityMeasuresList().add(communityMeasuresForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public IResponse addCommunityMeasures(CommunityMeasuresReq req) {
		AddCommunityMeasuresRes res = new AddCommunityMeasuresRes();
		if (null!=req.getPropertyId()) {
			CommunityPropertyEntity property = communityPropertyDao.get(req.getPropertyId());
			if(null == property) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			CommunityMeasuresEntity communityMeasures = new CommunityMeasuresEntity();
            try {
                communityMeasures.setAdoptTime(StringUtils.isNotEmpty(req.getAdoptTime()) ? 
                    new Timestamp(DateUtil.parseLongFormat(req.getAdoptTime()).getTime()) : 
                    	new Timestamp((new Date()).getTime()));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            communityMeasures.setDetails(StringUtils.isNotEmpty(req.getDetails()) ? req.getDetails() :"");
            communityMeasures.setLastModifyTime(new Timestamp(new Date().getTime()));
            communityMeasures.setMeasuresState(req.getMeasuresState());
            communityMeasures.setMeasuresType(req.getMeasuresType());
            communityMeasures.setOperator(StringUtils.isNotEmpty(req.getOperator()) ? req.getOperator():"");
            communityMeasures.setProperty(property);
			communityMeasuresDao.save(communityMeasures);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse modifyCommunityMeasures(CommunityMeasuresReq req) {
		ModifyCommunityMeasuresRes res = new ModifyCommunityMeasuresRes();
		if (null!=req.getCommunityMeasuresId() ) {
			CommunityMeasuresEntity communityMeasures = communityMeasuresDao.get(req.getCommunityMeasuresId()) ;
			if(null != communityMeasures){
                try {
                    communityMeasures.setAdoptTime(StringUtils.isNotEmpty(req.getAdoptTime()) ? 
                        new Timestamp(DateUtil.parseLongFormat(req.getAdoptTime()).getTime()) : communityMeasures.getAdoptTime());
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                communityMeasures.setDetails(StringUtils.isNotEmpty(req.getDetails()) ? req.getDetails() : communityMeasures.getDetails());
                communityMeasures.setLastModifyTime(new Timestamp(new Date().getTime()));
                communityMeasures.setMeasuresState(null!=req.getMeasuresState() ? req.getMeasuresState() : communityMeasures.getMeasuresState());
                communityMeasures.setMeasuresType(null!=req.getMeasuresType() ? req.getMeasuresType() : communityMeasures.getMeasuresType());
                communityMeasures.setOperator(StringUtils.isNotEmpty(req.getOperator()) ? req.getOperator() : communityMeasures.getOperator());
				res.setCommunityMeasuresId(communityMeasures.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse deleteCommunityMeasures(CommunityMeasuresReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityMeasuresId()) {
		CommunityMeasuresEntity communityMeasures = communityMeasuresDao.get(req.getCommunityMeasuresId());
			if (null != communityMeasures) {
				communityMeasures.setProperty(null);
				communityMeasuresDao.delete(communityMeasures);
				//communityMeasures.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityMeasuresInfo(CommunityMeasuresReq req) {
		GetCommunityMeasuresInfoRes res = new GetCommunityMeasuresInfoRes();
		if (null != req.getCommunityMeasuresId()) {
			CommunityMeasuresEntity communityMeasures = communityMeasuresDao.get(req.getCommunityMeasuresId());
			if (null != communityMeasures) {
				CommunityMeasuresForm communityMeasuresForm = new CommunityMeasuresForm();
				communityMeasuresForm.setCommunityMeasuresId(communityMeasures.getId());
                communityMeasuresForm.setAdoptTime(null != communityMeasures.getAdoptTime()? DateUtil.formatLongFormat(communityMeasures.getAdoptTime()) : "");
                communityMeasuresForm.setCreateTime(null != communityMeasures.getCreateTime()? DateUtil.formatLongFormat(communityMeasures.getCreateTime()) : "");
                communityMeasuresForm.setDetails(communityMeasures.getDetails());
                communityMeasuresForm.setLastModifyTime(null != communityMeasures.getLastModifyTime()? DateUtil.formatLongFormat(communityMeasures.getLastModifyTime()) : "");
                communityMeasuresForm.setMeasuresState(communityMeasures.getMeasuresState());
                communityMeasuresForm.setMeasuresType(communityMeasures.getMeasuresType());
                communityMeasuresForm.setOperator(communityMeasures.getOperator());
                if(null!=communityMeasures.getProperty()) {
                	CommunityEstateEntity estate= (CommunityEstateEntity) communityMeasures.getProperty();
                	communityMeasuresForm.setUnitCode(estate.getUnitCode());
                	communityMeasuresForm.setPropertyId(estate.getId());
                }else {
                	communityMeasuresForm.setUnitCode("");
                }
				res.setCommunityMeasuresForm(communityMeasuresForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}