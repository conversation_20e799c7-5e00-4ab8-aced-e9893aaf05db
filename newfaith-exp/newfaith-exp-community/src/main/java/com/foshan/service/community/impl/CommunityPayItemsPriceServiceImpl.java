package com.foshan.service.community.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityContractEntity;
import com.foshan.entity.community.CommunityMemberPropertyEntity;
import com.foshan.entity.community.CommunityPayItemsEntity;
import com.foshan.entity.community.CommunityPayItemsPriceEntity;
import com.foshan.entity.community.CommunityPropertyEntity;
import com.foshan.form.community.CommunityPayItemsPriceForm;
import com.foshan.form.community.request.CommunityPayItemsPriceReq;
import com.foshan.form.community.response.communityPayItemsPrice.GetCommunityPayItemsPriceListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityPayItemsPriceService;
import com.foshan.util.DateUtil;

@Transactional
@Service("communityPayItemsPriceService")
public class CommunityPayItemsPriceServiceImpl extends GenericCommunityService implements ICommunityPayItemsPriceService {

	@Override
	public IResponse getCommunityPayItemsPriceList(CommunityPayItemsPriceReq req) {
		GetCommunityPayItemsPriceListRes res = new GetCommunityPayItemsPriceListRes();
		Page<CommunityPayItemsPriceEntity> page = new Page<CommunityPayItemsPriceEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 200);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityPayItemsPriceEntity a  "
				+ " inner join a.payItems c   inner join a.memberProperty d ");
		hql.append(null!=req.getContractId() ? " inner join d.contract b ":"")
				.append(" where a.createTime is not null ")
				.append(null!=req.getContractId() ? " and  b.id="+req.getContractId() :"")
				.append(null!=req.getCommunityMemberPropertyId() ?" and  d.id="+req.getCommunityMemberPropertyId() : "")
				.append(null != req.getCommunityPayItemsId() ? " and c.id ='" + req.getCommunityPayItemsId() + "'": "")
				.append(null!=req.getPeriod() ? " and a.period ='" + req.getPeriod() + "'": "");

		hql.append(" ORDER BY a.lastModifyTime desc");
		page = communityPayItemsPriceDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityPayItemsPriceForm payItemsPriceForm = new CommunityPayItemsPriceForm();
			payItemsPriceForm.setStartTime(o.getComment());
			payItemsPriceForm.setPayItemsPriceId(o.getId());
			payItemsPriceForm.setPeriod(o.getPeriod());
			payItemsPriceForm.setPrice(o.getPrice().toString());
			payItemsPriceForm.setCreateTime(null != o.getCreateTime() ? DateUtil.formatLongFormat(o.getCreateTime()) : "");
			payItemsPriceForm.setEndTime(null != o.getEndTime() ? DateUtil.formatLongFormat(o.getEndTime()) : "");
			payItemsPriceForm.setLastModifyTime(null != o.getLastModifyTime() ? DateUtil.formatLongFormat(o.getLastModifyTime()) : "");
			payItemsPriceForm.setStartTime(null != o.getStartTime() ? DateUtil.formatLongFormat(o.getStartTime()) : "");
			payItemsPriceForm.setItemsName(o.getPayItems().getItemsName());
			payItemsPriceForm.setChargeCategory(o.getPayItems().getChargeCategory());
			payItemsPriceForm.setPriceUnit(o.getPayItems().getPriceUnit());
			payItemsPriceForm.setFeeCalType(o.getPayItems().getFeeCalType());
			payItemsPriceForm.setPayDate(o.getPayItems().getPayDate());
			payItemsPriceForm.setIsBreach(o.getPayItems().getIsBreach());
			payItemsPriceForm.setComment(StringUtils.isNotEmpty(o.getComment()) ? o.getComment() : "");
			payItemsPriceForm.setPayItemId(o.getPayItems().getId());
			res.getPayItemsPriceList().add(payItemsPriceForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public IResponse addCommunityPayItemsPrice(CommunityPayItemsPriceReq req) {
		GenericResponse res = new GenericResponse();
		if (null!=req.getCommunityPayItemsId() && null != req.getPeriod()) {
			CommunityPayItemsEntity payItems = communityPayItemsDao.get(req.getCommunityPayItemsId());
			
			if(null==payItems) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			CommunityPayItemsPriceEntity payItemsPrice = new CommunityPayItemsPriceEntity();
			CommunityContractEntity contract = null;
			if(null!=req.getContractId()) {
				contract = communityContractDao.get(req.getContractId());
//				if(null != contract) {
//					payItemsPrice.getContractList().add(contract);
//				}
			}
			
			if(null!=req.getCommunityMemberPropertyId()) {
				CommunityMemberPropertyEntity memberProperty = communityMemberPropertyDao.get(req.getCommunityMemberPropertyId());
				if(null != memberProperty) {
					if(null != contract) {
						memberProperty.setContract(contract);
					}
					payItemsPrice.setMemberProperty(memberProperty);
					CommunityPropertyEntity property = memberProperty.getProperty();
					if(!payItems.getPropertyList().contains(property)) {
						payItems.getPropertyList().add(property);
					}
				}
			}
			
			
			payItemsPrice.setComment(req.getComment());
			try {
				payItemsPrice.setEndTime(
						StringUtils.isNotEmpty(req.getEndTime()) ? DateUtil.parseLongFormat(req.getEndTime()) : null);
				payItemsPrice.setStartTime(
						StringUtils.isNotEmpty(req.getStartTime()) ? DateUtil.parseLongFormat(req.getStartTime()) : null);
			} catch (ParseException e) {
				e.printStackTrace();
			}
			payItemsPrice.setPrice(StringUtils.isNotEmpty(req.getPrice()) ? new BigDecimal(req.getPrice()) : null);
			payItemsPrice.setPeriod(req.getPeriod());
			payItemsPrice.setPayItems(payItems);
			payItemsPrice.setLastModifyTime(new Timestamp(new Date().getTime()));
			communityPayItemsPriceDao.save(payItemsPrice);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse modifyCommunityPayItemsPrice(CommunityPayItemsPriceReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityPayItemsPriceId()) {
			CommunityPayItemsPriceEntity payItemsPrice = communityPayItemsPriceDao.get(req.getCommunityPayItemsPriceId());
			if (null != payItemsPrice) {
				payItemsPrice.setComment(req.getComment());
				try {
					payItemsPrice.setEndTime(
							StringUtils.isNotEmpty(req.getEndTime()) ? DateUtil.parseLongFormat(req.getEndTime()) 
									: payItemsPrice.getEndTime());
					payItemsPrice.setStartTime(
							StringUtils.isNotEmpty(req.getStartTime()) ? DateUtil.parseLongFormat(req.getStartTime()) 
									: payItemsPrice.getStartTime());
				} catch (ParseException e) {
					e.printStackTrace();
				}
				payItemsPrice.setPrice(StringUtils.isNotEmpty(req.getPrice()) ? 
						new BigDecimal(req.getPrice()) : payItemsPrice.getPrice());
				payItemsPrice.setLastModifyTime(new Timestamp(new Date().getTime()));
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse deleteCommunityPayItemsPrice(CommunityPayItemsPriceReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityPayItemsPriceId()) {
			CommunityPayItemsPriceEntity payItemsPrice = communityPayItemsPriceDao.get(req.getCommunityPayItemsPriceId());
			if (null != payItemsPrice) {
				payItemsPrice.setMemberProperty(null);
				payItemsPrice.setPayItems(null);
//				payItemsPrice.setContractList(null);
				communityPayItemsPriceDao.delete(payItemsPrice);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	
	
	@Override
	@Audit(operate = "合同绑定收费价格")
	public IResponse contractBindingPayItemsPrice(CommunityPayItemsPriceReq req) {
		GenericResponse res = new GenericResponse();

//		if (StringUtils.isNotEmpty(req.getPayItemsPriceIdList()) && StringUtils.isNotEmpty(req.getContractIdList())) {
//			List<CommunityPayItemsPriceEntity> list = new ArrayList<CommunityPayItemsPriceEntity>();
//			String[] payItemPriceIds = req.getPayItemsPriceIdList().split(",");
//			for (String payItemPriceId : payItemPriceIds) {
//				CommunityPayItemsPriceEntity payItemPrice = communityPayItemsPriceDao.get(Integer.parseInt(payItemPriceId));
//				if (null != payItemPrice) {
//					list.add(payItemPrice);
//				}
//			}
//
//			String[] contractIds = req.getContractIdList().split(",");
//			for (String id : contractIds) {
//				CommunityContractEntity contract = communityContractDao.get(Integer.valueOf(id));
//				if (null != contract) {
//					for (CommunityPayItemsPriceEntity payItemsPrice : list) {
//						if (!payItemsPrice.getContractList().contains(contract)) {
//							contract.getPayItemsPriceList().add(payItemsPrice);
//						}
//					}
//				}
//			}
//
//			res.setRet(ResponseContext.RES_SUCCESS_CODE);
//			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		} else {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//		}
		return res;
	}
	
	@Override
	@Audit(operate = "合同解绑收费价格")
	public IResponse contractUnbindingPayItemsPrice(CommunityPayItemsPriceReq req) {
		GenericResponse res = new GenericResponse();
//		if (StringUtils.isNotEmpty(req.getPayItemsPriceIdList()) && StringUtils.isNotEmpty(req.getContractIdList())) {
//			List<CommunityPayItemsPriceEntity> list = new ArrayList<CommunityPayItemsPriceEntity>();
//			String[] payItemPriceIds = req.getPayItemsPriceIdList().split(",");
//			for (String payItemPriceId : payItemPriceIds) {
//				CommunityPayItemsPriceEntity payItemPrice = communityPayItemsPriceDao.get(Integer.parseInt(payItemPriceId));
//				if (null != payItemPrice) {
//					list.add(payItemPrice);
//				}
//			}
//
//			String[] contractIds = req.getContractIdList().split(",");
//			for (String id : contractIds) {
//				CommunityContractEntity contract = communityContractDao.get(Integer.valueOf(id));
//				if (null != contract) {
//					for (CommunityPayItemsPriceEntity payItemsPrice : list) {
//						if (payItemsPrice.getContractList().contains(contract)) {
//							contract.getPayItemsPriceList().remove(payItemsPrice);
//						}
//					}
//				}
//			}
//
//			res.setRet(ResponseContext.RES_SUCCESS_CODE);
//			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//
//		} else {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//		}

		return res;
	}


	@Override
	public IResponse getCommunityPayItemsPriceInfo(CommunityPayItemsPriceReq req) {
		
		return null;
	}

}
