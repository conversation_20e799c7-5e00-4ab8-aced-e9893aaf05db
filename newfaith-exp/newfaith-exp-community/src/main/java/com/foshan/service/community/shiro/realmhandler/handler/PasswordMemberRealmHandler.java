package com.foshan.service.community.shiro.realmhandler.handler;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authc.AccountException;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.CredentialsException;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.LockedAccountException;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.authc.pam.UnsupportedTokenException;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.springframework.stereotype.Component;

import com.foshan.entity.RoleEntity;
import com.foshan.entity.community.CommunityMemberEntity;
import com.foshan.entity.community.CommunityRoleEntity;
import com.foshan.form.community.CommunityRoleForm;
import com.foshan.form.community.request.CommunityMemberLoginReq;
import com.foshan.form.community.response.communityMember.CommunityMemberLoginRes;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.community.shiro.PhoneToken;
import com.foshan.service.community.shiro.realmhandler.AbstractMemberRealmHandler;
import com.foshan.service.community.shiro.realmhandler.RealmHandlerType;
import com.foshan.service.permission.shiro.ShiroHazelcastCache;
import com.foshan.util.DigestUtil;

/**
 * 党员密码登录鉴权处理
 * <AUTHOR>
 *
 */
@Component
@RealmHandlerType("1")
public class PasswordMemberRealmHandler extends AbstractMemberRealmHandler {

	/*
	 * 用户密码方式登陆的身份校验，查询相关的用户信息（此处用phone作为用户的id存于principals[0]，password作为密码），并通过SimpleAuthenticationInfo返回，shiro框架通过SimpleAuthenticationInfo中principals[0]做为id及其他传递的数据进行校验。
	 * 此处限制用户登陆错误次数，错误次数存在缓存
	 * 返回用户信息数据前将部分用户信息存于principals[1]
	 */
	@Override
	public SimpleAuthenticationInfo handleAuthenticationInfo(AuthenticationToken token, String realmName) {
		String phone = (String) token.getPrincipal();
		PhoneToken phoneToken = (PhoneToken) token;

		if (StringUtils.isNotEmpty(phone)) {

			String hql = "from CommunityMemberEntity m where "+(phoneToken.getAccountType()==0?"m.phone='":"m.loginName='") + phone + 
					"' and phoneVerifyState = 1 and userState = 1";
			CommunityMemberEntity member = communityMemberDao.getUniqueByHql(hql);
			if(null != member){
				ConcurrentHashMap<String, Object> cacheMap = ShiroHazelcastCache.shiroCache.get(phone);
				if(null == cacheMap){
					throw new CredentialsException();
				}
				Integer limit = (Integer) cacheMap.get("limit");
				if(null == limit){
					throw new CredentialsException();
				}
				limit = limit + 1;
				if (limit > contextInfo.getAuthLimit()) {
		            throw new LockedAccountException("用户" + phone + "尝试登陆次数超过" + contextInfo.getAuthLimit() + "次"
		            );
		        }
		        cacheMap.put("limit", limit);
				String password = member.getPassword();
				List<Object> principals = new ArrayList<Object>();
				principals.add(member.getPhone());
				CommunityMemberEntity shiroMember = new CommunityMemberEntity();
				shiroMember.setId(member.getId());
				shiroMember.setPhone(member.getPhone());
				shiroMember.setWeixin(member.getWeixin());
				shiroMember.setMiniProgramOpenId(member.getMiniProgramOpenId());
				shiroMember.setPassword(member.getPassword());
				shiroMember.setLoginName(StringUtils.isNotEmpty(member.getLoginName()) ? member.getLoginName() : "");
				shiroMember.setRegionCode(StringUtils.isNotEmpty(member.getRegionCode()) ? member.getRegionCode() : "");
				shiroMember.setUserName(StringUtils.isNotEmpty(member.getUserName()) ? member.getUserName() : "");
				shiroMember.setCommunityRoleList(member.getCommunityRoleList());
				principals.add(shiroMember);
				if (null != password) {
					return new SimpleAuthenticationInfo(principals, password, realmName);
				}
			}else{
				throw new UnknownAccountException();//没找到帐号
			}
		}

		return null;
	}

	/*
	 * 处理用户密码方式登陆逻辑，默认设置remember me 为true。
	 * 如果用户是首次登录，则创建缓存，设置登陆错误数=0。
	 * 非首次登陆，则登陆错误数+1
	 */
	@SuppressWarnings("unchecked")
	@Override
	public IResponse memberLogin(CommunityMemberLoginReq req, HttpServletRequest request, Subject curUser) {
		CommunityMemberLoginRes res = new CommunityMemberLoginRes();
		Integer accountType=0;
		if(StringUtils.isNotEmpty(req.getLoginName())) {
			req.setPhone(req.getLoginName());
			accountType=1;
		}
		
		PhoneToken token = null;
		try {
			token = new PhoneToken(req.getPhone(), DigestUtil.sm3Digest(req.getPassword()), req.getType());
			token.setAccountType(accountType);
		} catch (UnsupportedEncodingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		token.setRememberMe(true);

		// 密码方式判断cache中是否有该用户的缓存，没有则创建
		ConcurrentHashMap<String, Object> cacheMap = ShiroHazelcastCache.shiroCache.get(req.getPhone());

		// 首次登录的情况。发送过短信或曾经登录过则只修改登录方式。登录数据保存在cache中
		if (null == cacheMap) {
			cacheMap = new ConcurrentHashMap<String, Object>();
			Integer limit = 0;
			cacheMap.put("limit", limit);
			ShiroHazelcastCache.shiroCache.put(req.getPhone(), cacheMap);
		}
		Integer limit = (Integer) cacheMap.get("limit");
		if(null == limit){
			limit = 0;
			cacheMap.put("limit", limit);
			ShiroHazelcastCache.shiroCache.put(req.getPhone(), cacheMap);
		}
		
		try {
			curUser.login(token);
			curUser.getSession().setTimeout(1800000);// 180000毫秒/30分钟超时
			PrincipalCollection principals = curUser.getPrincipals();
			List<Object> principalList = principals.asList();
			CommunityMemberEntity principal = (CommunityMemberEntity) principalList.get(1);
			res.setMemberId(principal.getId());
			res.setPhone(StringUtils.isNotEmpty(principal.getPhone()) ? principal.getPhone() : "");
			res.setWeixin(principal.getWeixin());
			res.setSmartcardId(principal.getSmartcardId());
			res.setLoginName(StringUtils.isNotEmpty(principal.getLoginName()) ? principal.getLoginName() : "");
			res.setRegionCode(StringUtils.isNotEmpty(principal.getRegionCode()) ? principal.getRegionCode() : "");
			List<RoleEntity> communityRoleList = principal.getCommunityRoleList();
			List<CommunityRoleForm> communityRoleFormList = new ArrayList<CommunityRoleForm>();
			communityRoleList.forEach(o->{
				CommunityRoleForm r = new CommunityRoleForm();
				r.setRoleId(o.getId());
				r.setRoleName(o.getRoleName());
				r.setDisplayName(o.getDisplayName());
				communityRoleFormList.add(r);
			});
			res.setLoginName(StringUtils.isNotEmpty(principal.getLoginName()) ? principal.getLoginName() : "");
			res.setUserName(StringUtils.isNotEmpty(principal.getUserName()) ? principal.getUserName() : "");
			res.setCommunityRoleList(communityRoleFormList);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			//res.setCommunityId(principal.getCommunity().getId());
			res.setUserName(StringUtils.isNotEmpty(principal.getUserName()) ? principal.getUserName() : "");
			//res.setCommunityName(principal.getCommunity().getCommunityName());
			// 成功登陆后清除cache缓存
			cacheMap.remove("limit");
			ShiroHazelcastCache.shiroCache.put(req.getPhone(), cacheMap);
			return res;
		} catch (UnknownAccountException e0) {// 账号不存在
			res.setRet(ResponseContext.RES_PERM_NONEXISTENT_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
			return res;
		} catch (IncorrectCredentialsException e1) {// 账号/密码错误
			res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO );
			return res;
		} catch (LockedAccountException e2) {// 账号被锁定
			res.setRet(ResponseContext.RES_PERM_LOCK_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO + "账号被锁定");
			return res;
		} catch (AccountException e3) {// 账号异常
			res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO );
			return res;
		} catch (CredentialsException e4) {// 凭证异常
			res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO );
			return res;
		} catch (UnsupportedTokenException e5) {// 不支持的token异常
			res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO );
			return res;
		} catch (AuthenticationException e6) {// 未知登录异常
			res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO );
			return res;
		}		
	}
}
