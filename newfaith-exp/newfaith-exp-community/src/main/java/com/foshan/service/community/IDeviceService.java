package com.foshan.service.community;

import com.foshan.form.community.request.DeviceReq;
import com.foshan.form.response.IResponse;

public interface IDeviceService {
    public IResponse getDeviceList(DeviceReq req);
	public IResponse addDevice(DeviceReq req);
	public IResponse modifyDevice(DeviceReq req);
	public IResponse deleteDevice(DeviceReq req);
	public IResponse getDeviceInfo(DeviceReq req);
	public IResponse updateCameraDevice();
	public IResponse bindingDeviceAndMember(DeviceReq req);
	public IResponse unbindingDeviceAndMember(DeviceReq req);
}

