package com.foshan.service.community.impl;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityPaymentRecordEntity;
import com.foshan.entity.community.CommunityReceiptEntity;
import com.foshan.entity.community.CommunityReceiptReceivablesEntity;
import com.foshan.entity.community.CommunityReceivablesChangesEntity;
import com.foshan.entity.community.CommunityReceivablesEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityRefundForm;
import com.foshan.form.community.request.CommunityRefundNotifyReq;
import com.foshan.form.community.request.CommunityRefundReq;
import com.foshan.form.community.response.communityRefund.AddCommunityRefundRes;
import com.foshan.form.community.response.communityRefund.GetCommunityRefundInfoRes;
import com.foshan.form.community.response.communityRefund.GetCommunityRefundListRes;
import com.foshan.form.community.response.communityRefund.ModifyCommunityRefundRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityRefundService;
import com.foshan.util.CodeUtil;
import com.foshan.util.DateUtil;
import com.foshan.util.DigestUtil;
import com.foshan.util.HttpClientUtil;

@Transactional
@Service("communityRefundService")
public class CommunityRefundServiceImpl extends GenericCommunityService implements ICommunityRefundService {
	private final static Logger logger = LoggerFactory.getLogger(CommunityRefundServiceImpl.class);
    //测试
	@Override
	public IResponse getCommunityRefundList(CommunityRefundReq req) {
		GetCommunityRefundListRes res = new GetCommunityRefundListRes();
		Page<CommunityReceivablesChangesEntity> page = new Page<CommunityReceivablesChangesEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder(
			    "select distinct a from CommunityReceivablesChangesEntity a ");
			  
		if(req.getReceiptId() != null) {
			hql.append("inner join a.receipt r where r.id = " + req.getReceiptId().intValue());
		}else {
			hql.append("where 1=1 ");
		}
		hql.append(" and a.changeType = 2 ORDER BY a.id desc");
		page = communityReceivablesChangesDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityRefundForm communityRefundForm = new CommunityRefundForm();
			communityRefundForm.setCommunityRefundId(o.getId());
			communityRefundForm
					.setCreateTime(null != o.getCreateTime() ? DateUtil.formatLongFormat(o.getCreateTime()) : "");
			communityRefundForm.setLastModifyTime(
					null != o.getLastModifyTime() ? DateUtil.formatLongFormat(o.getLastModifyTime()) : "");
			communityRefundForm.setState(o.getState());
			communityRefundForm.setAccount(o.getRefundAccount());
			communityRefundForm.setAgent(o.getRecorder());
			communityRefundForm.setAmount(null != o.getChangeAmount() ? o.getChangeAmount().toString() : "");
			communityRefundForm.setBank(o.getRefundBank());
			communityRefundForm.setComment(o.getComment());
			communityRefundForm.setFailReason(o.getRefundFailReason());
			communityRefundForm.setOutTradeNo(o.getRefundOutTradeNo());
			communityRefundForm.setPayee(o.getRefundPayee());
			communityRefundForm.setRefundMethod(o.getRefundMethod());
			communityRefundForm.setRefundMethodName(o.getRefundMethodName());
			communityRefundForm.setRefundCode(o.getRefundCode());
			communityRefundForm
					.setRefundDate(null != o.getChangeDate() ? DateUtil.formatLongFormat(o.getChangeDate()) : "");
			communityRefundForm.setSupervisor(o.getApprover());
			communityRefundForm.setReceiptId(o.getReceipt() != null ? o.getReceipt().getId() : null);

			res.getCommunityRefundList().add(communityRefundForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	@Audit(operate = "新增应收款变更（减加）")
	public IResponse addCommunityRefund(CommunityRefundReq req) {
		AddCommunityRefundRes res = new AddCommunityRefundRes();
		// 验证输入
		if (req.getReceiptId() == null) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "收据ID不能为空！");
			return res;
		}
		if (StringUtils.isEmpty(req.getAmount())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "退款金额不能为空！");
			return res;
		}
		if (new BigDecimal(req.getAmount()).compareTo(BigDecimal.ZERO) <= 0) {
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "退款金额应大于0！");
			return res;
		}
//		if (StringUtils.isEmpty(req.getComment())) {
//			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
//			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "退款原因不能为空！");
//			return res;
//		}
		if (req.getRefundMethod() == null) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_CODE + "退款方式不能为空！");
			return res;
		}

		// 判断退款金额合理
		CommunityReceiptEntity communityReceiptEntity = communityReceiptDao.get(req.getReceiptId());
		if (communityReceiptEntity == null) {
			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "找不到对应的收据信息！");
			return res;
		}
		BigDecimal refundAmount = new BigDecimal(req.getAmount());
		BigDecimal paidAmount = BigDecimal.ZERO;
		BigDecimal refundedAmount = BigDecimal.ZERO;
		String paymentSessionSn = null;
		// 计算金额
		for (CommunityPaymentRecordEntity paymentRecord : communityReceiptEntity.getPaymentRecordList()) {
			if (paymentRecord.getStatus().intValue() == 2) {
				paymentSessionSn = paymentRecord.getPaymentSessionSn();
			}
		}
		for (CommunityReceiptReceivablesEntity receiptReceivables : communityReceiptEntity
				.getReceiptReceivablesList()) {
			paidAmount = paidAmount.add(receiptReceivables.getCurrentAmount());
		}

		// 计算已经退款金额
		for (CommunityReceivablesChangesEntity refund : communityReceiptEntity.getRefundList()) {
			if (refund.getRefundStatus().intValue() == 2) {
				refundedAmount = refundedAmount.add(refund.getChangeAmount());
			}
		}

		if (paidAmount.compareTo(BigDecimal.ZERO) == 0) {
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "找不到对应的实收记录！");
			return res;
		}

		if (refundedAmount.compareTo(paidAmount) >= 0) {
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "该项目金额已经全部退还！");
			return res;
		}

		if (refundAmount.compareTo(paidAmount.subtract(refundedAmount)) > 0) {
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo(
					ResponseContext.RES_INVALID_PARAM_INFO + "该项目可退款金额只有" + paidAmount.subtract(refundedAmount) + "元！");
			return res;
		}

		if (req.getRefundMethod().intValue() == 0) {
			// 现金退款
			if (StringUtils.isEmpty(req.getPayee())) {
				res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
				res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "退款人不能为空！");
				return res;
			}

		} else if (req.getRefundMethod().intValue() == 1) {
			if (StringUtils.isEmpty(req.getAccount())) {
				res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
				res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "退款目标账号不能为空！");
				return res;
			}
		}

		CommunityReceivablesChangesEntity createRefundRecord = createRefundRecord(req);
		createRefundRecord.setEstate(communityReceiptEntity.getEstate());
		if (req.getRefundMethod().intValue() == 3) {
			// 在线退款
			if (paymentSessionSn == null) {
				res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
				res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "不是在线支付项目不能发起在线退款！");
				return res;
			}

			// 构造退款请求
			Map<String, String> parameterMap = new TreeMap<String, String>();
			parameterMap.put("amount", req.getAmount());
			parameterMap.put("paymentSessionSn", paymentSessionSn);
			parameterMap.put("clientTradeNo", createRefundRecord.getRefundCode());

			try {

				if (StringUtils.isNotEmpty(req.getComment()))
					parameterMap.put("refundReason", URLEncoder.encode(req.getComment(), "UTF-8"));

			} catch (UnsupportedEncodingException e1) {
				e1.printStackTrace();
				throw new RuntimeException(e1);
			}
			parameterMap.put("notifyUrl", communityContextInfo.getRefundNotifyUrl());

			// 增加签名字段
			try {
				DigestUtil.fillSignatureParam(parameterMap, communityContextInfo.getPaymentMerchantCode(),
						communityContextInfo.getPaymentSecret());
			} catch (Exception e1) {
				res.setRet(ResponseContext.RES_PAYMENT_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_PAYMENT_ERROR_INFO + "构造在线退款请求签名错误！");
				e1.printStackTrace();
				return res;
			}

			String jsonstr = JSONObject.valueToString(parameterMap);
			String postResult;
			// 发起退款
			try {
				postResult = HttpClientUtil.jsonPost(communityContextInfo.getRefundServiceUrl(), "UTF-8", jsonstr,
						null);
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
			if (postResult.toUpperCase().contains("SUCCESS")) {
				ObjectMapper mapper = new ObjectMapper();
				createRefundRecord.setRefundStatus(1);
				try {
					@SuppressWarnings("unchecked")
					Map<String, Object> jsonObj = mapper.readValue(postResult, TreeMap.class);
					createRefundRecord.setPaymentRefundCode(jsonObj.get("paymentRefundCode") == null ? null
							: jsonObj.get("paymentRefundCode").toString());
					res.setPaymentRefundCode(createRefundRecord.getPaymentRefundCode());

				} catch (Exception e) {
					res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
					res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "发起退款失败！" + e.getMessage());
					logger.info("发起退款失败!:{}", e);
					return res;
				}

			} else {
				res.setRet(ResponseContext.RES_PAYMENT_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_PAYMENT_ERROR_INFO);

				logger.info("在线退款出错！:{}", postResult);
				return res;
			}
		} else {
			// 现金或银行转账
			if(communityReceiptEntity.getFeeType() == 1) {
				// 假定只有一次性收费可以退款，且收据只对应一条应收
				CommunityReceivablesEntity receivables = communityReceiptEntity.getReceiptReceivablesList().get(0).getReceivables();
				createRefundRecord.setReceivables(receivables);
				createRefundRecord.setPayItemsName(receivables.getPayItemsName());
			}
			createRefundRecord.setRefundStatus(2);
		}

		communityReceivablesChangesDao.save(createRefundRecord);

		// 创建退款记录
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		res.setRefundId(createRefundRecord.getId());

		return res;
	}

	private CommunityReceivablesChangesEntity createRefundRecord(CommunityRefundReq req) {
		CommunityReceivablesChangesEntity communityRefund = new CommunityReceivablesChangesEntity();

		communityRefund.setLastModifyTime(new Timestamp(new Date().getTime()));
		communityRefund.setState(EntityContext.RECORD_STATE_VALID);
		communityRefund.setRefundAccount(req.getAccount());
		communityRefund.setRecorder(req.getAgent());
		communityRefund
				.setChangeAmount(StringUtils.isNotEmpty(req.getAmount()) ? new BigDecimal(req.getAmount()) : null);
		communityRefund.setPaymentRefundCode(req.getPaymentRefundCode());
		communityRefund.setRefundBank(req.getBank());
		communityRefund.setComment(req.getComment());
		communityRefund.setRefundPayee(req.getPayee());
		communityRefund.setRefundMethod(req.getRefundMethod());
		communityRefund.setRefundMethodName(req.getRefundMethodName());
		communityRefund.setRefundCode(CodeUtil.getSNCode("CRF"));
		try {
			communityRefund.setChangeDate(
					StringUtils.isNotEmpty(req.getRefundDate()) ? DateUtil.parseLongFormat(req.getRefundDate()) : null);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		communityRefund.setRefundStatus(0);
		communityRefund.setChangeType(2);
		communityRefund.setApprover(req.getSupervisor());
		communityRefund.setReceipt(req.getReceiptId() != null ? communityReceiptDao.get(req.getReceiptId()) : null);

		return communityRefund;
	}

	@Override
	@Audit(operate = "修改应收款变更（减加）")
	public IResponse modifyCommunityRefund(CommunityRefundReq req) {
		ModifyCommunityRefundRes res = new ModifyCommunityRefundRes();
		if (null != req.getId()) {
			CommunityReceivablesChangesEntity communityRefund = communityReceivablesChangesDao.get(req.getId());
			if (null != communityRefund) {
				communityRefund.setLastModifyTime(new Timestamp(new Date().getTime()));
				communityRefund.setState(1);
				communityRefund.setRefundAccount(req.getAccount());
				communityRefund.setRecorder(req.getAgent());
				communityRefund.setChangeAmount(
						StringUtils.isNotEmpty(req.getAmount()) ? new BigDecimal(req.getAmount()) : null);
				communityRefund.setRefundBank(req.getBank());
				communityRefund.setComment(req.getComment());
				communityRefund.setRefundFailReason(req.getFailReason());
				communityRefund.setRefundOutTradeNo(req.getRefundOutTradeNo());
				communityRefund.setRefundPayee(req.getPayee());
				communityRefund.setRefundMethod(req.getRefundMethod());
				communityRefund.setRefundMethodName(req.getRefundMethodName());
				communityRefund.setRefundCode(req.getRefundCode());
				try {
					communityRefund.setChangeDate(
							StringUtils.isNotEmpty(req.getRefundDate()) ? DateUtil.parseLongFormat(req.getRefundDate())
									: null);
				} catch (ParseException e) {
					e.printStackTrace();
				}

				communityRefund.setApprover(req.getSupervisor());
				communityRefund
						.setReceipt(req.getReceiptId() != null ? communityReceiptDao.get(req.getReceiptId()) : null);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "删除应收款变更（减加）")
	public IResponse deleteCommunityRefund(CommunityRefundReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getId()) {
			CommunityReceivablesChangesEntity communityRefund = communityReceivablesChangesDao.get(req.getId());
			if (null != communityRefund) {
				communityReceivablesChangesDao.deleteById(req.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityRefundInfo(CommunityRefundReq req) {
		GetCommunityRefundInfoRes res = new GetCommunityRefundInfoRes();
		if (null != req.getId()) {
			CommunityReceivablesChangesEntity communityRefund = communityReceivablesChangesDao.get(req.getId());
			if (null != communityRefund) {
				CommunityRefundForm communityRefundForm = new CommunityRefundForm();
				communityRefundForm.setCommunityRefundId(communityRefund.getId());
				communityRefundForm.setCreateTime(null != communityRefund.getCreateTime()
						? DateUtil.formatLongFormat(communityRefund.getCreateTime())
						: "");
				communityRefundForm.setLastModifyTime(null != communityRefund.getLastModifyTime()
						? DateUtil.formatLongFormat(communityRefund.getLastModifyTime())
						: "");
				communityRefundForm.setState(communityRefund.getState());
				communityRefundForm.setAccount(communityRefund.getRefundAccount());
				communityRefundForm.setAgent(communityRefund.getRecorder());
				communityRefundForm.setAmount(
						null != communityRefund.getChangeAmount() ? communityRefund.getChangeAmount().toString() : "");
				communityRefundForm.setBank(communityRefund.getRefundBank());
				communityRefundForm.setComment(communityRefund.getComment());
				communityRefundForm.setFailReason(communityRefund.getRefundFailReason());
				communityRefundForm.setOutTradeNo(communityRefund.getRefundOutTradeNo());
				communityRefundForm.setPayee(communityRefund.getRefundPayee());
				communityRefundForm.setRefundMethod(communityRefund.getRefundMethod());
				communityRefundForm.setRefundMethodName(communityRefund.getRefundMethodName());
				communityRefundForm.setRefundCode(communityRefund.getRefundCode());
				communityRefundForm.setRefundDate(null != communityRefund.getChangeDate()
						? DateUtil.formatLongFormat(communityRefund.getChangeDate())
						: "");
				communityRefundForm.setSupervisor(communityRefund.getApprover());
				communityRefundForm.setReceiptId(
						communityRefund.getReceipt() != null ? communityRefund.getReceipt().getId() : null);
				res.setCommunityRefundForm(communityRefundForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse refundNotify(CommunityRefundNotifyReq req) {
		ModifyCommunityRefundRes res = new ModifyCommunityRefundRes();
		if (StringUtils.isEmpty(req.getReturnCode())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			return res;
		}
		if (StringUtils.isEmpty(req.getClientTradeNo()) && StringUtils.isEmpty(req.getPaymentRefundCode())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "ClientTradeNo和PaymentRefundCode不能同时为空");
			return res;
		}
		CommunityReceivablesChangesEntity communityRefund = null;
		if (StringUtils.isEmpty(req.getClientTradeNo())) {
			communityRefund = communityReceivablesChangesDao.get(req.getClientTradeNo());
		} else {
			String hql = "select r from CommunityReceivablesChangesEntity r where r.paymentRefundCode = :paymentRefundCode";
			Map<String, Object> paramsMap = new HashMap<String, Object>();
			paramsMap.put("paymentRefundCode", req.getPaymentRefundCode());
			communityRefund = communityReceivablesChangesDao.findUnique(hql, paramsMap);
		}
		if (communityRefund == null) {
			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "找不到对应的退款记录");
			logger.info("找不到编号为[{}]的退款记录或找不到编号为[{}]支付模块的退款记录！", req.getClientTradeNo(), req.getPaymentRefundCode());
			return res;
		}

		// 验收报文签名
		Map<String, String> reqMap = DigestUtil.jsonObjToMap(req);
		boolean signatureValid = false;
		try {
			signatureValid = DigestUtil.isSignatureValid(reqMap, communityContextInfo.getPaymentSecret(), "SM3");
		} catch (Exception e) {
			logger.error("[{}]验证支付模块的退款结果通知消息签名时发生异常！{}", req.getClientTradeNo(), e);
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "验证支付模块的退款结果通知消息签名时发生异常！");
			return res;
		}

		if (signatureValid == false) {
			logger.error("[{}]验证支付模块的退款结果通知消息签名失败！", req.getClientTradeNo());
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "验证支付模块的退款结果通知消息签名失败！");
			return res;
		}

		if (req.getReturnCode().equals("SUCCESS")) {
			if (StringUtils.isEmpty(req.getAmount())) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_CODE + "退款金额不能为空！");
				logger.info("退款金额不能为空！");
				return res;
			}

			BigDecimal refundAmount = new BigDecimal(req.getAmount());
			// 微信支付的金额单位是分，转成元
			refundAmount = refundAmount.divide(new BigDecimal("100"));
			if (refundAmount.compareTo(communityRefund.getChangeAmount()) != 0) {
				// 申请退款金额和申请退款金额不等
				communityRefund.setComment(communityRefund.getComment() + "[{" + DateUtil.formatLongFormat(new Date())
						+ "}]申请退款金额：" + communityRefund.getChangeAmount() + "元，实际退款金额：" + refundAmount + "元");
			}
			communityRefund.setChangeAmount(refundAmount);
			communityRefund.setChangeDate(new Date());
			communityRefund.setRefundOutTradeNo(req.getOutTradeNo());
			communityRefund.setPaymentRefundCode(req.getPaymentRefundCode());
			communityRefund.setLastModifyTime(new Timestamp(new Date().getTime()));
			communityRefund.setRefundStatus(2);

		} else {
			communityRefund.setRefundFailReason(req.getFailReason());
			communityRefund.setChangeDate(new Date());
			communityRefund.setRefundOutTradeNo(req.getOutTradeNo());
			communityRefund.setPaymentRefundCode(req.getPaymentRefundCode());
			communityRefund.setLastModifyTime(new Timestamp(new Date().getTime()));
			communityRefund.setRefundStatus(3);
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

}