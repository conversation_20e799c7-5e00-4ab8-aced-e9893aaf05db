package com.foshan.service.community.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.entity.community.CommunityBankDepositRecordEntity;
import com.foshan.entity.community.CommunityBuildingEntity;
import com.foshan.entity.community.CommunityDistrictEntity;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityMemberEntity;
import com.foshan.entity.community.CommunityMemberPropertyEntity;
import com.foshan.entity.community.CommunityMeterAttributesEntity;
import com.foshan.entity.community.CommunityMeterEntity;
import com.foshan.entity.community.CommunityMeterFormulaEntity;
import com.foshan.entity.community.CommunityMeterRecordEntity;
import com.foshan.entity.community.CommunityPayItemsEntity;
import com.foshan.entity.community.vo.ReservedFieldVo;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.request.ImportExcelReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.IImportExcelService;
import com.foshan.util.CodeUtil;
import com.foshan.util.DateUtil;
import com.foshan.util.DigestUtil;
import com.foshan.util.community.CommunityCache;

@Transactional
@Service("importExcelService")
public class ImportExcelServiceImpl extends GenericCommunityService implements IImportExcelService {
	private final static Logger logger = LoggerFactory.getLogger(ImportExcelServiceImpl.class);

	@Audit(operate = "导入单元信息")
	public IResponse importUnitInfo(HttpServletRequest request,ImportExcelReq req) {
		try {
			GenericResponse res  = unitInfoHandler(uploadFile(request),req.getAutoCreateUnit(),req.getDistrictId());
			return res;
		} catch (IllegalStateException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	
	@SuppressWarnings({ "resource", "unused", "unchecked" })
	private GenericResponse unitInfoHandler(File excelFile,Integer isAutogeneration,Integer districtId) throws IOException {
		GenericResponse res  = new GenericResponse();
		FileInputStream inputStream = new FileInputStream(excelFile);
		Workbook workBook = null;
		if (excelFile.getName().endsWith("xls")) {
			workBook = new HSSFWorkbook(inputStream);
		} else if (excelFile.getName().endsWith("xlsx")) {
			workBook = new XSSFWorkbook(inputStream);
		} else {
			logger.info(excelFile + "不是excel类型文件！！！");
			return null;
		}
		Object userObj = getPrincipal(false);

		Sheet sheet = workBook.getSheetAt(workBook.getActiveSheetIndex());
		StringBuilder info = new StringBuilder();
		int[] resultInt = new int[] { 0 };
		Map<String,Object> map =  new HashMap<String,Object>();
		sheet.forEach(o -> {
			if (o.getRowNum() > 0) {	
				getObjectData(map,o,districtId,info);
			}
		});
    	for (Iterator<String> it =  map.keySet().iterator();it.hasNext();){
    		String key = it.next();
    		Map<String,CommunityEstateEntity> estateMap = (Map<String, CommunityEstateEntity>) map.get(key);
    		List<CommunityEstateEntity> list = new ArrayList<CommunityEstateEntity>();
    		String floor = "";
    		int layers = 0;
    		boolean stute = true;
        	for (Iterator<String> estateIt =  estateMap.keySet().iterator();estateIt.hasNext();){
        		String estateKey = estateIt.next();
        		CommunityEstateEntity estate = estateMap.get(estateKey);
        		if(stute) {
        			floor = estate.getFloor()+"";
        			layers = estate.getBuilding().getLayers();
        			stute = false;
        		}
        		
        		if(null!=isAutogeneration && isAutogeneration==1) {
        			if(floor.equals(estate.getFloor().toString())) {
            			communityEstateDao.save(estate);
            			estate.getMemberPropertyList().forEach(o->{
            				communityMemberPropertyDao.saveOrUpdate(o);
            			});
            			list.add(estate);
        			}else {
        				break;
        			}
        		}else {
        			communityEstateDao.save(estate);
        			estate.getMemberPropertyList().forEach(o->{
        				communityMemberPropertyDao.saveOrUpdate(o);
        			});
        		}
        	}
        	if(null!=isAutogeneration && isAutogeneration==1) {//自动生成单元
        		for(int i=Integer.valueOf(floor)+1;i<=layers;i++) {
        			for(int j=0;j<list.size();j++) {
            			CommunityEstateEntity estate = null;
            			String unitCode = "";
            			String[] unitCodeArray = list.get(j).getUnitCode().split("-");
            			for(int u=0; u<unitCodeArray.length;u++) {//接装unitCode
            				if(u == unitCodeArray.length-1) {
            					String num = i+"";
            					unitCode = unitCode +"-"+(num.length()<2 ? "0"+num : num)+
            							unitCodeArray[u].substring(unitCodeArray[u].length()-2, unitCodeArray[u].length());
            				}else {
            					unitCode = StringUtils.isNotEmpty(unitCode) ? (unitCode+"-"+unitCodeArray[u]) : unitCodeArray[u];
            				}
            			}
            			if(estateMap.containsKey(unitCode)) {
            				estate = estateMap.get(unitCode);
            				communityEstateDao.save(estate);
            				estate.getMemberPropertyList().forEach(o->{
                				communityMemberPropertyDao.saveOrUpdate(o);
                			});
            			}else {
            				estate = new CommunityEstateEntity();
                			BeanUtils.copyProperties(list.get(j), estate);
                			estate.setId(null);
                			estate.setMemberPropertyList(null);
                			estate.setFloor(i);
                			estate.setUnitCode(unitCode);
                			//estate.setBankDepositPayItemsList(null);
                			estate.setBankDepositRecordList(null);
                			estate.setEventsList(null);
                			estate.setMeterList(null);
                			estate.setPayItemsList(null);
                			estate.setSubMemberPropertyList(null);
                			estate.setSpecialFeeFlag(0);
                			estate.setUserList(null);
                			estate.setEstateState(6);
            	            estate.setReservedField(mapper.writeValueAsString(new ReservedFieldVo()));
            	            estate.setAdditionalArea(new BigDecimal(0));
            	            estate.setSpecialFeeFlag(0);
            	            estate.setMeasuresList(new ArrayList<>());
            	            communityEstateDao.save(estate);
        					if (CommunityCache.estateList.containsKey(estate.getId())) {
        						CommunityCache.estateList.remove(estate.getId());
        					} else {
        						CommunityCache.estateList.put(estate.getId(), estate);
        					}
            			}
            		}
        		}
        	}
    	}

		res.setRetInfo(StringUtils.isNotEmpty(info.toString()) ? info.toString() : ResponseContext.RES_SUCCESS_CODE_INFO);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);

		return res;
	}
	
	
	@SuppressWarnings("unchecked")
	public Map<String,Object> getObjectData(Map<String,Object> map,Row o,Integer districtId,StringBuilder info) {

		Map<String,CommunityEstateEntity> estateMap = null;
		boolean uniqueStatus = true;
		String buildingName = cell2Str(o.getCell(0, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
		CommunityBuildingEntity building = communityBuildingDao.findUnique("select a from CommunityBuildingEntity a where a.buildingName='"
				+buildingName+"' and a.district.id="+districtId,"");
		if(null != building) {
			if(map.containsKey(building.getBuildingCode())) {
				estateMap = (Map<String, CommunityEstateEntity>) map.get(building.getBuildingCode());
			}else {
				estateMap = new  LinkedHashMap<String,CommunityEstateEntity>();
			}
			
			String unitCode = cell2Str(o.getCell(1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
			if(StringUtils.isNotEmpty(unitCode)){
				CommunityEstateEntity estate = communityEstateDao.findUnique(
						"select a from CommunityEstateEntity a where a.unitCode='"+unitCode+"' ", "");
				if(null == estate) {
					estate = new CommunityEstateEntity();
				}
	            try {
					estate.setReservedField(mapper.writeValueAsString(new ReservedFieldVo()));
				} catch (JsonProcessingException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				estate.setUnitCode(unitCode);
				String floor = cell2Str(o.getCell(2, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
				if(StringUtils.isNotEmpty(floor)) {
					estate.setFloor(StringUtils.isNotEmpty(floor.split("\\.")[0]) ? Integer.valueOf(floor.split("\\.")[0]):null);
				}
				estate.setRoomNumber(cell2Str(o.getCell(3, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
				String estateState = cell2Str(o.getCell(4, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
	            switch(estateState) {
	            case"未收楼":estate.setEstateState(0);break;
	            case"已入住":estate.setEstateState(1);break;
	            case"装修中":estate.setEstateState(2);break;
	            case"出租":estate.setEstateState(3);break;
	            case"离退":estate.setEstateState(4);break;
	            case"自住":estate.setEstateState(5);break;
	            case"空置":estate.setEstateState(6);break;
	            default:break;
	            }
	            String estateBuildingArea = cell2Str(o.getCell(5, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
	            estate.setBuildingArea(StringUtils.isNotEmpty(estateBuildingArea) ?  new BigDecimal(estateBuildingArea) : null);
	            String usableArea = cell2Str(o.getCell(6, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
	            estate.setUsableArea(StringUtils.isNotEmpty(usableArea) ?  new BigDecimal(usableArea) : null);
	            String chargingArea = cell2Str(o.getCell(7, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
	            estate.setChargingArea(StringUtils.isNotEmpty(chargingArea) ?  new BigDecimal(chargingArea) : null);
	            String additionalArea = cell2Str(o.getCell(8, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
	            estate.setAdditionalArea(StringUtils.isNotEmpty(additionalArea) ?  new BigDecimal(additionalArea) : new BigDecimal(0));
//	            estate.setUsageTerm(cell2Str(o.getCell(9, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
//				String isAcceptanceReceive = cell2Str(o.getCell(10, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
//	            switch(isAcceptanceReceive) {
//	            case"否":estate.setIsAcceptanceReceive(0);break;
//	            case"是":estate.setIsAcceptanceReceive(1);break;
//	            default:break;
//	            }
	            estate.setSpecialFeeFlag(0);
	            estate.setDecoration(cell2Str(o.getCell(9, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
	            estate.setEstateType(cell2Str(o.getCell(10, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
//	            Date acceptanceDate = o.getCell(13, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK).getDateCellValue();
//	            estate.setAcceptanceDate(acceptanceDate);   
	            estate.setComment(cell2Str(o.getCell(11, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
//	            String specialFeeFlag  = cell2Str(o.getCell(14, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
//	            switch(specialFeeFlag) {
//	            case"正常计费":estate.setSpecialFeeFlag(0);break;
//	            case"不计管理费":estate.setSpecialFeeFlag(1);break;
//	            case"不计分摊费":estate.setSpecialFeeFlag(2);break;
//	            case"不计违约金":estate.setSpecialFeeFlag(3);break;
//	            case"不计租金":estate.setSpecialFeeFlag(5);break;
//	            case"全部不计":estate.setSpecialFeeFlag(4);break;
//	            default:estate.setSpecialFeeFlag(0);break;
//	            }
	            
	            estate.setBuilding(building);
	            estate.setState(EntityContext.RECORD_STATE_VALID);
	            estate.setSpecialAllocationFlag(0);

	            String userName = cell2Str(o.getCell(12, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)).replaceAll("\r\n", " ");
	            if(userName.startsWith(" ")) {
	            	userName = userName.substring(1, userName.length());
	            } 
	            if(userName.endsWith(" ")) {
	            	userName = userName.substring(0, userName.length()-1);
	            }
				if(StringUtils.isNotEmpty(userName)){
					String homePhone = cell2Str(o.getCell(13, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
					homePhone = homePhone.replaceAll(",", "|").replaceAll("，", "|").
							replaceAll("\\\\", "|").replaceAll("、", "|").
							replaceAll("/", "|").replaceAll(" ", "|");
					String phone = "";
					if(homePhone.contains("|")) {
						String[] phones = homePhone.split("\\|");
						for(String p : phones) {
							if(p.length()==11) {
								phone = p;
								break;
							}else if(p.length()>0) {
								phone=p;
							}
						}
					}else {
						phone = homePhone;
					}

					CommunityMemberEntity member = null;
					List<CommunityMemberEntity> memberList = communityMemberDao.getListByHql(
							"select a from CommunityMemberEntity a where a.phone='"+phone+"'", "");
					if(null!=memberList && memberList.size()==1) {
						member = memberList.get(0);
					}else if(null!=memberList && memberList.size()>1) {
						uniqueStatus = false;
						info.append(estate.getUnitCode()+"单元的手机号（"+phone+"）有重复;");
					}
					if(uniqueStatus) {
						String idCard = cell2Str(o.getCell(20, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)).replaceAll("\r\n", " ");
			            if(idCard.startsWith(" ")) {
			            	idCard = idCard.substring(1, idCard.length());
			            } 
			            if(idCard.endsWith(" ")) {
			            	idCard = idCard.substring(0, idCard.length()-1);
			            }

						if(null == member) {
							member = new CommunityMemberEntity();
							member.setPassword("885a5baa6267ceccdc330420984498b12666ab816006dcd81ad077d4cbcc26fa");
							member.setLastPwdModifyTime(new Timestamp(new Date().getTime()));
							updateMember( member, phone, homePhone,userName, o, idCard, estate);
						}else {
				            String name = member.getUserName();
				            String card = member.getIdCard();
				            if(StringUtils.isNotEmpty(name)) {
				            	name=name.replaceAll("\r\n", " ");
					            if(name.startsWith(" ")) {
					            	name = idCard.substring(1, name.length());
					            } 
					            if(name.endsWith(" ")) {
					            	name = name.substring(0, name.length()-1);
					            }
				            }
				            if(StringUtils.isNotEmpty(card)) {
				            	card=card.replaceAll("\r\n", " ");
					            if(card.startsWith(" ")) {
					            	card = card.substring(1, card.length());
					            } 
					            if(card.endsWith(" ")) {
					            	card = card.substring(0, card.length()-1);
					            }
				            }
							if((StringUtils.isEmpty(member.getUserName()) && StringUtils.isEmpty(member.getIdCard()))
									|| (StringUtils.isNotEmpty(name)  && name.equals(userName))
									&&( StringUtils.isNotEmpty(card) && card.equals(idCard))) {
								updateMember( member, phone, homePhone,userName, o, idCard, estate);
							}else {
								info.append(estate.getUnitCode()+"单元的手机号（"+phone+"）已存在且业主姓名或身份号与导入的不相同，请核实后进行手工录入;");
							}
							
						}
					}
				}
				if (CommunityCache.estateList.containsKey(estate.getId())) {
					CommunityCache.estateList.remove(estate.getId());
				} else {
					CommunityCache.estateList.put(estate.getId(), estate);
				}
				if(uniqueStatus) {
					estateMap.put(estate.getUnitCode(), estate);
					map.put(building.getBuildingCode(),estateMap);
				}
			}
		}
		return map;
	}
	
	public void updateMember(CommunityMemberEntity member,String phone,String homePhone,
			String userName,Row o,String idCard,CommunityEstateEntity estate) {
		member.setPhone(phone);
		member.setHomePhone(homePhone);
		member.setPhoneVerifyState(1);
		member.setUserName(userName);
		member.setUserState(1);
		member.setMemberType(0);
        member.setHomeAddress(cell2Str(o.getCell(14, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
              
        member.setPoliceStation(cell2Str(o.getCell(15, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
        member.setPostalCode(cell2Str(o.getCell(16, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
        member.setNation(cell2Str(o.getCell(17, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
        member.setNativePlace(cell2Str(o.getCell(18, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
        
        String idType = cell2Str(o.getCell(19, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
        member.setIdType(idType);
        
        member.setIdCard(idCard);
        if(idType.equals("身份证") && StringUtils.isNotEmpty(idCard)) {
        	String card = "";
        	if(idCard.contains("、")) {
        		card = idCard.split("、")[0];
        	}else if(idCard.contains("\\")) {
        		card = idCard.split("\\\\")[0];
        	}else if(idCard.contains("/")) {
        		card = idCard.split("/")[0];
        	}else if(idCard.contains("|")) {
        		card = idCard.split("\\|")[0];
        	}else if(idCard.contains(".")) {
        		card = idCard.split("\\.")[0];
        	}else if(idCard.contains(",")) {
        		card = idCard.split(",")[0];
        	}else if(idCard.contains("，")) {
        		card = idCard.split("，")[0];
        	}else if(idCard.contains(" ")) {
        		card = idCard.split(" ")[0];
        	}else {
        		card = idCard;
        	}
        	
			String sex = getSex(card);
            switch(sex) {
            case"女":member.setSex(0);break;
            case"男":member.setSex(1);break;
            case"保密":member.setSex(2);break;
            default:break;
            }
            member.setBirthday(getBirthday(card));
        }
//        Date recordDate = o.getCell(25, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK).getDateCellValue();
        Date billingDate = o.getCell(21, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK).getDateCellValue();
        String buyersName = cell2Str(o.getCell(22, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
        String buyersAddress = cell2Str(o.getCell(23, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
        String comment = cell2Str(o.getCell(24, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
        
        communityMemberDao.saveOrUpdate(member);

        boolean inexistence = true; 
        if(null!=estate.getMemberPropertyList() && estate.getMemberPropertyList().size()>0){
        	for(CommunityMemberPropertyEntity memberProperty : estate.getMemberPropertyList()) {
      			if(memberProperty.getMemberType()==0 && 
      					memberProperty.getIsCurrentMember()==1&& 
      					!memberProperty.getMember().getId().toString().equals(member.getId().toString())) {
      				memberProperty.setIsCurrentOwner(0);
      				memberProperty.setTerminationDate(new Date());
      				memberProperty.setIsCurrentMember(0);
	                memberProperty.setBuyersAddress(buyersAddress);
	                memberProperty.setBuyersName(buyersName);
		            memberProperty.setBillingDate(billingDate);
		            memberProperty.setComment(comment);
    			}else if(memberProperty.getMember().getId().toString().equals(member.getId().toString())) {
    				inexistence = false;
    				memberProperty.setIsCurrentMember(1);
    				memberProperty.setTerminationDate(null);
    				memberProperty.setMemberType(0);
	                memberProperty.setBuyersAddress(buyersAddress);
	                memberProperty.setBuyersName(buyersName);
		            memberProperty.setBillingDate(billingDate);
		            memberProperty.setComment(comment);
    			}
        	}

        }
        if(inexistence){
        	CommunityMemberPropertyEntity memberProperty = new CommunityMemberPropertyEntity(); 
    		memberProperty.setMember(member);
    		memberProperty.setIsCurrentOwner(1);
            memberProperty.setAuditState(1);
            memberProperty.setMemberType(0);
            memberProperty.setIsCurrentMember(1);
            memberProperty.setBuyersAddress(buyersAddress);
            memberProperty.setBuyersName(buyersName);
			if(null!=billingDate) {
 				memberProperty.setBillingDate(billingDate);
    			//memberProperty.setBillingDateModify(new Timestamp(new Date().getTime()));
			}
            memberProperty.setComment(comment);
            memberProperty.setProperty(estate);
            memberProperty.setRecordDate(billingDate);
            communityMemberPropertyDao.save(memberProperty);
            estate.getMemberPropertyList().add(memberProperty);
		}
	}
	
	
	@Audit(operate = "导入楼栋信息")
	public IResponse importBuilding(HttpServletRequest request,ImportExcelReq req) {
		try {
			return buildingHandler(uploadFile(request),req.getDistrictId());
		} catch (IllegalStateException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	

	@SuppressWarnings({ "resource", "unused" })
	private GenericResponse buildingHandler(File excelFile,Integer districtId) throws IOException {
		GenericResponse res  = new GenericResponse();
		if(null == districtId) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			return res;
		}
		CommunityDistrictEntity district = communityDistrictDao.get(districtId);
		if(null == district) {
			res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			return res;
		}
		FileInputStream inputStream = new FileInputStream(excelFile);
		Workbook workBook = null;
		if (excelFile.getName().endsWith("xls")) {
			workBook = new HSSFWorkbook(inputStream);
		} else if (excelFile.getName().endsWith("xlsx")) {
			workBook = new XSSFWorkbook(inputStream);
		} else {
			logger.info(excelFile + "不是excel类型文件！！！");
			return null;
		}
		Object userObj = getPrincipal(false);

		Sheet sheet = workBook.getSheetAt(workBook.getActiveSheetIndex());
		StringBuilder info = new StringBuilder();
		int[] resultInt = new int[] { 0 };
		
		sheet.forEach(o -> {
			if (o.getRowNum() > 0) {				
				String buildingName = cell2Str(o.getCell(0, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
				if(StringUtils.isNotEmpty(buildingName)){
					CommunityBuildingEntity communityBuilding = communityBuildingDao.findUnique(
							"select a from CommunityBuildingEntity a where a.buildingName='"+buildingName+"' and a.district.id="+districtId, "") ;
					if(null == communityBuilding) {
						communityBuilding = new CommunityBuildingEntity();
					}
					communityBuilding.setDistrict(district);
					String buildingOrder = cell2Str(o.getCell(2, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
					communityBuilding.setBuildingOrder(StringUtils.isNotEmpty(buildingOrder) ? Integer.valueOf(buildingOrder) : 1);
					
	                communityBuilding.setAddress(cell2Str(o.getCell(3, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
	                String buildingCode = cell2Str(o.getCell(1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
	                communityBuilding.setBuildingCode(buildingCode);
//	                if(isNumber(buildingCode)) {
//	                	communityBuilding.setBuildingOrder(Integer.valueOf(buildingCode));
//	                }else {
//	                	communityBuilding.setBuildingOrder(1);
//	                }
	                communityBuilding.setBuildingName(buildingName);
	                communityBuilding.setState(EntityContext.RECORD_STATE_VALID);
//	                communityBuilding.setBuildingPermitNum(cell2Str(o.getCell(12, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
	                String buildingType = cell2Str(o.getCell(4, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
	                switch(buildingType) {
	                case"住宅":communityBuilding.setBuildingType(0);break;
	                case"别墅":communityBuilding.setBuildingType(1);break;
	                case"商铺":communityBuilding.setBuildingType(2);break;
	                case"停车场":communityBuilding.setBuildingType(3);break;
	                case"公共区域":communityBuilding.setBuildingType(4);break;
	                default:break;
	                }
//	                String classification = cell2Str(o.getCell(5, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
//	                switch(classification) {
//	                case"住宅用地":communityBuilding.setClassification(0);break;
//	                case"商业用地":communityBuilding.setClassification(1);break;
//	                case"工业用地":communityBuilding.setClassification(2);break;
//	                case"综合用地":communityBuilding.setClassification(3);break;
//	                case"其他用地":communityBuilding.setClassification(4);break;
//	                default:break;
//	                }
//	                communityBuilding.setConstruction(cell2Str(o.getCell(8, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
//	                communityBuilding.setDamagedLevel(cell2Str(o.getCell(9, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
	                communityBuilding.setDecoration(cell2Str(o.getCell(6, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
	                String layers = cell2Str(o.getCell(8, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)).split("\\.")[0];
	                communityBuilding.setLayers(StringUtils.isNotEmpty(layers) ? Integer.valueOf(layers):null);
//	                communityBuilding.setPermitLicenceNum(cell2Str(o.getCell(14, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
	                //communityBuilding.setComment(cell2Str(o.getCell(6, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
//	                String usableArea = cell2Str(o.getCell(15, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
//	                communityBuilding.setUsableArea(StringUtils.isNotEmpty(usableArea) ?  new BigDecimal(usableArea) : null);
	                String buildingArea = cell2Str(o.getCell(7, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
	                communityBuilding.setBuildingArea(StringUtils.isNotEmpty(buildingArea) ?  new BigDecimal(buildingArea) : null);
	                communityBuilding.setCompletionDate(o.getCell(5, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK).getDateCellValue());
	                //communityBuilding.setCappingDate(o.getCell(4, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK).getDateCellValue());
	                communityBuildingDao.save(communityBuilding);
	                
//					String unitCode = cell2Str(o.getCell(16, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
//					if(StringUtils.isNotEmpty(unitCode)){
//						CommunityEstateEntity estate = communityEstateDao.findUnique(
//								"select a from CommunityEstateEntity a where a.unitCode='"+unitCode+"' and a.building.id="+communityBuilding.getId(), "");
//						if(null == estate) {
//							estate = new CommunityEstateEntity();
//						}
//						estate.setUnitCode(unitCode);
//						String floor = cell2Str(o.getCell(17, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
//						if(StringUtils.isNotEmpty(floor)) {
//							estate.setFloor(StringUtils.isNotEmpty(floor.split("\\.")[0]) ? Integer.valueOf(floor.split("\\.")[0]):null);
//						}
//						estate.setRoomNumber(cell2Str(o.getCell(18, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
//						String estateState = cell2Str(o.getCell(19, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
//		                switch(estateState) {
//		                case"未收楼":estate.setEstateState(0);break;
//		                case"已入住":estate.setEstateState(1);break;
//		                case"装修中":estate.setEstateState(2);break;
//		                case"出租":estate.setEstateState(3);break;
//		                case"离退":estate.setEstateState(4);break;
//		                default:break;
//		                }
//		                String estateBuildingArea = cell2Str(o.getCell(20, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
//		                estate.setBuildingArea(StringUtils.isNotEmpty(estateBuildingArea) ?  new BigDecimal(estateBuildingArea) : null);
//		                String usageTerm = cell2Str(o.getCell(21, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
//		                estate.setUsableArea(StringUtils.isNotEmpty(usageTerm) ?  new BigDecimal(usageTerm) : null);
//		                String chargingArea = cell2Str(o.getCell(22, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
//		                estate.setChargingArea(StringUtils.isNotEmpty(chargingArea) ?  new BigDecimal(chargingArea) : null);
//		                String additionalArea = cell2Str(o.getCell(23, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
//		                estate.setAdditionalArea(StringUtils.isNotEmpty(additionalArea) ?  new BigDecimal(additionalArea) : null);
//		                estate.setUsageTerm(cell2Str(o.getCell(24, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
//						String isAcceptanceReceive = cell2Str(o.getCell(25, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
//		                switch(isAcceptanceReceive) {
//		                case"否":estate.setIsAcceptanceReceive(0);break;
//		                case"是":estate.setIsAcceptanceReceive(1);break;
//		                default:break;
//		                }
//		                estate.setOrientation(cell2Str(o.getCell(26, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
//		                estate.setDecoration(cell2Str(o.getCell(27, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
//		                estate.setEstateType(cell2Str(o.getCell(28, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
//		                estate.setComment(cell2Str(o.getCell(29, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
//		                estate.setBuilding(communityBuilding);
//		                estate.setState(EntityContext.RECORD_STATE_VALID);
//		                communityEstateDao.save(estate);
//		                
//					}
				}
			}
		});
		
		res.setRetInfo(StringUtils.isNotEmpty(info.toString()) ? info.toString() : ResponseContext.RES_SUCCESS_CODE_INFO);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);

		return res;
	}
	
	public  boolean isNumber(String str) {
		Pattern pattern = Pattern.compile("-?[0-9]+(\\\\.[0-9]+)?");
        Matcher m = pattern.matcher(str);
        return m.matches();
    }

	@Audit(operate = "导入抄表记录")
	public IResponse importMeterRecord(HttpServletRequest request,Integer replace) {
		try {
			return meterRecordHandler(uploadFile(request),replace);
		} catch (IllegalStateException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	@SuppressWarnings({ "resource", "unused" })
	private GenericResponse meterRecordHandler(File excelFile,Integer replace) throws IOException {
		GenericResponse res  = new GenericResponse();
		FileInputStream inputStream = new FileInputStream(excelFile);
		Workbook workBook = null;
		if (excelFile.getName().endsWith("xls")) {
			workBook = new HSSFWorkbook(inputStream);
		} else if (excelFile.getName().endsWith("xlsx")) {
			workBook = new XSSFWorkbook(inputStream);
		} else {
			logger.info(excelFile + "不是excel类型文件！！！");
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE );
			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "请上传excel（.xlsx）类型文件！");
			return res;
		}
		Object userObj = getPrincipal(false);

		Sheet sheet = workBook.getSheetAt(workBook.getActiveSheetIndex());
		StringBuilder info = new StringBuilder();
		StringBuilder error = new StringBuilder();
		int[] resultInt = new int[] { 0 };
		
		for(Row o : sheet) {
			if (o.getRowNum() > 0) {				
				String meterCode = cell2Str(o.getCell(0, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
				String meterName = cell2Str(o.getCell(1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
				String additionalAmount = cell2Str(o.getCell(6, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
				String additionalInstructions = cell2Str(o.getCell(7, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
				if(StringUtils.isNotEmpty(meterCode)){
					StringBuilder hql = new StringBuilder("select distinct a from CommunityMeterEntity a where a.meterCode='"+meterCode+"' and state=1");
					List<CommunityMeterEntity> list =  communityMeterDao.getListByHql(hql.toString(), "") ;
					if(null != list && list.size()==1) {
						CommunityMeterEntity meter =list.get(0);
						Date recordDate = o.getCell(3, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK).getDateCellValue();
						CommunityMeterRecordEntity parentMeterRecord = communityMeterRecordDao.getUniqueBySql
								(" SELECT *  FROM `t_community_meter_record` WHERE meterId="+meter.getId()+
										" AND state="+EntityContext.RECORD_STATE_VALID+"  ORDER BY recordDate DESC LIMIT 0,1" , "");
						CommunityMeterRecordEntity meterRecord = null;
						boolean state = false;
						if(null==parentMeterRecord || 
								(null!=parentMeterRecord &&
								!DateUtil.format(recordDate,0).equals(DateUtil.format(parentMeterRecord.getRecordDate(),0)))) {
							
							meterRecord  = new CommunityMeterRecordEntity();
							meterRecord.setLastRecordId(null!=parentMeterRecord ? parentMeterRecord.getId() : null);
							state = true;
						}else if(DateUtil.format(recordDate,0).equals(DateUtil.format(parentMeterRecord.getRecordDate(),0)) 
								&& null!=replace && replace==1) {
							meterRecord = parentMeterRecord;
							 parentMeterRecord = communityMeterRecordDao.get(parentMeterRecord.getLastRecordId());
							 state = true;
						}
						if(state) {
							meterRecord.setCommunityMeter(meter);
			                String recordNum = cell2Str(o.getCell(4, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
			                BigDecimal num = StringUtils.isNotEmpty(recordNum) ?  new BigDecimal(recordNum) : new BigDecimal(0);
			                String recordNumExceptionInfo = "";
			                String additionalAmountExceptionInfo = "";
			                if(isNumeric(recordNum)) {
			                	num = new BigDecimal(recordNum);
			                }else {
			                	num = new BigDecimal(0);
			                	recordNumExceptionInfo = "数据(本次读数)异常！";
			                }
							if(null != parentMeterRecord){
								if(parentMeterRecord.getRecordNum().compareTo(new BigDecimal("0"))!=0 && 
										num.compareTo(parentMeterRecord.getRecordNum()) == 1) {
									BigDecimal difference = num.subtract(parentMeterRecord.getRecordNum());
									if(difference.divide(parentMeterRecord.getRecordNum(),2, BigDecimal.ROUND_HALF_UP).compareTo(new BigDecimal(0.1)) == 1) {
										info.append("【表(编号："+meterCode+"，名称："+meterName+")异常；相比上个月数据超出10%以上！】");
										meterRecord.setExceptionState(1);
										//meterRecord.setExceptionRemark(info.toString());
									}
								}else if(parentMeterRecord.getRecordNum().compareTo(new BigDecimal("0"))!=0 &&
										num.compareTo(parentMeterRecord.getRecordNum()) == -1) {
									BigDecimal difference = parentMeterRecord.getRecordNum().subtract(num);
									if(difference.divide(parentMeterRecord.getRecordNum(),2, BigDecimal.ROUND_HALF_UP).compareTo(new BigDecimal(0.1)) == 1) {
										info.append("【表(编号："+meterCode+"，名称："+meterName+")异常；相比上个月数据减少10%以下！】");
										meterRecord.setExceptionState(1);
										//meterRecord.setExceptionRemark(info.toString());
									}
								}else {
									meterRecord.setExceptionState(0);
									meterRecord.setExceptionRemark("");
								}
							}else {
								meterRecord.setExceptionState(0);
								meterRecord.setExceptionRemark("");
							}

			                String isZero = cell2Str(o.getCell(2, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
			                switch(isZero) {
			                case"否":meterRecord.setIsZero(0);break;
			                case"是":meterRecord.setIsZero(1);break;
			                default:break;
			                }
			                if(isNumeric(additionalAmount)) {
			                	meterRecord.setAdditionalAmount(new BigDecimal(additionalAmount));
			                	meterRecord.setAdditionalInstructions(StringUtils.isNotEmpty(additionalInstructions) ? additionalInstructions :"");
			                }else {
			                	meterRecord.setAdditionalAmount(new BigDecimal(0));
			                	//additionalAmountExceptionInfo = "数据（额外用量）异常！";
			                }
			                info.append(StringUtils.isNotEmpty(info.toString()) ? 
			                		((StringUtils.isNotEmpty(recordNumExceptionInfo) ? recordNumExceptionInfo : "")+
			                		(StringUtils.isNotEmpty(additionalAmountExceptionInfo) ? additionalAmountExceptionInfo : "")+"】") : 
			                			((StringUtils.isNotEmpty(recordNumExceptionInfo)|| StringUtils.isNotEmpty(additionalAmountExceptionInfo)) ?
			                			"【表(编号："+meterCode+"，名称："+meterName+")异常；"+(StringUtils.isNotEmpty(recordNumExceptionInfo) ? recordNumExceptionInfo : "")+
				                		(StringUtils.isNotEmpty(additionalAmountExceptionInfo) ? additionalAmountExceptionInfo : "")+"】" :""));
			            
			                meterRecord.setRecordNum(num);
			                meterRecord.setState(EntityContext.RECORD_STATE_VALID);
			                meterRecord.setRecordDate(recordDate);
			                meterRecord.setLastModifyTime(new Timestamp(new Date().getTime()));
			                meterRecord.setRecorder(cell2Str(o.getCell(5, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
			                communityMeterRecordDao.save(meterRecord);
						}
					}else if(null != list && list.size()>1){
						error.append("【表(编号："+meterCode+"，名称："+meterName+")数据有重复！】");
					}else {
						error.append("【表(编号："+meterCode+"，名称："+meterName+")不存！】");
						//break;
					}
				}
//				else {
//					error.append("【表编号："+meterCode+"不能为空！】");
//					break;
//				}
			}
		}
		
		if(error.length() > 0) {
			res.setRetInfo(error.toString());
			res.setRet(ResponseContext.EXCELE_EXPORT_ERROR_CODE);
		}
		else {
			res.setRetInfo("导入成功！" + info.toString());
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
		}
	

		return res;
	}
	
	@Audit(operate = "导入停车信息")
	public IResponse importParkingRelation(HttpServletRequest request) {
		try {
			return parkingRelationHandler(uploadFile(request));
		} catch (IllegalStateException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	
	@SuppressWarnings({ "resource", "unused" })
	private GenericResponse parkingRelationHandler(File excelFile) throws IOException {
		GenericResponse res  = new GenericResponse();
		FileInputStream inputStream = new FileInputStream(excelFile);
		Workbook workBook = null;
		if (excelFile.getName().endsWith("xls")) {
			workBook = new HSSFWorkbook(inputStream);
		} else if (excelFile.getName().endsWith("xlsx")) {
			workBook = new XSSFWorkbook(inputStream);
		} else {
			logger.info(excelFile + "不是excel类型文件！！！");
			return null;
		}
		Object userObj = getPrincipal(false);

		Sheet sheet = workBook.getSheetAt(workBook.getActiveSheetIndex());
		StringBuilder info = new StringBuilder();
		int[] resultInt = new int[] { 0 };
		sheet.forEach(o -> {
			if (o.getRowNum() > 0) {				
				String parkingCardCode = cell2Str(o.getCell(0, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
				String unitCode = cell2Str(o.getCell(1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
				if(StringUtils.isNotEmpty(parkingCardCode) && StringUtils.isNotEmpty(unitCode)){
					CommunityEstateEntity parking = communityEstateDao.findUnique(
							"select a from CommunityEstateEntity a where a.unitCode='"+parkingCardCode+"'", "") ;
					CommunityEstateEntity unit = communityEstateDao.findUnique(
							"select a from CommunityEstateEntity a where a.unitCode='"+unitCode+"'", "");

					if(null != unit && null != parking) {
						String memberType = cell2Str(o.getCell(3, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
			            switch(memberType) {
			            case"出售":parking.setEstateState(1);break;
			            case"出租":parking.setEstateState(3);break;
			            default:break;
			            }
						CommunityMemberPropertyEntity memberProperty = communityMemberPropertyDao.findUnique(
								"select a from CommunityMemberPropertyEntity a where a.parentProperty.id="+unit.getId()+
								" and a.property.id="+parking.getId(),"");
						
						if(null == memberProperty) {
				            CommunityMemberEntity member = null;
				            for(CommunityMemberPropertyEntity p : unit.getMemberPropertyList()){
				            	if(p.getIsCurrentMember()==1 && p.getIsCurrentOwner()==1) {
				            		member = p.getMember();
				            	}
				            }
				            if(null != member) {
								memberProperty = new CommunityMemberPropertyEntity();
					            switch(memberType) {
					            case"出售":memberProperty.setMemberType(0);break;
					            case"出租":memberProperty.setMemberType(2);break;
					            default:break;
					            }
					            Date billingDate = o.getCell(4, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK).getDateCellValue();
					            Date endDate = o.getCell(5, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK).getDateCellValue();
					            memberProperty.setMember(member);
					            memberProperty.setIsCurrentMember(1);
					            memberProperty.setIsCurrentOwner(1);
								if(null!=billingDate) {
				     				memberProperty.setBillingDate(billingDate);
				        			//memberProperty.setBillingDateModify(new Timestamp(new Date().getTime()));
				    			}
					            memberProperty.setEndDate(endDate);
					            memberProperty.setParentProperty(unit);
					            memberProperty.setProperty(parking);
					            
		                    	ObjectMapper mapper = new ObjectMapper();
								ReservedFieldVo reservedField= null;
								if(StringUtils.isNotEmpty(unit.getReservedField())) {
									try {
										reservedField =  mapper.readValue(unit.getReservedField(), ReservedFieldVo.class);
									} catch (JsonMappingException e) {
										e.printStackTrace();
									} catch (JsonProcessingException e) {
										e.printStackTrace();
									}
								}else {
									reservedField = new ReservedFieldVo();
								}
								BigDecimal buildingArea = parking.getBuildingArea();
								BigDecimal propertyParkingArea = new BigDecimal("0");
								Integer propertyParkingNum =  0;
								BigDecimal defenceParkingArea =  new BigDecimal("0");
								Integer defenceParkingNum =  0;
								unit.getSubMemberPropertyList().add(memberProperty);
								if(null != unit.getSubMemberPropertyList()) {
									for(CommunityMemberPropertyEntity mp : unit.getSubMemberPropertyList()) {
										CommunityEstateEntity p = (CommunityEstateEntity) mp.getProperty();
										if(p.getEstateType().equals("产权车位")) {
											propertyParkingArea = propertyParkingArea.add(p.getBuildingArea());
											propertyParkingNum = propertyParkingNum+1;
										}else if(p.getEstateType().equals("人防车位")) {
											defenceParkingArea = defenceParkingArea.add(p.getBuildingArea());
											defenceParkingNum = defenceParkingNum+1;
										}
									}
								}
//								if(parking.getEstateType().equals("产权车位")) {
//									propertyParkingArea = propertyParkingArea.add(buildingArea);
//									propertyParkingNum = propertyParkingNum+1;
//								}else if(parking.getEstateType().equals("人防车位")) {
//									defenceParkingArea = defenceParkingArea.add(buildingArea);
//									defenceParkingNum = defenceParkingNum+1;
//								}
								reservedField.setPropertyParkingArea(propertyParkingArea.toString());
								reservedField.setPropertyParkingNum(propertyParkingNum.toString());
								reservedField.setDefenceParkingArea(defenceParkingArea.toString());
								reservedField.setDefenceParkingNum(defenceParkingNum.toString());
								String json="";
								try {
									json = mapper.writeValueAsString(reservedField);
								} catch (JsonProcessingException e) {
									// TODO Auto-generated catch block
									e.printStackTrace();
								}
								unit.setReservedField(json);
	        					if (CommunityCache.estateList.containsKey(unit.getId())) {
	        						CommunityCache.estateList.remove(unit.getId());
	        					} else {
	        						CommunityCache.estateList.put(unit.getId(), unit);
	        					}
					            
					            communityMemberPropertyDao.saveOrUpdate(memberProperty);
				            }else {
				            	info.append("【单元编号："+unitCode+")未绑定业主！】");
				            }
						}else {
							info.append("【(车位编号："+parkingCardCode+"，单元编号："+unitCode+")已有绑定！】");
						}
					}else {
						info.append("【(车位编号："+parkingCardCode+"，单元编号："+unitCode+")不存！】");
					}

				}
			}
		});
		
		res.setRetInfo(StringUtils.isNotEmpty(info.toString()) ? info.toString() : ResponseContext.RES_SUCCESS_CODE_INFO);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);

		return res;
	}
	@Audit(operate = "导入表")
	public IResponse importMeter(HttpServletRequest request) {
		try {
			return meterHandler(uploadFile(request));
		} catch (IllegalStateException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	@SuppressWarnings({ "resource", "unused" })
	private GenericResponse meterHandler(File excelFile) throws IOException {
		GenericResponse res  = new GenericResponse();
		FileInputStream inputStream = new FileInputStream(excelFile);
		Workbook workBook = null;
		if (excelFile.getName().endsWith("xls")) {
			workBook = new HSSFWorkbook(inputStream);
		} else if (excelFile.getName().endsWith("xlsx")) {
			workBook = new XSSFWorkbook(inputStream);
		} else {
			logger.info(excelFile + "不是excel类型文件！！！");
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE );
			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "请上传excel（.xlsx）类型文件！");
			return res;
		}
		Object userObj = getPrincipal(false);

		Sheet sheet = workBook.getSheetAt(workBook.getActiveSheetIndex());
		StringBuilder info = new StringBuilder();
		StringBuilder error = new StringBuilder();
		int[] resultInt = new int[] { 0 };
		
		for(Row o : sheet) {
			if (o.getRowNum() > 0) {				
				String meterCode = cell2Str(o.getCell(0, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
				String meterName = cell2Str(o.getCell(1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
				String initialData = cell2Str(o.getCell(3, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
				if(StringUtils.isNotEmpty(meterCode)){
					StringBuilder hql = new StringBuilder("select distinct a from CommunityMeterEntity a where a.meterCode='"+meterCode+"'");
					CommunityMeterEntity meter =  communityMeterDao.findUnique(hql.toString(), "") ;
					if(null == meter) {
						String isApportionedValue = cell2Str(o.getCell(2, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
						//String isCommonValue = cell2Str(o.getCell(3, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
//						String additionalAmount = cell2Str(o.getCell(5, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
//						String additionalInstructions = cell2Str(o.getCell(6, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
						String attributeName = cell2Str(o.getCell(4, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
						String payItemsCode = cell2Str(o.getCell(5, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
						
						String payItemsName = cell2Str(o.getCell(6, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
						String chargeCategory = cell2Str(o.getCell(7, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
						String allocationMethod = cell2Str(o.getCell(8, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
						String formulaName = cell2Str(o.getCell(9, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
						meter = new CommunityMeterEntity();
						if(StringUtils.isNotEmpty(formulaName)) {
							List<CommunityMeterFormulaEntity> formulaList =communityMeterFormulaDao.getListByHql(
									"select distinct a from CommunityMeterFormulaEntity a where a.formulaName='"+formulaName+"'", "");
							if(null!=formulaList && formulaList.size()>0) {
								meter.setMeterFormula(formulaList.get(0));
							}
						}
						meter.setPayItemsName(StringUtils.isNotEmpty(payItemsName) ? payItemsName:"");
						meter.setChargeCategory(StringUtils.isNotEmpty(chargeCategory) ? chargeCategory:"");
						meter.setMeterCode(meterCode);
						meter.setMeterName(meterName);
						meter.setInitialData(StringUtils.isNotEmpty(initialData) ? new BigDecimal(initialData) : new BigDecimal(0));
						meter.setIsCommon(1);
						meter.setIsApportioned(StringUtils.isNotEmpty(isApportionedValue) ? (isApportionedValue.equals("是") ? 1 : 0 ): 0);
						meter.setLevel(1);
						meter.setAllocationPeriod(1);
						meter.setIsDisabled(0);
						meter.setPayItems(getBreachPayItem());
						meter.setComment("");	
						meter.setState(EntityContext.RECORD_STATE_VALID);
						CommunityMeterAttributesEntity meterAttributes=(StringUtils.isNotEmpty(attributeName) ? 
								communityMeterAttributesDao.findUnique("select distinct a "
										+ "from CommunityMeterAttributesEntity a where a.attributeName='"+attributeName+"'", "") : null);
						meter.setMeterAttributes(meterAttributes);
						if(StringUtils.isNotEmpty(payItemsCode) && payItemsCode.contains("-")) {
							String payItemsId = payItemsCode.split("-")[1];
							CommunityPayItemsEntity payItems = communityPayItemsDao.get(Integer.valueOf(payItemsId));
							meter.setPayItems(payItems);
						}
						if(StringUtils.isNotEmpty(allocationMethod) ) {
							if(allocationMethod.equals("按面积分摊")) {
								meter.setAllocationMethod(1);
							}else if(allocationMethod.equals("自定义公式")) {
								meter.setAllocationMethod(2);
							}else if(allocationMethod.equals("按个数分摊")) {
								meter.setAllocationMethod(3);
							}
						}

						//meter.setAdditionalAmount(StringUtils.isNotEmpty(additionalAmount) ? new BigDecimal(additionalAmount) : new BigDecimal(0));
						//meter.setAdditionalInstructions(StringUtils.isNotEmpty(additionalInstructions) ? additionalInstructions : "");
						
						communityMeterDao.save(meter);
					}else {
						error.append("【表(编号："+meterCode+"，名称："+meterName+")已存！】");
						//break;
					}
				}
			}
		}
		
		if(error.length() > 0) {
			res.setRetInfo(error.toString());
			res.setRet(ResponseContext.EXCELE_EXPORT_ERROR_CODE);
		}else {
			res.setRetInfo("导入成功！" + info.toString());
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
		}

		return res;
	}
	
	
	public  boolean isNumeric(String str) { 
		if (str.isEmpty()) {
			return false;
		}
        Boolean strResult = str.matches("^\\-?[0-9]+(.[0-9]+)?$");    
        return strResult;
	}

	

	public File uploadFile(HttpServletRequest request) throws IllegalStateException, IOException {
		MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
		MultipartFile file = multiRequest.getFile("file");
		String tmpFile = System.getProperty("java.io.tmpdir");
		//String tmpFile = "F:/temp";
		tmpFile = tmpFile + File.separator + System.currentTimeMillis() + "-"
				+ DigestUtil.getMD5Str(file.getOriginalFilename()) + "-" + CodeUtil.getId(10000)
				+ file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
		file.transferTo(new File(tmpFile));
		File excelFile = new File(tmpFile);
		
		return excelFile;
	}
	

	private String cell2Str(Cell cell) {

		String str = "";
		if (cell.getCellType() == CellType.STRING) {
			str = StringUtils.isNotEmpty(cell.getStringCellValue()) ? cell.getStringCellValue() :"";
		} else if (cell.getCellType() == CellType.NUMERIC) {
			str = String.valueOf(cell.getNumericCellValue());
			if(str.contains("E10")) {
				DecimalFormat decimalFormat=new DecimalFormat("#");
				str = decimalFormat.format(Double.valueOf(str));
			}
		}else if(cell.getCellType() == CellType.FORMULA){
			str = cell.getCellFormula();
		}else if(cell.getCellType() == CellType.BOOLEAN){
			str=cell.getBooleanCellValue()+"";
		}else if(cell.getCellType() == CellType.ERROR){
			str=cell.getErrorCellValue()+"";
		}else if(cell.getCellType() == CellType.BLANK || cell.getCellType() == CellType. _NONE){
			str="";
		}else {
			//Date date = cell.getDateCellValue();
			//str=DateUtil.format(cell.getDateCellValue(),1);
			str="";
		}
		return str;
	}
	
	@Audit(operate = "导入回盘数据")
	public IResponse importBackDisk(HttpServletRequest request,Integer templateType,Integer bankDepositBatchId,String depositDate) {
		try {
			return backDiskHandler(uploadFile(request),templateType,bankDepositBatchId,depositDate);
		} catch (IllegalStateException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	
	@SuppressWarnings({ "resource", "unused" })
	private GenericResponse backDiskHandler(File excelFile,Integer templateType,Integer bankDepositBatchId,String depositDate) throws IOException {
		GenericResponse res  = new GenericResponse();
		StringBuilder info = new StringBuilder();
		if(null == templateType || null == bankDepositBatchId) {
			res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			return res;
		}
	//	if(templateType==0 || templateType==1) {
			FileInputStream inputStream = new FileInputStream(excelFile);
			Workbook workBook = null;
			if (excelFile.getName().endsWith("xls")) {
				workBook = new HSSFWorkbook(inputStream);
			} else if (excelFile.getName().endsWith("xlsx")) {
				workBook = new XSSFWorkbook(inputStream);
			} else {
				logger.info(excelFile + "不是excel类型文件！！！");
				return null;
			}
			Object userObj = getPrincipal(false);

			Sheet sheet = workBook.getSheetAt(workBook.getActiveSheetIndex());
			int[] resultInt = new int[] { 0 };
			sheet.forEach(o -> {
				if (o.getRowNum() > 0) {				
					String accountName = "";
					String bankAccount = "";
					String unitCode = "";
					BigDecimal depositAmount = null;
					String mark = "";
					if(templateType==0) {
						bankAccount = cell2Str(o.getCell(1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
						if(StringUtils.isNotEmpty(bankAccount)) {
							accountName = cell2Str(o.getCell(0, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							unitCode = cell2Str(o.getCell(3, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							depositAmount = new BigDecimal(cell2Str(o.getCell(4, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)));
							mark = cell2Str(o.getCell(5, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							info.append(saveAndUpdateData(accountName,bankAccount,unitCode,depositAmount, 
									mark,"",templateType,bankDepositBatchId,depositDate));
						}
					}else  if(templateType==1 && o.getRowNum() > 7){
						bankAccount = cell2Str(o.getCell(2, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
						if(StringUtils.isNotEmpty(bankAccount)) {
							accountName = cell2Str(o.getCell(1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							unitCode = cell2Str(o.getCell(5, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)).replace("普华物管费", "");
							depositAmount = (new BigDecimal(cell2Str(o.getCell(3, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK))));
							//depositAmount = depositAmount.divide(new BigDecimal(100));
							mark = cell2Str(o.getCell(4, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							info.append(saveAndUpdateData(accountName,bankAccount,unitCode,depositAmount, 
									mark,"",templateType,bankDepositBatchId,depositDate));
						}
					}else  if(templateType==2){
						bankAccount = cell2Str(o.getCell(2, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
						if(StringUtils.isNotEmpty(bankAccount)) {
							accountName = cell2Str(o.getCell(1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							unitCode = cell2Str(o.getCell(6, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)).replace("普华物管费", "");
							depositAmount = (new BigDecimal(cell2Str(o.getCell(3, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK))));
							//depositAmount = depositAmount.divide(new BigDecimal(100));
							mark = cell2Str(o.getCell(4, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							info.append(saveAndUpdateData(accountName,bankAccount,unitCode,depositAmount, 
									mark,"",templateType,bankDepositBatchId,depositDate));
						}
					}else if (templateType==3 ) {
						bankAccount = cell2Str(o.getCell(1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
						if(StringUtils.isNotEmpty(bankAccount)) {
							accountName = cell2Str(o.getCell(2, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							unitCode = cell2Str(o.getCell(10, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)).replace("普华物管费", "");
							depositAmount = (new BigDecimal(cell2Str(o.getCell(3, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK))));
							//depositAmount = depositAmount.divide(new BigDecimal(100));
							mark = cell2Str(o.getCell(13, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							info.append(saveAndUpdateData(accountName,bankAccount,unitCode,depositAmount, 
									mark,"",templateType,bankDepositBatchId,depositDate));
						}
					}else if(templateType==4) {
						bankAccount = cell2Str(o.getCell(0, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
						if(StringUtils.isNotEmpty(bankAccount)) {
							accountName = cell2Str(o.getCell(1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							unitCode = cell2Str(o.getCell(5, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)).replace("普华物管费", "");
							depositAmount = (new BigDecimal(cell2Str(o.getCell(2, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK))));
							//depositAmount = depositAmount.divide(new BigDecimal(100));
							mark = cell2Str(o.getCell(3, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							info.append(saveAndUpdateData(accountName,bankAccount,unitCode,depositAmount, 
									mark,"",templateType,bankDepositBatchId,depositDate));
						}
					}
					
					
//					System.out.println("accountName:"+accountName+";bankAccount:"+bankAccount+";unitCode:"
//					+unitCode+";depositAmount:"+depositAmount+";mark:"+mark);
				}
			});
		/*}else {
			String line="";
			try{
				InputStreamReader isr = new InputStreamReader(new FileInputStream(excelFile), "GB2312");
				if (!excelFile.getName().endsWith("txt")) {
					logger.info(excelFile + "不是excel类型文件！！！");
					
					return null;
				}
				BufferedReader in=new BufferedReader(isr);
				line=in.readLine();
				boolean firstLine=true;
				while (line!=null){
					if(!firstLine) {
						String[] str =  line.split("\\|");
						if(str.length == 6) {
							String accountName = str[1];
							String bankAccount = str[2];
							String unitCode = "";
							BigDecimal depositAmount = new BigDecimal(str[3]);
							String mark = str[4];
							String reservedValue = str[5];
							saveAndUpdateData(info,accountName,bankAccount,unitCode,depositAmount, 
									mark,reservedValue,templateType,bankDepositBatchId);
						}else if(str.length >0 && str.length != 6){
							//res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
							//res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO+"数据长度与约定不符!");
							//return res;
						}
					}
					firstLine=false;
					line=in.readLine();
				}
				in.close();
			} catch (IOException e){
				e.printStackTrace();
			}
		}*/
		res.setRetInfo(StringUtils.isNotEmpty(info.toString()) ? info.toString() : ResponseContext.RES_SUCCESS_CODE_INFO);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);

		return res;
	}
	
	public String saveAndUpdateData(String accountName,String bankAccount,
			String unitCode,BigDecimal depositAmount,String mark,String reservedValue,
			Integer templateType,Integer bankDepositBatchId,String depositDate) {
		String info="";
		if(StringUtils.isNotEmpty(bankAccount) && StringUtils.isNotEmpty(mark) && null!=depositAmount){
			StringBuilder hql = new StringBuilder("select distinct a from CommunityBankDepositRecordEntity a  "
					+ " inner join a.bankDepositBatch d inner join a.estate e where  "+
					"  e.unitCode='"+unitCode+"' and d.id="+bankDepositBatchId);
			CommunityBankDepositRecordEntity bankDepositRecord =  communityBankDepositRecordDao.findUnique(hql.toString(), "") ;
			if(null != bankDepositRecord) {
				if((templateType==0 && mark.equals("全部成功")) || (templateType==1 && mark.equals("交易成功")) 
						|| (templateType==2 && mark.equals("0000"))|| (templateType==3 && mark.equals("成功"))
						|| (templateType==4 && mark.equals("成功"))) {
					if(depositAmount.compareTo(bankDepositRecord.getDepositAmount())!=0) {
						info = "【记录(银行帐户："+bankAccount+")划账金额与回盘金额不相等！】";
					}
					
					try {
						bankDepositRecord.getBankDepositBatch().setDepositDate(StringUtils.isNotEmpty(depositDate) ? 
						        DateUtil.parseShortFormat(depositDate) : new Date());
					} catch (ParseException e) {
						e.printStackTrace();
					}
					bankDepositRecord.setFeedbackComment(mark+(StringUtils.isNotEmpty(reservedValue) ? "|"+reservedValue:""));
					bankDepositRecord.setIsDeposited(1);
				}else {
					bankDepositRecord.setFeedbackComment(mark+(StringUtils.isNotEmpty(reservedValue) ? "|"+reservedValue:""));
					bankDepositRecord.setIsDeposited(2);
//					for(CommunityReceivablesEntity receivables : bankDepositRecord.getReceivablesList()) {
//						receivables.setLockMark(0);
//					}
					try {
						bankDepositRecord.getBankDepositBatch().setDepositDate(StringUtils.isNotEmpty(depositDate) ? 
						        DateUtil.parseShortFormat(depositDate) : new Date());
					} catch (ParseException e) {
						e.printStackTrace();
					}
				}
			}else {
				info = "【记录(银行帐户："+bankAccount+")不存！】";
			}
		}else {
			info = "【记录(银行帐户："+bankAccount+")不存！】";
		}
		return info;
	}
	

	@SuppressWarnings("unused")
	private static boolean createMultilayerFile(String dir) {
		try {
			File dirPath = new File(dir);
			if (!dirPath.exists()) {
				dirPath.mkdirs();
			}
		} catch (Exception e) {
			System.out.println("创建多层目录操作出错: " + e.getMessage());
			e.printStackTrace();
			return false;
		}
		return true;
	}
	public  boolean isOSLinux() {
		Properties prop = System.getProperties();
		String os = prop.getProperty("os.name");
		boolean statu = false;
		if (os != null && os.toLowerCase().indexOf("linux") > -1) {
			statu=true;
		} 
		System.out.println("-----------------isOSLinux:"+statu+";name:"+os.toLowerCase().indexOf("linux"));
		return statu;
	}

	@Override
	public IResponse importSmartcardId(HttpServletRequest request) {
		try {
			return getSmartcardId(uploadFile(request));
		} catch (IllegalStateException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	
	@SuppressWarnings("resource")
	public GenericResponse getSmartcardId(File excelFile) throws IOException {
		GenericResponse res  = new GenericResponse();
		StringBuilder info = new StringBuilder();

		FileInputStream inputStream = new FileInputStream(excelFile);
		Workbook workBook = null;
		if (excelFile.getName().endsWith("xls")) {
			workBook = new HSSFWorkbook(inputStream);
		} else if (excelFile.getName().endsWith("xlsx")) {
			workBook = new XSSFWorkbook(inputStream);
		} else {
			logger.info(excelFile + "不是excel类型文件！！！");
			return res;
		}

		Sheet sheet = workBook.getSheetAt(workBook.getActiveSheetIndex());
		StringBuilder infoStr = new StringBuilder();
		sheet.forEach(o -> {
			if (o.getRowNum() > 0) {				
				String smartcardId = cell2Str(o.getCell(10, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
				//String phone = cell2Str(o.getCell(21, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
				String unitCode = cell2Str(o.getCell(41, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
				String[] code = unitCode.split("-");
				if(code.length==3) {
					unitCode = code[0]+"-";
					if(code[1].length()==1) {
						unitCode=unitCode+"0"+code[1];
					}else {
						unitCode=unitCode+code[1];
					}
					unitCode=unitCode+"-";
					if(code[2].length()==1) {
						unitCode=unitCode+"010"+code[2];
					}else if(code[2].length()==2) {
						unitCode=unitCode+"01"+code[2];
					}else if(code[2].length()==3) {
						unitCode=unitCode+"0"+code[2];
					}else if(code[2].length()==4) {
						unitCode=unitCode+code[2];
					}
				}
				CommunityEstateEntity estate = communityEstateDao.getUniqueByHql("select distinct a from CommunityEstateEntity a "
						+ "where a.unitCode='"+unitCode+"'", "");
				if(null != estate) {
					for(CommunityMemberPropertyEntity memberProperty :estate.getMemberPropertyList()) {
						if(null != memberProperty.getMember() && memberProperty.getIsCurrentMember()==1 && smartcardId.length()<=16) {
							memberProperty.getMember().setSmartcardId(smartcardId.replaceAll("	", ""));
						}
					}
				}else {
					infoStr.append(unitCode+";");
				}
			}
			
		});
		System.out.println(infoStr.toString());
		res.setRetInfo(StringUtils.isNotEmpty(info.toString()) ? info.toString() : ResponseContext.RES_SUCCESS_CODE_INFO);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);

		return res;
	}
	

	@Audit(operate = "导入表编码")
	public IResponse importMeterCode(HttpServletRequest request,ImportExcelReq req) {
		try {
			if(null == req.getDistrictId()) {
				GenericResponse res  = new GenericResponse();
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				return res;
			}
			return meterCodeHandler(uploadFile(request),req.getDistrictId());
		} catch (IllegalStateException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	
	@SuppressWarnings({ "resource", "unused" })
	private GenericResponse meterCodeHandler(File excelFile,Integer type) throws IOException {
		GenericResponse res  = new GenericResponse();
		FileInputStream inputStream = new FileInputStream(excelFile);
		Workbook workBook = null;
		if (excelFile.getName().endsWith("xls")) {
			workBook = new HSSFWorkbook(inputStream);
		} else if (excelFile.getName().endsWith("xlsx")) {
			workBook = new XSSFWorkbook(inputStream);
		} else {
			logger.info(excelFile + "不是excel类型文件！！！");
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE );
			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "请上传excel（.xlsx）类型文件！");
			return res;
		}
		Object userObj = getPrincipal(false);

		Sheet sheet = workBook.getSheetAt(workBook.getActiveSheetIndex());
		StringBuilder info = new StringBuilder();
		StringBuilder error = new StringBuilder();
		int[] resultInt = new int[] { 0 };
		
		for(Row o : sheet) {
			if (o.getRowNum() > 0) {	
				String meterCode = "";
				if(null!=type && type==0 ) {
					meterCode = cell2Str(o.getCell(2, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)) ;
				}else if(null!=type && type==1) {
					meterCode = cell2Str(o.getCell(1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)) ;
				}
				
				String meterName = cell2Str(o.getCell(0, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
				if(StringUtils.isNotEmpty(meterCode)){
					StringBuilder hql = new StringBuilder("select distinct a from CommunityMeterEntity a where a.meterName ='"+meterName+"'");
					CommunityMeterEntity meter =  communityMeterDao.findUnique(hql.toString(), "") ;
					if(null != meter) {
						meter.setMeterCode(meterCode);
					}else {
						error.append("【表(编号："+meterCode+"，名称："+meterName+")不存！】");
						//break;
					}
				}else {
					//error.append("【表编号："+meterCode+"不能为空！】");
					//break;
				}
			}
		}
		
		if(error.length() > 0) {
			res.setRetInfo(error.toString());
			res.setRet(ResponseContext.EXCELE_EXPORT_ERROR_CODE);
		}else {
			res.setRetInfo("导入成功！" + info.toString());
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
		}

		return res;
	}
	
	
	
	
	public IResponse importInitializeAgreementRelation(HttpServletRequest request) {
		try {
			return initializeAgreementHandler(uploadFile(request));
		} catch (IllegalStateException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	
	@SuppressWarnings({ "resource", "unused" })
	private GenericResponse initializeAgreementHandler(File excelFile) throws IOException {
		GenericResponse res  = new GenericResponse();
		FileInputStream inputStream = new FileInputStream(excelFile);
		Workbook workBook = null;
		if (excelFile.getName().endsWith("xls")) {
			workBook = new HSSFWorkbook(inputStream);
		} else if (excelFile.getName().endsWith("xlsx")) {
			workBook = new XSSFWorkbook(inputStream);
		} else {
			logger.info(excelFile + "不是excel类型文件！！！");
			return null;
		}
		Object userObj = getPrincipal(false);

		Sheet sheet = workBook.getSheetAt(workBook.getActiveSheetIndex());
		StringBuilder info = new StringBuilder();
		int[] resultInt = new int[] { 0 };
		sheet.forEach(o -> {
			if (o.getRowNum() > 1) {				
				String unitCode = cell2Str(o.getCell(0, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
				if(StringUtils.isNotEmpty(unitCode)){
					CommunityEstateEntity unit = null;
					try {
						unit=communityEstateDao.findUnique(
							"select a from CommunityEstateEntity a where a.unitCode='"+unitCode+"'", "") ;
					} catch (Exception e) {
						e.printStackTrace();
					}
					if(null != unit) {
						ReservedFieldVo reservedField= null;
						if(StringUtils.isNotEmpty(unit.getReservedField())) {
							try {
								reservedField =  mapper.readValue(unit.getReservedField(), ReservedFieldVo.class);
							} catch (JsonMappingException e) {
								e.printStackTrace();
							} catch (JsonProcessingException e) {
								e.printStackTrace();
							}
						}else {
							reservedField = new ReservedFieldVo();
						}
						String accountCustomerId = cell2Str(o.getCell(2, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
						String accountContractNo = cell2Str(o.getCell(6, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
						String accountContractTime = cell2Str(o.getCell(7, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
						String accountContractComment = cell2Str(o.getCell(8, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
						reservedField.setAccountCustomerId(accountCustomerId);
						reservedField.setAccountContractNo(accountContractNo);
						reservedField.setAccountContractTime(StringUtils.isNotEmpty(accountContractTime) ? 
								accountContractTime.substring(0, accountContractTime.length()-4)+"-"+
								accountContractTime.substring(4, accountContractTime.length()-2)+
								"-"+accountContractTime.substring(6, accountContractTime.length()) : "");
						reservedField.setAccountContractComment(accountContractComment);
						
						reservedField.setPropertyParkingArea(StringUtils.isNotEmpty(reservedField.getPropertyParkingArea()) ? reservedField.getPropertyParkingArea() :"0");
						reservedField.setPropertyParkingNum(StringUtils.isNotEmpty(reservedField.getPropertyParkingNum()) ? reservedField.getPropertyParkingNum() :"0");
						reservedField.setDefenceParkingArea(StringUtils.isNotEmpty(reservedField.getDefenceParkingArea()) ? reservedField.getDefenceParkingArea() :"0");
						reservedField.setDefenceParkingNum(StringUtils.isNotEmpty(reservedField.getDefenceParkingNum()) ? reservedField.getDefenceParkingNum() :"0");
						
						reservedField.setWaterMeterBase(StringUtils.isNotEmpty(reservedField.getWaterMeterBase()) ? reservedField.getWaterMeterBase() :"0");
						reservedField.setElectricMeterBase(StringUtils.isNotEmpty(reservedField.getElectricMeterBase()) ? reservedField.getElectricMeterBase() :"0");
						
						String json="";
						try {
							json = mapper.writeValueAsString(reservedField);
						} catch (JsonProcessingException e) {
							e.printStackTrace();
						}
						unit.setReservedField(json);
						communityEstateDao.update(unit);
    					if (CommunityCache.estateList.containsKey(unit.getId())) {
    						CommunityCache.estateList.remove(unit.getId());
    					} else {
    						CommunityCache.estateList.put(unit.getId(), unit);
    					}
					}else {
						info.append("【(单元编号："+unitCode+")不存！】");
					}

				}
			}
		});
		
		res.setRetInfo(StringUtils.isNotEmpty(info.toString()) ? info.toString() : ResponseContext.RES_SUCCESS_CODE_INFO);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);

		return res;
	}
	
	public String getBirthday(String IDCard) {
		String birthday = "";
		String year = "";
		String month = "";
		String day = "";
		if (StringUtils.isNotBlank(IDCard)) {
			// 15位身份证号
			if (IDCard.length() == 15) {
				// 身份证上的年份(15位身份证为1980年前的)
				year = "19" + IDCard.substring(6, 8);
				// 身份证上的月份
				month = IDCard.substring(8, 10);
				// 身份证上的日期
				day = IDCard.substring(10, 12);
				// 18位身份证号
			} else if (IDCard.length() == 18) {
				// 身份证上的年份
				year = IDCard.substring(6).substring(0, 4);
				// 身份证上的月份
				month = IDCard.substring(10).substring(0, 2);
				// 身份证上的日期
				day = IDCard.substring(12).substring(0, 2);
			}
			if(StringUtils.isNotBlank(year)&&StringUtils.isNotBlank(month)&&StringUtils.isNotBlank(day)) {
				birthday = year + "-" + month + "-" + day ;
			}
		}
		return birthday;
	}
   
	public String getSex(String IDCard) {
		String sex = "";
		if (StringUtils.isNotBlank(IDCard)) {
			// 15位身份证号
			if (IDCard.length() == 15) {
				if (Integer.parseInt(IDCard.substring(14, 15)) % 2 == 0) {
					sex = "女";
				} else {
					sex = "男";
				}
				// 18位身份证号
			} else if (IDCard.length() == 18) {
				// 判断性别
				if (Integer.parseInt(IDCard.substring(16).substring(0, 1)) % 2 == 0) {
					sex = "女";
				} else {
					sex = "男";
				}
			}
		}
		return sex;
	}
	
}
