package com.foshan.service.community.impl.statistics;

import java.util.HashMap;
import java.util.Map;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

public class ExcelUtil {
	public static void addMergeCellBorder(CellRangeAddress cra, Sheet sheet) {
		RegionUtil.setBorderTop(BorderStyle.THIN, cra, sheet);
		RegionUtil.setBorderBottom(BorderStyle.THIN, cra, sheet);
		RegionUtil.setBorderLeft(BorderStyle.THIN, cra, sheet);
		RegionUtil.setBorderRight(BorderStyle.THIN, cra, sheet);
	}
	
	public static Map<String, XSSFCellStyle> getExcelStyle(XSSFWorkbook wb) {
		Map<String, XSSFCellStyle> result = new HashMap<>();
		DataFormat df = wb.createDataFormat();

		//title
		XSSFCellStyle titleStyle1 = wb.createCellStyle();
		titleStyle1.setAlignment(HorizontalAlignment.CENTER); // 水平居中
		titleStyle1.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中对齐
		XSSFFont titleFont1 = wb.createFont();
		titleFont1.setFontName("微软雅黑");
		titleFont1.setFontHeightInPoints((short) 10);
		titleFont1.setBold(true);
		titleFont1.setColor(IndexedColors.BLUE.getIndex());
		titleStyle1.setFont(titleFont1);
		titleStyle1.setBorderTop(BorderStyle.THIN);
		titleStyle1.setBorderBottom(BorderStyle.THIN);
		titleStyle1.setBorderLeft(BorderStyle.THIN);
		titleStyle1.setBorderRight(BorderStyle.THIN);
		titleStyle1.setDataFormat(df.getFormat("@"));
		titleStyle1.setWrapText(true);
		result.put("title", titleStyle1);
		
		//title2
		XSSFCellStyle titleStyle2 = wb.createCellStyle();
		titleStyle2.setAlignment(HorizontalAlignment.CENTER); // 水平居中
		titleStyle2.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中对齐
		XSSFFont titleFont2 = wb.createFont();
		titleFont2.setFontName("微软雅黑");
		titleFont2.setFontHeightInPoints((short) 16);
		titleFont2.setBold(true);
		titleFont2.setColor(IndexedColors.BLUE.getIndex());
		titleStyle2.setFont(titleFont2);
		titleStyle2.setBorderTop(BorderStyle.NONE);
		titleStyle2.setBorderBottom(BorderStyle.NONE);
		titleStyle2.setBorderLeft(BorderStyle.NONE);
		titleStyle2.setBorderRight(BorderStyle.NONE);
		titleStyle2.setDataFormat(df.getFormat("@"));
		titleStyle2.setWrapText(true);
		result.put("title2", titleStyle2);
		

		//title3
		XSSFCellStyle titleStyle3 = wb.createCellStyle();
		titleStyle3.setAlignment(HorizontalAlignment.CENTER); // 水平居中
		titleStyle3.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中对齐
		XSSFFont titleFont3 = wb.createFont();
		titleFont3.setFontName("微软雅黑");
		titleFont3.setFontHeightInPoints((short) 10);
		titleFont3.setBold(true);
		titleFont3.setColor(IndexedColors.BLUE.getIndex());
		titleStyle3.setFont(titleFont3);
		titleStyle3.setBorderTop(BorderStyle.THIN);
		titleStyle3.setBorderBottom(BorderStyle.THIN);
		titleStyle3.setBorderLeft(BorderStyle.THIN);
		titleStyle3.setBorderRight(BorderStyle.THIN);
		titleStyle3.setDataFormat(df.getFormat("@"));
		titleStyle3.setWrapText(true);
		result.put("title3", titleStyle3);
		
		//cell
		XSSFFont cellFont = wb.createFont();
		cellFont.setFontName("微软雅黑");
		cellFont.setFontHeightInPoints((short) 10);

		XSSFCellStyle cellStyle = wb.createCellStyle();
		cellStyle.setFont(cellFont);
		cellStyle.setAlignment(HorizontalAlignment.CENTER);
		cellStyle.setBorderTop(BorderStyle.THIN);
		cellStyle.setBorderBottom(BorderStyle.THIN);
		cellStyle.setBorderLeft(BorderStyle.THIN);
		cellStyle.setBorderRight(BorderStyle.THIN);
		result.put("cell", cellStyle);

		//cell_left
		XSSFCellStyle cellStyle_left = wb.createCellStyle();
		cellStyle_left.setFont(cellFont);
		cellStyle_left.setAlignment(HorizontalAlignment.LEFT);
		cellStyle_left.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中对齐
		cellStyle_left.setBorderTop(BorderStyle.THIN);
		cellStyle_left.setBorderBottom(BorderStyle.THIN);
		cellStyle_left.setBorderLeft(BorderStyle.THIN);
		cellStyle_left.setBorderRight(BorderStyle.THIN);
		cellStyle_left.setDataFormat(df.getFormat("@"));
		result.put("cell_left", cellStyle_left);


		//cell_right1
		XSSFCellStyle cellStyle_right1 = wb.createCellStyle();
		cellStyle_right1.setFont(cellFont);
		cellStyle_right1.setAlignment(HorizontalAlignment.RIGHT);
		cellStyle_right1.setBorderTop(BorderStyle.THIN);
		cellStyle_right1.setBorderBottom(BorderStyle.THIN);
		cellStyle_right1.setBorderLeft(BorderStyle.THIN);
		cellStyle_right1.setBorderRight(BorderStyle.THIN);
		cellStyle_right1.setDataFormat(df.getFormat("0"));

		result.put("cell_right1", cellStyle_right1);

		//cell_right2
		XSSFCellStyle cellStyle_right2 = wb.createCellStyle();
		cellStyle_right2.setFont(cellFont);
		cellStyle_right2.setAlignment(HorizontalAlignment.RIGHT);
		cellStyle_right2.setBorderTop(BorderStyle.THIN);
		cellStyle_right2.setBorderBottom(BorderStyle.THIN);
		cellStyle_right2.setBorderLeft(BorderStyle.THIN);
		cellStyle_right2.setBorderRight(BorderStyle.THIN);
		cellStyle_right2.setDataFormat(df.getFormat("0.00"));

		result.put("cell_right2", cellStyle_right2);

		
		//total_double
		XSSFFont totalFont = wb.createFont();
		totalFont.setFontName("微软雅黑");
		totalFont.setFontHeightInPoints((short) 10);
		totalFont.setColor(IndexedColors.BLUE.getIndex());

		XSSFCellStyle cellStyle_total_double = wb.createCellStyle();
		cellStyle_total_double.setFont(totalFont);
		cellStyle_total_double.setAlignment(HorizontalAlignment.RIGHT);
		cellStyle_total_double.setBorderTop(BorderStyle.THIN);
		cellStyle_total_double.setBorderBottom(BorderStyle.THIN);
		cellStyle_total_double.setBorderLeft(BorderStyle.THIN);
		cellStyle_total_double.setBorderRight(BorderStyle.THIN);
		cellStyle_total_double.setDataFormat(df.getFormat("0.00"));
		result.put("total_double", cellStyle_total_double);
		
		
		//total_int
		XSSFCellStyle cellStyle_total_int = wb.createCellStyle();
		cellStyle_total_int.setFont(totalFont);
		cellStyle_total_int.setAlignment(HorizontalAlignment.RIGHT);
		cellStyle_total_int.setBorderTop(BorderStyle.THIN);
		cellStyle_total_int.setBorderBottom(BorderStyle.THIN);
		cellStyle_total_int.setBorderLeft(BorderStyle.THIN);
		cellStyle_total_int.setBorderRight(BorderStyle.THIN);
		cellStyle_total_int.setDataFormat(df.getFormat("0"));
		result.put("total_int", cellStyle_total_int);
		
		//total_kilo_int
		XSSFCellStyle cellStyle_total_kilo_int = wb.createCellStyle();
		cellStyle_total_kilo_int.setFont(totalFont);
		cellStyle_total_kilo_int.setAlignment(HorizontalAlignment.RIGHT);
		cellStyle_total_kilo_int.setBorderTop(BorderStyle.THIN);
		cellStyle_total_kilo_int.setBorderBottom(BorderStyle.THIN);
		cellStyle_total_kilo_int.setBorderLeft(BorderStyle.THIN);
		cellStyle_total_kilo_int.setBorderRight(BorderStyle.THIN);
		cellStyle_total_kilo_int.setDataFormat(df.getFormat("#,##0"));
		result.put("total_kilo_int", cellStyle_total_kilo_int);
		
		
		
		//underLineTotal
		XSSFFont underLineTotalFont = wb.createFont();
		underLineTotalFont.setFontName("微软雅黑");
		underLineTotalFont.setFontHeightInPoints((short) 10);
		underLineTotalFont.setBold(true);
		underLineTotalFont.setColor(IndexedColors.BLUE.getIndex());
		underLineTotalFont.setUnderline(Font.U_SINGLE);

		XSSFCellStyle cellStyle_underLineTotal = wb.createCellStyle();
		cellStyle_underLineTotal.setFont(underLineTotalFont);
		cellStyle_underLineTotal.setAlignment(HorizontalAlignment.RIGHT);
		cellStyle_underLineTotal.setBorderTop(BorderStyle.THIN);
		cellStyle_underLineTotal.setBorderBottom(BorderStyle.THIN);
		cellStyle_underLineTotal.setBorderLeft(BorderStyle.THIN);
		cellStyle_underLineTotal.setBorderRight(BorderStyle.THIN);
		cellStyle_underLineTotal.setDataFormat(df.getFormat("0.00"));
		result.put("underLineTotal", cellStyle_underLineTotal);
		
		return result;
	}
}
