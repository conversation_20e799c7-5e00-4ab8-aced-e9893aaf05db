package com.foshan.service.community.task;


import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.Session;
import org.hibernate.SessionFactory;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.foshan.service.quartz.IFaithJob;
import com.foshan.util.DateUtil;
import com.foshan.util.HttpClientUtil;
import com.foshan.util.SpringHandler;
import com.foshan.util.community.CommunityContextInfo;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ParkingSpaceTask implements IFaithJob {
	
	public  void sendParkingSpaceWarnSms() {
		CommunityContextInfo communityContextInfo = (CommunityContextInfo)SpringHandler.getBean("communityContextInfo");
		
        GregorianCalendar gc = new GregorianCalendar();
        gc.setTimeInMillis(new Date().getTime());
        gc.add(Calendar.DATE, 5);
        String date = DateUtil.formatShortFormat(new Timestamp(gc.getTimeInMillis()));
        String day = date.substring(date.length()-3, date.length())+" ";
		
		StringBuilder sql = new StringBuilder(
				"SELECT b.unitCode,c.`phone`,a.`billingDate` FROM `t_community_member_property` a INNER JOIN `t_community_property` b "
				+ "ON b.id=a.`propertyId` INNER JOIN t_account c ON c.id=a.`memberId` "
				+ "WHERE a.parentPropertyId IS NULL and b.estateState=3 AND a.memberType=2 AND a.isCurrentMember=1 "
				+ "AND a.isCurrentOwner=1 AND billingDate LIKE'%"+day+"%'");
		
		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();
		@SuppressWarnings("unchecked")
		List<Object[]> resultList = session.createNativeQuery(sql.toString()).list();
		for (Object[] temp : resultList) {
			String unitCode = (null != temp[0] ? temp[0].toString() : "");
			String phone = (null != temp[1] ? temp[1].toString() : "");
			if(StringUtils.isNotEmpty(phone)) {
				String verifyCode = "尊敬的业户：您预交的车位临停费将于5天后("+date+")到期，如需续期办理请前往客服中心。祝生活愉快！";
				String templateData = "{\"date\":\""+date+"\"}";
				sendSms(phone.replaceAll("、", ",").replaceAll("/", ",")
						.replaceAll("，", ",").replaceAll(" ", ",")
						.replaceAll("\\\\", ",")
						,templateData,"SMS_475270255",verifyCode,communityContextInfo);
			}
			
		}
	}
	
	public  void sendSms(String phone,String templateData,String templateCode,String verifyCode,CommunityContextInfo communityContextInfo) {
		ObjectMapper objectMapper = new ObjectMapper();
		ObjectNode json = objectMapper.createObjectNode();
		// 发送短信
		json.put("messageContent", verifyCode);
		json.put("userNumber", phone);
		json.put("scheduleTime", DateUtil.format(new Date(), 1));
		json.put("f", "1");
		json.put("templateCode", templateCode);
		json.put("templateData", templateData);

		Map<String, String> result = null;
		try {
			objectMapper.setSerializationInclusion(Include.NON_NULL);
			log.info("--------------------------------------调用短信接口地址：" + communityContextInfo.smsInterfaceUrl + ";   参数："
					+ json.toString() + "-------------------------");
			result = HttpClientUtil.post(communityContextInfo.smsInterfaceUrl, objectMapper.writeValueAsString(json), "utf-8");
			log.info("--------------------------------------调用短信接口返回：" + result
					+ "----------------------------------------");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
}
