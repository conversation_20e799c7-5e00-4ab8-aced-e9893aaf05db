package com.foshan.service.community.impl;

import static java.util.stream.Collectors.groupingBy;

import java.io.IOException;
import java.net.URISyntaxException;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.ClientProtocolException;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.entity.DepartmentEntity;
import com.foshan.entity.DictionaryDataEntity;
import com.foshan.entity.DictionaryEntity;
import com.foshan.entity.WxServiceAccountEntity;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.DeviceCorrelationEntity;
import com.foshan.entity.community.DeviceEntity;
import com.foshan.entity.community.ReceiveMessageTypeEntity;
import com.foshan.entity.community.WarningMessageEntity;
import com.foshan.form.WebsocketClientMessageForm;
import com.foshan.form.community.MsgBasicInfoForm;
import com.foshan.form.community.MsgContentForm;
import com.foshan.form.community.aIDevice.AIDeviceForm;
import com.foshan.form.community.aIDevice.GetEquipmentOrganizationListByUserIdForm;
import com.foshan.form.community.request.AIDeviceReq;
import com.foshan.form.community.request.EmergencyMessageReq;
import com.foshan.form.community.response.aIDevice.DeviceResponse;
import com.foshan.form.community.response.aIDevice.GetEquipmentOrganizationListByUserIdResponse;
import com.foshan.form.message.TemplateData;
import com.foshan.form.message.WXSubscribeMessage;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.IWeiXinApiService;
import com.foshan.service.community.IAIDeviceService;
import com.foshan.service.websocket.WebsocketClient;
import com.foshan.util.DateUtil;
import com.foshan.util.HttpClientUtil;
import com.foshan.util.JsonUtil;
import com.foshan.util.SpringHandler;
import com.foshan.util.WeiXinApiUtil;

@Transactional
@Service("aIDeviceService")
public  class AIDeviceServiceImpl extends GenericCommunityService implements IAIDeviceService {
	private String url ="http://www.aiqdkj.com:8021/";
	private static String cookie;
	private static String subscribeMessageTemplateId="";
	private static String page="";
	private static String emergencyMessageUrl = "";

    public IResponse gainAIDeviceParameter() {
    	GenericResponse res = new GenericResponse();
		List<DictionaryDataEntity> dictionaryDataList = dictionaryDataDao
				.getListByHql("select distinct a from DictionaryDataEntity a "
						+ " inner join a.dictionary  b where b.directoryCode='messageParameter' and a.state=1", "");

		if (null != dictionaryDataList && dictionaryDataList.size() > 0) {
			for(DictionaryDataEntity d : dictionaryDataList) {
				if(d.getDataName().equals("page")) {
					page = d.getDataKey();
				}else if(d.getDataName().equals("subscribeMessageTemplateId")){
					subscribeMessageTemplateId = d.getDataKey();
				}else if(d.getDataName().equals("emergencyMessageUrl")) {
					emergencyMessageUrl = d.getDataKey();
				}
			}
    		res.setRet(ResponseContext.RES_SUCCESS_CODE);
    		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		}else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("请在数据字典里配置“微信消息提醒相关参数（subscribeMessageTemplateId、emergencyMessageUrl和page）"
					+ "”,directoryCode为\\\"messageParameter\\\"！");
			return res;
		}
		if(StringUtils.isEmpty(page) || StringUtils.isEmpty(subscribeMessageTemplateId)
				|| StringUtils.isEmpty(emergencyMessageUrl)) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("请在数据字典里配置“微信消息提醒相关参数（subscribeMessageTemplateId、emergencyMessageUrl和page）"
					+ "”,directoryCode为\"messageParameter\"！");
			return res;
		}
		
		return res;
    }
	
	@Override
	public IResponse receiveWarningMessage(AIDeviceReq req) {
		GenericResponse res = new GenericResponse();
//        if(StringUtils.isNotEmpty(req.getSignature()) && StringUtils.isNotEmpty(req.getThirdPlatformId())
//        		&& StringUtils.isNotEmpty(req.getTimestamp())) {
//        	long nowTime = System.currentTimeMillis();
//        	long timestamp= Long.valueOf(req.getTimestamp());
//        	if((timestamp>nowTime &&(timestamp-nowTime)>600000)  || 
//        			(nowTime>timestamp&&(nowTime-timestamp)>600000)) {
//				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
//				res.setRetInfo("请求超出时间范围！");
//				return res;
//        	}
//			
//			MessageAccountEntity messageAccount = messageAccountDao.findUnique("select distinct a "
//					+ "from MessageAccountEntity a where accountName='"+req.getThirdPlatformId()+"'", "");
//			String secretKey = "";
//			if (null != messageAccount) {
//				secretKey = Sm4Util.decryptEcb(messageAccount.getSalt(),messageAccount.getSecretKey());
//			} else {
//				res.setRet(ResponseContext.RES_PERM_NONEXISTENT_CODE);
//				res.setRetInfo("第三方系统编号不存在！");
//				return res;
//			}
//        	
//            String sign = DigestUtil.getMD5Str(req.getThirdPlatformId() + "_" + secretKey + "_" + req.getTimestamp(), "").toUpperCase();
//            if(!sign.equals(req.getSignature().toUpperCase())) {
//            	res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
//            	res.setRetInfo("签名错误！");
//            	return res;
//            }


		
			WarningMessageEntity warningMessage = new WarningMessageEntity();
			warningMessage.setAlarmTime(StringUtils.isNotEmpty(req.getAlarmTime()) ? req.getAlarmTime() : "");
			warningMessage.setDeviceName(StringUtils.isNotEmpty(req.getDeviceName()) ? req.getDeviceName() : "");
			warningMessage.setEventRecordId(StringUtils.isNotEmpty(req.getEventRecordId()) ? req.getEventRecordId() : "");
			warningMessage.setEventType(StringUtils.isNotEmpty(req.getEventType()) ? req.getEventType() : "");
			warningMessage.setJsonData(StringUtils.isNotEmpty(req.getJsonData()) ? req.getJsonData() : "");
			warningMessage.setLevelName(StringUtils.isNotEmpty(req.getLevelName()) ? req.getLevelName() : "");
			warningMessage.setHandleComment("");
			warningMessage.setHandleState(0);
			WebsocketClientMessageForm websocketClientMessageForm = new WebsocketClientMessageForm();

			ObjectMapper mapper = new ObjectMapper();
			Map<String, TemplateData> dataMap =  new HashMap<String, TemplateData>();
			if(warningMessage.getDeviceName().contains("alarmType") || warningMessage.getDeviceName().contains("address")){
				
//				Map<String, Object> postParam = new HashMap<>();
//				postParam.put("id", warningMessage.getId());
//				Map<String, Object> map = HttpClientUtil.formData(url+"api/eventrecord/selectById",postParam,cookie);
//				if(null!=map && map.containsKey("statusCode") && map.get("statusCode").equals(200)){
//					String data =  (String) map.get("data");
//					try {
//						Map<String, Object> m  =  mapper.readValue(data, Map.class);
//						if(m.containsKey("data")) {
//							List<Map<String,Object>> list = (List<Map<String, Object>>) m.get("data");
//							if(null!=list && list.size()>0 && list.get(0).containsKey("instalAddress")) {
//								dataMap.put("thing2", new TemplateData(null, (String)list.get(0).get("instalAddress")));
//							}
//						}
//					} catch (JsonMappingException e) {
//						e.printStackTrace();
//					} catch (JsonProcessingException e) {
//						e.printStackTrace();
//					}
//				}
				String alarmTime = "";
				if(StringUtils.isNotEmpty(warningMessage.getAlarmTime())) {
					alarmTime = DateUtil.format(new Date(Long.valueOf(warningMessage.getAlarmTime())),1);
					warningMessage.setAlarmTime(alarmTime);
				}
				warningMessage.setEventType("银龄守护卡SOS警告");
				String deviceId = "";
				String address = "";
				try {
					Map<String, Object> m  =  mapper.readValue(warningMessage.getDeviceName(), Map.class);
					deviceId =(String)m.get("deviceImei");
					warningMessage.setDeviceCode(StringUtils.isNotEmpty(deviceId) ? deviceId : warningMessage.getDeviceCode());
					dataMap.put("character_string1", new TemplateData(null, warningMessage.getDeviceCode().toString()));
					address =  m.containsKey("address") ? (String)m.get("address") :"佛山市三水区御江南国际社区明湖广场";

				} catch (JsonMappingException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				} catch (JsonProcessingException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				Map<ReceiveMessageTypeEntity, List<DeviceCorrelationEntity>> deviceCorrelationMap = getDeviceCorrelation(deviceId,warningMessage);
				if(null == deviceCorrelationMap) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
		    		res.setRetInfo("设备未注册！");
		    		return res;
				}
				websocketClientMessageForm.setDetailInterface("getWarningMessageInfo");
				websocketClientMessageForm.setRequestParameterName("warningMessageId");
				websocketClientMessageForm.setSeverity(3);
				warningMessageDao.save(warningMessage);
				websocketClientMessageForm.setEventId(warningMessage.getId().toString());
				if(null != warningMessage.getDevice().getEstate()) {
					CommunityEstateEntity estate = warningMessage.getDevice().getEstate(); 
					address = estate.getBuilding().getDistrict().getDistrictName()
					+ estate.getBuilding().getBuildingName() + estate.getFloor() + estate.getRoomNumber();
				}

				if(address.length()>20) {
					address = address.substring(0, 17)+"...";
				}
				
				dataMap.put("thing2", new TemplateData(null, address));

//				dataMap.put("thing2", new TemplateData(null, (String)list.get(0).get("instalAddress")));
				dataMap.put("thing3", new TemplateData(null, "银龄守护卡SOS警告"));
				
				dataMap.put("date4", new TemplateData(null, alarmTime));
				dataMap.put("thing5", new TemplateData(null, "银龄守护卡"));
				sendMessage(deviceCorrelationMap,warningMessage.getId().toString()+"&alarmType=1",dataMap);
			 }else {
				 if(StringUtils.isEmpty(req.getDeviceId())) {
					 req.setDeviceId("0001");
				 }
				 if(StringUtils.isNotEmpty(warningMessage.getEventRecordId()) && StringUtils.isNotEmpty(req.getDeviceId())) {
					 warningMessage.setDeviceCode(req.getDeviceId());					 
					 Map<ReceiveMessageTypeEntity, List<DeviceCorrelationEntity>> deviceCorrelationMap = getDeviceCorrelation(req.getDeviceId(),warningMessage);
					 if(null == deviceCorrelationMap) {
			    		res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
			    		res.setRetInfo("设备未注册！");
			    		return res;
					 }
					 warningMessageDao.save(warningMessage);
					 Map<String, Object> postParam = new HashMap<>();
					 postParam.put("id", warningMessage.getEventRecordId());
					 Map<String, Object> map = HttpClientUtil.formData(url+"api/eventrecord/selectById",postParam,cookie);
					 if(null!=map && map.containsKey("statusCode") && map.get("statusCode").equals(200)){
						 String data =  (String) map.get("data");
						 try {
							 Map<String, Object> m  =  mapper.readValue(data, Map.class);
							 if(m.containsKey("data")) {
								 List<Map<String,Object>> list = (List<Map<String, Object>>) m.get("data");
								 if(null!=list && list.size()>0 && list.get(0).containsKey("instalAddress")) {
									 dataMap.put("thing2", new TemplateData(null, (String)list.get(0).get("instalAddress")));
								 }
							 }
						 } catch (JsonMappingException e) {
							 e.printStackTrace();
						 } catch (JsonProcessingException e) {
							 e.printStackTrace();
						 }
					 }
					 dataMap.put("thing3", new TemplateData(null, warningMessage.getEventType()));
					 dataMap.put("character_string1", new TemplateData(null, warningMessage.getEventRecordId()));
					 dataMap.put("date4", new TemplateData(null, warningMessage.getAlarmTime()));
					 dataMap.put("thing5", new TemplateData(null, warningMessage.getDeviceName()));
					 sendMessage(deviceCorrelationMap,warningMessage.getEventRecordId()+"&alarmType=0",dataMap) ;
					 websocketClientMessageForm.setDetailInterface("selectById");
					 websocketClientMessageForm.setEventId(warningMessage.getEventRecordId());
					 websocketClientMessageForm.setRequestParameterName("id");
					 websocketClientMessageForm.setSeverity(4);
				 }
			 }
			try {
				websocketClientMessageForm.setMessageTile(warningMessage.getEventType());
				websocketClientMessageForm.setMessageContent(warningMessage.getEventType());
				
				websocketClientMessageForm.setSourceType(1);
				String json="";
				try {
					websocketClientMessageForm.setRemark(mapper.writeValueAsString(warningMessage));
					json = mapper.writeValueAsString(websocketClientMessageForm);
				} catch (JsonProcessingException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				WebsocketClient.sendMessageByClientType(json,2);
			} catch (IOException e) {
				e.printStackTrace();
			}
			 
        	res.setRet(ResponseContext.RES_SUCCESS_CODE);
        	res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);


//        }else {
//    		res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//    		res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//        }

        return res;
	}
	
	public void sendMessage(Map<ReceiveMessageTypeEntity, List<DeviceCorrelationEntity>> deviceCorrelationMap,
			String eventRecordId,Map<String, TemplateData> dataMap) {
		for(ReceiveMessageTypeEntity t : deviceCorrelationMap.keySet()) {
			List<DeviceCorrelationEntity> deviceCorrelationList = deviceCorrelationMap.get(t);
			switch(t.getTypeValue()) {
			case 0://应急广播
				for(DeviceCorrelationEntity deviceCorrelation : deviceCorrelationList) {
					EmergencyMessageReq emergencyMessageReq = new EmergencyMessageReq();
					MsgBasicInfoForm msgBasicInfo = new MsgBasicInfoForm();
					msgBasicInfo.setMsgType(1);
					msgBasicInfo.setEventType("10000");
					msgBasicInfo.setSeverity(4);
					msgBasicInfo.setSenderName("智慧社区");
					Calendar nowTime = Calendar.getInstance();
					nowTime.add(Calendar.SECOND, deviceCorrelation.getDevice().getRunTime());
					msgBasicInfo.setEndTime(DateUtil.format(nowTime.getTime(),1));
					msgBasicInfo.setStartTime(DateUtil.format(new Date(), 1));
					emergencyMessageReq.setMsgBasicInfo(msgBasicInfo);
					MsgContentForm msgContent = new MsgContentForm();
					msgContent.setAreaCode(deviceCorrelation.getRegionCode());
					msgContent.setMsgTitle(deviceCorrelation.getDevice().getTitleMsg());
					msgContent.setMsgDesc(deviceCorrelation.getDevice().getContentMsg());
					
					emergencyMessageReq.setMsgContent(msgContent);
					getResult(emergencyMessageReq,emergencyMessageUrl);
				}
				break;
			case 1://小程序
				for(DeviceCorrelationEntity deviceCorrelation : deviceCorrelationList) {
					if(null != deviceCorrelation.getMember() &&  
							null!=deviceCorrelation.getMember().getWxServiceAccountList() &&
							deviceCorrelation.getMember().getWxServiceAccountList().size()>0) {
						for(WxServiceAccountEntity wxServiceAccount : deviceCorrelation.getMember().getWxServiceAccountList()) {
							if(wxServiceAccount.getWxService().getServiceCode().equals("phmjxcx")||
									wxServiceAccount.getWxService().getServiceCode().equals("gduc")) {
								sendSubscribeMessage(wxServiceAccount.getOpenId(), 
										page+eventRecordId, subscribeMessageTemplateId ,dataMap);
							}
						}
 
					}
				}
				break;
			case 2://短信
				
				break;
			default:
				break;	
			}
		}
	}
	public GenericResponse getResult(EmergencyMessageReq req,String url) {
		//InvoiceRes res = new InvoiceRes();
		String postResult="";
		String jsonstr="";
		try {
			jsonstr = mapper.writeValueAsString(req);
		} catch (JsonProcessingException e1) {
			e1.printStackTrace();
		}
		try {
			postResult = HttpClientUtil.jsonPost(url, "UTF-8", jsonstr,
					null);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		//ObjectMapper mapper = new ObjectMapper();
		GenericResponse res = null;
		try {
			res = mapper.readValue(postResult, GenericResponse.class);
		} catch (JsonMappingException e) {
			e.printStackTrace();
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}

		return res;
	}
	
	public Map<ReceiveMessageTypeEntity, List<DeviceCorrelationEntity>>  getDeviceCorrelation(String deviceCode,WarningMessageEntity warningMessage) {
		DeviceEntity device = deviceDao.findUnique("select a from DeviceEntity a where a.deviceCode='"+deviceCode+"'", "");
		if(null == device) {
    		return null;
		}
		warningMessage.setDevice(device);
		Map<ReceiveMessageTypeEntity, List<DeviceCorrelationEntity>> collect1 = new HashMap<>();
		if(null == device.getDeviceCorrelationList()) {
			return collect1;
		}
		collect1 = (Map<ReceiveMessageTypeEntity, List<DeviceCorrelationEntity>> ) device.getDeviceCorrelationList()
				.parallelStream().collect(groupingBy(DeviceCorrelationEntity::getReceiveMessageType));
		return collect1;
	}
	private void sendSubscribeMessage(String openId, String page, String templateId, Map<String, TemplateData> dataMap) {
        try {
        	IWeiXinApiService weiXinApiService = (IWeiXinApiService) SpringHandler.getBean("weiXinApiService");
            String accessToken = weiXinApiService.getAccessToken(1);
        	//String accessToken = WeiXinApiUtil.getTokenByServiceCode("gduc");
            WXSubscribeMessage subscribeMessage = new WXSubscribeMessage();
            // 拼接数据
            subscribeMessage.setAccess_token(accessToken);
            subscribeMessage.setTouser(openId);
            subscribeMessage.setTemplate_id(templateId);//事先定义好的模板id
            subscribeMessage.setPage(page);
            subscribeMessage.setData(dataMap);
            String jsonBody = JsonUtil.objectToJSON(subscribeMessage);
 
            WeiXinApiUtil.sendSubscribeMessage(accessToken,jsonBody,1);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
	public IResponse getWxMiniOpenId(AIDeviceReq req){
		GenericResponse res = new GenericResponse();
		String wechatCode = req.getWechatCode();
		if (StringUtils.isEmpty(wechatCode)) {
			res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO + "缺少code，请重新绑定！");
			return res;
		}
		DepartmentEntity department = getDepartmentByCode(StringUtils.isNotEmpty(req.getDepartmentCode()) 
				? req.getDepartmentCode():"root");
		Map<String, String> params = new HashMap<String, String>();
		params.put("appid", department.getWxMiniProgramAppId());
		params.put("secret", department.getWxMiniProgramAppSecret());
		params.put("js_code", wechatCode);
		params.put("grant_type", "authorization_code");

		String accessTokenRes = null;
		String openid="";
		try {
			accessTokenRes = HttpClientUtil.paramsGet("https://api.weixin.qq.com/sns/jscode2session", params);
		} catch (ClientProtocolException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (URISyntaxException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		if (null != accessTokenRes) {
			ObjectMapper mapper = new ObjectMapper();
			Map<String, String> resMap = new HashMap<>();
			try {
				resMap = mapper.readValue(accessTokenRes, Map.class);
			} catch (JsonParseException e) {
				e.printStackTrace();
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}
			openid = resMap.get("openid");
		} else {
			res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO + "获取不到openid，请重新绑定。");
			return res;
		}
		if (StringUtils.isEmpty(openid)) {
			res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO + "获取不到openid，请重新绑定。");
			return res;
		}
		DictionaryEntity dictionary = dictionaryDao.getUniqueByHql("select distinct a from DictionaryEntity a "
				+ "where a.directoryCode='weiXinMessageParameter' and a.state=1 ", "");
		if(null!=dictionary) {
			DictionaryDataEntity dictionaryData = dictionaryDataDao
					.getUniqueByHql("select distinct a from DictionaryDataEntity a "
							+ " inner join a.dictionary  b where b.directoryCode='weiXinMessageParameter' and a.state=1", "");
			if(null == dictionaryData) {
				dictionaryData = new DictionaryDataEntity();
				dictionaryData.setOrders(10);
				dictionaryData.setDataName("opendId");
				dictionaryData.setDataKey(openid);
				dictionaryData.setDictionary(dictionary);
				dictionaryData.setState(1);
				dictionaryData.setLastModifyTime(new Timestamp(new Date().getTime()));
				dictionaryDataDao.save(dictionaryData);
			}
		}else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("请在数据字典里配置“微信消息提醒相关参数（subscribeMessageTemplateId、opendId和page）,directoryCode为\\\"weiXinMessageParameter\\\"！");
			return res;
		}
    	res.setRet(ResponseContext.RES_SUCCESS_CODE);
    	res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	public IResponse doLogin(AIDeviceReq req) {
		GenericResponse res = new GenericResponse();
		//接口不通先屏蔽
/*      
		String s = "{\"jsonData\":\"{\\\"str_loginName\\\":\\\"gdgdwl\\\",\\\"str_password\\\":\\\"123456789a\\\",\\\"str_image_code\\\":\\\"\\\"}\",\"time\":1676515680357}";
		JSONObject jsonObject = new JSONObject(s);
		try {
			cookie = HttpClientUtil.getCookieLogin(jsonObject, "http://www.aiqdkj.com:8021/api/webapi/login/doLogin");
		} catch (IOException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
*/
		return res;
	} 
	
	public IResponse getEventRecordListByPaging(AIDeviceReq req) {
		DeviceResponse res = new DeviceResponse();
//		String requestBody = "{\"jsonData\":\""+(StringUtils.isNotEmpty(req.getJsonData()) ? 
//				req.getJsonData()+"\"" : "{\\\"strStartTime\\\":\\\""++"\\\",\\\"strEndTime\\\":\\\"2023-04-21\\\","
//						+ "\\\"event_type_id\\\":-1,\\\"event_processing_status_id\\\":-1,\\\"event_category_id\\\":-1,"
//						+ "\\\"now_curr\\\":1,\\\"now_limit\\\":10,\\\"first_add_time\\\":0,\\\"search_device_ids\\\":\\\"\\\"}\"")
//				+",\"userAccount\": \"gdgdwl\",\"userID\": \"219\",\"time\":"+getTimeStr()+"}";
		
		StringBuilder requestBody = new StringBuilder();
		requestBody.append("{\"jsonData\":\"{")
			.append("\\\"event_type_id\\\":"+(null!=req.getEventTypeId()? req.getEventTypeId() :"-1")+",")
			.append( "\\\"strStartTime\\\":\\\""+(StringUtils.isNotEmpty(req.getStartTime())? req.getStartTime() :DateUtil.format(new Date(),0))+"\\\",")
			.append( "\\\"strEndTime\\\":\\\""+(StringUtils.isNotEmpty(req.getEndTime())? req.getEndTime() :DateUtil.format(new Date(),1))+"\\\",")
			.append("\\\"event_processing_status_id\\\":\\\""+ (null!=req.getEventProcessingStatusId() ? req.getEventProcessingStatusId() :"-1")+"\\\",")
			.append("\\\"event_category_id\\\":"+(null!=req.getEventCategoryId() ? req.getEventCategoryId() :"-1"))
			.append(",\\\"now_curr\\\":"+(null!=req.getRequestPage() ? req.getRequestPage() : 1)+
	    		",\\\"now_limit\\\":"+(null!=req.getPageSize() ? req.getPageSize() : 10)+",\\\"first_add_time\\\":"+
	    		(null!=req.getFirstAddTime() ? req.getFirstAddTime() :"0")+",\\\"search_device_ids\\\":\\\""+
	    		(StringUtils.isNotEmpty(req.getSearchDeviceIds())? req.getSearchDeviceIds() :"")+"\\\"}\"");
	
	requestBody.append(",\"userAccount\": \"gdgdwl\",\"userID\": \"219\",\"time\":"+getTimeStr()+"}");
		String str = send(url+"api/webapi/eventrecordWX/get_event_record_list_by_paging",requestBody.toString());
		AIDeviceForm form = new AIDeviceForm();
		
		if (StringUtils.isNotEmpty(str)) {
			try {
				form = mapper.readValue(str, AIDeviceForm.class);
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			if(form.getStatusCode().equals(200)) {
				Map<String,Object> map = (Map) form.getData();
				res.setData(new JSONObject(map).toString());
	        	res.setRet(ResponseContext.RES_SUCCESS_CODE);
	        	res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else {
				res.setData("{}");
	        	res.setRet(ResponseContext.RES_QUERY_PAYMENT_RESULT_ERROR_CODE);
	        	res.setRetInfo(form.getMsg());
			}
		}else {
			
		}
		return res;
	} 
	
	public IResponse selectById(AIDeviceReq req) {
		DeviceResponse res = new DeviceResponse();
		if(StringUtils.isEmpty(req.getId())) {
    		res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
    		res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
    		return res;
		}
		Map<String, Object> postParam = new HashMap<>();
		postParam.put("id", req.getId());
		Map<String, Object> map = HttpClientUtil.formData(url+"api/eventrecord/selectById",postParam,cookie);
		if(null!=map && map.containsKey("statusCode") && map.get("statusCode").equals(200)){
			String m =  (String) map.get("data");
			res.setData(new JSONObject(m).toString());
        	res.setRet(ResponseContext.RES_SUCCESS_CODE);
        	res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		}else if(null!=map && map.containsKey("statusCode") && map.get("statusCode").equals(401)){
			
		}else {
		
		}	
		return res;
	} 
	
	public IResponse selectImgByEventRecordId(AIDeviceReq req) {
		DeviceResponse res = new DeviceResponse();
		if(StringUtils.isEmpty(req.getEventRecordId())) {
    		res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
    		res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
    		return res;
		}
		Map<String, Object> postParam = new HashMap<>();
		postParam.put("eventRecordId", req.getEventRecordId());
		Map<String, Object> map = HttpClientUtil.formData(url+"api/eventrecord/selectImgByEventRecordId",postParam,cookie);
		if(null!=map && map.containsKey("statusCode") && map.get("statusCode").equals(200)){
			String m =  (String) map.get("data");
			res.setData(new JSONObject(m).toString());
        	res.setRet(ResponseContext.RES_SUCCESS_CODE);
        	res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		}else if(null!=map && map.containsKey("statusCode") && map.get("statusCode").equals(401)){
			
		}else {
		
		}	
		return res;
	}
	
	public IResponse getBackVideo(AIDeviceReq req) {
		DeviceResponse res = new DeviceResponse();
		StringBuilder requestBody = new StringBuilder();
		requestBody.append("{\"jsonData\":\"{")
			.append("\\\"parameter_type\\\":"+(null!=req.getParameterType()? req.getParameterType() :"1")+",")
			.append( "\\\"startTime\\\":\\\""+(StringUtils.isNotEmpty(req.getStartTime())? req.getStartTime() :DateUtil.format(new Date(),0)+" 00:00:00")+"\\\",")
			.append( "\\\"endTime\\\":\\\""+(StringUtils.isNotEmpty(req.getEndTime())? req.getEndTime() :DateUtil.format(new Date(),1))+"\\\",")
			.append("\\\"video_cloud_equipment_id\\\":\\\""+ (StringUtils.isNotEmpty(req.getVideoCloudEquipmentId()) ? req.getVideoCloudEquipmentId() :"")+"\\\",")
		    .append("\\\"video_cloud_passageway_id\\\":\\\""+(StringUtils.isNotEmpty(req.getVideoCloudPassagewayId()) ? req.getVideoCloudPassagewayId() :"")+"\\\"}\"");
		
		requestBody.append(",\"userAccount\": \"gdgdwl\",\"userID\": \"219\",\"time\":"+getTimeStr()+"}");
		String str = send(url+"api/webapi/sys_config/getBackVideo",requestBody.toString());
		AIDeviceForm form = new AIDeviceForm();
		
		if (StringUtils.isNotEmpty(str)) {
			try {
				form = mapper.readValue(str, AIDeviceForm.class);
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			if(form.getStatusCode().equals(200)) {
				Map<String,Object> map = (Map) form.getData();
				res.setData(new JSONObject(map).toString());
	        	res.setRet(ResponseContext.RES_SUCCESS_CODE);
	        	res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else {
				res.setData("{}");
	        	res.setRet(ResponseContext.RES_QUERY_PAYMENT_RESULT_ERROR_CODE);
	        	res.setRetInfo(form.getMsg());
			}
		}else {
			
		}
		return res;
	} 
	
	public IResponse getRealVideo(AIDeviceReq req) {
		DeviceResponse res = new DeviceResponse();
		
		StringBuilder requestBody = new StringBuilder();
		requestBody.append("{\"jsonData\":\"{")
			.append("\\\"parameter_type\\\":"+(null!=req.getParameterType()? req.getParameterType() :"1")+",")
			.append( "\\\"device_id\\\":"+(StringUtils.isNotEmpty(req.getDeviceId())? req.getDeviceId() :0)+",")
			.append("\\\"video_cloud_equipment_id\\\":\\\""+ (StringUtils.isNotEmpty(req.getVideoCloudEquipmentId()) ? req.getVideoCloudEquipmentId() :"")+"\\\",")
		    .append("\\\"video_cloud_passageway_id\\\":\\\""+(StringUtils.isNotEmpty(req.getVideoCloudPassagewayId()) ? req.getVideoCloudPassagewayId() :"")+"\\\"}\"");
		
		requestBody.append(",\"userAccount\": \"gdgdwl\",\"userID\": \"219\",\"time\":"+getTimeStr()+"}");
		String str = send(url+"api/webapi/sys_config/getRealVideo",requestBody.toString());
		AIDeviceForm form = new AIDeviceForm();
		
		if (StringUtils.isNotEmpty(str)) {
			try {
				form = mapper.readValue(str, AIDeviceForm.class);
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			if(form.getStatusCode().equals(200)) {
				Map<String,Object> map = (Map) form.getData();
				res.setData(new JSONObject(map).toString());
	        	res.setRet(ResponseContext.RES_SUCCESS_CODE);
	        	res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else {
				res.setData("{}");
	        	res.setRet(ResponseContext.RES_QUERY_PAYMENT_RESULT_ERROR_CODE);
	        	res.setRetInfo(form.getMsg());
			}
		}else {
			
		}
		return res;
	} 
	
	public IResponse getEquipmentOrganizationListByUserId(AIDeviceReq req) {
		GetEquipmentOrganizationListByUserIdResponse res = new GetEquipmentOrganizationListByUserIdResponse();
		StringBuilder requestBody = new StringBuilder();
		requestBody.append("{\"jsonData\":\"{")
			.append("\\\"device_type_id\\\":"+(null!=req.getParameterType()? req.getParameterType() :"-1")+",")
			.append( "\\\"device_category_id\\\":"+(null!=req.getDeviceCategoryId()? req.getDeviceCategoryId() :"1")+",")
			.append("\\\"hide_device_status_id\\\":"+ (null!=req.getHideDeviceStatusId()? req.getHideDeviceStatusId() :"0")+"}\",");
		requestBody.append("\"userAccount\": \"gdgdwl\",\"userID\": \"219\",\"time\":"+getTimeStr()+"}");
		
		String str = send(url+"api/webapi/device/get_equipment_organization_list_by_user_id",requestBody.toString());
		GetEquipmentOrganizationListByUserIdForm form = new GetEquipmentOrganizationListByUserIdForm();
		
		if (StringUtils.isNotEmpty(str)) {
			try {
				form = mapper.readValue(str, GetEquipmentOrganizationListByUserIdForm.class);
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			if(form.getStatusCode().equals(200)) {
				//List<Map<String,Object>> list = (List) form.getData();
				form.getData().forEach(o->{
					Map<String,Object> map = (Map) o;
					res.getData().add(new JSONObject(map).toString());
				});
	        	res.setRet(ResponseContext.RES_SUCCESS_CODE);
	        	res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else {
	        	res.setRet(ResponseContext.RES_QUERY_PAYMENT_RESULT_ERROR_CODE);
	        	res.setRetInfo(form.getMsg());
			}
		}else {
			
		}
		return res;
	} 
	

	public IResponse updateStatus(AIDeviceReq req) {
		DeviceResponse res = new DeviceResponse();
		if(StringUtils.isEmpty(req.getEventRecordId()) ) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			return res;
		}
		Map<String, Object> postParam = new HashMap<>();
		postParam.put("eventRecordId", req.getEventRecordId());
		postParam.put("eventProcessingStatusId", StringUtils.isNotEmpty(req.getEventProcessingStatusId()) ? 
				req.getEventProcessingStatusId() : "1");
		postParam.put("event_confirm_status_id", StringUtils.isNotEmpty(req.getEventConfirmStatusId()) ? 
				req.getEventConfirmStatusId() : "1");
		postParam.put("alarmHandleFalseWhereId", StringUtils.isNotEmpty(req.getAlarmHandleFalseWhereId()) ? 
				req.getAlarmHandleFalseWhereId() : "0");
		postParam.put("alarmHandleTrueWhereId", StringUtils.isNotEmpty(req.getAlarmHandleTrueWhereId()) ? 
				req.getAlarmHandleTrueWhereId() : "0");
		Map<String, Object> map = HttpClientUtil.formData(url+"api/eventrecord/updateStatus",postParam,cookie);
		if(null!=map && map.containsKey("statusCode") && map.get("statusCode").equals(200)){
			String m =  (String) map.get("data");
			res.setData(new JSONObject(m).toString());
	    	res.setRet(ResponseContext.RES_SUCCESS_CODE);
	    	res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		}else if(null!=map && map.containsKey("statusCode") && map.get("statusCode").equals(401)){
			
		}else {
		
		}	
		return res;
	}
	
	public String send(String url,String req) {
		String str = "";
		try {
			str = HttpClientUtil.jsonPost(url,"UTF-8",req,null,cookie);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return str.replace("InvalidToken", "invalidToken");
	}
	
	public String getTimeStr() {
		return System.currentTimeMillis()+"";
	}

	
}
