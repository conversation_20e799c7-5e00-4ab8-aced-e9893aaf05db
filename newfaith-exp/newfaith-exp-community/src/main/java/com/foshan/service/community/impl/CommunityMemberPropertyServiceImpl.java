package com.foshan.service.community.impl;


import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.dao.generic.Page;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.RoleEntity;
import com.foshan.entity.community.CommunityContractEntity;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityMemberEntity;
import com.foshan.entity.community.CommunityMemberPropertyEntity;
import com.foshan.entity.community.CommunityPropertyEntity;
import com.foshan.entity.community.CommunityRoleEntity;
import com.foshan.entity.community.context.CommunityContext;
import com.foshan.entity.community.vo.ReservedFieldVo;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.AssetForm;
import com.foshan.form.community.CommunityContractForm;
import com.foshan.form.community.CommunityMemberContractForm;
import com.foshan.form.community.CommunityMemberForm;
import com.foshan.form.community.CommunityMemberPropertyForm;
import com.foshan.form.community.CommunityPayItemsPriceForm;
import com.foshan.form.community.request.CommunityMemberPropertyReq;
import com.foshan.form.community.response.communityMemberProperty.GetCommunityMemberPropertyInfoRes;
import com.foshan.form.community.response.communityMemberProperty.GetCommunityMemberPropertyListRes;
import com.foshan.form.community.response.exportExcel.ExportExcelRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityMemberPropertyService;
import com.foshan.util.DateUtil;
import com.foshan.util.DigestUtil;
import com.foshan.util.ExcelExportUtil;
import com.foshan.util.community.CommunityCache;


@Transactional
@Service("communityMemberPropertyService")
public  class CommunityMemberPropertyServiceImpl extends GenericCommunityService implements ICommunityMemberPropertyService {

	@Override
	public IResponse getCommunityMemberPropertyList(CommunityMemberPropertyReq req) {
		GetCommunityMemberPropertyListRes res = new GetCommunityMemberPropertyListRes();
		Page<CommunityMemberPropertyEntity> page = new Page<CommunityMemberPropertyEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		
		page = communityMemberPropertyDao.queryPage(page, queryCondition(req));
		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityMemberPropertyForm memberProperForm = new CommunityMemberPropertyForm();
			memberProperForm.setMemberPropertyId(o.getId());
			memberProperForm.setBillingDate(null != o.getBillingDate()? DateUtil.formatShortFormat(o.getBillingDate()) : "");
 			memberProperForm.setBillingDateModify(null != o.getBillingDateModify()? DateUtil.formatShortFormat(o.getBillingDateModify()) : "");
			memberProperForm.setCreateTime(null != o.getCreateTime()? DateUtil.formatShortFormat(o.getCreateTime()) : "");
			memberProperForm.setIsCurrentOwner(o.getIsCurrentOwner());
			memberProperForm.setRecordDate(null != o.getRecordDate()? DateUtil.formatShortFormat(o.getRecordDate()) : "");
			memberProperForm.setTerminationDate(null != o.getTerminationDate()? DateUtil.formatShortFormat(o.getTerminationDate()) : "");
//			memberProperForm.setAcceptanceDate(null != o.getAcceptanceDate()? DateUtil.formatLongFormat(o.getAcceptanceDate()) : "");
			memberProperForm.setMemberType(o.getMemberType());
			memberProperForm.setAuditState(o.getAuditState());
			memberProperForm.setIsCurrentMember(o.getIsCurrentMember());
			memberProperForm.setEndDate(null != o.getEndDate()? DateUtil.formatShortFormat(o.getEndDate()) : "");
			if(null != o.getContract()) {
				memberProperForm.setContract(getContractForm(o.getContract()));
			}
			memberProperForm.setComment(o.getComment());
			
			CommunityMemberForm memberForm = new CommunityMemberForm();
			memberForm.setMemberId(o.getMember().getId());
			memberForm.setEmail(o.getMember().getEmail());
			memberForm.setHomePhone(o.getMember().getHomePhone());
			memberForm.setNickName(o.getMember().getNickName());
			memberForm.setOfficePhone(o.getMember().getOfficePhone());
			memberForm.setPhone(o.getMember().getPhone());
			memberForm.setRegistName(o.getMember().getRegistName());
			memberForm.setSex(o.getMember().getSex());
			memberForm.setIdCard(o.getMember().getIdCard());
			
			memberForm.setSmartcardId(o.getMember().getSmartcardId());
			memberForm.setHeadImage(StringUtils.isNotEmpty(o.getMember().getHeadImage()) ? o.getMember().getHeadImage() : "");
			memberForm.setUserName(StringUtils.isNotEmpty(o.getMember().getUserName()) ? o.getMember().getUserName() : "");
			memberForm.setContactPerson(StringUtils.isNotEmpty(o.getMember().getContactPerson()) ? o.getMember().getContactPerson() : "");
			memberForm.setEmergencyContact(StringUtils.isNotEmpty(o.getMember().getEmergencyContact()) ? o.getMember().getEmergencyContact() : "");
			memberProperForm.setMemberForm(memberForm);
			memberProperForm.setPropertyForm(getCommunityPropertyForm(o.getProperty(),null,null));
			memberProperForm.setCarInfo(StringUtils.isNotEmpty(o.getCarInfo()) ? o.getCarInfo() : "");
			memberProperForm.setParentPropertyForm(getCommunityPropertyForm(o.getParentProperty(),null,null));
			memberProperForm.setBuyersName(StringUtils.isNotEmpty(o.getBuyersName()) ? o.getBuyersName() : "");
			memberProperForm.setBuyersAddress(StringUtils.isNotEmpty(o.getBuyersAddress()) ? o.getBuyersAddress() : "");
			memberProperForm.setBusinessType(StringUtils.isNotEmpty(o.getBusinessType()) ? o.getBusinessType() : "");
			memberProperForm.setPaytaxNo(StringUtils.isNotEmpty(o.getPaytaxNo()) ? o.getPaytaxNo() : "");
			memberProperForm.setBuyersBankAccount(StringUtils.isNotEmpty(o.getBuyersBankAccount()) ? o.getBuyersBankAccount() : "");
			memberProperForm.setBuyerEmail(StringUtils.isNotEmpty(o.getBuyerEmail()) ? o.getBuyerEmail() : "");
			memberProperForm.setContractInfo(StringUtils.isNotEmpty(o.getContractInfo()) ? o.getContractInfo() : "");
			memberProperForm.setRentType(o.getRentType());
			o.getPayItemsPriceList().forEach(p->{
				CommunityPayItemsPriceForm payItemsPriceForm = new CommunityPayItemsPriceForm();
				payItemsPriceForm.setStartTime(p.getComment());
				payItemsPriceForm.setPayItemsPriceId(p.getId());
				payItemsPriceForm.setPeriod(p.getPeriod());
				payItemsPriceForm.setPrice(p.getPrice().toString());
				payItemsPriceForm.setCreateTime(null != p.getCreateTime() ? DateUtil.formatLongFormat(p.getCreateTime()) : "");
				payItemsPriceForm.setEndTime(null != p.getEndTime() ? DateUtil.formatLongFormat(p.getEndTime()) : "");
				payItemsPriceForm.setLastModifyTime(null != p.getLastModifyTime() ? DateUtil.formatLongFormat(p.getLastModifyTime()) : "");
				payItemsPriceForm.setStartTime(null != p.getStartTime() ? DateUtil.formatLongFormat(p.getStartTime()) : "");
				payItemsPriceForm.setItemsName(p.getPayItems().getItemsName());
				payItemsPriceForm.setChargeCategory(p.getPayItems().getChargeCategory());
				payItemsPriceForm.setPriceUnit(p.getPayItems().getPriceUnit());
				payItemsPriceForm.setFeeCalType(p.getPayItems().getFeeCalType());
				payItemsPriceForm.setPayDate(p.getPayItems().getPayDate());
				payItemsPriceForm.setIsBreach(p.getPayItems().getIsBreach());
				
				memberProperForm.getPayItemsPriceForm().add(payItemsPriceForm);
			});
		
			res.getMemberPropertyList().add(memberProperForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	
	public CommunityMemberContractForm getContractForm(CommunityContractEntity contract) {
		CommunityMemberContractForm communityContractForm = new CommunityMemberContractForm();
		communityContractForm.setContractId(contract.getId());
        communityContractForm.setContractName(contract.getContractName());
		communityContractForm.setCreateTime(DateUtil.formatLongFormat(contract.getCreateTime()));
		communityContractForm.setLastModifyTime(DateUtil.formatLongFormat(contract.getLastModifyTime()));
		communityContractForm.setContractCode(contract.getContractCode());
		communityContractForm.setStartTime(DateUtil.formatLongFormat(contract.getStartTime()));
		communityContractForm.setEndTime(DateUtil.formatLongFormat(contract.getEndTime()));
		communityContractForm.setRentInfo(contract.getRentInfo());
		communityContractForm.setContractBrief(contract.getContractBrief());
		communityContractForm.setDepositInfo(contract.getDepositInfo());
		communityContractForm.setBusinessCategory(contract.getBusinessCategory());
		communityContractForm.setRentPayType(contract.getRentPayType());
		communityContractForm.setPaytaxNo(contract.getPaytaxNo());
		communityContractForm.setBuyersName(contract.getBuyersName());
		communityContractForm.setInvoiceDescription(contract.getInvoiceDescription());
		communityContractForm.setIsEffective(contract.getIsEffective());
		communityContractForm.setAssetForm(getAsset(contract.getAsset()));
		return communityContractForm;
	}

	protected AssetForm getAsset(AssetEntity asset) {
		
		AssetForm assetForm = new AssetForm();
		if (null != asset) {
			assetForm.setAssetId(asset.getId());
			assetForm.setAssetType(asset.getAssetType());
			assetForm.setImageFile(asset.getImageFile());
			assetForm.setSmallImageFile(asset.getSmallImageFile());
			assetForm.setIsCover(asset.getIsCover());
			assetForm.setPackageFlag(asset.getPackageFlag());
			assetForm.setTimeLength(asset.getTimeLength());
			assetForm.setSummaryShort(asset.getSummaryShort());
			assetForm.setParameterInfo(asset.getParameterInfo());
			assetForm.setAssetCode(asset.getAssetCode());
			assetForm.setAssetName(asset.getAssetName());
			assetForm.setAssetOrders(asset.getAssetOrders());
			assetForm.setPublishedTime(
					asset.getPublishedTime() != null ? 
							new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
							.format(asset.getPublishedTime()): "");
			assetForm.setPackageOrders(asset.getPackageCount());
			assetForm.setServiceCode(asset.getAssetCode());
			assetForm.setValidTime(asset.getValidTime() != null
					? new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(asset.getValidTime())
					: "");
			assetForm.setServiceId(asset.getServiceId());
			assetForm.setMiddleImageFile(asset.getMiddleImageFile());
			assetForm.setRecommendCount(asset.getRecommendCount());
			assetForm.setDirector(asset.getDirector());
			assetForm.setActorsDisplay(asset.getActorsDisplay());
			assetForm.setBroadcastCount(asset.getBroadcastCount());
			assetForm.setPreviewAssetId(asset.getPreviewAssetId());
			assetForm.setPreviewProviderId(asset.getPreviewProviderId());
			assetForm.setPackageCount(asset.getSubAssetSet().size());
			asset.getSubAssetSet().forEach(o -> {
				assetForm.getSubAssetList().add(getAsset(o));
			});
		}
		return assetForm;
	}
	public String queryCondition(CommunityMemberPropertyReq req) {
		Object userObj = getPrincipal(true);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityMemberPropertyEntity a "
				+ "inner join a.property b inner join a.member c ");
	
		hql.append(null!=req.getContractId() ? " inner join a.contract d where d.id="+req.getContractId():"  where 1=1 ")
			.append(StringUtils.isNotEmpty(req.getPropertyIdList()) ?" and b.id in ("+req.getPropertyIdList()+")":"")
			.append(StringUtils.isNotEmpty(req.getMemberIdList()) ?" and c.id in ("+req.getMemberIdList()+")":"")
			.append(null!=req.getAuditState() ? " and a.auditState = "+req.getAuditState() :"")
			.append(null!=req.getMemberType() ? " and a.memberType="+req.getMemberType() : "")
			.append(null!=req.getAuditState() ?" and a.auditState="+req.getAuditState():"")
			.append(null!=req.getIsCurrentMember() ?" and a.isCurrentMember="+req.getIsCurrentMember() :"")
			.append(StringUtils.isNotEmpty(req.getStartTime())&&StringUtils.isNotEmpty(req.getEndTime()) ? 
					" and a.createTime>='"+req.getStartTime()+"' and a.createTime<='"+req.getEndTime()+"'" : "")
			.append(StringUtils.isNotEmpty(req.getStartModifyTime())&&StringUtils.isNotEmpty(req.getEndModifyTime()) ? 
					" and a.billingDateModify>='"+req.getStartModifyTime()+"' and a.billingDateModify<='"+req.getEndModifyTime()+"'" : "")
			.append(null!=req.getRentType() ? " and a.rentType="+req.getRentType() :"")
			.append(null!=req.getIsCurrentOwner() ?" and a.isCurrentOwner="+req.getIsCurrentOwner() :"");
		if(null!=userObj && userObj instanceof CommunityMemberEntity) {
			CommunityMemberEntity member = (CommunityMemberEntity)userObj;
			hql.append(" and c.id="+member.getId());
		}
		hql.append(" ORDER BY a.createTime DESC");
		return hql.toString();
		
	}
	
	public IResponse exportBillingDate(CommunityMemberPropertyReq req, HttpServletResponse response) {
		ExportExcelRes res = new ExportExcelRes();
		List<CommunityMemberPropertyEntity> memberPropertyList = communityMemberPropertyDao
				.getListByHql(queryCondition(req), "");
		Map<String, String> heardMap = new LinkedHashMap<String, String>();
		heardMap.put("unitCode", "单元编号");
		heardMap.put("billingDate", "计费日期");
		heardMap.put("billingDateModify", "计费日期最后修改时间");
		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		for (int i = 0; i < memberPropertyList.size(); i++) {
			CommunityMemberPropertyEntity memberProperty = memberPropertyList.get(i);
			Map<String, Object> map = new HashMap<String, Object>();
			CommunityEstateEntity estate = (CommunityEstateEntity) memberProperty.getProperty();
			map.put("unitCode", estate.getUnitCode());
			map.put("billingDate", null != memberProperty.getBillingDate() ? 
					DateUtil.formatShortFormat(memberProperty.getBillingDate()) : "");
			map.put("billingDateModify", null != memberProperty.getBillingDateModify() ? 
					DateUtil.formatShortFormat(memberProperty.getBillingDateModify()) : "");
			dataList.add(map);

		}
		try {
			ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("10"), Short.valueOf("300"), null, "",
					"", "", response);
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}
		return res;
	}
	@Audit(operate = "新增租住")
	public IResponse addCommunityRentMemberProperty(CommunityMemberPropertyReq req) {
		CommunityContractEntity communityContract = null;
		if(null != req.getContractId()) {
			communityContract = communityContractDao.get(req.getContractId());
			if(null == communityContract) {
				GenericResponse res = new GenericResponse();
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
		}else {
			communityContract = new CommunityContractEntity();
			communityContract.setBusinessCategory(StringUtils.isNotEmpty(req.getBusinessCategory()) ? req.getBusinessCategory():"");
			communityContract.setBuyersName(StringUtils.isNotEmpty(req.getBuyersName()) ? req.getBuyersName():"");
			communityContract.setContractBrief(StringUtils.isNotEmpty(req.getContractBrief()) ? req.getContractBrief():"");
			communityContract.setContractCode(StringUtils.isNotEmpty(req.getContractCode()) ? req.getContractCode():"");
			communityContract.setContractName(StringUtils.isNotEmpty(req.getContractName()) ? req.getContractName():"");
			communityContract.setDepositInfo(StringUtils.isNotEmpty(req.getDepositInfo()) ? req.getDepositInfo():"");
			communityContract.setInvoiceDescription(StringUtils.isNotEmpty(req.getInvoiceDescription()) ? req.getInvoiceDescription():"");
			communityContract.setIsEffective(EntityContext.RECORD_STATE_VALID);
			communityContract.setLastModifyTime(new Timestamp(new Date().getTime()));
			communityContract.setPaytaxNo(StringUtils.isNotEmpty(req.getPaytaxNo()) ? req.getPaytaxNo():"");
			communityContract.setRentInfo(StringUtils.isNotEmpty(req.getRentInfo()) ? req.getRentInfo():"");
			communityContract.setRentPayType(StringUtils.isNotEmpty(req.getRentPayType()) ? req.getRentPayType():"");
			communityContract.setState(EntityContext.RECORD_STATE_VALID);
			if(StringUtils.isNotEmpty(req.getStartTime())) {
				try {
					communityContract.setStartTime(new Timestamp(DateUtil.parseLongFormat(req.getStartTime()).getTime()));
				} catch (ParseException e) {
					e.printStackTrace();
				}
			}
			if(StringUtils.isNotEmpty(req.getEndTime())) {
				try {
					communityContract.setEndTime(new Timestamp(DateUtil.parseLongFormat(req.getEndTime()).getTime()));
				} catch (ParseException e) {
					e.printStackTrace();
				}
			}
			communityContract.setAsset(
	                null != req.getAssetId() ? assetDao.get(req.getAssetId()) : null);
		}

		return addCommunityMemberProperty(req,communityContract);
	}
	@Override
	@Audit(operate = "新增会员资产")
	public IResponse addCommunityMemberProperty(CommunityMemberPropertyReq req,CommunityContractEntity communityContract) {
		GenericResponse res = new GenericResponse();
		if (StringUtils.isNotEmpty(req.getPropertyIdList()) && null!=req.getIsCurrentOwner()) {
			CommunityMemberEntity member = null;
			if(null != req.getMemberId()) {
				member = communityMemberDao.get(req.getMemberId());
			}else {
				Map<String,Object> map = addCommunityMember(req);
				if(!map.get("ret").toString().equals(ResponseContext.RES_SUCCESS_CODE)) {
					res.setRet((String)map.get("ret"));
					res.setRetInfo((String)map.get("retInfo"));
					return res;
				}
				member = (CommunityMemberEntity) map.get("member");
			}
			
			if(null == member) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
            String[] propertyIds = req.getPropertyIdList().split(",");
            for (String propertyId : propertyIds) {
            	CommunityEstateEntity property = communityEstateDao.get(Integer.parseInt(propertyId));
            	if(null != property) {
        			StringBuilder hql = new StringBuilder("select distinct a from CommunityMemberPropertyEntity a "
    						+ "inner join a.property b inner join a.member c  where b.id="+Integer.parseInt(propertyId));
        			if(req.getIsCurrentOwner() == 1) {
              			CommunityMemberPropertyEntity isCurrentOwner = communityMemberPropertyDao.
        						findUnique(hql.toString()+" and a.isCurrentMember=1 and a.memberType=0", "");
              			if(null != isCurrentOwner) {
/*            				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
            				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
            				return res;*/
		          			isCurrentOwner.setIsCurrentOwner(0);
		          			isCurrentOwner.setTerminationDate(new Date());
		          			isCurrentOwner.setIsCurrentMember(0);
            			}
        			}
        			
    				CommunityMemberPropertyEntity communityMemberProperty = new CommunityMemberPropertyEntity();
//    						communityMemberPropertyDao.
//    						findUnique(hql.toString()+" and c.id="+member.getId(), "");
//    				if(null == communityMemberProperty) {
//    					communityMemberProperty = new CommunityMemberPropertyEntity();
//    				}
    				communityMemberProperty.setContractInfo(StringUtils.isNotEmpty(req.getContractInfo()) ? req.getContractInfo() : "");
    				communityMemberProperty.setRentType(req.getRentType());
    				if(null!=req.getRentType() && (0==req.getRentType() || 1==req.getRentType())
    						&& null!=property.getRentState() && 1==property.getRentState()) {
    					property.setRentState(2);
    				}
    				communityMemberProperty.setAuditState(1);
					communityMemberProperty.setMember(member);
					communityMemberProperty.setProperty(property);
                    communityMemberProperty.setIsCurrentOwner(req.getIsCurrentOwner());
                    try {

            			if(StringUtils.isNotEmpty(req.getBillingDate())) {
                			communityMemberProperty.setBillingDate( 
        					        DateUtil.parseShortFormat(req.getBillingDate()));
                			//communityMemberProperty.setBillingDateModify(new Timestamp(new Date().getTime()));
            			}
                    	communityMemberProperty.setRecordDate(StringUtils.isNotEmpty(req.getRecordDate()) ? 
                    			DateUtil.parseShortFormat(req.getRecordDate()) : null);
                        communityMemberProperty.setTerminationDate(StringUtils.isNotEmpty(req.getTerminationDate()) ? 
                                DateUtil.parseShortFormat(req.getTerminationDate()) : null);
                            communityMemberProperty.setEndDate(StringUtils.isNotEmpty(req.getEndDate()) ? 
                        			DateUtil.parseShortFormat(req.getEndDate()) : null);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    communityMemberProperty.setComment(StringUtils.isNotEmpty(req.getComment()) ? req.getComment() : "");
                    communityMemberProperty.setAuditState(req.getAuditState());
                	communityMemberProperty.setMemberType(req.getMemberType());
                	communityMemberProperty.setIsCurrentMember(req.getIsCurrentMember());
                	communityMemberProperty.setIsCurrentOwner(req.getIsCurrentOwner());
                	communityMemberProperty.setCarInfo(StringUtils.isNotEmpty(req.getCarInfo()) ? req.getCarInfo() : "");
                	communityMemberProperty.setBuyersName(StringUtils.isNotEmpty(req.getBuyersName()) ? req.getBuyersName() : "");
                	communityMemberProperty.setBuyersAddress(StringUtils.isNotEmpty(req.getBuyersAddress()) ? req.getBuyersAddress() : "");
                	communityMemberProperty.setBusinessType(StringUtils.isNotEmpty(req.getBusinessType()) ? req.getBusinessType() : "");
                	communityMemberProperty.setPaytaxNo(StringUtils.isNotEmpty(req.getPaytaxNo()) ? req.getPaytaxNo() : "");
                	communityMemberProperty.setBuyersBankAccount(StringUtils.isNotEmpty(req.getBuyersBankAccount()) ? req.getBuyersBankAccount() : "");
                	communityMemberProperty.setBuyerEmail(StringUtils.isNotEmpty(req.getBuyerEmail()) ? req.getBuyerEmail() : "");
                	if(null != req.getParentPropertyId() ) {
                		CommunityEstateEntity parentEstate =  (CommunityEstateEntity) communityPropertyDao.get(req.getParentPropertyId());
                    	ObjectMapper mapper = new ObjectMapper();
                    	ReservedFieldVo reservedField= null;
						if(StringUtils.isNotEmpty(parentEstate.getReservedField())) {
							try {
								reservedField =  mapper.readValue(parentEstate.getReservedField(), ReservedFieldVo.class);
							} catch (JsonMappingException e) {
								e.printStackTrace();
							} catch (JsonProcessingException e) {
								e.printStackTrace();
							}
						}else {
							reservedField = new ReservedFieldVo();
						}
						BigDecimal buildingArea = new BigDecimal("0");
						
						BigDecimal propertyParkingArea =  new BigDecimal("0");
						Integer propertyParkingNum = 0;
						BigDecimal defenceParkingArea = new BigDecimal("0");
						Integer defenceParkingNum = 0;
              			List<CommunityMemberPropertyEntity> parkingList = communityMemberPropertyDao.
              					getListByHql("select distinct a from CommunityMemberPropertyEntity a where "
              							+ "a.isCurrentMember=1 and a.memberType=0  and a.parentProperty.id="
              							+req.getParentPropertyId(), "");
              			boolean mark = true;
              			if(null!=parkingList) {
              				for(CommunityMemberPropertyEntity parking : parkingList) {
        						CommunityEstateEntity estate = (CommunityEstateEntity) parking.getProperty();
        						buildingArea = estate.getBuildingArea();
        						if(estate.getEstateType().equals("产权车位")) {
        							propertyParkingArea = propertyParkingArea.add(buildingArea);
        							propertyParkingNum = propertyParkingNum+1;
        						}else if(estate.getEstateType().equals("人防车位")) {
        							defenceParkingArea = defenceParkingArea.add(buildingArea);
        							defenceParkingNum = defenceParkingNum+1;
        						}
        						if(property.getId().toString().equals(estate.getId().toString())) {
        							mark = false;
        						}
              				}
              			}
                		communityMemberProperty.setParentProperty(parentEstate);
                		if(communityMemberProperty.getMemberType()==0 && mark) {
    						CommunityEstateEntity estate = (CommunityEstateEntity) property;
    						buildingArea = estate.getBuildingArea();
    						if(estate.getEstateType().equals("产权车位")) {
    							propertyParkingArea = propertyParkingArea.add(buildingArea);
    							propertyParkingNum = propertyParkingNum+1;
    						}else if(estate.getEstateType().equals("人防车位")) {
    							defenceParkingArea = defenceParkingArea.add(buildingArea);
    							defenceParkingNum = defenceParkingNum+1;
    						}
                    	}
						reservedField.setPropertyParkingArea(propertyParkingArea.toString());
						reservedField.setPropertyParkingNum(propertyParkingNum.toString());
						reservedField.setDefenceParkingArea(defenceParkingArea.toString());
						reservedField.setDefenceParkingNum(defenceParkingNum.toString());
						String json="";
						try {
							json = mapper.writeValueAsString(reservedField);
						} catch (JsonProcessingException e) {
							e.printStackTrace();
						}
						parentEstate.setReservedField(json);
						if (CommunityCache.estateList.containsKey(parentEstate.getId())) {
							CommunityCache.estateList.remove(parentEstate.getId());
						} else {
							CommunityCache.estateList.put(parentEstate.getId(), parentEstate);
						}
                	}
                	communityMemberPropertyDao.save(communityMemberProperty);
                	if(null != communityContract) {
                		if(null==communityContract.getId()) {
                			communityContractDao.save(communityContract);
                		}
                		communityMemberProperty.setContract(communityContract);
                	}
                	if(null != communityMemberProperty.getParentProperty() && communityMemberProperty.getMemberType()==0) {
                		property.setEstateState(1);
                	}else if(null != communityMemberProperty.getParentProperty() && communityMemberProperty.getMemberType()==2) {
                		property.setEstateState(3);
                	}
                }
            }
            //虽然选择导入主业，但时修改的主业资料，同步改业主资料。
            if(null != req.getMemberId() ) {
				member.setEmail(StringUtils.isNotEmpty(req.getEmail()) ? req.getEmail() : member.getEmail());
				member.setHomePhone(
							StringUtils.isNotEmpty(req.getHomePhone()) ? req.getHomePhone() : member.getHomePhone());
				member.setPhone(StringUtils.isNotEmpty(req.getPhone()) ? req.getPhone() : member.getPhone());
				member.setSex(null != req.getSex() ? req.getSex() : member.getSex());
				member.setUserName(
						StringUtils.isNotEmpty(req.getUserName()) ? req.getUserName() : member.getUserName());
				member.setContactPerson(StringUtils.isNotEmpty(req.getContactPerson()) ? req.getContactPerson() : member.getContactPerson());
				member.setEmergencyContact(StringUtils.isNotEmpty(req.getEmergencyContact()) ? req.getEmergencyContact() : member.getEmergencyContact());
				member.setRelation(StringUtils.isNotEmpty(req.getRelation()) ? req.getRelation() : member.getRelation());
				member.setHomeAddress(StringUtils.isNotEmpty(req.getHomeAddress()) ? req.getHomeAddress() : member.getHomeAddress());
				member.setCompany(StringUtils.isNotEmpty(req.getCompany()) ? req.getCompany() : member.getCompany());
				member.setPostalCode(StringUtils.isNotEmpty(req.getPostalCode()) ? req.getPostalCode() : member.getPostalCode());
				member.setNation(StringUtils.isNotEmpty(req.getNation()) ? req.getNation() : member.getNation());
				member.setPoliceStation(StringUtils.isNotEmpty(req.getPoliceStation()) ? req.getPoliceStation() : member.getPoliceStation());
				member.setBirthday(StringUtils.isNotEmpty(req.getBirthday()) ? req.getBirthday() : member.getBirthday());
				member.setPhoneVerifyState(EntityContext.RECORD_STATE_VALID);
				member.setIdType(StringUtils.isNotEmpty(req.getIdType()) ? req.getIdType() : member.getIdType());
				member.setIdCard(StringUtils.isNotEmpty(req.getIdCard()) ? req.getIdCard() : member.getIdCard());
            }
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;	
	}
	
	
	@Audit(operate = "新增业主")
	public Map<String,Object> addCommunityMember(CommunityMemberPropertyReq req) {
		Map<String,Object> map = new HashMap<String,Object>();
		String phone = (StringUtils.isNotEmpty(req.getPhone()) ? req.getPhone() : "");
		if (StringUtils.isEmpty(phone) ||
				StringUtils.isEmpty(req.getRoleIdList())) {
			map.put("ret", ResponseContext.RES_NULL_ERROR_CODE);
			map.put("retInfo", ResponseContext.RES_NULL_ERROR_INFO );
			return map;
		}
		CommunityMemberEntity member  = null;
		member= communityMemberDao.getUniqueBySql("SELECT * FROM t_account WHERE phone='"+req.getPhone()+"' and userState = 1",
				"");
		if (null != member) {
			//普通注册流程
			map.put("ret", ResponseContext.RES_NULL_ERROR_CODE);
			map.put("retInfo", "对不起，您注册的用户名或手机号已被注册！" );
			return map;
		} else {
			member = new CommunityMemberEntity();
			String[] rolerId = req.getRoleIdList().split(",");
			for (String id : rolerId) {
				RoleEntity role = roleDao.get(Integer.valueOf(id));
				if (null != role) {
					member.getCommunityRoleList().add(role);
				}
			}
			
			//密码选填
			String password = req.getPassword();
			if(StringUtils.isNotBlank(password)) {
				try {
					member.setPassword(DigestUtil.sm3Digest(req.getPassword()));
					member.setLastPwdModifyTime(new Timestamp(new Date().getTime()));
				} catch (UnsupportedEncodingException e) {
					e.printStackTrace();
				}
			}
			member.setPhone(phone);
			member.setUserName(StringUtils.isNotEmpty(req.getUserName()) ? req.getUserName() :"");
			member.setPhoneVerifyState(EntityContext.RECORD_STATE_VALID);
			member.setIdCard(StringUtils.isNotEmpty(req.getIdCard())?req.getIdCard():"");
			member.setUserState(EntityContext.RECORD_STATE_VALID);
			member.setSex(null != req.getSex() ? req.getSex() : 2);
			member.setRegionCode(StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "");
			member.setHeadImage(StringUtils.isNotEmpty(req.getHeadImage()) ? req.getHeadImage() :"");
			member.setComment(StringUtils.isNotEmpty(req.getComment()) ? req.getComment(): "");
			member.setComment(req.getComment());
			member.setContactPerson(req.getContactPerson());
			member.setEmergencyContact(req.getEmergencyContact());
			member.setRelation(req.getRelation());
//			member.setBuyersName(req.getBuyersName());
//			member.setBuyersAddress(req.getBuyersAddress());
//			member.setBusinessType(req.getBusinessType());
//			member.setInvoiceType(req.getInvoiceType());
//			member.setPaytaxNo(req.getPaytaxNo());
//			member.setBuyersBankAccount(req.getBuyersBankAccount());
			member.setTvNo(req.getTvNo());
			member.setMac(req.getMac());
			member.setIp(req.getIp());
			member.setMemberType(req.getMemberType());
			member.setWeixinOpenId(req.getWeixinOpenId());
			member.setWeixinAvatar(req.getWeixinAvatar());
			member.setHomeAddress(req.getHomeAddress());
			member.setCompany(req.getCompany());
			member.setPostalCode(req.getPostalCode());
			member.setNation(req.getNation());
			member.setPoliceStation(req.getPoliceStation());
			member.setNativePlace(req.getNativePlace());
			member.setOfficeAddress(req.getOfficeAddress());
			member.setExchangeRegion(req.getExchangeRegion());
			member.setExchangeHall(req.getExchangeHall());
			member.setMiniProgramOpenId(req.getMiniProgramOpenId());
			member.setIsSmartcardMaster(req.getIsSmartcardMaster());
			member.setBirthday(req.getBirthday());
			// 默认开通手机微信小程序应用登录权限
			member.setPhoneVerifyState(EntityContext.RECORD_STATE_VALID);
			member.setQq(req.getQq());
			member.setHomePhone(req.getHomePhone());
			member.setWeixin(req.getWeixin());
			member.setPhoto(req.getPhoto());
			member.setIdType(req.getIdType());
			member.setIdCard(req.getIdCard());
			
			communityMemberDao.save(member);
			map.put("member", member);
		}
		map.put("ret", ResponseContext.RES_SUCCESS_CODE);
		map.put("retInfo", ResponseContext.RES_SUCCESS_CODE_INFO);
		return map;
	}

	@Audit(operate = "修改合同")
	public IResponse modifyCommunityRentMemberProperty(CommunityMemberPropertyReq req) {
		GenericResponse res = new GenericResponse();
		if(null != req.getContractId()) {
			CommunityContractEntity communityContract = communityContractDao.get(req.getContractId());
			if(null == communityContract) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			communityContract.setBusinessCategory(StringUtils.isNotEmpty(req.getBusinessCategory()) 
					? req.getBusinessCategory() : communityContract.getBusinessCategory());
			communityContract.setBuyersName(StringUtils.isNotEmpty(req.getBuyersName()) ? 
					req.getBuyersName():communityContract.getBuyersName());
			communityContract.setContractBrief(StringUtils.isNotEmpty(req.getContractBrief()) ? 
					req.getContractBrief():communityContract.getContractBrief());
			communityContract.setContractCode(StringUtils.isNotEmpty(req.getContractCode()) ? 
					req.getContractCode():communityContract.getContractCode());
			communityContract.setContractName(StringUtils.isNotEmpty(req.getContractName()) ? 
					req.getContractName():communityContract.getContractName());
			communityContract.setDepositInfo(StringUtils.isNotEmpty(req.getDepositInfo()) ? 
					req.getDepositInfo():communityContract.getDepositInfo());
			communityContract.setInvoiceDescription(StringUtils.isNotEmpty(req.getInvoiceDescription()) ? 
					req.getInvoiceDescription():communityContract.getInvoiceDescription());
			//communityContract.setIsEffective(EntityContext.RECORD_STATE_VALID);
			communityContract.setLastModifyTime(new Timestamp(new Date().getTime()));
			communityContract.setPaytaxNo(StringUtils.isNotEmpty(req.getPaytaxNo()) ? 
					req.getPaytaxNo():communityContract.getPaytaxNo());
			communityContract.setRentInfo(StringUtils.isNotEmpty(req.getRentInfo()) ? 
					req.getRentInfo():communityContract.getRentInfo());
			communityContract.setRentPayType(StringUtils.isNotEmpty(req.getRentPayType()) ? 
					req.getRentPayType():communityContract.getRentPayType());
			//communityContract.setState(EntityContext.RECORD_STATE_VALID);
			if(StringUtils.isNotEmpty(req.getStartTime())) {
				try {
					communityContract.setStartTime(new Timestamp(DateUtil.parseLongFormat(req.getStartTime()).getTime()));
				} catch (ParseException e) {
					e.printStackTrace();
				}
			}
			if(StringUtils.isNotEmpty(req.getEndTime())) {
				try {
					communityContract.setEndTime(new Timestamp(DateUtil.parseLongFormat(req.getEndTime()).getTime()));
				} catch (ParseException e) {
					e.printStackTrace();
				}
			}
			communityContract.setAsset(
	                null != req.getAssetId() ? assetDao.get(req.getAssetId()) : communityContract.getAsset());
			return modifyCommunityMemberProperty(req);
		}else {
			return modifyCommunityMemberProperty(req);
		}

	}

	@Audit(operate = "修改业主")
	public Map<String,Object> modifyCommunityMember(CommunityMemberPropertyReq req,CommunityMemberEntity member) {
		Map<String,Object> map = new HashMap<String,Object>();
		if(null!=req.getMemberId() ) {
			if(StringUtils.isNotEmpty(req.getPhone()) && !req.getPhone().equals(member.getPhone())) {
				CommunityMemberEntity m= communityMemberDao.getUniqueBySql(
						"SELECT * FROM t_account WHERE phone='"+req.getPhone()+"' and userState = 1","");
				if (null != m && !m.getId().toString().equalsIgnoreCase(req.getMemberId().toString())) {
					//普通注册流程
					map.put("ret", ResponseContext.RES_DATA_DUPLICATE_CODE);
					map.put("retInfo", "对不起，您注册的用户名或手机号已被注册！" );
					return map;
				}
			}
			if (null != member) {
				if(StringUtils.isNotEmpty(req.getRoleIdList())) {
					member.setCommunityRoleList(null);
					String[] rolerId = req.getRoleIdList().split(",");
					List<RoleEntity> roleList = new ArrayList<RoleEntity>();
					for (String id : rolerId) {
						RoleEntity role = roleDao.get(Integer.valueOf(id));
						if (null != role) {
							roleList.add(role);
						}
					}
					member.setCommunityRoleList(roleList);
				}
				member.setLastModifyTime(new Timestamp(new Date().getTime()));
				member.setEmail(StringUtils.isNotEmpty(req.getEmail()) ? req.getEmail() : member.getEmail());
				member.setPhone(StringUtils.isNotEmpty(req.getPhone()) ? req.getPhone() : member.getPhone());
				member.setSex(null != req.getSex() ? req.getSex() : member.getSex());
				member.setSmartcardId(
						StringUtils.isNotEmpty(req.getSmartcardId()) ? req.getSmartcardId() : member.getSmartcardId());
				member.setHeadImage(
						StringUtils.isNotEmpty(req.getHeadImage()) ? req.getHeadImage() : member.getHeadImage());
				member.setRegionCode(
						StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : member.getRegionCode());
				member.setUserName(
						StringUtils.isNotEmpty(req.getUserName()) ? req.getUserName() : member.getUserName());
				member.setComment(StringUtils.isNotEmpty(req.getComment()) ? req.getComment():member.getComment());
				member.setContactPerson(StringUtils.isNotEmpty(req.getContactPerson()) ? req.getContactPerson() :member.getContactPerson());
				member.setEmergencyContact(StringUtils.isNotEmpty(req.getEmergencyContact()) ?req.getEmergencyContact():member.getEmergencyContact());
				member.setRelation(StringUtils.isNotEmpty(req.getRelation()) ? req.getRelation():member.getRelation());
//				member.setBuyersName(StringUtils.isNotEmpty(req.getBuyersName()) ? req.getBuyersName():member.getBuyersName());
//				member.setBuyersAddress(StringUtils.isNotEmpty(req.getBuyersAddress()) ? req.getBuyersAddress():member.getBuyersAddress());
//				member.setBusinessType(StringUtils.isNotEmpty(req.getBusinessType()) ? req.getBusinessType():member.getBusinessType());
//				member.setInvoiceType(StringUtils.isNotEmpty(req.getInvoiceType()) ? req.getInvoiceType():member.getInvoiceType());
//				member.setPaytaxNo(StringUtils.isNotEmpty(req.getPaytaxNo()) ? req.getPaytaxNo():member.getPaytaxNo());
//				member.setBuyersBankAccount(StringUtils.isNotEmpty(req.getBuyersBankAccount()) ? req.getBuyersBankAccount():member.getBuyersBankAccount());
				member.setTvNo(StringUtils.isNotEmpty(req.getTvNo()) ? req.getTvNo():member.getTvNo());
				member.setMac(StringUtils.isNotEmpty(req.getMac()) ?req.getMac():member.getMac());
				member.setIp(StringUtils.isNotEmpty(req.getIp()) ? req.getIp():member.getIp());
				member.setWeixinOpenId(StringUtils.isNotEmpty(req.getWeixinOpenId()) ? req.getWeixinOpenId():member.getWeixinOpenId());
				member.setWeixinAvatar(StringUtils.isNotEmpty(req.getWeixinAvatar()) ? req.getWeixinAvatar():member.getWeixinAvatar());
				member.setHomeAddress(StringUtils.isNotEmpty(req.getHomeAddress()) ? req.getHomeAddress():member.getHomeAddress());
				member.setCompany(StringUtils.isNotEmpty(req.getCompany()) ? req.getCompany():member.getCompany());
				member.setPostalCode(StringUtils.isNotEmpty(req.getPostalCode()) ? req.getPostalCode():member.getPostalCode());
				member.setNation(StringUtils.isNotEmpty(req.getNation()) ? req.getNation():member.getNation());
				member.setPoliceStation(StringUtils.isNotEmpty(req.getPoliceStation()) ? req.getPoliceStation():member.getPoliceStation());
				member.setNativePlace(StringUtils.isNotEmpty(req.getNativePlace()) ? req.getNativePlace():member.getNativePlace());
				member.setOfficeAddress(StringUtils.isNotEmpty(req.getOfficeAddress()) ? req.getOfficeAddress():member.getOfficeAddress());
				member.setExchangeRegion(StringUtils.isNotEmpty(req.getExchangeRegion()) ? req.getExchangeRegion():member.getExchangeRegion());
				member.setExchangeHall(StringUtils.isNotEmpty(req.getExchangeHall()) ? req.getExchangeHall():member.getExchangeHall());
				member.setMiniProgramOpenId(StringUtils.isNotEmpty(req.getMiniProgramOpenId()) ? req.getMiniProgramOpenId():member.getMiniProgramOpenId());
				member.setIsSmartcardMaster(null!=req.getIsSmartcardMaster() ? req.getIsSmartcardMaster():member.getIsSmartcardMaster());
				member.setHomePhone(StringUtils.isNotEmpty(req.getHomePhone()) ? req.getHomePhone():member.getHomePhone());
				member.setBirthday(StringUtils.isNotEmpty(req.getBirthday()) ? req.getBirthday():member.getBirthday());
				member.setPhoneVerifyState(null!=req.getPhoneVerifyState() ? req.getPhoneVerifyState():member.getPhoneVerifyState());
				member.setQq(StringUtils.isNotEmpty(req.getQq()) ? req.getQq():member.getQq());
				member.setWeixin(StringUtils.isNotEmpty(req.getWeixin()) ? req.getWeixin():member.getWeixin());
				member.setPhoto(StringUtils.isNotEmpty(req.getPhoto()) ? req.getPhoto():member.getPhoto());
				member.setIdType(StringUtils.isNotEmpty(req.getIdType()) ? req.getIdType():member.getIdType());
				member.setIdCard(StringUtils.isNotEmpty(req.getIdCard()) ? req.getIdCard():member.getIdCard());
				
				map.put("ret", ResponseContext.RES_SUCCESS_CODE);
				map.put("retInfo", ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				map.put("ret", ResponseContext.RES_NULL_ERROR_CODE);
				map.put("retInfo", "参数有误！！");
			}
		}else {
			map.put("ret", ResponseContext.RES_NULL_ERROR_CODE);
			map.put("retInfo", ResponseContext.RES_NULL_ERROR_INFO );
		}

		return map;
	}
	
	@Override
	@Audit(operate = "会员绑定资产")
	public IResponse bindingCommunityMemberProperty(CommunityMemberPropertyReq req) {
		GenericResponse res = new GenericResponse();
		Object userObj = getPrincipal(true);
		
		if(null!= userObj && userObj instanceof CommunityMemberEntity ) {
			if (StringUtils.isNotEmpty(req.getPhone()) 
					&& StringUtils.isNotEmpty(req.getUserName()) && null!=req.getPropertyId()) {
				CommunityMemberEntity member = (CommunityMemberEntity) userObj;
				StringBuilder hql = new StringBuilder("select distinct a from CommunityMemberPropertyEntity a "
						+ "inner join a.property b inner join a.member c  where b.id="+req.getPropertyId());
				String currentOwnerHql = " and auditState=1  and a.isCurrentOwner="+CommunityContext.IS_CURRENT_OWNER+
						" and c.phone='"+req.getPhone()+"'" + " and c.userName='"+req.getUserName()+"'";
				CommunityMemberPropertyEntity communityMemberProperty = communityMemberPropertyDao.findUnique(hql.toString()+currentOwnerHql, "");
            	if(null == communityMemberProperty) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO+" 查无此会员或会员信息不匹配");
					return res;
            	}
            	CommunityMemberPropertyEntity memberProperty = communityMemberPropertyDao.
						findUnique(hql.toString()+" and c.id="+member.getId(), "");
				if(null != memberProperty ) {
					if(memberProperty.getAuditState()==1) {
						res.setRet(ResponseContext.RES_DATA_DUPLICATE_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_DUPLICATE_INFO);
						return res;
					}
				}else {
					communityMemberProperty = new CommunityMemberPropertyEntity(); 
				}
            	
        		memberProperty.setMember(member);
        		memberProperty.setProperty(communityMemberProperty.getProperty());
                memberProperty.setComment(StringUtils.isNotEmpty(req.getComment()) ? req.getComment() : memberProperty.getComment());

        		 try {
         			if(StringUtils.isNotEmpty(req.getBillingDate())) {
         				memberProperty.setBillingDate( 
    					        DateUtil.parseShortFormat(req.getBillingDate()));
            			memberProperty.setBillingDateModify(new Timestamp(new Date().getTime()));
        			}
                 	memberProperty.setEndDate(StringUtils.isNotEmpty(req.getEndDate()) ? 
                			DateUtil.parseShortFormat(req.getEndDate()) : null);
                 } catch (ParseException e) {
                     e.printStackTrace();
                 }
        		 memberProperty.setIsCurrentOwner(req.getIsCurrentOwner());
                 try {
                	 memberProperty.setRecordDate(StringUtils.isNotEmpty(req.getRecordDate()) ? 
                         DateUtil.parseShortFormat(req.getRecordDate()) : null);
                 } catch (ParseException e) {
                     e.printStackTrace();
                 }
/*                 try {
                	 memberProperty.setTerminationDate(StringUtils.isNotEmpty(req.getTerminationDate()) ? 
                         DateUtil.parseLongFormat(req.getTerminationDate()) : null);
                 } catch (ParseException e) {
                     e.printStackTrace();
                 }*/
                 memberProperty.setAuditState(0);
        		communityMemberPropertyDao.save(memberProperty);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			}
		}else {
			res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_INSUFFICIENT_INFO);
		}

		return res;
		
	}

	@Override
	@Audit(operate = "修改会员资产")
	public IResponse modifyCommunityMemberProperty(CommunityMemberPropertyReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getMemberPropertyId() && null!=req.getIsCurrentOwner()) {
			CommunityMemberPropertyEntity memberProperty = communityMemberPropertyDao.get(req.getMemberPropertyId());
			if(null == memberProperty) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			
			StringBuilder hql = new StringBuilder("select distinct a from CommunityMemberPropertyEntity a "
					+ "inner join a.property b inner join a.member c  where b.id="+req.getPropertyId());
			if(req.getIsCurrentOwner() == 1) {
      			CommunityMemberPropertyEntity isCurrentOwner = communityMemberPropertyDao.
						findUnique(hql.toString()+" and a.isCurrentMember=1 and a.memberType=0", "");
    			if(null != isCurrentOwner && !isCurrentOwner.getId().toString().equals(memberProperty.getId().toString())) {
          			isCurrentOwner.setIsCurrentOwner(0);
          			isCurrentOwner.setTerminationDate(new Date());
          			isCurrentOwner.setIsCurrentMember(0);
    			}
			}
			
			if(null!=req.getMemberId()) {
				CommunityMemberEntity member = communityMemberDao.get(req.getMemberId());
				memberProperty.setMember(null!=member ? member : memberProperty.getMember());
				Map<String,Object> map = modifyCommunityMember(req, member);
				if(!map.get("ret").toString().equals(ResponseContext.RES_SUCCESS_CODE)) {
					res.setRet((String)map.get("ret"));
					res.setRetInfo((String)map.get("retInfo"));
					return res;
				}
			}
			if(null!=req.getPropertyId()&& !req.getPropertyId().equals(memberProperty.getProperty().getId())) {
				CommunityPropertyEntity property = communityPropertyDao.get(req.getPropertyId());
				memberProperty.setProperty(null!=property ? property : memberProperty.getProperty());
			}
//            memberProperty.setInTime(StringUtils.isNotEmpty(req.getInTime()) ? Timestamp.valueOf(req.getInTime()) : 
//            	memberProperty.getInTime());
//            memberProperty.setAuditState(null!=req.getAuditState() ? req.getAuditState() : memberProperty.getAuditState());
//            memberProperty.setRelation(null!=req.getRelation() ? req.getRelation() : memberProperty.getRelation());
			
    		memberProperty.setLastModifyTime(new Timestamp(new Date().getTime()));
            try {
     			if(StringUtils.isNotEmpty(req.getBillingDate())) {
     				memberProperty.setBillingDate( 
					        DateUtil.parseShortFormat(req.getBillingDate()));
        			memberProperty.setBillingDateModify(new Timestamp(new Date().getTime()));
    			}else {
        			memberProperty.setBillingDate(memberProperty.getBillingDate());
    			}

            	memberProperty.setRecordDate(StringUtils.isNotEmpty(req.getRecordDate()) ? 
            			DateUtil.parseShortFormat(req.getRecordDate()) : memberProperty.getRecordDate());
//            	memberProperty.setAcceptanceDate(StringUtils.isNotEmpty(req.getAcceptanceDate()) ? 
//            			DateUtil.parseLongFormat(req.getAcceptanceDate()) : memberProperty.getAcceptanceDate());
            	memberProperty.setEndDate(StringUtils.isNotEmpty(req.getEndDate()) ? 
            			DateUtil.parseShortFormat(req.getEndDate()) : memberProperty.getEndDate());
            	memberProperty.setEndDate(StringUtils.isNotEmpty(req.getEndDate()) ? 
            			DateUtil.parseShortFormat(req.getEndDate()) : memberProperty.getEndDate());
            } catch (ParseException e) {
                e.printStackTrace();
            }
            try {
            	memberProperty.setTerminationDate(StringUtils.isNotEmpty(req.getTerminationDate()) ? 
                    DateUtil.parseShortFormat(req.getTerminationDate()) : null);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            memberProperty.setContractInfo(StringUtils.isNotEmpty(req.getContractInfo()) ? req.getContractInfo() : memberProperty.getContractInfo());
            memberProperty.setRentType(null!=req.getRentType() ? req.getRentType(): memberProperty.getRentType());
            CommunityEstateEntity property =(CommunityEstateEntity) memberProperty.getProperty();
			if(null!=memberProperty.getRentType() && (0==memberProperty.getRentType() || 1==memberProperty.getRentType())) {
				if(null!=req.getIsCurrentMember() && req.getIsCurrentMember()==0 && 
						null!=req.getIsCurrentOwner() && req.getIsCurrentOwner()==0) {
					property.setRentState(1);
				}else if(null!=req.getIsCurrentMember() && req.getIsCurrentMember()==1 && 
						null!=req.getIsCurrentOwner() && req.getIsCurrentOwner()==1){
					property.setRentState(2);
				}
			}
			if(memberProperty.getIsCurrentMember()==1 && memberProperty.getIsCurrentOwner()==1 
					&&null!=req.getIsCurrentMember() && null!=req.getIsCurrentOwner() &&req.getIsCurrentMember()==0 
					&& req.getIsCurrentOwner()==0){
				property.setEstateState(4);
			}else if(memberProperty.getIsCurrentMember()==0 && memberProperty.getIsCurrentOwner()==0 
					&&null!=req.getIsCurrentMember() && null!=req.getIsCurrentOwner() &&req.getIsCurrentMember()==1
					&& req.getIsCurrentOwner()==1){
				property.setEstateState(null!=req.getEstateState() ? req.getEstateState() : 1);
			}
            memberProperty.setAuditState(null!=req.getAuditState() ? req.getAuditState():memberProperty.getAuditState());
            memberProperty.setMemberType(null!=req.getMemberType() ? req.getMemberType() : memberProperty.getMemberType());
            memberProperty.setIsCurrentMember(null!=req.getIsCurrentMember() ? req.getIsCurrentMember() : memberProperty.getIsCurrentMember());
            memberProperty.setIsCurrentOwner(null!=req.getIsCurrentOwner() ? req.getIsCurrentOwner() : memberProperty.getIsCurrentOwner());
            memberProperty.setCarInfo(StringUtils.isNotEmpty(req.getCarInfo()) ? req.getCarInfo() :  memberProperty.getCarInfo());
            memberProperty.setBuyersName(StringUtils.isNotEmpty(req.getBuyersName()) ? req.getBuyersName() : memberProperty.getBuyersName());
            memberProperty.setBuyersAddress(StringUtils.isNotEmpty(req.getBuyersAddress()) ? req.getBuyersAddress() : memberProperty.getBuyersAddress());
            memberProperty.setBusinessType(StringUtils.isNotEmpty(req.getBusinessType()) ? req.getBusinessType() : null);
            memberProperty.setPaytaxNo(StringUtils.isNotEmpty(req.getPaytaxNo()) ? req.getPaytaxNo() : null);
            memberProperty.setBuyersBankAccount(StringUtils.isNotEmpty(req.getBuyersBankAccount()) ? req.getBuyersBankAccount() : memberProperty.getBuyersBankAccount());
            memberProperty.setComment(StringUtils.isNotEmpty(req.getComment()) ? req.getComment() : memberProperty.getComment());
            memberProperty.setBuyerEmail(StringUtils.isNotEmpty(req.getBuyerEmail()) ? req.getBuyerEmail() : memberProperty.getBuyerEmail());
            if(null != req.getSetPaymentAccountIsNull() && req.getSetPaymentAccountIsNull() ==1) {
            	memberProperty.getProperty().setPaymentAccount(null);
            }
            
        	if((null==req.getParentPropertyId() || (null!=req.getParentPropertyId() && req.getParentPropertyId().toString().
        			equals(memberProperty.getParentProperty().getId().toString())) ) &&
        			null != memberProperty.getParentProperty() && memberProperty.getMemberType()==0) {//如果单元不变，重新计算车位信息
        		CommunityEstateEntity parentEstate =  (CommunityEstateEntity) memberProperty.getParentProperty();
            	ObjectMapper mapper = new ObjectMapper();
            	List<CommunityEstateEntity> list = communityEstateDao.getListByHql("select distinct a from CommunityEstateEntity a "
            			+ "inner join a.memberPropertyList b where b.isCurrentMember=1 and b.memberType=0 and b.parentProperty.id="+
            			memberProperty.getParentProperty().getId(), "");
				ReservedFieldVo reservedField= null;
				if(StringUtils.isNotEmpty(parentEstate.getReservedField())) {
					try {
						reservedField =  mapper.readValue(parentEstate.getReservedField(), ReservedFieldVo.class);
					} catch (JsonMappingException e) {
						e.printStackTrace();
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}
				}else {
					reservedField = new ReservedFieldVo();
				}
				BigDecimal propertyParkingArea = new BigDecimal("0");
				Integer propertyParkingNum =  0;
				BigDecimal defenceParkingArea = new BigDecimal("0");
				Integer defenceParkingNum = 0;
            	if(null != list) {
            		for(CommunityEstateEntity o : list) {

        				BigDecimal buildingArea = o.getBuildingArea();
        				
        				if(o.getEstateType().equals("产权车位")) {
        					propertyParkingArea = propertyParkingArea.add(buildingArea);
        					propertyParkingNum = propertyParkingNum+1;
        				}else if(o.getEstateType().equals("人防车位")) {
        					defenceParkingArea = defenceParkingArea.add(buildingArea);
        					defenceParkingNum = defenceParkingNum+1;
        				}
            		}
            	}
				reservedField.setPropertyParkingArea(propertyParkingArea.toString());
				reservedField.setPropertyParkingNum(propertyParkingNum.toString());
				reservedField.setDefenceParkingArea(defenceParkingArea.toString());
				reservedField.setDefenceParkingNum(defenceParkingNum.toString());
				String json="";
				try {
					json = mapper.writeValueAsString(reservedField);
				} catch (JsonProcessingException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				parentEstate.setReservedField(json);
				if (CommunityCache.estateList.containsKey(parentEstate.getId())) {
					CommunityCache.estateList.remove(parentEstate.getId());
				} else {
					CommunityCache.estateList.put(parentEstate.getId(), parentEstate);
				}
        	}else if((null!=req.getParentPropertyId() && !req.getParentPropertyId().toString().
        			equals(memberProperty.getParentProperty().getId().toString())) &&
        			null != memberProperty.getParentProperty() && memberProperty.getMemberType()==0) {//如果车位挂在另单元上，先更新之前单元的车位数据，再更新更改的单元数据
        		
        		CommunityEstateEntity parentEstate =  (CommunityEstateEntity) memberProperty.getParentProperty();
            	List<CommunityEstateEntity> list = communityEstateDao.getListByHql("select distinct a from CommunityEstateEntity a "
            			+ "inner join a.memberPropertyList b where b.isCurrentMember=1 and b.memberType=0 and b.parentProperty.id="+
            			memberProperty.getParentProperty().getId(), "");
				ReservedFieldVo reservedField= null;
				if(StringUtils.isNotEmpty(parentEstate.getReservedField())) {
					try {
						reservedField =  mapper.readValue(parentEstate.getReservedField(), ReservedFieldVo.class);
					} catch (JsonMappingException e) {
						e.printStackTrace();
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}
				}else {
					reservedField = new ReservedFieldVo();
				}
				BigDecimal propertyParkingArea = new BigDecimal("0");
				Integer propertyParkingNum =  0;
				BigDecimal defenceParkingArea = new BigDecimal("0");
				Integer defenceParkingNum = 0;
            	if(null != list) {
            		for(CommunityEstateEntity o : list) {
            			if(!req.getPropertyId().toString().equals(o.getId().toString())) {
            				BigDecimal buildingArea = o.getBuildingArea();
            				
            				if(o.getEstateType().equals("产权车位")) {
            					propertyParkingArea = propertyParkingArea.add(buildingArea);
            					propertyParkingNum = propertyParkingNum+1;
            				}else if(o.getEstateType().equals("人防车位")) {
            					defenceParkingArea = defenceParkingArea.add(buildingArea);
            					defenceParkingNum = defenceParkingNum+1;
            				}
            			}
            		}
            	}
				reservedField.setPropertyParkingArea(propertyParkingArea.toString());
				reservedField.setPropertyParkingNum(propertyParkingNum.toString());
				reservedField.setDefenceParkingArea(defenceParkingArea.toString());
				reservedField.setDefenceParkingNum(defenceParkingNum.toString());
				String json="";
				try {
					json = mapper.writeValueAsString(reservedField);
				} catch (JsonProcessingException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				parentEstate.setReservedField(json);
				if (CommunityCache.estateList.containsKey(parentEstate.getId())) {
					CommunityCache.estateList.remove(parentEstate.getId());
				} else {
					CommunityCache.estateList.put(parentEstate.getId(), parentEstate);
				}
				
				propertyParkingArea = new BigDecimal("0");
				propertyParkingNum =  0;
				defenceParkingArea = new BigDecimal("0");
				defenceParkingNum = 0;
				CommunityEstateEntity newParentEstate = (CommunityEstateEntity)communityPropertyDao.get(req.getParentPropertyId());
				for(CommunityMemberPropertyEntity mp : newParentEstate.getSubMemberPropertyList()) {
					if(mp.getIsCurrentMember()==1 && mp.getMemberType()==0) {
						CommunityEstateEntity o = (CommunityEstateEntity) mp.getProperty();
						BigDecimal buildingArea = o.getBuildingArea();
        				if(o.getEstateType().equals("产权车位")) {
        					propertyParkingArea = propertyParkingArea.add(buildingArea);
        					propertyParkingNum = propertyParkingNum+1;
        				}else if(o.getEstateType().equals("人防车位")) {
        					defenceParkingArea = defenceParkingArea.add(buildingArea);
        					defenceParkingNum = defenceParkingNum+1;
        				}
					}
				}
				CommunityEstateEntity estate = (CommunityEstateEntity) memberProperty.getProperty();
				BigDecimal buildingArea = estate.getBuildingArea();
				if(estate.getEstateType().equals("产权车位")) {
					propertyParkingArea = propertyParkingArea.add(buildingArea);
					propertyParkingNum = propertyParkingNum+1;
				}else if(estate.getEstateType().equals("人防车位")) {
					defenceParkingArea = defenceParkingArea.add(buildingArea);
					defenceParkingNum = defenceParkingNum+1;
				}
				
				if(StringUtils.isNotEmpty(newParentEstate.getReservedField())) {
					try {
						reservedField =  mapper.readValue(newParentEstate.getReservedField(), ReservedFieldVo.class);
					} catch (JsonMappingException e) {
						e.printStackTrace();
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}
				}else {
					reservedField = new ReservedFieldVo();
				}
        		
				reservedField.setPropertyParkingArea(propertyParkingArea.toString());
				reservedField.setPropertyParkingNum(propertyParkingNum.toString());
				reservedField.setDefenceParkingArea(defenceParkingArea.toString());
				reservedField.setDefenceParkingNum(defenceParkingNum.toString());
				try {
					json = mapper.writeValueAsString(reservedField);
				} catch (JsonProcessingException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				newParentEstate.setReservedField(json);
                memberProperty.setParentProperty(newParentEstate);
				if (CommunityCache.estateList.containsKey(newParentEstate.getId())) {
					CommunityCache.estateList.remove(newParentEstate.getId());
				} else {
					CommunityCache.estateList.put(newParentEstate.getId(), newParentEstate);
				}
        	}
        	if(memberProperty.getIsCurrentMember() ==1 && 
        			null == memberProperty.getTerminationDate() && 
        			null != memberProperty.getParentProperty() && 
        			memberProperty.getMemberType() == 0) {
        		CommunityEstateEntity estate = (CommunityEstateEntity) memberProperty.getProperty();
        		estate.setEstateState(1);
        	}else if(memberProperty.getIsCurrentMember()==1 && 
        			null==memberProperty.getTerminationDate() && 
        			null != memberProperty.getParentProperty() && 
        			memberProperty.getMemberType()==2) {
        		CommunityEstateEntity estate = (CommunityEstateEntity) memberProperty.getProperty();
        		estate.setEstateState(3);
        	}else if(memberProperty.getIsCurrentMember()==0 && 
        			null!=memberProperty.getTerminationDate() && 
        			null != memberProperty.getParentProperty()) {
        		CommunityEstateEntity estate = (CommunityEstateEntity) memberProperty.getProperty();
        		estate.setEstateState(6);
        	}
			
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "删除会员资产")
	public IResponse deleteCommunityMemberProperty(CommunityMemberPropertyReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getMemberPropertyId()) {
			CommunityMemberPropertyEntity memberProperty = communityMemberPropertyDao.get(req.getMemberPropertyId());
			if (null != memberProperty) {
				
				if(null != memberProperty.getParentProperty() && memberProperty.getMemberType()==0) {
	        		
	        		CommunityEstateEntity parentEstate =  (CommunityEstateEntity) memberProperty.getParentProperty();
	            	List<CommunityEstateEntity> list = communityEstateDao.getListByHql("select distinct a from CommunityEstateEntity a "
	            			+ "inner join a.memberPropertyList b where b.isCurrentMember=1 and b.memberType=0 and b.parentProperty.id="+
	            			memberProperty.getParentProperty().getId()+" and a.id!="+memberProperty.getProperty().getId(), "");
					ReservedFieldVo reservedField= null;
					if(StringUtils.isNotEmpty(parentEstate.getReservedField())) {
						try {
							reservedField =  mapper.readValue(parentEstate.getReservedField(), ReservedFieldVo.class);
						} catch (JsonMappingException e) {
							e.printStackTrace();
						} catch (JsonProcessingException e) {
							e.printStackTrace();
						}
					}else {
						reservedField = new ReservedFieldVo();
					}
					BigDecimal propertyParkingArea = new BigDecimal("0");
					Integer propertyParkingNum =  0;
					BigDecimal defenceParkingArea = new BigDecimal("0");
					Integer defenceParkingNum = 0;
	            	if(null != list) {
	            		for(CommunityEstateEntity o : list) {
            				BigDecimal buildingArea = o.getBuildingArea();
            				
            				if(o.getEstateType().equals("产权车位")) {
            					propertyParkingArea = propertyParkingArea.add(buildingArea);
            					propertyParkingNum = propertyParkingNum+1;
            				}else if(o.getEstateType().equals("人防车位")) {
            					defenceParkingArea = defenceParkingArea.add(buildingArea);
            					defenceParkingNum = defenceParkingNum+1;
            				}
	            		}
	            	}
					reservedField.setPropertyParkingArea(propertyParkingArea.toString());
					reservedField.setPropertyParkingNum(propertyParkingNum.toString());
					reservedField.setDefenceParkingArea(defenceParkingArea.toString());
					reservedField.setDefenceParkingNum(defenceParkingNum.toString());
					String json="";
					try {
						json = mapper.writeValueAsString(reservedField);
					} catch (JsonProcessingException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
					parentEstate.setReservedField(json);

	        	}
				CommunityEstateEntity property =(CommunityEstateEntity) memberProperty.getProperty();
				if(null!=property.getRentType() && (0==property.getRentType() || 1==property.getRentType())) {
					property.setRentState(1);
				}
				
				memberProperty.setMember(null);
				memberProperty.setParentProperty(null);
				memberProperty.setProperty(null);
				memberProperty.setContract(null);
				communityMemberPropertyDao.delete(memberProperty);
				
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityMemberPropertyInfo(CommunityMemberPropertyReq req) {
		GetCommunityMemberPropertyInfoRes res = new GetCommunityMemberPropertyInfoRes();
		if (null != req.getMemberPropertyId()) {
			CommunityMemberPropertyEntity o = communityMemberPropertyDao.get(req.getMemberPropertyId());
			if (null != o) {
				DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
				CommunityMemberPropertyForm memberProperForm = new CommunityMemberPropertyForm();
//				memberProperForm.setAuditState(o.getAuditState());
				memberProperForm.setCreateTime(sdf.format(o.getCreateTime()));
				if(null != o.getMember()) {
					CommunityMemberForm memberForm = new CommunityMemberForm();
					memberForm.setMemberId(o.getMember().getId());
					memberForm.setEmail(o.getMember().getEmail());
					memberForm.setHomePhone(o.getMember().getHomePhone());
					memberForm.setNickName(o.getMember().getNickName());
					memberForm.setOfficePhone(o.getMember().getOfficePhone());
					memberForm.setPhone(o.getMember().getPhone());
					memberForm.setRegistName(o.getMember().getRegistName());
					memberForm.setSex(o.getMember().getSex());
					memberForm.setSmartcardId(o.getMember().getSmartcardId());
					memberForm.setIdCard(StringUtils.isNotEmpty(o.getMember().getIdCard()) ? o.getMember().getIdCard() : "");
					memberForm.setHomeAddress(StringUtils.isNotEmpty(o.getMember().getHomeAddress()) ? o.getMember().getHomeAddress() : "");
					memberForm.setHeadImage(StringUtils.isNotEmpty(o.getMember().getHeadImage()) ? o.getMember().getHeadImage() : "");
					memberForm.setUserName(StringUtils.isNotEmpty(o.getMember().getUserName()) ? o.getMember().getUserName() : "");
					memberForm.setBirthday(o.getMember().getBirthday());
					memberForm.setNativePlace(o.getMember().getNativePlace());
					memberForm.setNation(o.getMember().getNation());
					memberForm.setNation(o.getMember().getNation());
					memberForm.setPoliceStation(o.getMember().getPoliceStation());
					memberForm.setPostalCode(o.getMember().getPostalCode());
					memberForm.setCompany(o.getMember().getCompany());
					memberForm.setContactPerson(StringUtils.isNotEmpty(o.getMember().getContactPerson()) ?
							o.getMember().getContactPerson() :"");
					memberForm.setEmergencyContact(o.getMember().getEmergencyContact());
					memberForm.setComment(o.getMember().getComment());
//					memberForm.setBuyersName(o.getMember().getBuyersName());
//					memberForm.setBuyersAddress(o.getMember().getBuyersAddress());
//					memberForm.setBusinessType(o.getMember().getBusinessType());
//					memberForm.setInvoiceType(o.getMember().getInvoiceType());
//					memberForm.setPaytaxNo(o.getMember().getPaytaxNo());
//					memberForm.setBuyersBankAccount(o.getMember().getBuyersBankAccount());
					memberForm.setIdType(o.getMember().getIdType());
					memberForm.setIdCard(o.getMember().getIdCard());
					memberForm.setRelation(
							StringUtils.isNotEmpty(o.getMember().getRelation()) ? o.getMember().getRelation() : "");
					memberProperForm.setMemberForm(memberForm);
				}
				if(null != o.getContract()) {
					memberProperForm.setContract(getContractForm(o.getContract()));
				}
				memberProperForm.setContractInfo(StringUtils.isNotEmpty(o.getContractInfo()) ? o.getContractInfo() : "");
				memberProperForm.setRentType(o.getRentType());
				memberProperForm.setPropertyForm(getCommunityPropertyForm(o.getProperty(),null,null));
				memberProperForm.setMemberType(o.getMemberType());
				memberProperForm.setAuditState(o.getAuditState());
				memberProperForm.setIsCurrentMember(o.getIsCurrentMember());
				memberProperForm.setBillingDate(null != o.getBillingDate()? 
						DateUtil.formatShortFormat(o.getBillingDate()) : "");
				memberProperForm.setBillingDateModify(null != o.getBillingDateModify()? 
						DateUtil.formatShortFormat(o.getBillingDateModify()) : "");
				memberProperForm.setCreateTime(null != o.getCreateTime()? 
						DateUtil.formatShortFormat(o.getCreateTime()) : "");
				memberProperForm.setIsCurrentOwner(o.getIsCurrentOwner());
				memberProperForm.setRecordDate(null != o.getRecordDate()? 
						DateUtil.formatShortFormat(o.getRecordDate()) : "");
				memberProperForm.setTerminationDate(null != o.getTerminationDate()? 
						DateUtil.formatShortFormat(o.getTerminationDate()) : "");
//				memberProperForm.setAcceptanceDate(null != o.getAcceptanceDate()? DateUtil.formatLongFormat(o.getAcceptanceDate()) : "");
				memberProperForm.setMemberPropertyId(o.getId());
				memberProperForm.setCarInfo(StringUtils.isNotEmpty(o.getCarInfo()) ? 
						o.getCarInfo() : "");
				memberProperForm.setParentPropertyForm(getCommunityPropertyForm(o.getParentProperty(),null,null));
				memberProperForm.setEndDate(null != o.getEndDate() ? 
						DateUtil.formatShortFormat(o.getEndDate()) : "");
				memberProperForm.setComment(StringUtils.isNotEmpty(o.getComment()) ? o.getComment() :"");
				memberProperForm.setBuyersName(StringUtils.isNotEmpty(o.getBuyersName()) ? 
						o.getBuyersName() : "");
				memberProperForm.setBuyersAddress(StringUtils.isNotEmpty(o.getBuyersAddress()) ? 
						o.getBuyersAddress() : "");
				memberProperForm.setBusinessType(StringUtils.isNotEmpty(o.getBusinessType()) ? 
						o.getBusinessType() : "");
				memberProperForm.setPaytaxNo(StringUtils.isNotEmpty(o.getPaytaxNo()) ?
						o.getPaytaxNo() : "");
				memberProperForm.setBuyersBankAccount(StringUtils.isNotEmpty(o.getBuyersBankAccount()) ?
						o.getBuyersBankAccount() : "");
				memberProperForm.setBuyerEmail(StringUtils.isNotEmpty(o.getBuyerEmail()) ?
						o.getBuyerEmail() : "");
				o.getPayItemsPriceList().forEach(p->{
					CommunityPayItemsPriceForm payItemsPriceForm = new CommunityPayItemsPriceForm();
					payItemsPriceForm.setStartTime(p.getComment());
					payItemsPriceForm.setPayItemsPriceId(p.getId());
					payItemsPriceForm.setPeriod(p.getPeriod());
					payItemsPriceForm.setPrice(p.getPrice().toString());
					payItemsPriceForm.setCreateTime(null != p.getCreateTime() ? DateUtil.formatLongFormat(p.getCreateTime()) : "");
					payItemsPriceForm.setEndTime(null != p.getEndTime() ? DateUtil.formatLongFormat(p.getEndTime()) : "");
					payItemsPriceForm.setLastModifyTime(null != p.getLastModifyTime() ? DateUtil.formatLongFormat(p.getLastModifyTime()) : "");
					payItemsPriceForm.setStartTime(null != p.getStartTime() ? DateUtil.formatLongFormat(p.getStartTime()) : "");
					payItemsPriceForm.setItemsName(p.getPayItems().getItemsName());
					payItemsPriceForm.setChargeCategory(p.getPayItems().getChargeCategory());
					payItemsPriceForm.setPriceUnit(p.getPayItems().getPriceUnit());
					payItemsPriceForm.setFeeCalType(p.getPayItems().getFeeCalType());
					payItemsPriceForm.setPayDate(p.getPayItems().getPayDate());
					payItemsPriceForm.setIsBreach(p.getPayItems().getIsBreach());
					memberProperForm.getPayItemsPriceForm().add(payItemsPriceForm);
				});
				res.setMemberProperty(memberProperForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	
	@Override
	@Audit(operate = "审核会员资产")
	public IResponse auditCommunityMemberProperty(CommunityMemberPropertyReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getMemberPropertyId() && null!=req.getAuditState()) {
			CommunityMemberPropertyEntity memberProperty = communityMemberPropertyDao.get(req.getMemberPropertyId());
			if (null != memberProperty && memberProperty.getAuditState()==0) {
				
				memberProperty.setAuditState(req.getAuditState());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	
	public IResponse initializeParkingData() {
		GenericResponse res = new GenericResponse();
		StringBuilder hql = new StringBuilder("select distinct a from CommunityMemberPropertyEntity a "
				+ " where a.isCurrentMember=1 AND a.memberType=0 AND a.parentProperty IS NOT NULL ");
		List<CommunityMemberPropertyEntity> list = communityMemberPropertyDao.getListByHql(hql.toString(), "");
		ObjectMapper mapper = new ObjectMapper();
		list.forEach(o->{
			CommunityEstateEntity parentProperty = (CommunityEstateEntity) o.getParentProperty();
			
				try {
					ReservedFieldVo reservedField= null;
					if(StringUtils.isNotEmpty(parentProperty.getReservedField())) {
						try {
							reservedField =  mapper.readValue(parentProperty.getReservedField(), ReservedFieldVo.class);
						} catch (JsonMappingException e) {
							e.printStackTrace();
						} catch (JsonProcessingException e) {
							e.printStackTrace();
						}
					}else {
						reservedField = new ReservedFieldVo();
					}
					
					CommunityEstateEntity estate = (CommunityEstateEntity) o.getProperty();
					BigDecimal buildingArea = estate.getBuildingArea();

					BigDecimal propertyParkingArea = StringUtils.isNotEmpty(reservedField.getPropertyParkingArea()) ? 
							new BigDecimal(reservedField.getPropertyParkingArea()) : new BigDecimal("0");
					Integer propertyParkingNum = StringUtils.isNotEmpty(reservedField.getPropertyParkingNum()) ? 
							Integer.valueOf(reservedField.getPropertyParkingNum()) : 0;
					BigDecimal defenceParkingArea = StringUtils.isNotEmpty(reservedField.getDefenceParkingArea()) ? 
							new BigDecimal(reservedField.getDefenceParkingArea()) : new BigDecimal("0");
					Integer defenceParkingNum = StringUtils.isNotEmpty(reservedField.getDefenceParkingNum()) ? 
							Integer.valueOf(reservedField.getDefenceParkingNum()) : 0;
					if(StringUtils.isNotEmpty(estate.getEstateType()) && estate.getEstateType().equals("产权车位")) {
						propertyParkingArea = propertyParkingArea.add(buildingArea);
						propertyParkingNum = propertyParkingNum+1;
					}else if(StringUtils.isNotEmpty(estate.getEstateType()) && estate.getEstateType().equals("人防车位")) {
						defenceParkingArea = defenceParkingArea.add(buildingArea);
						defenceParkingNum = defenceParkingNum+1;
					}
					reservedField.setPropertyParkingArea(propertyParkingArea.toString());
					reservedField.setPropertyParkingNum(propertyParkingNum.toString());
					reservedField.setDefenceParkingArea(defenceParkingArea.toString());
					reservedField.setDefenceParkingNum(defenceParkingNum.toString());
					
					reservedField.setAccountContractNo(StringUtils.isNotEmpty(reservedField.getAccountContractNo()) ? reservedField.getAccountContractNo():"");
					reservedField.setElectricMeterBase(StringUtils.isNotEmpty(reservedField.getElectricMeterBase()) ? reservedField.getElectricMeterBase():"0");
					reservedField.setAccountCustomerId(StringUtils.isNotEmpty(reservedField.getAccountCustomerId()) ? reservedField.getAccountCustomerId():"");
					reservedField.setWaterMeterBase(StringUtils.isNotEmpty(reservedField.getWaterMeterBase()) ? reservedField.getWaterMeterBase():"0");
					reservedField.setAccountContractTime(StringUtils.isNotEmpty(reservedField.getAccountContractTime()) ? reservedField.getAccountContractTime():"");
					reservedField.setAccountContractComment(StringUtils.isNotEmpty(reservedField.getAccountContractComment()) ? reservedField.getAccountContractComment():"");
					
					String json =  mapper.writeValueAsString(reservedField);
					parentProperty.setReservedField(json);
					if (CommunityCache.estateList.containsKey(parentProperty.getId())) {
						CommunityCache.estateList.remove(parentProperty.getId());
					} else {
						CommunityCache.estateList.put(parentProperty.getId(), parentProperty);
					}
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
		
			
		});
		
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public IResponse ownerApprove(CommunityMemberPropertyReq req) {
		GenericResponse res = new GenericResponse();
		Object userObj = getPrincipal(true);
		if (null != userObj) {
			CommunityMemberEntity member = null;
			if(userObj instanceof CommunityMemberEntity) {
				if(StringUtils.isEmpty(req.getPhone()) || StringUtils.isEmpty(req.getIdCard()) 
						|| (StringUtils.isEmpty(req.getUnitCode()) && req.getPropertyId()==null)) {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
					return res;
				}
				member = (CommunityMemberEntity) userObj;
				//String homePhone = member.getHomePhone();
				String phone = member.getPhone();
				if(StringUtils.isNotEmpty(phone)) {
					//String phone = homePhone.split("\\\\")[0].replaceAll("\\s*","");
					if(!phone.equals(req.getPhone())) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo("对不起，认证的“手机号码”有误；请重新核对后再认证！");
						return res;
					}
				}else {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("对不起，您个人信息里的“家庭电话”无数据；请重新核对！");
					return res;
				}
				if(StringUtils.isNotEmpty(member.getIdCard()) ) {
					if(!member.getIdCard().contains(req.getIdCard())) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo("对不起，认证的“身份证号码”有误；请重新核对后再认证！");
						return res;
					}
				}else {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("对不起，您个人信息里的“身份证号码”无数据；请重新核对！");
					return res;
				}
				boolean state = false;
				for(CommunityMemberPropertyEntity memberProperty : member.getMemberPropertyList()) {
					if(null != memberProperty.getProperty() && 
							memberProperty.getIsCurrentMember()==1 && 
							memberProperty.getMemberType()==0) {
						CommunityEstateEntity estate = (CommunityEstateEntity) memberProperty.getProperty();
						if((StringUtils.isNotEmpty(req.getUnitCode()) && estate.getUnitCode().equals(req.getUnitCode())) || 
								(null!=req.getPropertyId() && estate.getId().equals(req.getPropertyId()))) {
							memberProperty.setAuditState(1);
							state = true;
						}
					}
				}
				if(state) {
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
					return res;
				}else {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("对不起，认证失败；请重新核对此房产信息！");
					return res;
				}
			}else {
				res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_INSUFFICIENT_INFO);
				return res;
			}
		} else {
			res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_INSUFFICIENT_INFO);
		}
		return res;
	}

	@Override
	public IResponse residentApprove(CommunityMemberPropertyReq req) {
		
		return null;
	}
	
		
}
