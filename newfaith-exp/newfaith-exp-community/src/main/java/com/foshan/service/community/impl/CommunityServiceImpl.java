package com.foshan.service.community.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.foshan.entity.community.CommunityEntity;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityService;
import org.springframework.transaction.annotation.Transactional;
import com.foshan.form.community.request.CommunityReq;
import com.foshan.form.community.response.community.AddCommunityRes;
import com.foshan.form.community.response.community.ModifyCommunityRes;
import com.foshan.form.community.response.community.GetCommunityInfoRes;
import com.foshan.form.community.response.community.GetCommunityListRes;
import com.foshan.form.RegionForm;
import com.foshan.form.community.CommunityForm;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.IResponse;
import com.foshan.dao.generic.Page;

@Transactional
@Service("communityService")
public class CommunityServiceImpl extends GenericCommunityService implements ICommunityService{

	@Override
	public IResponse getCommunityList(CommunityReq req) {
		GetCommunityListRes res = new GetCommunityListRes();
		Page<CommunityEntity> page = new Page<CommunityEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityEntity a where 1=1 ");
		hql.append(StringUtils.isNotEmpty(req.getCommunityName()) ? " and a.communityName like'%"+req.getCommunityName()+"%'":"");
		hql.append(" ORDER BY a.id desc");
		page = communityDao.queryPage(page, hql.toString());
		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityForm communityForm = new CommunityForm();
			communityForm.setCommunityId(o.getId());
            communityForm.setCommunityAddress(o.getCommunityAddress());
            communityForm.setAreaSize(o.getAreaSize());
            communityForm.setCommunityName(o.getCommunityName());
            if(null != o.getRegion()) {
            	RegionForm regionForm = new RegionForm(o.getRegion().getId(),o.getRegion().getRegionCode() ,o.getRegion().getRegionName() 
            			, o.getRegion().getStartRegionCode(), o.getRegion().getEndRegionCode());
            	communityForm.setRegionForm(regionForm);
            }
			res.getCommunityList().add(communityForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	@Audit(operate = "新增社区")
	public IResponse addCommunity(CommunityReq req) {
		AddCommunityRes res = new AddCommunityRes();
		//if () {
			CommunityEntity community = new CommunityEntity();
			
            community.setCommunityAddress(StringUtils.isNotEmpty(req.getCommunityAddress()) ? req.getCommunityAddress() : "");
            community.setAreaSize(StringUtils.isNotEmpty(req.getAreaSize()) ? req.getAreaSize() : "");
            community.setCommunityName(StringUtils.isNotEmpty(req.getCommunityName()) ? req.getCommunityName() : "");
            community.setRegion(regionDao.get(req.getRegionId()));
			communityDao.save(community);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		} else {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//		}
		return res;
	}

	@Transactional
	@Override
	@Audit(operate = "修改社区")
	public IResponse modifyCommunity(CommunityReq req) {
		ModifyCommunityRes res = new ModifyCommunityRes();
		if (null!=req.getCommunityId() ) {
			CommunityEntity community = communityDao.get(req.getCommunityId()) ;
			if(null != community){
                community.setCommunityAddress(StringUtils.isNotEmpty(req.getCommunityAddress()) ? req.getCommunityAddress() : community.getCommunityAddress());
                community.setAreaSize(StringUtils.isNotEmpty(req.getAreaSize()) ? req.getAreaSize() : community.getAreaSize());
                community.setCommunityName(StringUtils.isNotEmpty(req.getCommunityName()) ? req.getCommunityName() : community.getCommunityName());
                community.setRegion(regionDao.get(req.getRegionId()));
                communityDao.saveOrUpdate(community);
				res.setCommunityId(community.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "删除社区")
	public IResponse deleteCommunity(CommunityReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityId()) {
		CommunityEntity community = communityDao.get(req.getCommunityId());
			if (null != community) {
				community.setRegion(null);
				communityDao.deleteById(req.getCommunityId());
				//community.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityInfo(CommunityReq req) {
		GetCommunityInfoRes res = new GetCommunityInfoRes();
		if (null != req.getCommunityId()) {
			CommunityEntity community = communityDao.get(req.getCommunityId());
			if (null != community) {
				CommunityForm communityForm = new CommunityForm();
				
				communityForm.setCommunityId(community.getId());
                communityForm.setCommunityAddress(community.getCommunityAddress());
                communityForm.setAreaSize(community.getAreaSize());
                communityForm.setCommunityName(community.getCommunityName());
                if(null != community.getRegion()) {
                	RegionForm regionForm = new RegionForm(community.getRegion().getId(),community.getRegion().getRegionCode() ,
                			community.getRegion().getRegionName() , community.getRegion().getStartRegionCode(), community.getRegion().getEndRegionCode());
                	communityForm.setRegionForm(regionForm);
                }
				res.setCommunityForm(communityForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}