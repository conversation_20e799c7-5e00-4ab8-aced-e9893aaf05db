package com.foshan.service.community.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityReceivablesChangesEntity;
import com.foshan.entity.community.CommunityReceivablesEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityReceivablesChangesForm;
import com.foshan.form.community.CommunityReceivablesForm;
import com.foshan.form.community.request.CommunityReceivablesChangesReq;
import com.foshan.form.community.response.communityReceivablesChanges.AddCommunityReceivablesChangesRes;
import com.foshan.form.community.response.communityReceivablesChanges.GetCommunityReceivablesChangesInfoRes;
import com.foshan.form.community.response.communityReceivablesChanges.GetCommunityReceivablesChangesListRes;
import com.foshan.form.community.response.communityReceivablesChanges.ModifyCommunityReceivablesChangesRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityReceivablesChangesService;
import com.foshan.util.DateUtil;

@Transactional
@Service("communityReceivablesChangesService")
public class CommunityReceivablesChangesServiceImpl extends GenericCommunityService
		implements ICommunityReceivablesChangesService {

	@Override
	public IResponse getCommunityReceivablesChangesList(CommunityReceivablesChangesReq req) {
		GetCommunityReceivablesChangesListRes res = new GetCommunityReceivablesChangesListRes();
		Page<CommunityReceivablesChangesEntity> page = new Page<CommunityReceivablesChangesEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder(
				"select distinct a from CommunityReceivablesChangesEntity a inner join a.estate e  inner join a.receivables c where a.state=1 and a.changeType < 2 ");

		if (req.getEstateId() != null) {
			hql.append("and e.id =" + req.getEstateId() + " ");
		}
		hql.append(null!= req.getReceivablesId() ? "and c.id =" + req.getReceivablesId()  : "");
		//	.append(null!= req.getReceiptId() ? "and b.id =" + req.getReceiptId()  : "");
		hql.append("ORDER BY a.id desc");
		page = communityReceivablesChangesDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityReceivablesChangesForm communityReceivablesChangesForm = new CommunityReceivablesChangesForm();
			communityReceivablesChangesForm.setCommunityReceivablesChangesId(o.getId());
			communityReceivablesChangesForm.setApprover(o.getApprover());
			communityReceivablesChangesForm
					.setChangeAmount(null != o.getChangeAmount() ? o.getChangeAmount().toString() : "");
			communityReceivablesChangesForm
					.setChangeDate(null != o.getChangeDate() ? DateUtil.formatLongFormat(o.getChangeDate()) : "");
			communityReceivablesChangesForm.setChangeType(o.getChangeType());
			communityReceivablesChangesForm.setComment(o.getComment());
			// communityReceivablesChangesForm.setOldData(o.getOldData());
			// communityReceivablesChangesForm.setOldId(o.getOldId());
			communityReceivablesChangesForm.setPayItemsName(o.getPayItemsName());
			communityReceivablesChangesForm
					.setReceivableAmount(null != o.getReceivableAmount() ? o.getReceivableAmount().toString() : "");
			communityReceivablesChangesForm.setReceivableDate(
					null != o.getReceivableDate() ? DateUtil.formatLongFormat(o.getReceivableDate()) : "");
			communityReceivablesChangesForm
					.setReceivedAmount(null != o.getReceivedAmount() ? o.getReceivedAmount().toString() : "");
			communityReceivablesChangesForm.setRecorder(o.getRecorder());
			if (null != o.getReceivables()) {
				CommunityReceivablesForm communityReceivablesForm = new CommunityReceivablesForm();
				communityReceivablesForm.setCommunityReceivablesId(o.getReceivables().getId());
				communityReceivablesForm.setChargeCategory(o.getReceivables().getChargeCategory());
				communityReceivablesForm.setChargeSource(o.getReceivables().getChargeSource());
				communityReceivablesForm.setPayItemsName(o.getReceivables().getPayItemsName());
				communityReceivablesForm.setReceivableDate(null != o.getReceivables().getReceivableDate()
						? DateUtil.formatLongFormat(o.getReceivables().getReceivableDate())
						: "");
				communityReceivablesChangesForm.setReceivablesForm(communityReceivablesForm);
			}
			res.getCommunityReceivablesChangesList().add(communityReceivablesChangesForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Audit(operate = "增加调整金额")
	@Override
	public IResponse addCommunityReceivablesChanges(CommunityReceivablesChangesReq receivablesChangesReq) {
		AddCommunityReceivablesChangesRes res = new AddCommunityReceivablesChangesRes();
		if(null != receivablesChangesReq.getReceivablesChangesList()) {
			for(CommunityReceivablesChangesReq req : receivablesChangesReq.getReceivablesChangesList()) {
				if (req.getChangeType() == null) {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRet(ResponseContext.RES_NULL_ERROR_INFO + "调整类型不能为空！");
					return res;

				}
				if (StringUtils.isEmpty(req.getChangeAmount())) {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRet(ResponseContext.RES_NULL_ERROR_INFO + "调整金额不能为空！");
					return res;

				}
				if (req.getReceivablesId() == null) {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRet(ResponseContext.RES_NULL_ERROR_INFO + "需要调整的应收ID不能为空！");
					return res;

				}
				CommunityReceivablesEntity communityReceivablesEntity = communityReceivablesDao.get(req.getReceivablesId());
				if (communityReceivablesEntity == null) {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRet(ResponseContext.RES_DATA_NULL_INFO + "找不到对的“应收款项”！");
					return res;
				}

				BigDecimal changeAmount = new BigDecimal(req.getChangeAmount());
				if (changeAmount.compareTo(BigDecimal.ZERO) <= 0) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRet(ResponseContext.RES_DATA_ERROR_INFO + "调整金额必需大于0！");
					return res;
				}

				// 为适配科耐的数据割接，减免金额存库为负数
				changeAmount = req.getChangeType().intValue() == 0 ? BigDecimal.ZERO.subtract(changeAmount) : changeAmount;

				CommunityReceivablesChangesEntity communityReceivablesChanges = new CommunityReceivablesChangesEntity();

				communityReceivablesChanges.setChangeAmount(changeAmount);

				BigDecimal newReceivedAmount = communityReceivablesEntity.getReceivedAmount()
						.subtract(communityReceivablesChanges.getChangeAmount());
				if (newReceivedAmount.compareTo(BigDecimal.ZERO) < 0) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRet(ResponseContext.RES_DATA_ERROR_INFO + "调整后对应“应收款”的“已收金额”不能小于零！");
					return res;
				}
				if(newReceivedAmount.compareTo(communityReceivablesEntity.getReceivableAmount()) > 0) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRet(ResponseContext.RES_DATA_ERROR_INFO + "调整后对应“应收款”的“已收金额”不能大于“应收金额”！");
					return res;
				}

				communityReceivablesChanges.setApprover(req.getApprover());
				communityReceivablesChanges.setChangeType(req.getChangeType());
				communityReceivablesChanges.setComment(req.getComment());
				// communityReceivablesChanges.setOldData(req.getOldData());
				// communityReceivablesChanges.setOldId(req.getOldId());
				communityReceivablesChanges.setPayItemsName(StringUtils.isEmpty(req.getPayItemsName()) ? communityReceivablesEntity.getPayItemsName() : req.getPayItemsName());
				
				communityReceivablesEntity.setComment(communityReceivablesEntity.getComment()+" "+req.getComment());
				
				try {
					communityReceivablesChanges.setChangeDate(
							StringUtils.isNotEmpty(req.getChangeDate()) ? DateUtil.parseLongFormat(req.getChangeDate())
									: new Date());
				} catch (ParseException e) {
					e.printStackTrace();
				}
				communityReceivablesChanges.setState(EntityContext.RECORD_STATE_VALID);
				communityReceivablesChanges.setRecorder(StringUtils.isEmpty(req.getRecorder()) ? "杨慧霞" : req.getRecorder());
				communityReceivablesChanges.setEstate(communityReceivablesEntity.getEstate());
				communityReceivablesChanges.setReceivableAmount(communityReceivablesEntity.getReceivableAmount());
				communityReceivablesChanges.setReceivableDate(communityReceivablesEntity.getReceivableDate());
				communityReceivablesChanges.setReceivedAmount(communityReceivablesEntity.getReceivedAmount());
				communityReceivablesEntity.setReceivedAmount(newReceivedAmount);
				communityReceivablesEntity.setLastModifyTime(new Timestamp(System.currentTimeMillis()));
				communityReceivablesChanges.setRecorder(req.getRecorder());
				communityReceivablesChanges.setReceivables(communityReceivablesEntity);
				communityReceivablesChangesDao.save(communityReceivablesChanges);
				// 修改补差额对象
				modifySubReceivaedManageAmount(communityReceivablesChanges.getId(), communityReceivablesEntity, BigDecimal.ZERO.subtract(changeAmount),communityReceivablesChanges.getChangeDate(), 1);
				
				
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//				} else {
//					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//					res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//				}
			}

		}else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	//暂时没有用到
	@Audit(operate = "修改调整金额")
	@Override
	public IResponse modifyCommunityReceivablesChanges(CommunityReceivablesChangesReq req) {
		ModifyCommunityReceivablesChangesRes res = new ModifyCommunityReceivablesChangesRes();
		if (StringUtils.isEmpty(req.getChangeAmount())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRet(ResponseContext.RES_NULL_ERROR_INFO + "需要调整金额不能为空！");
			return res;

		}
		if (null != req.getCommunityReceivablesChangesId()) {
			CommunityReceivablesChangesEntity communityReceivablesChanges = communityReceivablesChangesDao
					.get(req.getCommunityReceivablesChangesId());
			if (null != communityReceivablesChanges) {
				communityReceivablesChanges.setApprover(req.getApprover());
				BigDecimal changeAmount = StringUtils.isNotEmpty(req.getChangeAmount())
						? new BigDecimal(req.getChangeAmount())
						: null;
				BigDecimal offsetAmount = changeAmount.subtract(communityReceivablesChanges.getChangeAmount());
				communityReceivablesChanges.setChangeAmount(changeAmount);
				try {
					communityReceivablesChanges.setChangeDate(
							StringUtils.isNotEmpty(req.getChangeDate()) ? DateUtil.parseLongFormat(req.getChangeDate())
									: null);
				} catch (ParseException e) {
					e.printStackTrace();
				}
				communityReceivablesChanges.setChangeType(req.getChangeType());
				communityReceivablesChanges.setComment(req.getComment());
				// communityReceivablesChanges.setOldData(req.getOldData());
				// communityReceivablesChanges.setOldId(req.getOldId());
				communityReceivablesChanges.setPayItemsName(req.getPayItemsName());
				communityReceivablesChanges
						.setReceivableAmount(communityReceivablesChanges.getReceivables().getReceivableAmount());
				communityReceivablesChanges.getReceivables().setReceivedAmount(
						communityReceivablesChanges.getReceivables().getReceivedAmount().add(offsetAmount));
				try {
					communityReceivablesChanges.setReceivableDate(StringUtils.isNotEmpty(req.getReceivableDate())
							? DateUtil.parseLongFormat(req.getReceivableDate())
							: null);
				} catch (ParseException e) {
					e.printStackTrace();
				}
				communityReceivablesChanges
						.setReceivedAmount(communityReceivablesChanges.getReceivables().getReceivedAmount());

				communityReceivablesChanges.setRecorder(req.getRecorder());
				communityReceivablesChanges.setLastModifyTime(new Timestamp(System.currentTimeMillis()));
				// communityReceivablesChanges.setReceivables(null!=req.getReceivablesId() ?
				// communityReceivablesDao.get(null!=req.getReceivablesId()) : null);
				res.setCommunityReceivablesChangesId(communityReceivablesChanges.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "找不到对应的调整金额！");
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "调整金额ID不能为空！");
		}
		return res;
	}

	@Audit(operate = "删除调整金额")
	@Override
	public IResponse deleteCommunityReceivablesChanges(CommunityReceivablesChangesReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityReceivablesChangesId()) {
			CommunityReceivablesChangesEntity communityReceivablesChanges = communityReceivablesChangesDao
					.get(req.getCommunityReceivablesChangesId());
			if (null != communityReceivablesChanges && communityReceivablesChanges.getChangeType()==0) {
				
				communityReceivablesChanges.getReceivables().setReceivedAmount(communityReceivablesChanges
						.getReceivables().getReceivedAmount().add(communityReceivablesChanges.getChangeAmount()));
//				communityReceivablesChanges.getReceivables().setReceivedAmount(communityReceivablesChanges
//						.getReceivables().getReceivedAmount().add(communityReceivablesChanges.getChangeAmount()));
				communityReceivablesChanges.setState(EntityContext.RECORD_STATE_INVALID);
				//同步修改补收对象
				deleteSubReceivaedManageAmount(communityReceivablesChanges.getId(), communityReceivablesChanges.getReceivables(), 1);
				// communityReceivablesChangesDao.deleteById(req.getCommunityReceivablesChangesId());
				// communityReceivablesChanges.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "找不到对应的调整金额！");
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "调整金额ID不能为空！");
		}

		return res;
	}
	
	
	public IResponse cancelDisposableRefund(CommunityReceivablesChangesReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityReceivablesChangesId()) {
			CommunityReceivablesChangesEntity communityReceivablesChanges = communityReceivablesChangesDao
					.get(req.getCommunityReceivablesChangesId());
			if (null != communityReceivablesChanges 
//					&& null != communityReceivablesChanges.getReceivables() 
//					&& null!=communityReceivablesChanges.getReceivables().getPayItem()
//					&& communityReceivablesChanges.getReceivables().getPayItem().getPayDate()==0
					) {
				if(null == communityReceivablesChanges.getRefundMethod()) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("数据异常！");
					return res;
				}
				
				if(communityReceivablesChanges.getRefundMethod() == 3) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("该收据退款通过第三方平台线上退款，不允许取消退款。");
					return res;
				}
				if(communityReceivablesChanges.getChangeType() != 2) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("对不起，此数据为非退款数据！");
					return res;
				}
				
				communityReceivablesChanges.setEstate(null);
				communityReceivablesChanges.setReceivables(null);
				communityReceivablesChanges.setReceipt(null);
				communityReceivablesChangesDao.delete(communityReceivablesChanges);

				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Audit(operate = "获取调整金额详情")
	@Override
	public IResponse getCommunityReceivablesChangesInfo(CommunityReceivablesChangesReq req) {
		GetCommunityReceivablesChangesInfoRes res = new GetCommunityReceivablesChangesInfoRes();
		if (null != req.getCommunityReceivablesChangesId()) {
			CommunityReceivablesChangesEntity communityReceivablesChanges = communityReceivablesChangesDao
					.getUniqueByHql(
							"select distinct a from CommunityReceivablesChangesEntity a where a.state=1 and a.id = '"
									+ req.getCommunityReceivablesChangesId() + "'");
			if (null != communityReceivablesChanges) {
				CommunityReceivablesChangesForm communityReceivablesChangesForm = new CommunityReceivablesChangesForm();
				communityReceivablesChangesForm.setCommunityReceivablesChangesId(communityReceivablesChanges.getId());
				communityReceivablesChangesForm.setApprover(communityReceivablesChanges.getApprover());
				communityReceivablesChangesForm.setChangeAmount(null != communityReceivablesChanges.getChangeAmount()
						? communityReceivablesChanges.getChangeAmount().toString()
						: "");
				communityReceivablesChangesForm.setChangeDate(null != communityReceivablesChanges.getChangeDate()
						? DateUtil.formatLongFormat(communityReceivablesChanges.getChangeDate())
						: "");
				communityReceivablesChangesForm.setChangeType(communityReceivablesChanges.getChangeType());
				communityReceivablesChangesForm.setComment(communityReceivablesChanges.getComment());
				// communityReceivablesChangesForm.setOldData(communityReceivablesChanges.getOldData());
				// communityReceivablesChangesForm.setOldId(communityReceivablesChanges.getOldId());
				communityReceivablesChangesForm.setPayItemsName(communityReceivablesChanges.getPayItemsName());
				communityReceivablesChangesForm
						.setReceivableAmount(null != communityReceivablesChanges.getReceivableAmount()
								? communityReceivablesChanges.getReceivableAmount().toString()
								: "");
				communityReceivablesChangesForm
						.setReceivableDate(null != communityReceivablesChanges.getReceivableDate()
								? DateUtil.formatLongFormat(communityReceivablesChanges.getReceivableDate())
								: "");
				communityReceivablesChangesForm
						.setReceivedAmount(null != communityReceivablesChanges.getReceivedAmount()
								? communityReceivablesChanges.getReceivedAmount().toString()
								: "");
				communityReceivablesChangesForm.setRecorder(communityReceivablesChanges.getRecorder());
				if (null != communityReceivablesChanges.getReceivables()) {
					CommunityReceivablesForm communityReceivablesForm = new CommunityReceivablesForm();
					communityReceivablesForm
							.setCommunityReceivablesId(communityReceivablesChanges.getReceivables().getId());
					communityReceivablesForm
							.setChargeCategory(communityReceivablesChanges.getReceivables().getChargeCategory());
					communityReceivablesForm
							.setChargeSource(communityReceivablesChanges.getReceivables().getChargeSource());
					communityReceivablesForm
							.setPayItemsName(communityReceivablesChanges.getReceivables().getPayItemsName());
					communityReceivablesChangesForm.setReceivablesForm(communityReceivablesForm);
				}
				res.setCommunityReceivablesChangesForm(communityReceivablesChangesForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}