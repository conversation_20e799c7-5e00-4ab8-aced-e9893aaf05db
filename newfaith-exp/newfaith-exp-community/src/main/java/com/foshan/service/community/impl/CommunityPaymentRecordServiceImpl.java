package com.foshan.service.community.impl;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.Cache;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.dao.generic.Page;
import com.foshan.entity.DepartmentEntity;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityMemberEntity;
import com.foshan.entity.community.CommunityMemberPropertyEntity;
import com.foshan.entity.community.CommunityPaymentRecordEntity;
import com.foshan.entity.community.CommunityReceiptEntity;
import com.foshan.entity.community.CommunityReceiptReceivablesEntity;
import com.foshan.entity.community.CommunityReceivablesEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityPaymentRecordForm;
import com.foshan.form.community.CommunityReceiptForm;
import com.foshan.form.community.CommunityReceiptReceivablesForm;
import com.foshan.form.community.CommunityReceiveDetailForm;
import com.foshan.form.community.request.CommunityPaymentRecordReq;
import com.foshan.form.community.request.CommunityPaymentReq;
import com.foshan.form.community.response.communityPaymentRecord.AddCommunityPaymentRecordRes;
import com.foshan.form.community.response.communityPaymentRecord.GetCommunityPaymentRecordInfoRes;
import com.foshan.form.community.response.communityPaymentRecord.GetCommunityPaymentRecordListRes;
import com.foshan.form.community.response.communityPaymentRecord.ModifyCommunityPaymentRecordRes;
import com.foshan.form.community.response.communityReceiptReceivables.AddCommunityReceiveRes;
import com.foshan.form.request.PaymentNotifyReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityPaymentRecordService;
import com.foshan.util.AlipayApiUtil;
import com.foshan.util.AlipayApiUtil.AlipayAccessTokenResult;
import com.foshan.util.CodeUtil;
import com.foshan.util.DateUtil;
import com.foshan.util.DigestUtil;
import com.foshan.util.HttpClientUtil;
import com.foshan.util.WeiXinApiUtil;
import com.foshan.util.community.CommunityCache;
import com.hazelcast.spring.cache.HazelcastCacheManager;

@Transactional
@Service("communityPaymentRecordService")
public class CommunityPaymentRecordServiceImpl extends GenericCommunityService
		implements ICommunityPaymentRecordService {
	private final static Logger logger = LoggerFactory.getLogger(CommunityPaymentRecordServiceImpl.class);
	@Resource
	private HazelcastCacheManager cacheManager;

//	@Audit(operate = "获取付款记录列表")
	@Override
	public IResponse getCommunityPaymentRecordList(CommunityPaymentRecordReq req) {
		GetCommunityPaymentRecordListRes res = new GetCommunityPaymentRecordListRes();
		Page<CommunityPaymentRecordEntity> page = new Page<CommunityPaymentRecordEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

		CommunityMemberEntity member = null;
		Object userObj = getPrincipal(true);
		if (null == userObj) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		} 
		
		if(userObj instanceof CommunityMemberEntity){
			member = (CommunityMemberEntity) userObj;
			if(null == req.getEstateId()) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO+"estateId为不能为空！");
				return res;
			}
			//当前用户没有权限查阅该账单
			if(!canManageBillOfEstate(member.getId(),req.getEstateId())) {
				res.setRet(ResponseContext.RES_PERM_BEYOND_AUTHORITY_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_BEYOND_AUTHORITY_INFO);
				return res;
			}
		}
		
		StringBuilder hql = new StringBuilder("select distinct a from CommunityPaymentRecordEntity a ");
		if (req.getEstateId() != null) {
			hql.append("inner join a.estate e ");
		}
		if (req.getReceiptId() != null) {
			hql.append("inner join a.receipt r ");
		}
		hql.append("where a.status > 1 ");
		if (req.getEstateId() != null) {
			hql.append("and e.id = " + req.getEstateId() + " ");
		}
		if (req.getReceiptId() != null) {
			hql.append("and r.id = " + req.getReceiptId() + " ");
		}
		if (StringUtils.isNotEmpty(req.getStartTime())) {
			hql.append("and a.paymentDate >='" + req.getStartTime() + "' ");
		}
		if (StringUtils.isNotEmpty(req.getEndTime())) {
			hql.append("and a.paymentDate <= '" + req.getEndTime() + "' ");
		}
		hql.append("ORDER BY a.id desc");

		page = communityPaymentRecordDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityPaymentRecordForm communityPaymentRecordForm = new CommunityPaymentRecordForm();
			communityPaymentRecordForm.setCommunityPaymentRecordId(o.getId());
			communityPaymentRecordForm.setAmount(null != o.getAmount() ? o.getAmount().toString() : "");
			communityPaymentRecordForm.setBankAccount(o.getBankAccount());
			communityPaymentRecordForm.setComment(o.getComment());
			communityPaymentRecordForm.setPayerIdentify(o.getPayerIdentify());
			communityPaymentRecordForm.setPayerName(o.getPayerName());
			communityPaymentRecordForm
					.setPaymentDate(null != o.getPaymentDate() ? DateUtil.formatLongFormat(o.getPaymentDate()) : "");
			communityPaymentRecordForm.setPaymentMethod(o.getPaymentMethod());
			communityPaymentRecordForm.setPaymentRecordCode(o.getPaymentRecordCode());
			communityPaymentRecordForm.setPaymentType(o.getPaymentType());
			communityPaymentRecordForm.setStatus(o.getStatus());
			communityPaymentRecordForm.setOutTradeNo(o.getOutTradeNo());
			communityPaymentRecordForm.setEstateId(o.getEstate() != null ? o.getEstate().getId() : null);
			communityPaymentRecordForm.setUnitCode(o.getEstate() != null ? o.getEstate().getUnitCode() : null);
			communityPaymentRecordForm.setReceiptId(o.getReceipt() != null ? o.getReceipt().getId() : null);
			res.getCommunityPaymentRecordList().add(communityPaymentRecordForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Audit(operate = "增加付款记录")
	@Override
	public IResponse addCommunityPaymentRecord(CommunityPaymentRecordReq req) {
		AddCommunityPaymentRecordRes res = new AddCommunityPaymentRecordRes();
		CommunityPaymentRecordEntity communityPaymentRecord = new CommunityPaymentRecordEntity();

		communityPaymentRecord
				.setAmount(StringUtils.isNotEmpty(req.getAmount()) ? new BigDecimal(req.getAmount()) : null);
		communityPaymentRecord.setBankAccount(req.getBankAccount());
		communityPaymentRecord.setComment(req.getComment());
		communityPaymentRecord.setPayerIdentify(req.getPayerIdentify());
		communityPaymentRecord.setPayerName(req.getPayerName());
		try {
			communityPaymentRecord.setPaymentDate(
					StringUtils.isNotEmpty(req.getPaymentDate()) ? DateUtil.parseLongFormat(req.getPaymentDate())
							: null);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		communityPaymentRecord.setPaymentMethod(req.getPaymentMethod());
		communityPaymentRecord.setPaymentRecordCode(req.getPaymentRecordCode());
		communityPaymentRecord.setPaymentType(req.getPaymentType());
		communityPaymentRecord.setStatus(req.getStatus());
		communityPaymentRecord.setOutTradeNo(req.getWxTradeNo());
		communityPaymentRecord.setEstate(communityEstateDao.get(req.getEstateId()));
		communityPaymentRecord.setReceipt(communityReceiptDao.get(req.getReceiptId()));
		communityPaymentRecordDao.save(communityPaymentRecord);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Audit(operate = "修改付款记录")
	@Override
	public IResponse modifyCommunityPaymentRecord(CommunityPaymentRecordReq req) {
		ModifyCommunityPaymentRecordRes res = new ModifyCommunityPaymentRecordRes();
		if (null != req.getCommunityPaymentRecordId()) {
			CommunityPaymentRecordEntity communityPaymentRecord = communityPaymentRecordDao
					.get(req.getCommunityPaymentRecordId());
			if (null != communityPaymentRecord) {
				communityPaymentRecord
						.setAmount(StringUtils.isNotEmpty(req.getAmount()) ? new BigDecimal(req.getAmount()) : null);
				communityPaymentRecord.setBankAccount(req.getBankAccount());
				communityPaymentRecord.setComment(req.getComment());
				communityPaymentRecord.setPayerIdentify(req.getPayerIdentify());
				communityPaymentRecord.setPayerName(req.getPayerName());
				try {
					communityPaymentRecord.setPaymentDate(StringUtils.isNotEmpty(req.getPaymentDate())
							? DateUtil.parseLongFormat(req.getPaymentDate())
							: null);
				} catch (ParseException e) {
					e.printStackTrace();
				}
				communityPaymentRecord.setPaymentMethod(req.getPaymentMethod());
				communityPaymentRecord.setPaymentRecordCode(req.getPaymentRecordCode());
				communityPaymentRecord.setPaymentType(req.getPaymentType());
				communityPaymentRecord.setStatus(req.getStatus());
				communityPaymentRecord.setOutTradeNo(req.getWxTradeNo());
				communityPaymentRecord.setEstate(communityEstateDao.get(req.getEstateId()));
				communityPaymentRecord.setReceipt(communityReceiptDao.get(req.getReceiptId()));
				res.setCommunityPaymentRecordId(communityPaymentRecord.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Audit(operate = "删除付款记录")
	@Override
	public IResponse deleteCommunityPaymentRecord(CommunityPaymentRecordReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityPaymentRecordId()) {
			CommunityPaymentRecordEntity communityPaymentRecord = communityPaymentRecordDao
					.get(req.getCommunityPaymentRecordId());
			if (null != communityPaymentRecord) {
				communityPaymentRecordDao.deleteById(req.getCommunityPaymentRecordId());
				// communityPaymentRecord.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

//	@Audit(operate = "获取付款记录详情")
	@Override
	public IResponse getCommunityPaymentRecordInfo(CommunityPaymentRecordReq req) {
		GetCommunityPaymentRecordInfoRes res = new GetCommunityPaymentRecordInfoRes();
		if (null != req.getCommunityPaymentRecordId()) {
			CommunityMemberEntity member = null;
			Object userObj = getPrincipal(true);
			if (null == userObj) {
				res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
				return res;
			} 
			CommunityPaymentRecordEntity communityPaymentRecord = communityPaymentRecordDao
					.get(req.getCommunityPaymentRecordId());
			if (null != communityPaymentRecord) {
				// 检查数据权限
				if(userObj instanceof CommunityMemberEntity){
					member = (CommunityMemberEntity) userObj;
					if(!canManageBillOfEstate(member.getId(), communityPaymentRecord.getEstate().getId())) {
						res.setRet(ResponseContext.RES_PERM_BEYOND_AUTHORITY_CODE);
						res.setRetInfo(ResponseContext.RES_PERM_BEYOND_AUTHORITY_INFO);
						return res;
					}
				}
				
				CommunityPaymentRecordForm communityPaymentRecordForm = new CommunityPaymentRecordForm();
				communityPaymentRecordForm.setCommunityPaymentRecordId(communityPaymentRecord.getId());
				communityPaymentRecordForm.setAmount(
						null != communityPaymentRecord.getAmount() ? communityPaymentRecord.getAmount().toString()
								: "");
				communityPaymentRecordForm.setBankAccount(communityPaymentRecord.getBankAccount());
				communityPaymentRecordForm.setComment(communityPaymentRecord.getComment());
				communityPaymentRecordForm.setPayerIdentify(communityPaymentRecord.getPayerIdentify());
				communityPaymentRecordForm.setPayerName(communityPaymentRecord.getPayerName());
				communityPaymentRecordForm.setPaymentDate(null != communityPaymentRecord.getPaymentDate()
						? DateUtil.formatLongFormat(communityPaymentRecord.getPaymentDate())
						: "");
				communityPaymentRecordForm.setPaymentMethod(communityPaymentRecord.getPaymentMethod());
				communityPaymentRecordForm.setPaymentRecordCode(communityPaymentRecord.getPaymentRecordCode());
				communityPaymentRecordForm.setPaymentType(communityPaymentRecord.getPaymentType());
				communityPaymentRecordForm.setStatus(communityPaymentRecord.getStatus());
				communityPaymentRecordForm.setOutTradeNo(communityPaymentRecord.getOutTradeNo());
				communityPaymentRecordForm.setEstateId(
						communityPaymentRecord.getEstate() != null ? communityPaymentRecord.getEstate().getId() : null);
				communityPaymentRecordForm.setReceiptId(
						communityPaymentRecord.getReceipt() != null ? communityPaymentRecord.getReceipt().getId()
								: null);
				res.setCommunityPaymentRecordForm(communityPaymentRecordForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	@Override
	public IResponse getCommunityPaymentRecordStatusByCode(CommunityPaymentRecordReq req) {
		GetCommunityPaymentRecordInfoRes res = new GetCommunityPaymentRecordInfoRes();
		if (StringUtils.isNoneEmpty(req.getPaymentRecordCode())) {
		
			CommunityPaymentRecordEntity communityPaymentRecord = communityPaymentRecordDao.findUnique("select t from CommunityPaymentRecordEntity t where t.paymentRecordCode ='" +req.getPaymentRecordCode()+"'");
			if (null != communityPaymentRecord) {
				CommunityPaymentRecordForm communityPaymentRecordForm = new CommunityPaymentRecordForm();
				communityPaymentRecordForm.setStatus(communityPaymentRecord.getStatus());
				res.setCommunityPaymentRecordForm(communityPaymentRecordForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	/**
	 *
	 */
	@SuppressWarnings("unchecked")
	@Audit(operate = "管理端付款操作")
	@Override
	public IResponse addCommunityReceive(CommunityPaymentReq req) throws RuntimeException {

		AddCommunityReceiveRes res = new AddCommunityReceiveRes();
		CommunityReceiptForm communityReceiptForm = req.getCommunityReceiptForm();
		// 检查必填字段
		if (null == communityReceiptForm) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "收据对象不能为空！");
			return res;
		}
		if (null == req.getReceiveDetialList() || 0 == req.getReceiveDetialList().size()) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "收费详情列表不能为空！");
			return res;
		}
		if (null == communityReceiptForm.getPaymentType()) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "收据的收款类型不能为空！");
			return res;
		}
		if (StringUtils.isEmpty(communityReceiptForm.getEstateId())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "房子或车位ID不能为空！");
			return res;
		}
		if (StringUtils.isEmpty(communityReceiptForm.getPaymentMethod())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "支付方法不能为空！");
			return res;
		}
		// 收款对应的应收列表
		Map<CommunityReceivablesEntity, BigDecimal> receiveDetailMap = new HashMap<CommunityReceivablesEntity, BigDecimal>();
		// 实收总金额
		BigDecimal totalPayableAmount = BigDecimal.ZERO;
		Integer feeType = 0;
		// 把生成的应收存在缓存中
		Cache cache = cacheManager.getCache("prePaymentReceivableCache");
		for (CommunityReceiveDetailForm detailForm : req.getReceiveDetialList()) {
			CommunityReceivablesEntity receivablesEntity = null;
			if (detailForm.getReceivablesId() >= 0) {
				// 非预收
				receivablesEntity = communityReceivablesDao.get(detailForm.getReceivablesId());
			} else {
				// 预收，从缓存读出
				receivablesEntity = cache.get(detailForm.getReceivablesId(), CommunityReceivablesEntity.class);
			}

			if (null == receivablesEntity) {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo("收款失败，没找到对应的应收数据,receivablesId:[" + detailForm.getReceivablesId()
						+ "]!请检查缓存prePaymentReceivableCache配置！");
				return res;
			}

			if (receivablesEntity.getLockMark().intValue() == 1) {
				res.setRet(ResponseContext.RES_DATA_ERROR_INFO);
				res.setRetInfo("收款失败," + receivablesEntity.getPayItemsName() + ",receivablesId:["
						+ detailForm.getReceivablesId() + "]已被锁盘锁定，暂不能收款！");
				return res;
			}

			// 当前应收的未收金额
			BigDecimal payableAmount = receivablesEntity.getReceivableAmount()
					.subtract(receivablesEntity.getReceivedAmount());
			if (payableAmount.compareTo(BigDecimal.ZERO) <= 0) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo("收款失败，receivablesId:[" + detailForm.getReceivablesId() + "]，已经完成收款，无需再付!");
				return res;
			}
			// 避免多收
			if (payableAmount.compareTo(detailForm.getCurrentAmount()) < 0) {
				// 实收收款金额 > 应收未收时，按应收未收金额收款。
				logger.info("receivablesId[{}]:应收未收金额为{}元，少于请求收款金额{}元，将按{}进行实际收款！", receivablesEntity.getId(),
						payableAmount, detailForm.getCurrentAmount(), payableAmount);
			} else if (payableAmount.compareTo(detailForm.getCurrentAmount()) > 0) {
				// 部分收款
				logger.info("receivablesId[{}]:部分收款，应收未收金额为{}元，大于请求收款金额{}元，将按{}进行实际收款！", receivablesEntity.getId(),
						payableAmount, detailForm.getCurrentAmount(), detailForm.getCurrentAmount());
				payableAmount = detailForm.getCurrentAmount();
			}

			totalPayableAmount = totalPayableAmount.add(payableAmount);
			detailForm.setCurrentAmount(payableAmount);
			receiveDetailMap.put(receivablesEntity, payableAmount);
			if (receivablesEntity.getPayItem() != null) {
				feeType = receivablesEntity.getPayItem().getPayDate() > 0 ? 0 : 1;
			}
		}
		communityReceiptForm.setFeeType(feeType);

		// 判断有没有需要付款的应收项
		if (receiveDetailMap.keySet().size() == 0) {
			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "没找到对应的项目！");
			return res;
		}

		// 生成收款记录
		CommunityPaymentRecordEntity communityPaymentRecord = new CommunityPaymentRecordEntity();
		communityPaymentRecord.setAmount(totalPayableAmount);
		communityPaymentRecord.setBankAccount(null);

		CommunityEstateEntity communityEstateEntity = null;
		CommunityMemberPropertyEntity memberProperty = null;
		if (StringUtils.isNotEmpty(communityReceiptForm.getEstateId())) {
			// 获取单元
			communityEstateEntity = communityEstateDao.get(Integer.parseInt(communityReceiptForm.getEstateId()));
			if (communityEstateEntity != null) {
				// 获取单元当前业主
				for (CommunityMemberPropertyEntity o : communityEstateEntity.getMemberPropertyList()) {
					if (o.getIsCurrentOwner().intValue() == 1) {
						memberProperty = o;
						break;
					}
				}
			} else {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "找不到对应的单元数据！");
				return res;
			}
		}

		// 如果前端没有填写收据编号将自动生成收据编号
		if (StringUtils.isNotEmpty(communityReceiptForm.getReceiptCode())) {
			Pattern pattern = Pattern.compile("^\\w+_\\d{6}_\\d{6}");
			Matcher matcher = pattern.matcher(communityReceiptForm.getReceiptCode());
			if(!matcher.matches()) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo("对不起，输入的单据编号格式有误,请按XXXX_yyyyMM_DDDDDD格式提交！");
				return res;
			}
			
			// 前端传的收据编号不为空，需要判断是否与库已有收据编号有重复
			String hql = "select a from CommunityReceiptEntity a where a.receiptCode = '"
					+ communityReceiptForm.getReceiptCode() + "'";
			List<CommunityReceiptEntity> listByHql = communityReceiptDao.getListByHql(hql);
			// 如果单据编号重得，返回错误提示。
			if (listByHql.size() > 0) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "收据编号[" + communityReceiptForm.getReceiptCode()
						+ "]已经存在！");
				return res;
			}
		}

		communityPaymentRecord.setComment(
				StringUtils.isNotEmpty(communityReceiptForm.getComment()) ? communityReceiptForm.getComment() : "物业缴费");
		communityPaymentRecord.setPayerName(memberProperty == null ? communityReceiptForm.getPayerName() : memberProperty.getMember().getUserName());
		communityPaymentRecord.setPaymentDate(new Date());
		communityPaymentRecord.setPaymentRecordCode(CodeUtil.getSNCode("CPR"));
		communityPaymentRecord.setPaymentType(communityReceiptForm.getPaymentType());
		communityPaymentRecord.setPaymentMethod(communityReceiptForm.getPaymentMethod());
		communityPaymentRecord.setPaymentScene(req.getPaymentScene());
		communityPaymentRecord.setHaveInvoice(communityReceiptForm.getHaveInvoice());
		communityPaymentRecord.setReceiptCode(communityReceiptForm.getReceiptCode());
		// 记录付款详情，1、可作为每次支付请求的审核日志，2、在线支付时，业务系统收到支付结果后用于还原付款详情用。
		try {
			String detail = new ObjectMapper().writeValueAsString(req.getReceiveDetialList());
			communityPaymentRecord.setPaymentDetail(detail);
		} catch (JsonProcessingException e2) {
			logger.error("生成支付记录时，付款详情转换成JSON出错！");
			throw new RuntimeException(e2);
		}
		// 记录收款的单元对象
		communityPaymentRecord.setEstate(communityEstateEntity);
		communityPaymentRecord.setState(EntityContext.RECORD_STATE_VALID);
		communityPaymentRecord.setStatus(0);
		communityPaymentRecordDao.save(communityPaymentRecord);

		if (StringUtils.isEmpty(req.getPaymentScene())) {
			// 线下支付
			communityPaymentRecord.setStatus(1);
			res = payment(communityReceiptForm, communityPaymentRecord, receiveDetailMap);
		}
		else {
			// 线上支付
			// 检查paymentScene传值的合法性
			if(!req.getPaymentScene().equals("WX_PUBLIC") 
					&& !req.getPaymentScene().equals("WX_NAVITE_QR")
					&& !req.getPaymentScene().equals("WX_MOBILE_WEBSITE")
					&& !req.getPaymentScene().equals("WX_MINIPROGRAM")
					&& !req.getPaymentScene().equals("AL_MINIPROGRAM")
					&& !req.getPaymentScene().equals("AL_NAVITE_QR")
					&& !req.getPaymentScene().equals("AL_PAYCODE")
					&& !req.getPaymentScene().equals("WX_PAYCODE")
					) {
					res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
					res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "暂不支持"+req.getPaymentScene() +"线上支付方式！");
					return res;
			}
			// 线上支付公共参数设置
			Map<String, String> parameterMap = new TreeMap<String, String>();
			parameterMap.put("clientTradeNo", communityPaymentRecord.getPaymentRecordCode());
			parameterMap.put("orderTotalAmount", communityPaymentRecord.getAmount().toString());
			parameterMap.put("notifyUrl", communityContextInfo.paymentNotifyUrl);
			parameterMap.put("clientIp", "127.0.0.1");
			parameterMap.put("merchantCode", communityContextInfo.getPaymentMerchantCode());
			try {
				parameterMap.put("orderMemo", URLEncoder.encode(communityPaymentRecord.getComment(), "UTF-8"));
			} catch (UnsupportedEncodingException e1) {
				e1.printStackTrace();
				throw new RuntimeException(e1);
			}
			// 不同支付方式构造不同参数
			if (req.getPaymentScene().equals("WX_MINIPROGRAM")) {
				// 获取openId
				if (StringUtils.isEmpty(req.getCode())) {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "小程序支付，CODE不能为空！");
					return res;
				}
				DepartmentEntity department = getDepartmentByCode(req.getDepartmentCode());
				String openId = null;
				if(department != null && StringUtils.isNotEmpty(department.getWxMiniProgramAppId()) && StringUtils.isNotEmpty(department.getWxMiniProgramAppId()) ) {
					parameterMap.put("appId", department.getWxMiniProgramAppId());
					openId = WeiXinApiUtil.getOpenIdByCode(department.getWxMiniProgramAppId(),
							department.getWxMiniProgramAppSecret(), req.getCode());
				}
				else {
					parameterMap.put("appId", communityContextInfo.miniProgramAppid);
				    openId = WeiXinApiUtil.getOpenIdByCode(communityContextInfo.miniProgramAppid,
						communityContextInfo.miniProgramAppsecret, req.getCode());
				}
				if (openId == null) {
					throw new RuntimeException("获取小程序OpendId失败");
				}
	
				communityPaymentRecord.setPayerIdentify(openId);
				parameterMap.put("paymentPluginId", "com.foshan.plugin.payment.WxPaymentPlugin");
				parameterMap.put("openId", communityPaymentRecord.getPayerIdentify());
				parameterMap.put("paymentScene", "WX_MINIPROGRAM");

			} else if (req.getPaymentScene().equals("WX_NAVITE_QR")) {
				parameterMap.put("paymentPluginId", "com.foshan.plugin.payment.WxPaymentPlugin");
				parameterMap.put("authCode", "0");
				parameterMap.put("paymentScene", "WX_NAVITE_QR");

			} else if (req.getPaymentScene().equals("WX_PAYCODE")) {
				if (StringUtils.isEmpty(req.getCode())) {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "18位付款码不能为空！");
					return res;
				}

				parameterMap.put("paymentPluginId", "com.foshan.plugin.payment.WxPaymentPlugin");
				parameterMap.put("authCode", req.getCode());
				parameterMap.put("paymentScene", "WX_PAYCODE");
			} else if (req.getPaymentScene().equals("AL_PAYCODE")) {
				if (StringUtils.isEmpty(req.getCode())) {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "付款码不能为空！");
					return res;
				}

				parameterMap.put("paymentPluginId", "com.foshan.plugin.payment.AlipayImmediatePaymentPlugin");
				parameterMap.put("authCode", req.getCode());
				parameterMap.put("paymentScene", "AL_PAYCODE");
				
				DepartmentEntity department = getDepartmentByCode(req.getDepartmentCode());
				if(department != null) {
					parameterMap.put("appId", StringUtils.isNotEmpty(department.getWxMiniProgramAppId() ) ? department.getWxMiniProgramAppId() : communityContextInfo.miniProgramAppid);
				}
				else {
					parameterMap.put("appId", communityContextInfo.miniProgramAppid);
				}

			} else if (req.getPaymentScene().equals("AL_NAVITE_QR")) {
				parameterMap.put("paymentPluginId", "com.foshan.plugin.payment.AlipayImmediatePaymentPlugin");
				parameterMap.put("authCode", "0");
				parameterMap.put("paymentScene", "AL_NAVITE_QR");
				
			} else if (req.getPaymentScene().equals("AL_MINIPROGRAM")) {
				// 获取openId
				if (StringUtils.isEmpty(req.getCode())) {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "支付宝小程序支付，authCode不能为空！");
					return res;
				}
				if (StringUtils.isEmpty(req.getDepartmentCode())) {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "支付宝小程序支付，departmentCode不能为空！");
					return res;
				}
				DepartmentEntity department = getDepartmentByCode(req.getDepartmentCode());
				if(department == null){
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "找不到Department[DepartmentCode="+req.getDepartmentCode()+"]对应的数据");
					return res;
				}
				String userId = null;
				if(StringUtils.isNotEmpty(department.getAlipayMiniProgramAppId()) && StringUtils.isNotEmpty(department.getAlipayApplicationPrivateKey())  && StringUtils.isNotEmpty(department.getAlipayPulbicKey()) ) {
					parameterMap.put("appId", department.getAlipayMiniProgramAppId());
					AlipayAccessTokenResult result = AlipayApiUtil.getAccessToken(department.getAlipayMiniProgramAppId(),department.getAlipayApplicationPrivateKey(),department.getAlipayPulbicKey(),req.getCode());
					userId = result.getUserId();
					//openId = WeiXinApiUtil.getOpenIdByCode(department.getWxMiniProgramAppId(),
					//		department.getWxMiniProgramAppSecret(), req.getCode());
				}
				else {
					res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
					res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "Department["+req.getDepartmentCode()+"]表中没有配置对应的支付宝小程序参数");
					return res;
				}
				if (StringUtils.isEmpty(userId)) {
					throw new RuntimeException("获取支付宝userId失败");
				}
	
				communityPaymentRecord.setPayerIdentify(userId);
				parameterMap.put("paymentPluginId", "com.foshan.plugin.payment.AlipayImmediatePaymentPlugin");
				parameterMap.put("openId", communityPaymentRecord.getPayerIdentify());
				parameterMap.put("paymentScene", "AL_MINIPROGRAM");

			} 

			// 增加线上支付请求签名字段
			try {
				DigestUtil.fillSignatureParam(parameterMap, communityContextInfo.getPaymentMerchantCode(),
						communityContextInfo.getPaymentSecret());
			} catch (Exception e1) {
				res.setRet(ResponseContext.RES_PAYMENT_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_PAYMENT_ERROR_INFO + "构造在线支付请求签名错误！");
				e1.printStackTrace();
				return res;
			}

			// 向支付模块发起支付请求
			String jsonstr = JSONObject.valueToString(parameterMap);
			String postResult;
			try {
				postResult = HttpClientUtil.jsonPost(communityContextInfo.getPaymentServiceUrl(), "UTF-8", jsonstr,
						null);
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
			// 处理支付模块返回的支付结果
			ObjectMapper mapper = new ObjectMapper();
			Map<String, Object> jsonObj = null;
			try {
				jsonObj = mapper.readValue(postResult, TreeMap.class);
				logger.info("业务系统申请在线支付结果：{}", jsonObj);
				res.setPaymentOnlineResultInfo(jsonObj);

			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			if (jsonObj != null && jsonObj.get("returnCode") != null) {
				communityPaymentRecord.setPaymentSessionSn(
						jsonObj.get("paymentSessionSn") != null ? jsonObj.get("paymentSessionSn").toString() : null);
				if (jsonObj.get("returnCode").toString().equals("SUCCESS")) {
					communityPaymentRecord.setStatus(1);
					res.setCommunityPaymentRecordId(communityPaymentRecord.getId());
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} else {
					StringBuilder failedReason = new StringBuilder(
							jsonObj.get("returnInfo") != null ? jsonObj.get("returnInfo").toString() : "");
					failedReason.append(
							jsonObj.get("errorCode") != null ? "[" + jsonObj.get("errorCode") + "]".toString() : "");
					failedReason.append(
							jsonObj.get("errorMessage") != null ? "[" + jsonObj.get("errorMessage") + "]".toString()
									: "");
					res.setRet(ResponseContext.RES_PAYMENT_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_PAYMENT_ERROR_INFO + failedReason.toString());
					// logger.info("在线支付出错！:{}", postResult);
				}
			} else {
				res.setRet(ResponseContext.RES_PAYMENT_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_PAYMENT_ERROR_INFO + "返回报文格式错误！");
				logger.info("在线支付出错，JSON报文格式错误或缺少returnCode字段！");
			}

		}
		return res;
	}
	
	@SuppressWarnings("unchecked")
	// @Audit(operate = "手机端付款操作")
	@Override
	public IResponse addCommunityReceiveByMember(CommunityPaymentReq req) throws RuntimeException {
		AddCommunityReceiveRes res = new AddCommunityReceiveRes();
		CommunityReceiptForm communityReceiptForm = req.getCommunityReceiptForm();
		// 检查必填字段
		if (null == communityReceiptForm) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "收据对象不能为空！");
			return res;
		}
		if (null == req.getReceiveDetialList() || 0 == req.getReceiveDetialList().size()) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "收费详情列表不能为空！");
			return res;
		}
		if (null == communityReceiptForm.getPaymentType()) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "收据的收款类型不能为空！");
			return res;
		}
		if (StringUtils.isEmpty(communityReceiptForm.getEstateId())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "房子或车位ID不能为空！");
			return res;
		}
		if (StringUtils.isEmpty(communityReceiptForm.getPaymentMethod())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "支付方法名称不能为空！");
			return res;
		}
		if(StringUtils.isEmpty(req.getDepartmentCode())){
			req.setDepartmentCode("root");
		}
		if (StringUtils.isEmpty(req.getPaymentScene())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "线上支付方法不能为空！");
			return res;
		}
		else {
			if(!req.getPaymentScene().equals("WX_PUBLIC") 
				&& !req.getPaymentScene().equals("WX_NAVITE_QR")
				&& !req.getPaymentScene().equals("WX_MOBILE_WEBSITE")
				&& !req.getPaymentScene().equals("WX_MINIPROGRAM")
				&& !req.getPaymentScene().equals("AL_MINIPROGRAM")
				&& !req.getPaymentScene().equals("AL_NAVITE_QR")
				&& !req.getPaymentScene().equals("AL_PAYCODE")
				&& !req.getPaymentScene().equals("WX_PAYCODE")
				) {
				res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
				res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "暂不支持"+req.getPaymentScene() +"线上支付方式！");
				return res;
			}
		}
	
		Map<CommunityReceivablesEntity, BigDecimal> receiveDetailMap = new HashMap<CommunityReceivablesEntity, BigDecimal>();
		// 实收总金额
		BigDecimal totalPayableAmount = BigDecimal.ZERO;
		Integer feeType = 0;
		// 把生成的应收存在缓存中
		Cache cache = cacheManager.getCache("prePaymentReceivableCache");
		for (CommunityReceiveDetailForm detailForm : req.getReceiveDetialList()) {
			CommunityReceivablesEntity receivablesEntity = null;
			if (detailForm.getReceivablesId() >= 0) {
				// 非预收
				receivablesEntity = communityReceivablesDao.get(detailForm.getReceivablesId());
			} else {
				// 预收，从缓存读出
				receivablesEntity = cache.get(detailForm.getReceivablesId(), CommunityReceivablesEntity.class);
			}

			if (null == receivablesEntity) {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo("收款失败，没找到对应的应收数据,receivablesId:[" + detailForm.getReceivablesId()
						+ "]");
				return res;
			}

			if (receivablesEntity.getLockMark().intValue() == 1) {
				res.setRet(ResponseContext.RES_DATA_ERROR_INFO);
				res.setRetInfo("收款失败," + receivablesEntity.getPayItemsName() + ",receivablesId:["
						+ detailForm.getReceivablesId() + "]已被锁盘锁定，暂不能收款，请过一天再试！");
				return res;
			}

			// 当前应收的未收金额
			BigDecimal payableAmount = receivablesEntity.getReceivableAmount()
					.subtract(receivablesEntity.getReceivedAmount());
			if (payableAmount.compareTo(BigDecimal.ZERO) <= 0) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo("收款失败，receivablesId:[" + detailForm.getReceivablesId() + "]，已经完成收款，无需再付!");
				return res;
			}
			// 避免多收
			if (payableAmount.compareTo(detailForm.getCurrentAmount()) < 0) {
				// 实收收款金额 > 应收未收时，按应收未收金额收款。
				logger.info("receivablesId[{}]:应收未收金额为{}元，少于请求收款金额{}元，将按{}进行实际收款！", receivablesEntity.getId(),
						payableAmount, detailForm.getCurrentAmount(), payableAmount);
			} else if (payableAmount.compareTo(detailForm.getCurrentAmount()) > 0) {
				// 部分收款
				logger.info("receivablesId[{}]:部分收款，应收未收金额为{}元，大于请求收款金额{}元，将按{}进行实际收款！", receivablesEntity.getId(),
						payableAmount, detailForm.getCurrentAmount(), detailForm.getCurrentAmount());
				payableAmount = detailForm.getCurrentAmount();
			}

			totalPayableAmount = totalPayableAmount.add(payableAmount);
			detailForm.setCurrentAmount(payableAmount);
			receiveDetailMap.put(receivablesEntity, payableAmount);
			if (receivablesEntity.getPayItem() != null) {
				feeType = receivablesEntity.getPayItem().getPayDate() > 0 ? 0 : 1;
			}
		}
		communityReceiptForm.setFeeType(feeType);

		// 判断有没有需要付款的应收项
		if (receiveDetailMap.keySet().size() == 0) {
			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "没找到对应的项目！");
			return res;
		}

		// 生成收款记录
		CommunityPaymentRecordEntity communityPaymentRecord = new CommunityPaymentRecordEntity();
		communityPaymentRecord.setAmount(totalPayableAmount);

		communityPaymentRecord.setBankAccount(null);
		CommunityEstateEntity communityEstateEntity = null;
		CommunityMemberPropertyEntity memberProperty = null;
		if (StringUtils.isNotEmpty(communityReceiptForm.getEstateId())) {
			// 获取单元
			communityEstateEntity = communityEstateDao.get(Integer.parseInt(communityReceiptForm.getEstateId()));
			if (communityEstateEntity != null) {
				// 获取单元当前业主
				for (CommunityMemberPropertyEntity o : communityEstateEntity.getMemberPropertyList()) {
					if (o.getIsCurrentOwner().intValue() == 1) {
						memberProperty = o;
						break;
					}
				}
			} else {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "找不到对应的单元数据！");
				return res;
			}
		}
		communityReceiptForm.setReceiptCode(null);
		communityPaymentRecord.setComment(
				StringUtils.isNotEmpty(communityReceiptForm.getComment()) ? communityReceiptForm.getComment() : "物业缴费");
		communityPaymentRecord.setPayerName(memberProperty == null ? communityReceiptForm.getPayerName() : memberProperty.getMember().getUserName());
		communityPaymentRecord.setPaymentDate(new Date());
		communityPaymentRecord.setPaymentRecordCode(CodeUtil.getSNCode("CPR"));
		communityPaymentRecord.setPaymentType(communityReceiptForm.getPaymentType());
		communityPaymentRecord.setPaymentMethod(communityReceiptForm.getPaymentMethod());
		communityPaymentRecord.setPaymentScene(req.getPaymentScene());
		communityPaymentRecord.setHaveInvoice(communityReceiptForm.getHaveInvoice());
		communityPaymentRecord.setReceiptCode(communityReceiptForm.getReceiptCode());
		// 记录付款详情，1、可作为每次支付请求的审核日志，2、在线支付时，业务系统收到支付结果后用于还原付款详情用。
		try {
			String detail = new ObjectMapper().writeValueAsString(req.getReceiveDetialList());
			communityPaymentRecord.setPaymentDetail(detail);
		} catch (JsonProcessingException e2) {
			logger.error("生成支付记录时，付款详情转换成JSON出错！");
			throw new RuntimeException(e2);
		}
		// 记录收款的单元对象
		communityPaymentRecord.setEstate(communityEstateEntity);
		communityPaymentRecord.setState(EntityContext.RECORD_STATE_VALID);
		communityPaymentRecord.setStatus(0);
		communityPaymentRecordDao.save(communityPaymentRecord);

		// 线上支付公共参数设置
		Map<String, String> parameterMap = new TreeMap<String, String>();
		parameterMap.put("clientTradeNo", communityPaymentRecord.getPaymentRecordCode());
		parameterMap.put("orderTotalAmount", communityPaymentRecord.getAmount().toString());
		parameterMap.put("notifyUrl", communityContextInfo.paymentNotifyUrl);
		parameterMap.put("clientIp", "127.0.0.1");
		parameterMap.put("merchantCode", communityContextInfo.getPaymentMerchantCode());
		// 普华的商户号需要用订单号前缀区分社区物业收费和商圈的付款，
		// orderType = 0 为社区物业缴费，orderType = 1 为商圈付款
		parameterMap.put("orderType", "0");
		try {
			parameterMap.put("orderMemo", URLEncoder.encode(communityPaymentRecord.getComment(), "UTF-8"));
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
			throw new RuntimeException(e1);
		}
		// 不同支付方式构造不同参数
		if (req.getPaymentScene().equals("WX_MINIPROGRAM")) {
			// 获取openId
			if (StringUtils.isEmpty(req.getCode())) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "小程序支付，CODE不能为空！");
				return res;
			}
			DepartmentEntity department = getDepartmentByCode(req.getDepartmentCode());
			String openId = null;
			if(department != null && StringUtils.isNotEmpty(department.getWxMiniProgramAppId()) && StringUtils.isNotEmpty(department.getWxMiniProgramAppId()) ) {
				parameterMap.put("appId", department.getWxMiniProgramAppId());
				openId = WeiXinApiUtil.getOpenIdByCode(department.getWxMiniProgramAppId(),
						department.getWxMiniProgramAppSecret(), req.getCode());
			}
			else {
				parameterMap.put("appId", communityContextInfo.miniProgramAppid);
			    openId = WeiXinApiUtil.getOpenIdByCode(communityContextInfo.miniProgramAppid,
					communityContextInfo.miniProgramAppsecret, req.getCode());
			}
			if (openId == null) {
				throw new RuntimeException("获取小程序OpendId失败");
			}
			
			communityPaymentRecord.setPayerIdentify(openId);

			parameterMap.put("paymentPluginId", "com.foshan.plugin.payment.WxPaymentPlugin");
			parameterMap.put("openId", communityPaymentRecord.getPayerIdentify());
			parameterMap.put("paymentScene", "WX_MINIPROGRAM");

		} else if (req.getPaymentScene().equals("WX_NAVITE_QR")) {

			parameterMap.put("paymentPluginId", "com.foshan.plugin.payment.WxPaymentPlugin");
			parameterMap.put("authCode", "0");
			parameterMap.put("paymentScene", "WX_NAVITE_QR");

		} else if (req.getPaymentScene().equals("WX_PAYCODE")) {
			if (StringUtils.isEmpty(req.getCode())) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "18位付款码不能为空！");
				return res;
			}

			parameterMap.put("paymentPluginId", "com.foshan.plugin.payment.WxPaymentPlugin");
			parameterMap.put("authCode", req.getCode());
			parameterMap.put("paymentScene", "WX_PAYCODE");
			
			DepartmentEntity department = getDepartmentByCode(req.getDepartmentCode());
			if(department != null) {
				parameterMap.put("appId", StringUtils.isNotEmpty(department.getWxMiniProgramAppId() ) ? department.getWxMiniProgramAppId() : communityContextInfo.miniProgramAppid);
			}
			else {
				parameterMap.put("appId", communityContextInfo.miniProgramAppid);
			}
	
		} else if (req.getPaymentScene().equals("AL_PAYCODE")) {
			if (StringUtils.isEmpty(req.getCode())) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "付款码不能为空！");
				return res;
			}
			parameterMap.put("paymentPluginId", "com.foshan.plugin.payment.AlipayImmediatePaymentPlugin");
			parameterMap.put("authCode", req.getCode());
			parameterMap.put("paymentScene", "AL_PAYCODE");

		} else if (req.getPaymentScene().equals("AL_NAVITE_QR")) {
			parameterMap.put("paymentPluginId", "com.foshan.plugin.payment.AlipayImmediatePaymentPlugin");
			parameterMap.put("authCode", "0");
			parameterMap.put("paymentScene", "AL_NAVITE_QR");
		} else if (req.getPaymentScene().equals("AL_MINIPROGRAM")) {
			// 获取openId
			if (StringUtils.isEmpty(req.getCode())) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "支付宝小程序支付，authCode不能为空！");
				return res;
			}
			if (StringUtils.isEmpty(req.getDepartmentCode())) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "支付宝小程序支付，departmentCode不能为空！");
				return res;
			}
			DepartmentEntity department = getDepartmentByCode(req.getDepartmentCode());
			if(department == null){
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "找不到Department[DepartmentCode="+req.getDepartmentCode()+"]对应的数据");
				return res;
			}
			String userId = null;
			if(StringUtils.isNotEmpty(department.getAlipayMiniProgramAppId()) && StringUtils.isNotEmpty(department.getAlipayApplicationPrivateKey())  && StringUtils.isNotEmpty(department.getAlipayPulbicKey()) ) {
				parameterMap.put("appId", department.getAlipayMiniProgramAppId());
				AlipayAccessTokenResult result = AlipayApiUtil.getAccessToken(department.getAlipayMiniProgramAppId(),department.getAlipayApplicationPrivateKey(),department.getAlipayPulbicKey(),req.getCode());
				userId = StringUtils.isNotEmpty(result.getUserId()) ?  result.getUserId() : result.getOpenId();
				//openId = WeiXinApiUtil.getOpenIdByCode(department.getWxMiniProgramAppId(),
				//		department.getWxMiniProgramAppSecret(), req.getCode());
			}
			else {
				res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
				res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "Department["+req.getDepartmentCode()+"]表中没有配置对应的支付宝小程序参数");
				return res;
			}
			if (StringUtils.isEmpty(userId)) {
				throw new RuntimeException("获取支付宝userId和openId失败");
			}

			communityPaymentRecord.setPayerIdentify(userId);
			parameterMap.put("paymentPluginId", "com.foshan.plugin.payment.AlipayImmediatePaymentPlugin");
			parameterMap.put("openId", communityPaymentRecord.getPayerIdentify());
			parameterMap.put("paymentScene", "AL_MINIPROGRAM");

		} 


		// 增加线上支付请求签名字段
		try {
			DigestUtil.fillSignatureParam(parameterMap, communityContextInfo.getPaymentMerchantCode(),
					communityContextInfo.getPaymentSecret());
		} catch (Exception e1) {
			res.setRet(ResponseContext.RES_PAYMENT_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PAYMENT_ERROR_INFO + "构造在线支付请求签名错误！");
			e1.printStackTrace();
			return res;
		}

		// 向支付模块发起支付请求
		String jsonstr = JSONObject.valueToString(parameterMap);
		String postResult;
		try {
			postResult = HttpClientUtil.jsonPost(communityContextInfo.getPaymentServiceUrl(), "UTF-8", jsonstr,
					null);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		// 处理支付模块返回的支付结果
		ObjectMapper mapper = new ObjectMapper();
		Map<String, Object> jsonObj = null;
		try {
			jsonObj = mapper.readValue(postResult, TreeMap.class);
			logger.info("业务系统申请在线支付结果：{}", jsonObj);
			res.setPaymentOnlineResultInfo(jsonObj);

		} catch (JsonMappingException e) {
			e.printStackTrace();
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		if (jsonObj != null && jsonObj.get("returnCode") != null) {
			communityPaymentRecord.setPaymentSessionSn(
					jsonObj.get("paymentSessionSn") != null ? jsonObj.get("paymentSessionSn").toString() : null);
			if (jsonObj.get("returnCode").toString().equals("SUCCESS")) {
				communityPaymentRecord.setStatus(1);
				// 微信小程序的逻辑改过来后，需要注释下面这句，不需要返回Id了
				res.setCommunityPaymentRecordId(communityPaymentRecord.getId()); 
				res.setCommunityPaymentRecordCode(communityPaymentRecord.getPaymentRecordCode());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				StringBuilder failedReason = new StringBuilder(
						jsonObj.get("returnInfo") != null ? jsonObj.get("returnInfo").toString() : "");
				failedReason.append(
						jsonObj.get("errorCode") != null ? "[" + jsonObj.get("errorCode") + "]".toString() : "");
				failedReason.append(
						jsonObj.get("errorMessage") != null ? "[" + jsonObj.get("errorMessage") + "]".toString()
								: "");
				res.setRet(ResponseContext.RES_PAYMENT_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_PAYMENT_ERROR_INFO + failedReason.toString());
				// logger.info("在线支付出错！:{}", postResult);
			}
		} else {
			res.setRet(ResponseContext.RES_PAYMENT_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PAYMENT_ERROR_INFO + "返回报文格式错误！");
			logger.info("在线支付出错，JSON报文格式错误或缺少returnCode字段！");
		}

		return res;
	}
	

	@Audit(operate = "支付回调处理")
	@Override
	public IResponse paymentNotify(PaymentNotifyReq req) {
		AddCommunityReceiveRes res = new AddCommunityReceiveRes();
		logger.info("收到支付模块支付记录编号为[{}]的支付结果通知!", req.getClientTradeNo());
		if (StringUtils.isEmpty(req.getReturnCode())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "返回码不能为空！");
			return res;
		}
		if (StringUtils.isEmpty(req.getClientTradeNo())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "支付记录SN不能为空！");
			return res;
		}
		if (!communityContextInfo.getPaymentMerchantCode().equals(req.getMerchantCode())) {
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "非本商户的支付消息,不作任何处理！");
			return res;
		}
		// 验收报文签名
		Map<String, String> reqMap = DigestUtil.jsonObjToMap(req);
		boolean signatureValid = false;
		try {
			signatureValid = DigestUtil.isSignatureValid(reqMap, communityContextInfo.getPaymentSecret(), "SM3");
		} catch (Exception e) {
			logger.error("[{}]验证支付模块的支付结果通知消息签名时发生异常！{}", req.getPaymentSessionSn(), e);
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "验证支付模块的支付结果通知消息签名时发生异常！");
			return res;
		}

		if (signatureValid == false) {
			logger.error("[{}]验证支付模块的支付结果通知消息签名失败！", req.getPaymentSessionSn());
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "验证支付模块的支付结果通知消息签名失败！");
			return res;
		}

		// 读取支付记录
		CommunityPaymentRecordEntity paymentRecord = communityPaymentRecordDao
				.findUnique("select e from CommunityPaymentRecordEntity e where e.paymentRecordCode ='"
						+ req.getClientTradeNo() + "'");
		if (paymentRecord == null) {
			logger.info("查不到对应的支付记录[{}]!", req.getClientTradeNo());
			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
			res.setRetInfo(
					ResponseContext.RES_DATA_NULL_INFO + "[" + req.getClientTradeNo() + "]查不到对应的支付记录，注意检查是否产生了单边账！");
			return res;
		}

		if (paymentRecord.getStatus().intValue() == 2 || paymentRecord.getStatus().intValue() == 3) {
			logger.info("编号为[{}]的支付会话已经完成支付!", req.getClientTradeNo());
			res.setRet(ResponseContext.RES_PAYMENT_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PAYMENT_ERROR_INFO + "编号为[" + req.getClientTradeNo() + "]的支付会话已经完成支付！");
			return res;
		}

		// 支付成功
		if (req.getReturnCode().toUpperCase().equals("SUCCESS")) {
			logger.info("编号为[{}]支付记录，支付成功!", req.getClientTradeNo());
			if (req.getAmount() != null && req.getAmount().compareTo(BigDecimal.ZERO) > 0) {
				
				res = paymentSuccessHandle(req, paymentRecord);
//				if(!res.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
//					logger.info("paymentRecordId["+paymentRecord.getId()+"]付款失败, 请及时处理单边账!!原因：" + res.getRetInfo());
//				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO + "SUCCESS!");
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "付款金额不能为零!");
			}
		} else {
			paymentRecord.setStatus(3);
			res.setRet(ResponseContext.RES_PAYMENT_ERROR_CODE);
			res.setRetInfo(
					ResponseContext.RES_PAYMENT_ERROR_INFO + "[" + req.getPaymentSessionSn() + "]第三方支付平台付款时发生错误!");
		}
		paymentRecord.setLastModifyTime(new Timestamp(System.currentTimeMillis()));

		// 构造返回结果
		return res;
	}

//	@Audit(operate = "向支付模块刷新支付结果操作")
	@Override
	public IResponse queryPaymentResult(CommunityPaymentRecordReq req) throws RuntimeException {

		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityPaymentRecordId()) {
			CommunityMemberEntity member = null;
			Object userObj = getPrincipal(true);
			if (null == userObj) {
				res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
				return res;
			} 
			CommunityPaymentRecordEntity communityPaymentRecord = communityPaymentRecordDao
					.get(req.getCommunityPaymentRecordId());
			if (null != communityPaymentRecord) {
				// 检查数据权限
				if(userObj instanceof CommunityMemberEntity){
					member = (CommunityMemberEntity) userObj;
					if(!canManageBillOfEstate(member.getId(), communityPaymentRecord.getEstate().getId())) {
						res.setRet(ResponseContext.RES_PERM_BEYOND_AUTHORITY_CODE);
						res.setRetInfo(ResponseContext.RES_PERM_BEYOND_AUTHORITY_INFO);
						return res;
					}
				}
				
				Map<String, String> parameterMap = new TreeMap<String, String>();
				parameterMap.put("paymentSessionCode", communityPaymentRecord.getPaymentSessionSn());

				// 增加签名字段
				try {
					DigestUtil.fillSignatureParam(parameterMap, communityContextInfo.getPaymentMerchantCode(),
							communityContextInfo.getPaymentSecret());
				} catch (Exception e1) {
					res.setRet(ResponseContext.RES_PAYMENT_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_PAYMENT_ERROR_INFO + "构造查询在线支付结果请求签名时发生错误！");
					e1.printStackTrace();
					return res;
				}

				String jsonstr = JSONObject.valueToString(parameterMap);
				String postResult;
				try {
					postResult = HttpClientUtil.jsonPost(communityContextInfo.getQueryPaymentResultServiceUrl(),
							"UTF-8", jsonstr, null);
				} catch (Exception e) {
					throw new RuntimeException(e);
				}

				ObjectMapper mapper = new ObjectMapper();
				try {
					@SuppressWarnings("unchecked")
					Map<String, Object> jsonObj = mapper.readValue(postResult, TreeMap.class);
					if (ResponseContext.RES_SUCCESS_CODE.equals(jsonObj.get("ret").toString())) {
						// 本地支付记录数据与支付系统不一致时，处理单边账。
						if (communityPaymentRecord.getStatus().intValue() == 1) {
							PaymentNotifyReq notifyReq = new PaymentNotifyReq();
							notifyReq.setAmount(communityPaymentRecord.getAmount());
							notifyReq.setOutTradeNo(communityPaymentRecord.getOutTradeNo());
							notifyReq.setClientTradeNo(communityPaymentRecord.getPaymentRecordCode());
							notifyReq.setPaymentSessionSn(communityPaymentRecord.getPaymentSessionSn());
							logger.info("业务系统为发起支付状态，而支付系统数据为支付成功，模拟接收到支付消息，处理单边账！{}" ,notifyReq);
							//paymentSuccessHandle(notifyReq, communityPaymentRecord);
						}
					}
					res.setRet(jsonObj.get("ret").toString());
					res.setRetInfo(jsonObj.get("retInfo").toString());
				} catch (JsonMappingException e) {
					e.printStackTrace();
					res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO + "查询在线支付结果返回报文格式出错!");
				} catch (JsonProcessingException e) {
					e.printStackTrace();
					res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO + "查询在线支付结果返回报文格式出错!");
				}

			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "communityPaymentRecordId不能为空！");
		}
		return res;
	}
	
	/**
	 * 
	 * @Title: 临时修复应收款的ResourceNo,
	 * 
	 * */
//	@Override
//	public GenericResponse addReceivablesNO(CommunityReceivablesReq req) {
//		GenericResponse res =  new GenericResponse();
//		res.setRet(ResponseContext.RES_SUCCESS_CODE);
//		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		if(StringUtils.isEmpty(req.getStartTime())) {
//			req.setStartTime("2022-11-30 12:06:08");
//		}
//		String Hql = "SELECT r FROM CommunityReceivablesEntity r WHERE r.createTime >= '" + req.getStartTime() +  "' AND r.receivablesNO IS NULL";
//		List<CommunityReceivablesEntity> listByHql = communityReceivablesDao.getListByHql(Hql);
//		int i=1;
//		logger.info("开始修复ReceivablesNO，共有{}条件记录需要处理！",listByHql.size());
//		for(CommunityReceivablesEntity receivable : listByHql) {
//			receivable.setReceivablesNO(generateSnLastValue("RECEIVABLESNO").toString());
//			communityReceivablesDao.update(receivable);
//			if (i % 100 == 0) {
//				communityReceivablesDao.flush();
//				communityReceivablesDao.clear();
//			}
//			i++;
//		}
//		logger.info("ReceivablesNO处理完成!!",listByHql.size());
//		return res ;
//	}

	private AddCommunityReceiveRes paymentSuccessHandle(PaymentNotifyReq req,
			CommunityPaymentRecordEntity paymentRecord) {
		AddCommunityReceiveRes res = new AddCommunityReceiveRes();
		// 构造收据收据
		CommunityReceiptForm receiptForm = new CommunityReceiptForm();
		receiptForm.setAmount(req.getAmount().toString());
		receiptForm.setAgent(paymentRecord.getPaymentScene().startsWith("WX_") ? "微信支付"
				: paymentRecord.getPaymentScene().startsWith("AL_") ? "支付宝支付" : "在线支付");
		receiptForm.setBankAccount(null);
		receiptForm.setCashier(null);
		receiptForm.setComment(paymentRecord.getComment());
		receiptForm.setHaveInvoice(paymentRecord.getHaveInvoice());
		receiptForm.setCurrencyAmount(req.getAmount().toString());
		receiptForm.setExchangeRate(null);
		receiptForm.setReceiptCode(null);
		receiptForm.setMoneytype("人民币");

		// 业主名
		receiptForm.setPayerName(paymentRecord.getPayerName());
		// 支付方法 微信、现金、POS、银行代收
		receiptForm.setPaymentMethod(paymentRecord.getPaymentMethod());
		// 0：应收款；1：退款； 2：预收；
		receiptForm.setPaymentType(paymentRecord.getPaymentType());
		// 后面加了其它支付方式再进行细化！！！！！！！
		receiptForm.setReceiptCodePrefix(receiptForm.getAgent());
		receiptForm.setPrintNum(0);
		receiptForm.setReceiptDate(DateUtil.format(new Date(), 1));

		receiptForm.setReceiptType("收据");
		receiptForm.setSupervisor(null);
		receiptForm
				.setEstateId(paymentRecord.getEstate() == null ? null : paymentRecord.getEstate().getId().toString());
		receiptForm.setOutTradeNo(req.getOutTradeNo());

		// 还原付款详情
		Map<CommunityReceivablesEntity, BigDecimal> paymentDetailMap = new HashMap<CommunityReceivablesEntity, BigDecimal>();
		List<CommunityReceiveDetailForm> paymentDetailList = null;
		try {
			paymentDetailList = (ArrayList<CommunityReceiveDetailForm>) new ObjectMapper()
					.readValue(paymentRecord.getPaymentDetail(), new TypeReference<List<CommunityReceiveDetailForm>>() {
					});
		} catch (JsonMappingException e) {
			e.printStackTrace();
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		if (paymentDetailList == null || paymentDetailList.size() == 0) {
			// 还原收收款详情失败
			logger.error("编号为[{}]支付记录，支付成功，收到支付结果通知，但从收款记录中还原收款详情失败!", req.getClientTradeNo());
			res.setRet(ResponseContext.RES_PAYMENT_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PAYMENT_ERROR_INFO + "编号为[" + req.getClientTradeNo()
					+ "]支付记录,支付成功，收到支付结果通知，但从支付记录中读取收款详情失败！");
			return res;
		}
		Integer feeType = 0;
		Cache cache = cacheManager.getCache("prePaymentReceivableCache");
		for (CommunityReceiveDetailForm detailForm : paymentDetailList) {
			CommunityReceivablesEntity receivablesEntity = null;
			if (detailForm.getReceivablesId() >= 0) {
				// 非预收
				receivablesEntity = communityReceivablesDao.get(detailForm.getReceivablesId());
			} else {
				// 预收，从缓存读出
				receivablesEntity = cache.get(detailForm.getReceivablesId(), CommunityReceivablesEntity.class);
			}
			if (null == receivablesEntity) {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo("收款失败，没找到对应的应收数据,receivablesId:[" + detailForm.getReceivablesId() + "]!");
				return res;
			}
//			if(receivablesEntity.getLockMark().intValue() == 1) {
//				res.setRet(ResponseContext.RES_DATA_ERROR_INFO);
//				res.setRetInfo("发起收款成功，但接收收款成功通知时该应收处于锁盘锁定状态，修改应收失败，需要核实处理单边账！,"+receivablesEntity.getPayItemsName()+",receivablesId:[" + detailForm.getReceivablesId() + "]已被锁盘锁定，暂不能收款！");
//				// 此处后续优化思路，发现收款的应收已经锁定，立即自动发起退款操作。
//				return res;
//			}
			feeType = receivablesEntity.getPayItem().getPayDate() > 0 ? 0 : 1;
			paymentDetailMap.put(receivablesEntity, detailForm.getCurrentAmount());
		}

		receiptForm.setReceiptCode(paymentRecord.getReceiptCode());
		receiptForm.setFeeType(feeType);
		res = payment(receiptForm, paymentRecord, paymentDetailMap);
		if(!res.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
			logger.info("paymentRecordId["+paymentRecord.getId()+"]付款失败, 请及时处理单边账!!原因：" + res.getRetInfo());
		}
		else {
			paymentRecord.setStatus(2);
		}
		paymentRecord.setOutTradeNo(req.getOutTradeNo());
		paymentRecord.setPaymentSessionSn(req.getPaymentSessionSn());
	
		return res;
	}

	/**
	 * 
	 * @Title: 根据收款明细进行收款，生成收据
	 * @Description:根据收款明细进行收款，生成收据
	 * @param receiptForm      收据信息
	 * @param paymentRecord    付款记录
	 * @param receiveDetailMap Map<CommunityReceivablesEntity,BigDecimal>
	 *                         收款详情，即每个应收项对应收多少钱，所有应收记录已经持久化。
	 * @return
	 */
	private AddCommunityReceiveRes payment(CommunityReceiptForm receiptForm, CommunityPaymentRecordEntity paymentRecord,
			Map<CommunityReceivablesEntity, BigDecimal> receiveDetailMap) {
		logger.info("进入付款逻辑！");
		AddCommunityReceiveRes res = new AddCommunityReceiveRes();
		// 检查必填字段
		if (null == receiptForm) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "收据对象不能为空！");
			return res;
		}
		if (null == receiveDetailMap || receiveDetailMap.size() == 0) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "收款详情列表不能为空！");
			return res;
		}

		if (null == receiptForm.getAmount()) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "收款金额不能为空！");
			return res;
		}
		if (null == receiptForm.getPaymentType()) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "收款类型不能为空！");
			return res;
		}
		if (null == receiptForm.getReceiptDate()) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "收款日期不能为空！");
			return res;
		}
		if (null == receiptForm.getPayerName()) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "付款人不能为空！");
			return res;
		}
		if (null == receiptForm.getAgent()) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "经办人不能为空！");
			return res;
		}

		// 生成收据
		CommunityReceiptEntity communityReceipt = new CommunityReceiptEntity();

		CommunityEstateEntity communityEstateEntity = null;
		if (StringUtils.isNotEmpty(receiptForm.getEstateId())) {
			communityEstateEntity = communityEstateDao.get(Integer.parseInt(receiptForm.getEstateId()));
		}

		communityReceipt.setAgent(receiptForm.getAgent());
		communityReceipt.setBankAccount(receiptForm.getBankAccount());
		communityReceipt.setCashier(receiptForm.getCashier());
		communityReceipt.setComment(receiptForm.getComment());
		communityReceipt.setFeeType(receiptForm.getFeeType());
		communityReceipt.setCurrencyAmount(StringUtils.isNotEmpty(receiptForm.getCurrencyAmount())
				? new BigDecimal(receiptForm.getCurrencyAmount())
				: null);
		communityReceipt.setExchangeRate(
				StringUtils.isNotEmpty(receiptForm.getExchangeRate()) ? new BigDecimal(receiptForm.getExchangeRate())
						: null);
		communityReceipt.setMoneytype(receiptForm.getMoneytype());
		communityReceipt.setPayerName(receiptForm.getPayerName());
		communityReceipt.setPaymentMethod(receiptForm.getPaymentMethod());
		communityReceipt.setPaymentType(receiptForm.getPaymentType());
		communityReceipt.setReceiptType("收据");
		communityReceipt.setPrintNum(receiptForm.getPrintNum());
		try {
			communityReceipt.setReceiptDate(StringUtils.isNotEmpty(receiptForm.getReceiptDate())
					? DateUtil.parseLongFormat(receiptForm.getReceiptDate())
					: new Date());
		} catch (ParseException e) {
			e.printStackTrace();
		}
		if (StringUtils.isEmpty(receiptForm.getReceiptCode())) {
			// 生成收据编号
			String receiptCode = getReceiptCode(receiptForm.getReceiptCodePrefix(), communityReceipt.getReceiptDate());
			receiptForm.setReceiptCode(receiptCode);
		}
		communityReceipt.setReceiptCode(receiptForm.getReceiptCode());
		communityReceipt.setReceiptType(receiptForm.getReceiptType());
		communityReceipt.setSupervisor(receiptForm.getSupervisor());
		communityReceipt.setWxTradeNo(receiptForm.getOutTradeNo());
		communityReceipt.setHaveInvoice(receiptForm.getHaveInvoice());
		communityReceipt.setState(1);
		communityReceipt.setEstate(communityEstateEntity);
		communityReceiptDao.save(communityReceipt);
		logger.info("生成收据成功，收据编号[{}]！", communityReceipt.getReceiptCode());
		logger.info("--根据收款详情进行收款--");

		// 由于预收没有ID，保证应收对象处于非游离态
		List<CommunityReceivablesEntity> receivablesList = new ArrayList<CommunityReceivablesEntity>();
		Map<CommunityReceivablesEntity, BigDecimal> currentReceiveDetailMap = new HashMap<CommunityReceivablesEntity, BigDecimal>();
		List<Integer> preReceivableIdList = new ArrayList<Integer>();
		for (CommunityReceivablesEntity receivable : receiveDetailMap.keySet()) {
			BigDecimal paymentAmount = receiveDetailMap.get(receivable);
			// 处理预收
			if (receivable.getId() < 0) {
				// 严紧来说，需要缓存中复制新的就收对象参与后续的运算，
				// 否则后面运算有出错，无法再找回缓存，后续再进行优化！！！！！！
				preReceivableIdList.add(receivable.getId());
				receivable.setId(null);
				// 预收，写库前生成编号。
				receivable.setReceivablesNO(
						generateSnLastValue(CommunityReceivablesServiceImpl.RECEIVABLESNO_SN_TYPE).toString());
			}
			receivable.setEstate(communityEstateEntity);
			receivablesList.add(receivable);
			communityReceivablesDao.saveOrUpdate(receivable);
			currentReceiveDetailMap.put(receivable, paymentAmount);
			logger.info("receivableId:" + receivable.getId() + ",payItemId:"
					+ (receivable.getPayItem() != null ? receivable.getPayItem().getId() : null) + ",priority:"
					+ (receivable.getPayItem() != null ? receivable.getPayItem().getPriority() : null));
		}
		// 实收款
		List<CommunityReceiptReceivablesForm> commnuityReceiptReceivablesFormList = new ArrayList<CommunityReceiptReceivablesForm>();
		// 本次实际付款总金额
		BigDecimal totalPaymentAmount = BigDecimal.ZERO;
		for (CommunityReceivablesEntity receivable : currentReceiveDetailMap.keySet()) {
			// 本次收款金额
			BigDecimal paymentAmount = currentReceiveDetailMap.get(receivable);
			if (paymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
				break;
			}

			CommunityReceiptReceivablesEntity communityReceiptReceivables = new CommunityReceiptReceivablesEntity();
			// 设置实收表的已收金额 = 当前应收表的已收
			communityReceiptReceivables.setReceivedAmount(receivable.getReceivedAmount());
			// 应付未付金额 = 应付金额 - 已付金额
			BigDecimal payableAmount = receivable.getReceivableAmount().subtract(receivable.getReceivedAmount());
			if (payableAmount.compareTo(BigDecimal.ZERO) <= 0) {
				// 已经完成付款，不需要付款
				continue;
			} else if (paymentAmount.compareTo(payableAmount) >= 0) {
				receivable.setReceivedAmount(receivable.getReceivedAmount().add(payableAmount));
				receivable.setLastModifyTime(new Timestamp(System.currentTimeMillis()));
				communityReceiptReceivables.setCurrentAmount(payableAmount);
			} else {
				// 部分付款
				receivable.setReceivedAmount(receivable.getReceivedAmount().add(paymentAmount));
				receivable.setLastModifyTime(new Timestamp(System.currentTimeMillis()));
				communityReceiptReceivables.setCurrentAmount(paymentAmount);

			}
			totalPaymentAmount = totalPaymentAmount.add(communityReceiptReceivables.getCurrentAmount());
			logger.info("修改应收成功，应收ID为[{}]！", receivable.getId());

			// 生成实收
			communityReceiptReceivables.setReceivables(receivable);
			communityReceiptReceivables.setReceipt(communityReceipt);
			communityReceiptReceivables.setInvoiceState(0);

			communityReceiptReceivablesDao.save(communityReceiptReceivables);
			logger.info("生成实收成功，实收ID为[{}]！", communityReceiptReceivables.getId());
			
			// 修改补差额对象
			modifySubReceivaedManageAmount(communityReceiptReceivables.getId(), receivable, paymentAmount, communityReceipt.getReceiptDate() ,0);
			
			CommunityReceiptReceivablesForm communityReceiptReceivablesForm = new CommunityReceiptReceivablesForm();
			communityReceiptReceivablesForm.setCommunityReceiptReceivablesId(communityReceiptReceivables.getId());
			communityReceiptReceivablesForm.setCurrentAmount(communityReceiptReceivables.getCurrentAmount().toString());
			communityReceiptReceivablesForm
					.setReceivedAmount(communityReceiptReceivables.getReceivedAmount().toString());
			communityReceiptReceivablesForm.setReceivablesForm(null);
			commnuityReceiptReceivablesFormList.add(communityReceiptReceivablesForm);

		}
		communityReceipt.setAmount(totalPaymentAmount);

		if (paymentRecord != null) {
			paymentRecord.setReceipt(communityReceipt);
			paymentRecord.setReceiptCode(communityReceipt.getReceiptCode());
			paymentRecord.setStatus(2);
			logger.info("修改支付记录[{}]状态为支付成功！", paymentRecord.getPaymentRecordCode());
		}

		// 删除应收缓存
		for (CommunityReceivablesEntity receivables : receiveDetailMap.keySet()) {
			if (receivables.getReceivableAmount().compareTo(receivables.getReceivedAmount()) <= 0) {
				CommunityCache.removeReceivableCache(receivables, CommunityCache.receivableCalEstateList);
			} else {
				CommunityCache.putReceivableCache(receivables, CommunityCache.receivableCalEstateList);
			}
		}

		// 收款成功删除缓存中的预收
		Cache cache = cacheManager.getCache("prePaymentReceivableCache");
		if (cache != null) {
			for (Integer preReceivableId : preReceivableIdList) {
				cache.evict(preReceivableId);
			}
		}
		res.setReceiptId(communityReceipt.getId());
		res.setCommnuityReceiptReceivablesFormList(commnuityReceiptReceivablesFormList);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		return res;
	}
}