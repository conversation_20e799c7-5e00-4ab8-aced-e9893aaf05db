package com.foshan.service.community.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.foshan.entity.community.CommunityBuildingEntity;
import com.foshan.entity.community.CommunityDistrictEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityBuildingService;
import com.foshan.util.DateUtil;

import org.springframework.transaction.annotation.Transactional;
import com.foshan.form.community.request.CommunityBuildingReq;
import com.foshan.form.community.response.communityBuilding.AddCommunityBuildingRes;
import com.foshan.form.community.response.communityBuilding.ModifyCommunityBuildingRes;
import com.foshan.form.community.response.communityBuilding.GetCommunityBuildingInfoRes;
import com.foshan.form.community.response.communityBuilding.GetCommunityBuildingListRes;
import com.foshan.form.community.CommunityBuildingForm;
import com.foshan.form.community.CommunityDistrict;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.IResponse;
import com.foshan.dao.generic.Page;

@Transactional
@Service("communityBuildingService")
public class CommunityBuildingServiceImpl extends GenericCommunityService implements ICommunityBuildingService{

	@Override
	public IResponse getCommunityBuildingList(CommunityBuildingReq req) {
		GetCommunityBuildingListRes res = new GetCommunityBuildingListRes();
		Page<CommunityBuildingEntity> page = new Page<CommunityBuildingEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityBuildingEntity a where a.state="+EntityContext.RECORD_STATE_VALID);
		hql.append(StringUtils.isNotEmpty(req.getBuildingName()) ? " and a.buildingName like'%"+req.getBuildingName()+"%'":"")
			.append(null!=req.getBuildingType() ? " and a.buildingType="+req.getBuildingType() : "")
			.append(StringUtils.isNotEmpty(req.getBuildingCode()) ? " and a.buildingCode like'%"+req.getBuildingCode()+"%'":"")
			.append(null!=req.getDistrictId() ? " and a.district.id="+req.getDistrictId() : 
				StringUtils.isNotEmpty(req.getDistrictIdList()) ? "  and a.district.id in("+req.getDistrictIdList()+")":"");
		hql.append(" ORDER BY a.buildingOrder asc");
		page = communityBuildingDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityBuildingForm communityBuildingForm = new CommunityBuildingForm();
			communityBuildingForm.setCommunityBuildingId(o.getId());
            communityBuildingForm.setAddress(o.getAddress());
            communityBuildingForm.setBuildingCode(o.getBuildingCode());
            communityBuildingForm.setBuildingName(o.getBuildingName());
            communityBuildingForm.setBuildingPermitNum(o.getBuildingPermitNum());
            communityBuildingForm.setBuildingType(o.getBuildingType());
            communityBuildingForm.setClassification(o.getClassification());
            communityBuildingForm.setCompletionDate(null != o.getCompletionDate() ? DateUtil.format(o.getCompletionDate(),1) :"");
            communityBuildingForm.setCappingDate(null!=o.getCappingDate() ? DateUtil.format(o.getCappingDate(),1) :"");
            communityBuildingForm.setConstruction(o.getConstruction());
            communityBuildingForm.setDamagedLevel(o.getDamagedLevel());
            communityBuildingForm.setDecoration(o.getDecoration());
            communityBuildingForm.setLayers(o.getLayers());
            communityBuildingForm.setComment(o.getComment());
			communityBuildingForm.setCreateTime(DateUtil.formatLongFormat(o.getCreateTime()));
			communityBuildingForm.setLastModifyTime(DateUtil.formatLongFormat(o.getLastModifyTime()));
            communityBuildingForm.setUsableArea(null!=o.getUsableArea() ? o.getUsableArea().toString():"");
            communityBuildingForm.setBuildingArea(null!=o.getBuildingArea() ? o.getBuildingArea().toString() : "");
            communityBuildingForm.setPermitLicenceNum(o.getPermitLicenceNum());
            communityBuildingForm.setBuildingOrder(o.getBuildingOrder());
            if(null != o.getDistrict()) {
            	communityBuildingForm.setDistrictForm(new CommunityDistrict (o.getDistrict().getId(),o.getDistrict().getDistrictName()));
            }
            
			res.getCommunityBuildingList().add(communityBuildingForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	@Audit(operate = "新增楼栋")
	public IResponse addCommunityBuilding(CommunityBuildingReq req) {
		AddCommunityBuildingRes res = new AddCommunityBuildingRes();
		if (null != req.getDistrictId() ) {
			CommunityDistrictEntity district = communityDistrictDao.get(req.getDistrictId());
			if(null == district) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			
			CommunityBuildingEntity communityBuilding = new CommunityBuildingEntity();
			
            communityBuilding.setAddress(req.getAddress());
            communityBuilding.setBuildingCode(req.getBuildingCode());
            communityBuilding.setBuildingName(req.getBuildingName());
            communityBuilding.setBuildingPermitNum(req.getBuildingPermitNum());
            communityBuilding.setBuildingType(req.getBuildingType());
            communityBuilding.setClassification(req.getClassification());
            communityBuilding.setState(EntityContext.RECORD_STATE_VALID);
            communityBuilding.setLastModifyTime(new Timestamp(new Date().getTime()));
            try {
            	communityBuilding.setCompletionDate(StringUtils.isNotEmpty(req.getCompletionDate()) ? DateUtil.parse(req.getCompletionDate(), 1) : null);
            	communityBuilding.setCappingDate(StringUtils.isNotEmpty(req.getCappingDate()) ? DateUtil.parse(req.getCappingDate(), 1) : null );
  			} catch (ParseException e) {
  				e.printStackTrace();
  			}
            communityBuilding.setDistrict(district);
            communityBuilding.setBuildingOrder(null!=req.getBuildingOrder() ? req.getBuildingOrder() : 1);
            communityBuilding.setConstruction(req.getConstruction());
            communityBuilding.setDamagedLevel(req.getDamagedLevel());
            communityBuilding.setDecoration(req.getDecoration());
            communityBuilding.setLayers(req.getLayers());
            communityBuilding.setPermitLicenceNum(req.getPermitLicenceNum());
            communityBuilding.setComment(StringUtils.isNotEmpty(req.getComment()) ? req.getComment(): "");
            communityBuilding.setUsableArea(StringUtils.isNotEmpty(req.getUsableArea()) ?  new BigDecimal(req.getUsableArea()) : new BigDecimal(0));
            communityBuilding.setBuildingArea(StringUtils.isNotEmpty(req.getBuildingArea()) ?  new BigDecimal(req.getBuildingArea()) : new BigDecimal(0));
            
			communityBuildingDao.save(communityBuilding);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "修改楼栋")
	public IResponse modifyCommunityBuilding(CommunityBuildingReq req) {
		ModifyCommunityBuildingRes res = new ModifyCommunityBuildingRes();
		if (null!=req.getCommunityBuildingId() ) {
			CommunityBuildingEntity communityBuilding = communityBuildingDao.get(req.getCommunityBuildingId()) ;
			if(null != communityBuilding && null != req.getDistrictId()){
				CommunityDistrictEntity district = communityDistrictDao.get(req.getDistrictId());
				if(null == district) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
				communityBuilding.setDistrict(district);
                communityBuilding.setAddress(StringUtils.isNotEmpty(req.getAddress()) ? req.getAddress() : communityBuilding.getAddress());
                communityBuilding.setBuildingCode(StringUtils.isNotEmpty(req.getBuildingCode()) ? req.getBuildingCode(): communityBuilding.getBuildingCode());
                communityBuilding.setBuildingName(StringUtils.isNotEmpty(req.getBuildingName()) ?req.getBuildingName(): communityBuilding.getBuildingName());
                communityBuilding.setBuildingPermitNum(StringUtils.isNotEmpty(req.getBuildingPermitNum())? req.getBuildingPermitNum(): communityBuilding.getBuildingPermitNum());
                communityBuilding.setBuildingType(null!=req.getBuildingType()?req.getBuildingType():communityBuilding.getBuildingType());
                communityBuilding.setClassification(null!=req.getClassification()?req.getClassification(): communityBuilding.getClassification());
                communityBuilding.setConstruction(StringUtils.isNotEmpty(req.getConstruction())?req.getConstruction(): communityBuilding.getConstruction());
                communityBuilding.setDamagedLevel(null!=req.getDamagedLevel() ? req.getDamagedLevel() : communityBuilding.getDamagedLevel());
                communityBuilding.setDecoration(StringUtils.isNotEmpty(req.getDecoration()) ? req.getDecoration():communityBuilding.getDecoration());
                communityBuilding.setBuildingOrder(null!=req.getBuildingOrder() ? req.getBuildingOrder() : communityBuilding.getBuildingOrder());
                communityBuilding.setLayers(null!=req.getLayers() ? req.getLayers() : communityBuilding.getLayers());
                communityBuilding.setPermitLicenceNum(StringUtils.isNotEmpty(req.getPermitLicenceNum()) ? req.getPermitLicenceNum() : communityBuilding.getPermitLicenceNum());
                communityBuilding.setLastModifyTime(new Timestamp(new Date().getTime()));
                communityBuilding.setComment(StringUtils.isNotEmpty(req.getComment()) ? req.getComment(): "");
                communityBuilding.setUsableArea(StringUtils.isNotEmpty(req.getUsableArea()) ?  new BigDecimal(req.getUsableArea()) : new BigDecimal(0));
                communityBuilding.setBuildingArea(StringUtils.isNotEmpty(req.getBuildingArea()) ?  new BigDecimal(req.getBuildingArea()) : new BigDecimal(0));
                
                try {
                	communityBuilding.setCompletionDate(StringUtils.isNotEmpty(req.getCompletionDate()) ? DateUtil.parse(req.getCompletionDate(), 1) : communityBuilding.getCompletionDate());
                	communityBuilding.setCappingDate(StringUtils.isNotEmpty(req.getCappingDate()) ? DateUtil.parse(req.getCappingDate(), 1) : communityBuilding.getCappingDate() );
      			} catch (ParseException e) {
      				e.printStackTrace();
      			}
				res.setCommunityBuildingId(communityBuilding.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "删除楼栋")
	public IResponse deleteCommunityBuilding(CommunityBuildingReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityBuildingId()) {
		CommunityBuildingEntity communityBuilding = communityBuildingDao.get(req.getCommunityBuildingId());
			if (null != communityBuilding) {
				communityBuilding.setDistrict(null);
				//communityBuildingDao.deleteById(req.getCommunityBuildingId());
				communityBuilding.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityBuildingInfo(CommunityBuildingReq req) {
		GetCommunityBuildingInfoRes res = new GetCommunityBuildingInfoRes();
		if (null != req.getCommunityBuildingId()) {
			CommunityBuildingEntity communityBuilding = communityBuildingDao.get(req.getCommunityBuildingId());
			if (null != communityBuilding) {
				CommunityBuildingForm communityBuildingForm = new CommunityBuildingForm();
				
				communityBuildingForm.setCreateTime(DateUtil.formatLongFormat(communityBuilding.getCreateTime()));
				communityBuildingForm.setLastModifyTime(DateUtil.formatLongFormat(communityBuilding.getLastModifyTime()));
				communityBuildingForm.setCommunityBuildingId(communityBuilding.getId());
                communityBuildingForm.setAddress(communityBuilding.getAddress());
                communityBuildingForm.setBuildingCode(communityBuilding.getBuildingCode());
                communityBuildingForm.setBuildingName(communityBuilding.getBuildingName());
                communityBuildingForm.setBuildingPermitNum(communityBuilding.getBuildingPermitNum());
                communityBuildingForm.setBuildingType(communityBuilding.getBuildingType());
                communityBuildingForm.setCompletionDate(null != communityBuilding.getCompletionDate() ? DateUtil.format(communityBuilding.getCompletionDate(),1) :"");
                communityBuildingForm.setCappingDate(null!=communityBuilding.getCappingDate() ? DateUtil.format(communityBuilding.getCappingDate(),1) :"");
                communityBuildingForm.setClassification(communityBuilding.getClassification());
                communityBuildingForm.setConstruction(communityBuilding.getConstruction());
                communityBuildingForm.setDamagedLevel(communityBuilding.getDamagedLevel());
                communityBuildingForm.setDecoration(communityBuilding.getDecoration());
                communityBuildingForm.setLayers(communityBuilding.getLayers());
                communityBuildingForm.setPermitLicenceNum(communityBuilding.getPermitLicenceNum());
                communityBuildingForm.setComment(communityBuilding.getComment());
                communityBuildingForm.setBuildingOrder(communityBuilding.getBuildingOrder());
                communityBuildingForm.setUsableArea(null!=communityBuilding.getUsableArea() ? communityBuilding.getUsableArea().toString():"");
                communityBuildingForm.setBuildingArea(null!=communityBuilding.getBuildingArea() ? communityBuilding.getBuildingArea().toString() : "");
                if(null != communityBuilding.getDistrict()) {
                	communityBuildingForm.setDistrictForm(
                			new CommunityDistrict (communityBuilding.getDistrict().getId(),
                					communityBuilding.getDistrict().getDistrictName()));
                }
				res.setCommunityBuildingForm(communityBuildingForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}