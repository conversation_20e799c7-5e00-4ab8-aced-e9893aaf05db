package com.foshan.service.community.task;

import com.foshan.form.community.request.CommunityInspectionRecordReq;
import com.foshan.service.community.ICommunityInspectionRecordService;
import com.foshan.service.quartz.IFaithJob;
import com.foshan.util.SpringHandler;

public class CommunityInspectionRecordTask implements IFaithJob {
	@SuppressWarnings("unchecked")
	public void communityQueryInvoiceResultTask() {
		try {
			ICommunityInspectionRecordService communityInspectionRecordService = (ICommunityInspectionRecordService) SpringHandler.getBean("communityInspectionRecordService");
			communityInspectionRecordService.addCommunityInspectionRecord(new CommunityInspectionRecordReq());
		} catch (Exception ex) {
			
			ex.printStackTrace();
		}
	}
	
	
}
