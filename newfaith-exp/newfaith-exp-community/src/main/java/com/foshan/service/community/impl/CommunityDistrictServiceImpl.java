package com.foshan.service.community.impl;

import static java.util.Comparator.comparingInt;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityBuilderEntity;
import com.foshan.entity.community.CommunityBuildingEntity;
import com.foshan.entity.community.CommunityDistrictEntity;
import com.foshan.entity.community.CommunityEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityBuilderForm;
import com.foshan.form.community.CommunityBuildingForm;
import com.foshan.form.community.CommunityDistrictForm;
import com.foshan.form.community.CommunityForm;
import com.foshan.form.community.request.CommunityDistrictReq;
import com.foshan.form.community.response.communityDistrict.AddCommunityDistrictRes;
import com.foshan.form.community.response.communityDistrict.GetCommunityDistrictInfoRes;
import com.foshan.form.community.response.communityDistrict.GetCommunityDistrictListRes;
import com.foshan.form.community.response.communityDistrict.ModifyCommunityDistrictRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityDistrictService;
import com.foshan.util.DateUtil;

@Transactional
@Service("communityDistrictService")
public class CommunityDistrictServiceImpl extends GenericCommunityService implements ICommunityDistrictService{

	@Override
	public IResponse getCommunityDistrictList(CommunityDistrictReq req) {
		GetCommunityDistrictListRes res = new GetCommunityDistrictListRes();
		Page<CommunityDistrictEntity> page = new Page<CommunityDistrictEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityDistrictEntity a where 1=1 ");
		hql.append(StringUtils.isNotEmpty(req.getDistrictName()) ? " and a.districtName like'%"+req.getDistrictName()+"%'":"")
			.append(StringUtils.isNotEmpty(req.getDistrictCode()) ? " and a.districtCode like'%"+req.getDistrictCode()+"%'":"")
			.append(null!=req.getState() ? " and state="+req.getState():"");
		hql.append(" ORDER BY a.districtOrder asc");
		page = communityDistrictDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
		page.getResultList().forEach(o -> {
			CommunityDistrictForm communityDistrictForm = new CommunityDistrictForm();
			communityDistrictForm.setCommunityDistrictId(o.getId());
            communityDistrictForm.setAddress(o.getAddress());
            communityDistrictForm.setAreaSize(o.getAreaSize());
            communityDistrictForm.setHousingNumber(o.getHousingNumber());
            communityDistrictForm.setDistrictName(o.getDistrictName());
            communityDistrictForm.setPeriod(o.getPeriod());
            communityDistrictForm.setRatio(o.getRatio());       
            communityDistrictForm.setBuildingNumber(o.getBuildingNumber());
            communityDistrictForm.setComment(o.getComment());
            communityDistrictForm.setCompletionDate(null!=o.getCompletionDate() ? DateUtil.format(o.getCompletionDate(),1) : null);
            communityDistrictForm.setDistrictCode(o.getDistrictCode());
            communityDistrictForm.setDistrictOrder(o.getDistrictOrder());
            communityDistrictForm.setHandoverDate(null != o.getHandoverDate() ? DateUtil.format(o.getHandoverDate(),1) : null);
            communityDistrictForm.setPopulation(o.getPopulation());
            communityDistrictForm.setTakeTime(o.getTakeTime());
            communityDistrictForm.setCreateTime(null != o.getCreateTime() ? sdf.format(o.getCreateTime()) : "");
            communityDistrictForm.setLastModifyTime(null != o.getLastModifyTime() ? sdf.format(o.getLastModifyTime()) : "");
            communityDistrictForm.setState(o.getState());
            if(null != o.getCommunity()) {
            	CommunityForm communityForm = new CommunityForm(o.getCommunity().getId(),
            			o.getCommunity().getCommunityName(),o.getCommunity().getCommunityAddress(),o.getCommunity().getAreaSize());
            	communityDistrictForm.setCommunityForm(communityForm);
            }
            o.getBuilderList().forEach(p->{
            	CommunityBuilderForm builderForm = new CommunityBuilderForm(p.getId(),p.getBuilderName(),p.getBuilderAddress(),p.getLegalRepresentative());
            	communityDistrictForm.getBuilderFormList().add(builderForm);
            });
			res.getCommunityDistrictList().add(communityDistrictForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	public IResponse getCommunityDistrictTree() {
		GetCommunityDistrictListRes res = new GetCommunityDistrictListRes();
		StringBuilder hql = new StringBuilder("select distinct a from CommunityDistrictEntity a where a.state="
			+ EntityContext.RECORD_STATE_VALID);
		hql.append(" ORDER BY a.districtOrder asc");
		List<CommunityDistrictEntity> list = communityDistrictDao.getListByHql(hql.toString(), "");
		for(CommunityDistrictEntity o : list) {
			CommunityDistrictForm district = new CommunityDistrictForm();
			district.setCommunityDistrictId(o.getId());
			district.setDistrictName(o.getDistrictName());
			o.getBuildingList().sort(comparingInt(CommunityBuildingEntity::getBuildingOrder));
			o.getBuildingList().forEach(p->{
				CommunityBuildingForm building = new CommunityBuildingForm();
				building.setCommunityBuildingId(p.getId());
				building.setBuildingName(p.getBuildingName());
				district.getBuildingList().add(building);
			});
			res.getCommunityDistrictList().add(district);
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		
		return res;
	}

	@Override
	@Audit(operate = "新增社区")
	public IResponse addCommunityDistrict(CommunityDistrictReq req) {
		AddCommunityDistrictRes res = new AddCommunityDistrictRes();
		if (null != req.getCommunityId() && StringUtils.isNotEmpty(req.getDistrictName())) {
			CommunityDistrictEntity communityDistrict = new CommunityDistrictEntity();
			CommunityEntity community = communityDao.get(req.getCommunityId());
			if(null == community) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			
            communityDistrict.setAddress(req.getAddress());
            communityDistrict.setAreaSize(req.getAreaSize());
            communityDistrict.setHousingNumber(req.getHousingNumber());
            communityDistrict.setDistrictName(req.getDistrictName());
            communityDistrict.setPeriod(req.getPeriod());
            communityDistrict.setRatio(req.getRatio());
            communityDistrict.setBuildingNumber(null!=req.getBuildingNumber() ? req.getBuildingNumber() : null);
            communityDistrict.setComment(req.getComment());
            communityDistrict.setDistrictOrder(null!=req.getDistrictOrder() ? req.getDistrictOrder() : 10);
            try {
				communityDistrict.setCompletionDate(StringUtils.isNotEmpty(req.getCompletionDate()) ? DateUtil.parse(req.getCompletionDate(), 1) : null);
	            communityDistrict.setHandoverDate(StringUtils.isNotEmpty(req.getHandoverDate()) ? DateUtil.parse(req.getHandoverDate(), 1) : null );
			} catch (ParseException e) {
				e.printStackTrace();
			}
			if (StringUtils.isNotEmpty(req.getBuilderIdList())) {
				String[] builderIds = req.getBuilderIdList().split(",");
				List<CommunityBuilderEntity> builderList = new ArrayList<CommunityBuilderEntity>();
				for (String builderId : builderIds) {
					builderList.add(communityBuilderDao.get(Integer.parseInt(builderId)));
				}
				communityDistrict.setBuilderList(builderList);
			}
            communityDistrict.setCommunity(communityDao.get(req.getCommunityId()));
            communityDistrict.setDistrictCode(req.getDistrictCode());
            communityDistrict.setPopulation(req.getPopulation());
            communityDistrict.setTakeTime(req.getTakeTime());
            communityDistrict.setLastModifyTime(new Timestamp(new Date().getTime()));
            communityDistrict.setState(EntityContext.RECORD_STATE_VALID);
			communityDistrictDao.save(communityDistrict);
			res.setCommunityDistrictId(communityDistrict.getId());
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "修改社区")
	public IResponse modifyCommunityDistrict(CommunityDistrictReq req) {
		ModifyCommunityDistrictRes res = new ModifyCommunityDistrictRes();
		if (null!=req.getCommunityDistrictId() && null!=req.getCommunityId() && StringUtils.isNotEmpty(req.getDistrictName())) {
			CommunityDistrictEntity communityDistrict = communityDistrictDao.get(req.getCommunityDistrictId()) ;
			CommunityEntity community = communityDao.get(req.getCommunityId());
			if(null != communityDistrict && null != community){
                communityDistrict.setAddress(req.getAddress());
                communityDistrict.setAreaSize(req.getAreaSize());
                communityDistrict.setHousingNumber(req.getHousingNumber());
                communityDistrict.setDistrictName(req.getDistrictName());
                communityDistrict.setPeriod(req.getPeriod());
                communityDistrict.setRatio(req.getRatio());
                communityDistrict.setBuildingNumber(null!=req.getBuildingNumber() 
                		? req.getBuildingNumber() : communityDistrict.getBuildingNumber());
                communityDistrict.setComment(req.getComment());
                communityDistrict.setDistrictOrder(null!=req.getDistrictOrder() 
                		? req.getDistrictOrder() : communityDistrict.getDistrictOrder());
                try {
    				communityDistrict.setCompletionDate(StringUtils.isNotEmpty(req.getCompletionDate()) 
    						? DateUtil.parse(req.getCompletionDate(), 1) : null);
    	            communityDistrict.setHandoverDate(StringUtils.isNotEmpty(req.getHandoverDate()) 
    	            		? DateUtil.parse(req.getHandoverDate(), 1) : null );
    			} catch (ParseException e) {
    				e.printStackTrace();
    			}
                
                communityDistrict.setBuilderList(null);
    			if (StringUtils.isNotEmpty(req.getBuilderIdList())) {
    				String[] builderIds = req.getBuilderIdList().split(",");
    				List<CommunityBuilderEntity> builderList = new ArrayList<CommunityBuilderEntity>();
    				for (String builderId : builderIds) {
    					builderList.add(communityBuilderDao.get(Integer.parseInt(builderId)));
    				}
    				communityDistrict.setBuilderList(builderList);
    			}
    			communityDistrict.setLastModifyTime(new Timestamp(new Date().getTime()));
    			communityDistrict.setState(null!=req.getState() ? req.getState() : communityDistrict.getState());
                communityDistrict.setCommunity(community);
                communityDistrict.setDistrictCode(req.getDistrictCode());
                communityDistrict.setPopulation(req.getPopulation());
                communityDistrict.setTakeTime(req.getTakeTime());
				res.setCommunityDistrictId(communityDistrict.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "删除社区")
	public IResponse deleteCommunityDistrict(CommunityDistrictReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityDistrictId()) {
		CommunityDistrictEntity communityDistrict = communityDistrictDao.get(req.getCommunityDistrictId());
			if (null != communityDistrict) {
				//communityDistrictDao.deleteById(req.getCommunityDistrictId());
				communityDistrict.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityDistrictInfo(CommunityDistrictReq req) {
		GetCommunityDistrictInfoRes res = new GetCommunityDistrictInfoRes();
		if (null != req.getCommunityDistrictId()) {
			CommunityDistrictEntity communityDistrict = communityDistrictDao.get(req.getCommunityDistrictId());
			if (null != communityDistrict) {
				CommunityDistrictForm communityDistrictForm = new CommunityDistrictForm();
				DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
				communityDistrictForm.setCommunityDistrictId(communityDistrict.getId());
                communityDistrictForm.setAddress(communityDistrict.getAddress());
                communityDistrictForm.setAreaSize(communityDistrict.getAreaSize());
                communityDistrictForm.setHousingNumber(communityDistrict.getHousingNumber());
                communityDistrictForm.setDistrictName(communityDistrict.getDistrictName());
                communityDistrictForm.setPeriod(communityDistrict.getPeriod());
                communityDistrictForm.setRatio(communityDistrict.getRatio());
                communityDistrictForm.setBuildingNumber(communityDistrict.getBuildingNumber());
                communityDistrictForm.setComment(communityDistrict.getComment());
                communityDistrictForm.setDistrictCode(communityDistrict.getDistrictCode());
                communityDistrictForm.setCompletionDate(null!=communityDistrict.getCompletionDate() ? DateUtil.format(communityDistrict.getCompletionDate(),1) : null);
                communityDistrictForm.setHandoverDate(null!=communityDistrict.getHandoverDate() ? DateUtil.format(communityDistrict.getHandoverDate(),1) : null);
                communityDistrictForm.setPopulation(communityDistrict.getPopulation());
                communityDistrictForm.setTakeTime(communityDistrict.getTakeTime());
                communityDistrictForm.setDistrictOrder(communityDistrict.getDistrictOrder());
                
                communityDistrictForm.setCreateTime(null != communityDistrict.getCreateTime() ? sdf.format(communityDistrict.getCreateTime()) : "");
                communityDistrictForm.setLastModifyTime(null != communityDistrict.getLastModifyTime() ? sdf.format(communityDistrict.getLastModifyTime()) : "");
                communityDistrictForm.setState(communityDistrict.getState());
                
                if(null != communityDistrict.getCommunity()) {
                	CommunityForm communityForm = new CommunityForm(communityDistrict.getCommunity().getId(),
                			communityDistrict.getCommunity().getCommunityName(),communityDistrict.getCommunity().getCommunityAddress(),communityDistrict.getCommunity().getAreaSize());
                	communityDistrictForm.setCommunityForm(communityForm);
                }
                communityDistrict.getBuilderList().forEach(p->{
                	CommunityBuilderForm builderForm = new CommunityBuilderForm(p.getId(),p.getBuilderName(),p.getBuilderAddress(),p.getLegalRepresentative());
                	communityDistrictForm.getBuilderFormList().add(builderForm);
                });
                
				res.setCommunityDistrictForm(communityDistrictForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}