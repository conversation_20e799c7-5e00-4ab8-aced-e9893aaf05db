package com.foshan.service.community.impl;

import org.springframework.stereotype.Service;

import com.foshan.entity.community.CommunityBankDepositRecordEntity;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityBankDepositRecordService;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.form.community.request.CommunityBankDepositRecordReq;
import com.foshan.form.community.response.communityBankDepositRecord.AddCommunityBankDepositRecordRes;
import com.foshan.form.community.response.communityBankDepositRecord.ModifyCommunityBankDepositRecordRes;
import com.foshan.form.community.response.exportExcel.ExportExcelRes;
import com.foshan.form.community.response.communityBankDepositRecord.GetCommunityBankDepositRecordInfoRes;
import com.foshan.form.community.response.communityBankDepositRecord.GetCommunityBankDepositRecordListRes;
import com.foshan.form.community.CommunityBankDepositRecordForm;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.IResponse;
import com.foshan.util.DateUtil;
import com.foshan.util.ExcelExportUtil;
import com.foshan.util.SpringHandler;
import com.foshan.dao.generic.Page;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.jdbc.Work;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

@Transactional
@Service("communityBankDepositRecordService")
public class CommunityBankDepositRecordServiceImpl extends GenericCommunityService implements ICommunityBankDepositRecordService{

	@Override
	public IResponse getCommunityBankDepositRecordList(CommunityBankDepositRecordReq req) {
		GetCommunityBankDepositRecordListRes res = new GetCommunityBankDepositRecordListRes();
		Page<CommunityBankDepositRecordEntity> page = new Page<CommunityBankDepositRecordEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityBankDepositRecordEntity a inner join a.estate b where 1=1 ");
		hql.append(null!=req.getBankDepositBatchId() ? " and a.bankDepositBatch.id="+req.getBankDepositBatchId():"")
			.append(StringUtils.isNotEmpty(req.getUnitCode()) ? " and b.unitCode like'%"+req.getUnitCode()+"%'" :"");
		hql.append(" ORDER BY b.unitCode asc");
		page = communityBankDepositRecordDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityBankDepositRecordForm communityBankDepositRecordForm = new CommunityBankDepositRecordForm();
			communityBankDepositRecordForm.setCommunityBankDepositRecordId(o.getId());
            communityBankDepositRecordForm.setCreateTime(null != o.getCreateTime()? DateUtil.formatLongFormat(o.getCreateTime()) : "");
            communityBankDepositRecordForm.setLastModifyTime(null != o.getLastModifyTime()? DateUtil.formatLongFormat(o.getLastModifyTime()) : "");
            communityBankDepositRecordForm.setState(o.getState());
            communityBankDepositRecordForm.setBankAccount(o.getBankAccount());
            communityBankDepositRecordForm.setDepositAmount(null!=o.getDepositAmount() ? o.getDepositAmount().toString() : "");
            communityBankDepositRecordForm.setDepositComment(o.getDepositComment());
            communityBankDepositRecordForm.setFeedbackComment(o.getFeedbackComment());
            communityBankDepositRecordForm.setIsDeposited(o.getIsDeposited());
            communityBankDepositRecordForm.setUnitCode(null!=o.getEstate() ?
            		o.getEstate().getUnitCode():"");
            communityBankDepositRecordForm.setAccountName(o.getEstate().getPaymentAccount().getAccountName());
            communityBankDepositRecordForm.setBankName(o.getEstate().getPaymentAccount().getBankName());
            communityBankDepositRecordForm.setOutstandingAmount(null!=o.getOutstandingAmount() ? o.getOutstandingAmount().toString() : "");
			res.getCommunityBankDepositRecordList().add(communityBankDepositRecordForm);
		});
		
       SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
        Session session = sessionFactory.openSession();
        //Transaction tx = session.beginTransaction();
        session.doWork(new Work() {
            @Override
            public void execute(Connection connection) throws SQLException {
                // TODO Auto-generated method stub
                Statement st = (Statement) connection.createStatement();
        		StringBuilder sql = new StringBuilder(" from t_community_bank_deposit_record a INNER JOIN `t_community_property` b "
        				+ "ON b.id=a.propertyId where  1=1 ");
        		sql.append(null!=req.getBankDepositBatchId() ? " and a.bankDepositBatchId="+req.getBankDepositBatchId():"")
        			.append(StringUtils.isNotEmpty(req.getUnitCode()) ? " and b.unitCode like'%"+req.getUnitCode()+"%'" :"");
                ResultSet rs = st.executeQuery("SELECT *FROM (SELECT COUNT(*) AS totalCount,SUM(depositAmount) AS totalAmount"+
        			sql.toString()+") t1 ,(SELECT SUM(depositAmount) AS depositAmount,COUNT(*) AS depositNumber "+sql.toString()+" and a.isDeposited=1 ) t2");
                while (rs.next()) {
                    BigDecimal totalAmount = null != rs.getBigDecimal("totalAmount") ? 
                    		rs.getBigDecimal("totalAmount") : new BigDecimal(0);
                    BigDecimal depositAmount = null != rs.getBigDecimal("depositAmount") ? 
                    		rs.getBigDecimal("depositAmount") : new BigDecimal(0);
                    Integer depositNumber = rs.getInt("depositNumber") ;
            		res.setDepositNumber(depositNumber);
            		res.setTotalAmount(totalAmount.toString());
            		res.setDepositAmount(depositAmount.toString());
                }

                connection.close();
                rs.close();
                st.close();
            }
        });
//	    		tx.commit();
        if (session != null) {
            session.close();
        }

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	@Audit(operate = "新增银行划账记录")
	public IResponse addCommunityBankDepositRecord(CommunityBankDepositRecordReq req) {
		AddCommunityBankDepositRecordRes res = new AddCommunityBankDepositRecordRes();
		//if () {
			CommunityBankDepositRecordEntity communityBankDepositRecord = new CommunityBankDepositRecordEntity();
			
            communityBankDepositRecord.setLastModifyTime(new Timestamp(new Date().getTime()));
            communityBankDepositRecord.setState(req.getState());
            communityBankDepositRecord.setBankAccount(req.getBankAccount());
            communityBankDepositRecord.setDepositAmount(StringUtils.isNotEmpty(req.getDepositAmount()) ?  new BigDecimal(req.getDepositAmount()) : null);
            communityBankDepositRecord.setDepositComment(req.getDepositComment());
            communityBankDepositRecord.setFeedbackComment(req.getFeedbackComment());
            communityBankDepositRecord.setIsDeposited(req.getIsDeposited());
            communityBankDepositRecord.setOutstandingAmount(StringUtils.isNotEmpty(req.getOutstandingAmount()) ?  new BigDecimal(req.getOutstandingAmount()) : null);
			communityBankDepositRecordDao.save(communityBankDepositRecord);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		} else {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//		}
		return res;
	}

	@Override
	@Audit(operate = "修改银行划账记录")
	public IResponse modifyCommunityBankDepositRecord(CommunityBankDepositRecordReq req) {
		ModifyCommunityBankDepositRecordRes res = new ModifyCommunityBankDepositRecordRes();
		if (null!=req.getCommunityBankDepositRecordId() ) {
			CommunityBankDepositRecordEntity communityBankDepositRecord = communityBankDepositRecordDao.get(req.getCommunityBankDepositRecordId()) ;
			if(null != communityBankDepositRecord){
                communityBankDepositRecord.setLastModifyTime(new Timestamp(new Date().getTime()));
                communityBankDepositRecord.setState(req.getState());
                communityBankDepositRecord.setBankAccount(req.getBankAccount());
                communityBankDepositRecord.setDepositAmount(StringUtils.isNotEmpty(req.getDepositAmount()) ?  new BigDecimal(req.getDepositAmount()) : null);
                communityBankDepositRecord.setDepositComment(req.getDepositComment());
                communityBankDepositRecord.setFeedbackComment(req.getFeedbackComment());
                communityBankDepositRecord.setIsDeposited(req.getIsDeposited());
                communityBankDepositRecord.setOutstandingAmount(StringUtils.isNotEmpty(req.getOutstandingAmount()) ?  new BigDecimal(req.getOutstandingAmount()) : null);
				res.setCommunityBankDepositRecordId(communityBankDepositRecord.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "删除银行划账记录")
	public IResponse deleteCommunityBankDepositRecord(CommunityBankDepositRecordReq req) {
		GenericResponse res = new GenericResponse();
		if (StringUtils.isNotEmpty(req.getCommunityBankDepositRecordIdList())) {
			String[] bankDepositRecordId = req.getCommunityBankDepositRecordIdList().split(",");
			for (String id : bankDepositRecordId) {
				CommunityBankDepositRecordEntity communityBankDepositRecord = communityBankDepositRecordDao.get(Integer.valueOf(id));
				if (null != communityBankDepositRecord 
						&& communityBankDepositRecord.getBankDepositBatch().getBatchState() == 0) {
					communityBankDepositRecord.getReceivablesList().forEach(p->{
						p.setLockMark(0);
						p.setBankDepositRecord(null);
					});
					communityBankDepositRecord.setReceivablesList(null);
					communityBankDepositRecord.setBankDepositBatch(null);
					communityBankDepositRecord.setEstate(null);
					communityBankDepositRecordDao.delete(communityBankDepositRecord);
					//communityBankDepositRecordDao.deleteById(req.getCommunityBankDepositRecordId());
					//communityBankDepositRecord.setState(EntityContext.RECORD_STATE_INVALID);
				} else {
	                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
			}
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityBankDepositRecordInfo(CommunityBankDepositRecordReq req) {
		GetCommunityBankDepositRecordInfoRes res = new GetCommunityBankDepositRecordInfoRes();
		if (null != req.getCommunityBankDepositRecordId()) {
			CommunityBankDepositRecordEntity communityBankDepositRecord = communityBankDepositRecordDao.get(req.getCommunityBankDepositRecordId());
			if (null != communityBankDepositRecord) {
				CommunityBankDepositRecordForm communityBankDepositRecordForm = new CommunityBankDepositRecordForm();
				communityBankDepositRecordForm.setCommunityBankDepositRecordId(communityBankDepositRecord.getId());
                communityBankDepositRecordForm.setCreateTime(null != communityBankDepositRecord.getCreateTime()? DateUtil.formatLongFormat(communityBankDepositRecord.getCreateTime()) : "");
                communityBankDepositRecordForm.setLastModifyTime(null != communityBankDepositRecord.getLastModifyTime()? DateUtil.formatLongFormat(communityBankDepositRecord.getLastModifyTime()) : "");
                communityBankDepositRecordForm.setState(communityBankDepositRecord.getState());
                communityBankDepositRecordForm.setBankAccount(communityBankDepositRecord.getBankAccount());
                communityBankDepositRecordForm.setDepositAmount(null!=communityBankDepositRecord.getDepositAmount() ? communityBankDepositRecord.getDepositAmount().toString() : "");
                communityBankDepositRecordForm.setDepositComment(communityBankDepositRecord.getDepositComment());
                communityBankDepositRecordForm.setFeedbackComment(communityBankDepositRecord.getFeedbackComment());
                communityBankDepositRecordForm.setIsDeposited(communityBankDepositRecord.getIsDeposited());
                communityBankDepositRecordForm.setOutstandingAmount(null!=communityBankDepositRecord.getOutstandingAmount() ? communityBankDepositRecord.getOutstandingAmount().toString() : "");
                communityBankDepositRecordForm.setAccountName(communityBankDepositRecord.getEstate().getPaymentAccount().getAccountName());
                communityBankDepositRecordForm.setBankName(communityBankDepositRecord.getEstate().getPaymentAccount().getBankName());
                res.setCommunityBankDepositRecordForm(communityBankDepositRecordForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	public IResponse  exportCommunityBankDepositRecordList(CommunityBankDepositRecordReq req,HttpServletResponse response) {
		ExportExcelRes res = new ExportExcelRes();
		Map<String,String> heardMap = new LinkedHashMap<String,String>();
		heardMap.put("unitCode", "单元编号");
		heardMap.put("createTime", "创建时间");
		heardMap.put("bankAccount", "银行账户");
		heardMap.put("depositAmount", "应划金额");
		heardMap.put("outstandingAmount", "应收未金额");
		heardMap.put("isDeposited", "划账标识");
		heardMap.put("depositComment", "划账备注");
		heardMap.put("feedbackComment", "回盘备注");
		List<Map<String,Object>> dataList = new ArrayList<Map<String,Object>>();
		StringBuilder hql = new StringBuilder("select distinct a from CommunityBankDepositRecordEntity a where 1=1 ");
		hql.append(null!=req.getBankDepositBatchId() ? " and a.bankDepositBatch.id="+req.getBankDepositBatchId():"");
		hql.append(" ORDER BY a.id desc");
		List<CommunityBankDepositRecordEntity> list = communityBankDepositRecordDao.getListByHql(hql.toString(),"");
		if(null != list) {
			list.forEach(o->{
				Map<String,Object> map = new HashMap<String,Object>();
				map.put("unitCode",null!=o.getReceivablesList().get(0)?
	            		o.getReceivablesList().get(0).getEstate().getUnitCode():"");
				map.put("createTime", null != o.getCreateTime()? DateUtil.formatLongFormat(o.getCreateTime()) : "");
				map.put("bankAccount", o.getBankAccount());
				map.put("depositAmount", o.getDepositAmount().toString());
				switch(o.getIsDeposited()) {
				case 0:map.put("isDeposited", "未回盘");break;
				case 1:map.put("isDeposited", "已回盘，划账成功");break;
				case 2:map.put("isDeposited", "已回盘，划账失败");break;
				default:break;
				}
				map.put("outstandingAmount", o.getOutstandingAmount().toString());
				map.put("depositComment", o.getDepositComment());
				map.put("feedbackComment", o.getFeedbackComment());
				dataList.add(map);
			});
		}
		try {
			ExcelExportUtil.exportExcelFile (heardMap,dataList,
					Short.valueOf("9"),Short.valueOf("500"),null,"","","",
					 response);
		} catch (Exception e) {
			e.printStackTrace();
            res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
            res.setRetInfo("导出异常！");
		}
		return res;
	}
}