package com.foshan.service.community.shiro.realmhandler;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.stereotype.Component;


@Component
public class RealmHandlerProcessor implements BeanFactoryPostProcessor {
	
	private static final String HANDLER_PACKAGE = "com.foshan.service.community.shiro.realmhandler.handler";
	
	/**
	 * @see RealmHandlerContext,初始化RealmHandlerContext.
	 * 扫描HANDLER_PACKAGE中定义路径下的类，并将RealmHandlerContext注册到spring。
	 * 注意：这里使用spring的ClassPathScanningCandidateComponentProvider扫描，升spring版本可能有坑。
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
		Map<String, Class> handlerMap = new HashMap<String, Class>();
		ClassPathScanningCandidateComponentProvider provider = new ClassPathScanningCandidateComponentProvider(false);
		provider.addIncludeFilter(new AnnotationTypeFilter(RealmHandlerType.class));
		Set<BeanDefinition> beanDef = provider.findCandidateComponents(HANDLER_PACKAGE);
		beanDef.forEach(o->{
			try {
				Class clazz = Class.forName(o.getBeanClassName());
				String type = ((RealmHandlerType) clazz.getAnnotation(RealmHandlerType.class)).value();
				handlerMap.put(type,clazz);
			} catch (ClassNotFoundException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			
		});
		RealmHandlerContext context = new RealmHandlerContext(handlerMap);
		beanFactory.registerSingleton("realmHandlerContext", context);
	}

}
