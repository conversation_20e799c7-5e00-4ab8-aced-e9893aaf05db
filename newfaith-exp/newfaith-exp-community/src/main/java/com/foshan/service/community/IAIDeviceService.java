package com.foshan.service.community;

import javax.servlet.http.HttpServletRequest;

import com.foshan.form.community.request.AIDeviceReq;
import com.foshan.form.response.IResponse;

public interface IAIDeviceService {
	public IResponse receiveWarningMessage(AIDeviceReq req);
	public IResponse doLogin(AIDeviceReq req);
	public IResponse getEventRecordListByPaging(AIDeviceReq req);
	public IResponse selectById(AIDeviceReq req) ;
	public IResponse selectImgByEventRecordId(AIDeviceReq req) ;
	public IResponse getBackVideo(AIDeviceReq req);
	public IResponse getRealVideo(AIDeviceReq req) ;
	public IResponse getEquipmentOrganizationListByUserId(AIDeviceReq req) ;
	public IResponse updateStatus(AIDeviceReq req);
	public IResponse getWxMiniOpenId(AIDeviceReq req);
	public IResponse gainAIDeviceParameter();
}
