package com.foshan.service.community;

import com.foshan.form.community.request.CommunityMeterFormulaReq;
import com.foshan.form.response.IResponse;

public interface ICommunityMeterFormulaService {
    public IResponse getCommunityMeterFormulaList(CommunityMeterFormulaReq req);
	public IResponse addCommunityMeterFormula(CommunityMeterFormulaReq req);
	public IResponse modifyCommunityMeterFormula(CommunityMeterFormulaReq req);
	public IResponse deleteCommunityMeterFormula(CommunityMeterFormulaReq req);
	public IResponse getCommunityMeterFormulaInfo(CommunityMeterFormulaReq req);
	public IResponse relevanceMeter(CommunityMeterFormulaReq req);
	public IResponse cancelRelevanceMeter(CommunityMeterFormulaReq req);
}

