package com.foshan.service.community.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.FilenameFilter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.jdbc.Work;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.entity.community.CommunityEventCategoryEntity;
import com.foshan.form.community.ReceivablesStatisticsForm;
import com.foshan.form.community.request.CommunityStatisticsReq;
import com.foshan.form.community.request.ReceiptStatisticsReq;
import com.foshan.form.community.request.ReceivablesChangesReq;
import com.foshan.form.community.request.RefundReq;
import com.foshan.form.community.response.communityStatistics.GetBuildingOccupancyRes;
import com.foshan.form.community.response.communityStatistics.GetCommunityBaseStatisticsRes;
import com.foshan.form.community.response.communityStatistics.GetCommunityEventStatisticsRes;
import com.foshan.form.community.response.communityStatistics.GetCommunityReceivablesRes;
import com.foshan.form.community.response.communityStatistics.GetMemberPropertyRes;
import com.foshan.form.community.response.communityStatistics.GetNoReceivablesRes;
import com.foshan.form.community.response.communityStatistics.GetReceiptDjbFileListRes;
import com.foshan.form.community.response.communityStatistics.GetReceiptRes;
import com.foshan.form.community.response.communityStatistics.GetReceivablesChangesRes;
import com.foshan.form.community.response.communityStatistics.GetReceivablesStatisticsRes;
import com.foshan.form.community.response.communityStatistics.GetRufundRes;
import com.foshan.form.community.statistics.BuildingOccupancyVo;
import com.foshan.form.community.statistics.CommunityNoReceivablesMeasuresVo;
import com.foshan.form.community.statistics.CommunityNoReceivablesSummaryVo;
import com.foshan.form.community.statistics.CommunityNoReceivablesVo;
import com.foshan.form.community.statistics.CommunityReceiptVo;
import com.foshan.form.community.statistics.CommunityReceivablesChangesVo;
import com.foshan.form.community.statistics.CommunityReceivablesVo;
import com.foshan.form.community.statistics.EverydayReceiptVo;
import com.foshan.form.community.statistics.MemberPropertyVo;
import com.foshan.form.community.statistics.ReceiptDjbVo;
import com.foshan.form.community.statistics.RefundVo;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.community.ICommunityStatisticsService;
import com.foshan.service.community.impl.statistics.BuildingOccupancyUtil;
import com.foshan.service.community.impl.statistics.CommunityNoReceivablesUtil;
import com.foshan.service.community.impl.statistics.CommunityReceivableUtil;
import com.foshan.service.community.impl.statistics.EverydayReceiptUtil;
import com.foshan.service.community.impl.statistics.MemberPropertyUtil;
import com.foshan.service.community.impl.statistics.ReceiptUtil;
import com.foshan.service.community.impl.statistics.ReceivablesChangesUtil;
import com.foshan.service.community.impl.statistics.RefundUtil;
import com.foshan.util.DateUtil;
import com.foshan.util.ExcelExportUtil;
import com.foshan.util.SpringHandler;
import com.foshan.util.community.CommunityCache;
import com.foshan.util.community.CommunityPage;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional
@Service("communityStatisticsService")
public class CommunityStatisticsServiceImpl extends GenericCommunityService implements ICommunityStatisticsService {

	@SuppressWarnings("rawtypes")
	@Override
	public IResponse getBaseStatistics() {
		GetCommunityBaseStatisticsRes res = new GetCommunityBaseStatisticsRes();
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		Date date = new Date(System.currentTimeMillis());
		String today = formatter.format(date);

		Integer accountNumber = accountDao.countByHql("select count(a) from CommunityMemberEntity a");
		Integer accountDayNumber = accountDao.countByHql(
				"select count(a) from CommunityMemberEntity a where a.registTime <= '" + today + " 00:00:00'");
		Integer owner = accountDao.countByHql("select count(a) from CommunityMemberEntity a where a.memberType = 0");
		Integer tenant = accountDao
				.countByHql("select count(a) from CommunityMemberPropertyEntity a where a.memberType = 2");
		Integer dependents = accountDao.countByHql("select count(a) from CommunityMemberEntity a   where a.id "
				+ "IN(SELECT DISTINCT b.property.id FROM CommunityMemberPropertyEntity b WHERE b.memberType=1)");

		res.setMember(accountNumber);
		res.setMemberToday(accountNumber - accountDayNumber);
		res.setHousehold(accountNumber);
		res.setOwner(owner);
		res.setTenant(tenant);
		res.setDependents(dependents);

		List<Map<String, Object>> list = communityBuildingDao.queryListBysql(
				"SELECT p.buildingType, COUNT( * ) as count FROM `t_community_building` p GROUP BY p.buildingType");

		for (Map map : list) {
			Integer count = Integer.parseInt(map.get("count").toString());
			switch ((Integer) map.get("buildingType")) {
			case 0:
				res.setHousing(count);
				break;
			case 1:
				res.setVilla(count);
				break;
			case 2:
				res.setShops(count);
				break;
			case 3:
				res.setParking(count);
				break;
			case 4:
				res.setPublicAreas(count);
				break;
			}
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		return res;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public IResponse getEventStatistics() {
		GetCommunityEventStatisticsRes res = new GetCommunityEventStatisticsRes();

		Map<String, Integer> eventDistribution = new HashMap<>();
		List<Map<String, Object>> distributionList = communityBuildingDao.queryListBysql(
				"SELECT p.`eventCategoryId`,COUNT( * ) AS count FROM `t_community_events` p GROUP BY p.`eventCategoryId`");

		for (Map distributionMap : distributionList) {
			CommunityEventCategoryEntity eventCategory = communityEventCategoryDao
					.get((Integer) distributionMap.get("eventCategoryId"));
			if (null != eventCategory.getParentEventCategory()) {
				eventDistribution.put(eventCategory.getParentEventCategory().getCategoryName(),
						null != eventDistribution.get(eventCategory.getParentEventCategory().getCategoryName())
								? eventDistribution.get(eventCategory.getParentEventCategory().getCategoryName())
										+ Integer.parseInt(distributionMap.get("count").toString())
								: Integer.parseInt(distributionMap.get("count").toString()));
			} else {
				eventDistribution.put(eventCategory.getCategoryName(),
						Integer.parseInt(distributionMap.get("count").toString()));
			}
		}
		res.setEventDistribution(eventDistribution);

		List<Map<String, Object>> eventStatesList = communityBuildingDao.queryListBysql(
				" SELECT p.eventCategoryId, p.`eventState`,COUNT( * ) as count FROM `t_community_events` p GROUP BY p.`eventCategoryId`,p.`eventState`");

		for (Map map : eventStatesList) {
			CommunityEventCategoryEntity eventCategory = communityEventCategoryDao
					.get((Integer) map.get("eventCategoryId"));
			if (null != eventCategory.getParentEventCategory()) {
				map.put("eventCategoryId", eventCategory.getParentEventCategory().getId());
			}

		}

		for (int i = 0; i < eventStatesList.size(); i++) {
			for (int j = 0; j < eventStatesList.size(); j++) {
				if (i != j
						&& eventStatesList.get(i).get("eventCategoryId") == eventStatesList.get(j)
								.get("eventCategoryId")
						&& eventStatesList.get(i).get("eventState") == eventStatesList.get(j).get("eventState")) {
					eventStatesList.get(i).put("count", Integer.parseInt(eventStatesList.get(i).get("count").toString())
							+ Integer.parseInt(eventStatesList.get(j).get("count").toString()));
					eventStatesList.remove(j);
					j = j - 1;

				}
			}
		}

		Map<String, List<Map<String, Integer>>> outerMap = new HashMap();

		for (Map<String, Object> stringObjectMap : eventStatesList) {
			HashMap innerMap = new HashMap();
			Map map = stringObjectMap;
			CommunityEventCategoryEntity eventCategory = communityEventCategoryDao
					.get((Integer) map.remove("eventCategoryId"));
			stringObjectMap.put("categoryName", eventCategory.getCategoryName());
			innerMap.put("eventState", Integer.parseInt(map.get("eventState").toString()));
			innerMap.put("count", Integer.parseInt(map.get("count").toString()));
			if (null != outerMap.get(eventCategory.getCategoryName())) {
				outerMap.get(eventCategory.getCategoryName()).add(innerMap);
			} else {
				List<Map<String, Integer>> list = new ArrayList<>();
				list.add(innerMap);
				outerMap.put(eventCategory.getCategoryName(), list);
			}
		}
		res.setEventStates(outerMap);

		List<Map<String, Map<String, Integer>>> monthlyEventStatistics = new ArrayList<>();
		for (int j = 0; j < 6; j++) {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
			SimpleDateFormat monthFormat = new SimpleDateFormat("M");
			Calendar lastMonthFirstDateCal = Calendar.getInstance();
			lastMonthFirstDateCal.add(Calendar.MONTH, -j);
			lastMonthFirstDateCal.set(Calendar.DAY_OF_MONTH, 1);
			String monthFirstDay = format.format(lastMonthFirstDateCal.getTime()) + " 00:00:00";

			Calendar lastMonthEndDateCal = Calendar.getInstance();
			lastMonthEndDateCal.add(Calendar.MONTH, -j);
			lastMonthEndDateCal.set(Calendar.DAY_OF_MONTH, lastMonthEndDateCal.getActualMaximum(Calendar.DAY_OF_MONTH));
			String monthEndDay = format.format(lastMonthEndDateCal.getTime()) + " 23:59:59";

			List<Map<String, Object>> monthlyEventStatisticsList = communityBuildingDao.queryListBysql(
					"SELECT p.eventCategoryId,COUNT( * ) AS count FROM `t_community_events` p WHERE createTime BETWEEN '"
							+ monthFirstDay + "' AND '" + monthEndDay + "' GROUP BY p.`eventCategoryId`");

			Map<String, Map<String, Integer>> innerMap = new HashMap<>();
			Map<String, Integer> innerEventDistribution = new HashMap<>();
			for (Map distributionMap : monthlyEventStatisticsList) {
				CommunityEventCategoryEntity eventCategory = communityEventCategoryDao
						.get((Integer) distributionMap.get("eventCategoryId"));
				if (null != eventCategory.getParentEventCategory()) {
					innerEventDistribution.put(eventCategory.getParentEventCategory().getCategoryName(),
							null != innerEventDistribution.get(eventCategory.getParentEventCategory().getCategoryName())
									? innerEventDistribution
											.get(eventCategory.getParentEventCategory().getCategoryName())
											+ Integer.parseInt(distributionMap.get("count").toString())
									: Integer.parseInt(distributionMap.get("count").toString()));
				} else {
					innerEventDistribution.put(eventCategory.getCategoryName(),
							Integer.parseInt(distributionMap.get("count").toString()));
				}
			}
			innerMap.put(monthFormat.format(lastMonthFirstDateCal.getTime()) + "月", innerEventDistribution);
			monthlyEventStatistics.add(innerMap);
		}
		res.setMonthlyEventStatistics(monthlyEventStatistics);

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public IResponse getReceivablesStatistics(CommunityStatisticsReq req) {
		GetReceivablesStatisticsRes res = new GetReceivablesStatisticsRes();
		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();
		// Transaction tx = session.beginTransaction();
		session.doWork(new Work() {
			@Override
			public void execute(Connection connection) throws SQLException {
				Statement st = (Statement) connection.createStatement();
				String month = DateUtil.formatShortFormat(new Date());
				month = StringUtils.isNotEmpty(req.getMonth()) ? req.getMonth()
						: month.substring(0, month.length() - 3);
				String sql = "SELECT d.id,d.districtName,SUM(a.receivableAmount) AS receivableAmount,SUM(a.receivedAmount) AS receivedAmount "
						+ "FROM `t_community_receivables` a INNER JOIN `t_community_property` b ON b.id=a.estateId "
						+ "INNER JOIN t_community_building c ON c.id=b.buildingId INNER JOIN t_community_district d ON "
						+ "d.id=c.districtId WHERE a.receivableDate LIKE'" + month + "%' GROUP BY d.id";
				ResultSet rs = st.executeQuery(sql);
				BigDecimal totalReceivableAmount = new BigDecimal(0);
				BigDecimal totalReceivedAmount = new BigDecimal(0);
				while (rs.next()) {
					BigDecimal receivableAmount = null != rs.getBigDecimal("receivableAmount")
							? rs.getBigDecimal("receivableAmount")
							: new BigDecimal(0);
					BigDecimal receivedAmount = null != rs.getBigDecimal("receivedAmount")
							? rs.getBigDecimal("receivedAmount")
							: new BigDecimal(0);
					totalReceivableAmount = totalReceivableAmount.add(receivableAmount);
					totalReceivedAmount = totalReceivedAmount.add(receivedAmount);
					ReceivablesStatisticsForm receivablesStatistics = new ReceivablesStatisticsForm();
					receivablesStatistics.setDistrictName(rs.getString("districtName"));
					receivablesStatistics.setReceivableAmount(receivableAmount.toString());
					receivablesStatistics.setReceivedAmount(receivedAmount.toString());
					String collectionRate = ((receivedAmount.divide(receivableAmount, 4, RoundingMode.HALF_UP))
							.multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP)).toString() + "%";
					receivablesStatistics.setCollectionRate(collectionRate);
					res.getReceivablesStatisticsFormList().add(receivablesStatistics);
				}
				res.setTotalReceivableAmount(totalReceivableAmount.toString());
				res.setTotalReceivedAmount(totalReceivedAmount.toString());
				connection.close();
				rs.close();
				st.close();
			}
		});
//	    		tx.commit();
		if (session != null) {
			session.close();
		}
		return res;
	}

	// 查询应收报表
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public IResponse getCommunityReceivables(CommunityStatisticsReq req) {
		GetCommunityReceivablesRes<CommunityReceivablesVo> res = new GetCommunityReceivablesRes<>();
		if (StringUtils.isNotEmpty(req.getStartDate()) && StringUtils.isNotEmpty(req.getEndDate())
				&& (req.getReceivableAmountFlag().equals(1) || req.getReceivedAmountFlag().equals(1)
						|| req.getArrearsFlag().equals(1))) {
			String[] title = null;
			List<CommunityReceivablesVo> voList = new LinkedList<>();

			String key = req.hashCode() + "_" + req.getReceivableAmountFlag() + "_" + req.getReceivedAmountFlag() + "_"
					+ req.getArrearsFlag();

			CommunityPage<List<CommunityReceivablesVo>> page = new CommunityPage<>();

			if (CommunityCache.statisticsCache.containsKey("应收报表")
					&& CommunityCache.statisticsCache.get("应收报表").containsKey(key)
					&& CommunityCache.statisticsCache.get("应收报表").get(key).containsKey("data")
					&& CommunityCache.statisticsCache.get("应收报表").get(key).containsKey("title")
					&& req.getRefreshFlag().equals(0)) {
				title = (String[]) CommunityCache.statisticsCache.get("应收报表").get(key).get("title");
				voList = (List<CommunityReceivablesVo>) CommunityCache.statisticsCache.get("应收报表").get(key).get("data");
			} else {
				CommunityCache.statisticsCache.remove("应收报表");
				Map<String, Object> result = CommunityReceivableUtil.getCommunityReceivableList(req.getDistrictIds(),req.getBuildingIds(),
						req.getChargeCategorys(), req.getPayItemsNames(), req.getStartDate(), req.getEndDate(),
						req.getReceivableAmountFlag(), req.getReceivedAmountFlag(), req.getArrearsFlag(),
						req.getOnlyArrears());

				title = (String[]) result.get("title");
				voList = (List<CommunityReceivablesVo>) result.get("data");
				// 写缓存
				if (null != voList && voList.size() > 0 && null != title && title.length > 1) {
					Map<String, Map<String, Object>> temp = new HashMap<>();
					temp.put(key, result);
					CommunityCache.statisticsCache.put("应收报表", temp);
				}
			}

			page = CommunityCache.getPage(voList, req.getPageSize(), req.getRequestPage());

			res.setCurrentPage(page.getCurrentPage());
			res.setPageSize(page.getPageSize());
			res.setTotal(page.getTotalPage());
			res.setTotalResult(page.getTotalCount());
			res.setDataList(new LinkedList(page.getResultList()));
			if (res.getDataList().size() > 0) {
				res.setTitles(title);
			}
			res.setTotalResult(voList.size());
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	// 查询应收未收报表
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public IResponse getNoReceivables(CommunityStatisticsReq req) {
		GetNoReceivablesRes<CommunityNoReceivablesVo> res = new GetNoReceivablesRes<>();
		if (StringUtils.isNotEmpty(req.getEndDate())) {
			String[] title = null;
			List<CommunityNoReceivablesVo> voList = new LinkedList<>();

			String key = req.hashCode() + "_" + req.getEndDate();

			CommunityPage<List<CommunityNoReceivablesVo>> page = new CommunityPage<>();

			if (CommunityCache.statisticsCache.containsKey("应收未收报表")
					&& CommunityCache.statisticsCache.get("应收未收报表").containsKey(key)
					&& CommunityCache.statisticsCache.get("应收未收报表").get(key).containsKey("data")
					&& CommunityCache.statisticsCache.get("应收未收报表").get(key).containsKey("title")
					&& req.getRefreshFlag().equals(0)) {
				title = (String[]) CommunityCache.statisticsCache.get("应收未收报表").get(key).get("title");
				voList = (List<CommunityNoReceivablesVo>) CommunityCache.statisticsCache.get("应收未收报表").get(key)
						.get("data");
			} else {
				CommunityCache.statisticsCache.remove("应收未收报表");
				Map<String, Object> result = CommunityNoReceivablesUtil.getNoReceivableList(req.getDistrictIds(),req.getBuildingIds(),
						req.getChargeCategorys(), req.getPayItemsNames(), req.getEndDate());

				title = (String[]) result.get("title");
				voList = (List<CommunityNoReceivablesVo>) result.get("data");
				// 写缓存
				if (null != voList && voList.size() > 0 && null != title && title.length > 1) {
					Map<String, Map<String, Object>> temp = new HashMap<>();
					temp.put(key, result);
					CommunityCache.statisticsCache.put("应收未收报表", temp);
				}
			}

			page = CommunityCache.getPage(voList, req.getPageSize(), req.getRequestPage());

			res.setCurrentPage(page.getCurrentPage());
			res.setPageSize(page.getPageSize());
			res.setTotal(page.getTotalPage());
			res.setTotalResult(page.getTotalCount());
			res.setDataList(new LinkedList(page.getResultList()));
			if (res.getDataList().size() > 0) {
				res.setTitles(title);
			}
			res.setTotalResult(voList.size());
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	// 查询业主信息报表
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public IResponse getMemberProperty(CommunityStatisticsReq req) {
		GetMemberPropertyRes<MemberPropertyVo> res = new GetMemberPropertyRes<>();

		List<MemberPropertyVo> voList = new LinkedList<>();

		String key = (null != req.getDistrictIds() && req.getDistrictIds().length > 0 ? req.getDistrictIds().hashCode()
				: "全区")
				+ "_"
				+ (null != req.getBuildingIds() && req.getBuildingIds().length > 0 ? req.getBuildingIds().hashCode()
						: "楼阁")
				+ "_"
				+ (null != req.getEstateStates() && req.getEstateStates().length > 0 ? req.getEstateStates().hashCode()
						: "房屋状态");

		CommunityPage<List<MemberPropertyVo>> page = new CommunityPage<>();

		if (CommunityCache.cache.containsKey("业主信息报表") && CommunityCache.cache.get("业主信息报表").containsKey(key)
				&& req.getRefreshFlag().equals(0)) {
			voList = (List<MemberPropertyVo>) CommunityCache.cache.get("业主信息报表").get(key);
			page = CommunityCache.getPage(voList, req.getPageSize(), req.getRequestPage());
		} else {
			CommunityCache.cache.remove("业主信息报表");
			String sql = MemberPropertyUtil.getMemberPropertySql(req.getDistrictIds(), req.getBuildingIds(),
					req.getEstateStates(),req.getEstateTypeList());
			voList = getMemberPropertyList(sql);
			page = CommunityCache.getPage(voList, req.getPageSize(), req.getRequestPage());

			// 写缓存
			if (voList.size() > 0) {
				Map<String, Collection> temp = new HashMap<>();
				temp.put(key, voList);
				CommunityCache.cache.put("业主信息报表", temp);
			}
		}

		res.setCurrentPage(page.getCurrentPage());
		res.setPageSize(page.getPageSize());
		res.setTotal(page.getTotalPage());
		res.setTotalResult(page.getTotalCount());
		res.setDataList(new LinkedList(page.getResultList()));
		res.setTotalResult(voList.size());
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		return res;
	}

	// 查询应收减免列表
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public IResponse getCommunityReceivablesChanges(ReceivablesChangesReq req) {
		GetReceivablesChangesRes<CommunityReceivablesChangesVo> res = new GetReceivablesChangesRes<>();
		if (StringUtils.isNotEmpty(req.getReceivablesStartDate()) && StringUtils.isNotEmpty(req.getReceivablesEndDate())
				&& StringUtils.isNotEmpty(req.getChangeStartDate()) && StringUtils.isNotEmpty(req.getChangeEndDate())) {
			List<CommunityReceivablesChangesVo> voList = new LinkedList<>();

			String key = req.getReceivablesStartDate() + "_" + req.getReceivablesEndDate() + "_"
					+ req.getChangeStartDate() + "_" + req.getChangeEndDate() + "_"
					+ (null != req.getDistrictIds() && req.getDistrictIds().length > 0
							? StringUtils.join(req.getDistrictIds(), "_")
							: "全区");

			CommunityPage<List<CommunityReceivablesChangesVo>> page = new CommunityPage<>();

			if (CommunityCache.cache.containsKey("减免透视") && CommunityCache.cache.get("减免透视").containsKey(key)
					&& req.getRefreshFlag().equals(0)) {
				voList = (List<CommunityReceivablesChangesVo>) CommunityCache.cache.get("减免透视").get(key);
				page = CommunityCache.getPage(voList, req.getPageSize(), req.getRequestPage());
			} else {
				CommunityCache.cache.remove("减免透视");
				String sql = ReceivablesChangesUtil.getReceivablesChangesSql(req.getDistrictIds(),
						req.getReceivablesStartDate(), req.getReceivablesEndDate(), req.getChangeStartDate(),
						req.getChangeEndDate());

				voList = getReceivalbesChangesList(sql);
				page = CommunityCache.getPage(voList, req.getPageSize(), req.getRequestPage());

				// 写缓存
				if (voList.size() > 0) {
					Map<String, Collection> temp = new HashMap<>();
					temp.put(key, voList);
					CommunityCache.cache.put("减免透视", temp);
				}
			}

			res.setCurrentPage(page.getCurrentPage());
			res.setPageSize(page.getPageSize());
			res.setTotal(page.getTotalPage());
			res.setTotalResult(page.getTotalCount());
			res.setDataList(new LinkedList(page.getResultList()));
			res.setTotalResult(voList.size());
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	// 查询收款透视列表
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public IResponse getReceipt(ReceiptStatisticsReq req) {
		GetReceiptRes<CommunityReceiptVo> res = new GetReceiptRes<>();
		if (StringUtils.isNotEmpty(req.getReceiptStartDate()) && StringUtils.isNotEmpty(req.getReceivablesEndDate())
				&& StringUtils.isNotEmpty(req.getReceiptStartDate())
				&& StringUtils.isNotEmpty(req.getReceiptEndDate())) {
			String[] title = null;
			List<CommunityReceiptVo> voList = new LinkedList<>();

			String key = req.hashCode() + "";

			CommunityPage<List<CommunityReceiptVo>> page = new CommunityPage<>();

			if (CommunityCache.statisticsCache.containsKey("收款透视")
					&& CommunityCache.statisticsCache.get("收款透视").containsKey(key)
					&& CommunityCache.statisticsCache.get("收款透视").get(key).containsKey("data")
					&& CommunityCache.statisticsCache.get("收款透视").get(key).containsKey("title")
					&& req.getRefreshFlag().equals(0)) {
				title = (String[]) CommunityCache.statisticsCache.get("收款透视").get(key).get("title");
				voList = (List<CommunityReceiptVo>) CommunityCache.statisticsCache.get("收款透视").get(key).get("data");
			} else {
				CommunityCache.statisticsCache.remove("收款透视");
				Map<String, Object> result = ReceiptUtil.getReceiptList(req.getDistrictIds(),req.getBuildingIds(), req.getChargeCategorys(),
						req.getPayItemsNames(), req.getReceivablesStartDate(), req.getReceivablesEndDate(),
						req.getReceiptStartDate(), req.getReceiptEndDate());

				title = (String[]) result.get("title");
				voList = (List<CommunityReceiptVo>) result.get("data");
				// 写缓存
				if (null != voList && voList.size() > 0 && null != title && title.length > 1) {
					Map<String, Map<String, Object>> temp = new HashMap<>();
					temp.put(key, result);
					CommunityCache.statisticsCache.put("收款透视", temp);
				}
			}

			page = CommunityCache.getPage(voList, req.getPageSize(), req.getRequestPage());

			res.setCurrentPage(page.getCurrentPage());
			res.setPageSize(page.getPageSize());
			res.setTotal(page.getTotalPage());
			res.setTotalResult(page.getTotalCount());
			res.setDataList(new LinkedList(page.getResultList()));
			if (res.getDataList().size() > 0) {
				res.setTitles(title);
			}
			res.setTotalResult(voList.size());
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	// 查询退款数据列表
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public IResponse getRefund(RefundReq req) {
		GetRufundRes<RefundVo> res = new GetRufundRes<>();
		if (StringUtils.isNotEmpty(req.getStartDate()) && StringUtils.isNotEmpty(req.getEndDate())
				&& null != req.getQueryFlag()
				&& (null != req.getRefundFlag() || null != req.getArrearsFlag() || null != req.getReceivableFlag())
				&& (req.getRefundFlag().equals(1) || req.getArrearsFlag().equals(1)
						|| req.getReceivableFlag().equals(1))) {
			String[] title = null;
			List<RefundVo> voList = new LinkedList<>();

			String key = req.hashCode() + "_" + req.getRefundFlag() + "_" + req.getArrearsFlag() + "_"
					+ req.getReceivableFlag() + "_" + req.getQueryFlag();

			CommunityPage<List<RefundVo>> page = new CommunityPage<>();

			if (CommunityCache.statisticsCache.containsKey("退款报表")
					&& CommunityCache.statisticsCache.get("退款报表").containsKey(key)
					&& CommunityCache.statisticsCache.get("退款报表").get(key).containsKey("data")
					&& CommunityCache.statisticsCache.get("退款报表").get(key).containsKey("title")
					&& req.getRefreshFlag().equals(0)) {
				title = (String[]) CommunityCache.statisticsCache.get("退款报表").get(key).get("title");
				voList = (List<RefundVo>) CommunityCache.statisticsCache.get("退款报表").get(key).get("data");
			} else {
				CommunityCache.statisticsCache.remove("退款报表");
				Map<String, Object> result = RefundUtil.getRefundList(req.getDistrictIds(), req.getChargeCategorys(),
						req.getPayItemsNames(), req.getStartDate(), req.getEndDate(), req.getRefundFlag(),
						req.getArrearsFlag(), req.getReceivableFlag(), req.getQueryFlag());

				title = (String[]) result.get("title");
				voList = (List<RefundVo>) result.get("data");
				// 写缓存
				if (null != voList && voList.size() > 0 && null != title && title.length > 1) {
					Map<String, Map<String, Object>> temp = new HashMap<>();
					temp.put(key, result);
					CommunityCache.statisticsCache.put("退款报表", temp);
				}
			}

			page = CommunityCache.getPage(voList, req.getPageSize(), req.getRequestPage());

			res.setCurrentPage(page.getCurrentPage());
			res.setPageSize(page.getPageSize());
			res.setTotal(page.getTotalPage());
			res.setTotalResult(page.getTotalCount());
			res.setDataList(new LinkedList(page.getResultList()));
			if (res.getDataList().size() > 0) {
				res.setTitles(title);
			}
			res.setTotalResult(voList.size());
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	// 查询入住率报表
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public IResponse getBuildingOccupancy(CommunityStatisticsReq req) {
		GetBuildingOccupancyRes<BuildingOccupancyVo> res = new GetBuildingOccupancyRes<>();
		req.setRefreshFlag(1);
		String[] title = null;
		List<BuildingOccupancyVo> voList = new LinkedList<>();

		String key = req.hashCode() + "";

		CommunityPage<List<BuildingOccupancyVo>> page = new CommunityPage<>();

		if (CommunityCache.statisticsCache.containsKey("入住率报表")
				&& CommunityCache.statisticsCache.get("入住率报表").containsKey(key)
				&& CommunityCache.statisticsCache.get("入住率报表").get(key).containsKey("data")
				&& CommunityCache.statisticsCache.get("入住率报表").get(key).containsKey("title")
				&& req.getRefreshFlag().equals(0)) {
			title = (String[]) CommunityCache.statisticsCache.get("入住率报表").get(key).get("title");
			voList = (List<BuildingOccupancyVo>) CommunityCache.statisticsCache.get("入住率报表").get(key).get("data");
		} else {
			CommunityCache.statisticsCache.remove("入住率报表");
			Map<String, Object> result = BuildingOccupancyUtil.getBuildingOccupancyList(req.getDistrictIds(),
					req.getChargeCategorys());

			title = (String[]) result.get("title");
			voList = (List<BuildingOccupancyVo>) result.get("data");
			// 写缓存
			if (null != voList && voList.size() > 0 && null != title && title.length > 1) {
				Map<String, Map<String, Object>> temp = new HashMap<>();
				temp.put(key, result);
				CommunityCache.statisticsCache.put("入住率报表", temp);
			}
		}

		page = CommunityCache.getPage(voList, req.getPageSize(), req.getRequestPage());

		res.setCurrentPage(page.getCurrentPage());
		res.setPageSize(page.getPageSize());
		res.setTotal(page.getTotalPage());
		res.setTotalResult(page.getTotalCount());
		res.setDataList(new LinkedList(page.getResultList()));
		if (res.getDataList().size() > 0) {
			res.setTitles(title);
		}
		res.setTotalResult(voList.size());
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		return res;
	}

	@Override
	public IResponse getReceiptDjbFileList() {
		GetReceiptDjbFileListRes res = new GetReceiptDjbFileListRes();

		File path = new File("/data/resource/communityFile/djb");

		if (!path.exists()) {
			path.mkdirs();
		}

		File[] files = path.listFiles(new FilenameFilter() {
			public boolean accept(File dir, String name) {
				return name.endsWith(".xlsx");
			}
		});

		for (File file : files) {
			res.getFileList().add(file.getName());
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	// 导出应收报表
	public void exportCommunityReceivablesExcel(HttpServletResponse response) {
		if (CommunityCache.statisticsCache.containsKey("应收报表")) {
			CommunityReceivableUtil.transReceivablesExcel(response);
		}
	}

	// 导出应收未收报表
	@Override
	public void exportNoReceivablesExcel(HttpServletResponse response) {
		if (CommunityCache.statisticsCache.containsKey("应收未收报表")) {
			CommunityNoReceivablesUtil.transNoReceivablesExcel(response);
		}
	}

	// 导出应收未收措施表
	@Override
	public void exportNoReceivablesMeasuresExcel(CommunityStatisticsReq req, HttpServletResponse response) {


		if (StringUtils.isNotEmpty(req.getEndDate())) {
			String[] titles = { "序号", "单元编号", "姓名", "时段", "合计", "欠违约金金额", "原因", "措施", "进展", "继续电话催费", "减免违约金" };

			List<CommunityNoReceivablesMeasuresVo> voList = getCommunityNoReceivableMeasuresList(
					CommunityNoReceivablesUtil.getNoReceivablesMeasuresSql(req.getDistrictIds(), req.getEndDate()));
			if (voList.size() > 0) {
				CommunityNoReceivablesUtil.transNoReceivablesMeasuresExcel(titles, voList, req.getEndDate(), response);
			}
		}
	}

	// 导出应收未收分析表
	@Override
	public void exportNoReceivablesSummaryExcel(CommunityStatisticsReq req, HttpServletResponse response) {
		
		if (StringUtils.isNotEmpty(req.getEndDate())) {
			
			String[] titles1 = { "楼盘", "管理费欠费", "", "公摊费欠费", "", "代收水电欠费", "", "违约金欠费", "", "车位费欠费", "", "欠费3个月以内", "",
					"欠费3个月以上", "" };
			
			String[] titles2 = { "楼盘", "户数", "金额", "户数", "金额", "户数", "金额", "户数", "金额", "户数", "金额", "户数", "金额", "户数", "金额" };

			List<CommunityNoReceivablesSummaryVo> voList = getCommunityNoReceivablesSummaryList(
					CommunityNoReceivablesUtil.getNoReceivablesSummarySql(req.getEndDate()));
			if (voList.size() > 0) {
				CommunityNoReceivablesUtil.transNoReceivablesSummaryExcel(titles1, titles2, voList, req.getEndDate(),
						response);
			}
		}
	}

	// 导出业主信息报表
	@Override
	public void exportMemberPropertyExcel(HttpServletResponse response) {
		String[] titles = { "楼盘名称", "楼阁名称", "单元编号", "房间号", "建筑面积", "使用面积", "收费面积", "附加面积", "单元类型", "产权车位面积", "产权车位个数",
				"人防车位面积", "人防车位个数", "单元状态", "业主姓名","收楼日期", "入住日期", "计费日期", "离退日期", "业主类型", "生日", "性别", "联系电话", "证件类型", "证件号码",
				"紧急联系人", "紧急联系人联系方式", "购方名称", "购方地址", "购方银行账号", "购方邮箱", "划账银行名称", "划账银行账号","水表底数","电表底数", "账户名", "联系地址", "单元备注",
				"业主备注" };

		MemberPropertyUtil.transMemberPropertyExcel(titles, response);
	}

	// 导出应收减免报表
	@Override
	public void exportCommunityReceivablesChangesExcel(HttpServletResponse response) {

		String[] titles = { "楼盘名称", "楼阁", "单元编号", "收费项目", "应收金额", "应收年", "应收月", "应收日期", "减免日期", "减免金额", "备注", "经办人" };

		ReceivablesChangesUtil.transReceivableChangeExcel(titles, response);
	}

	// 导出收款透视报表
	@Override
	public void exportReceiptExcel(HttpServletResponse response) {
		if (CommunityCache.statisticsCache.containsKey("收款透视")) {
			ReceiptUtil.transReceiptExcel(response);
		}
	}

	// 导出退款报表
	@Override
	public void exportRefundExcel(HttpServletResponse response) {
		if (CommunityCache.statisticsCache.containsKey("退款报表")) {
			RefundUtil.transRefundExcel(response);
		}
	}

	// 导出入住率报表
	@Override
	public void exportBuildingOccupancyExcel(HttpServletResponse response) {
		if (CommunityCache.statisticsCache.containsKey("入住率报表")) {
			BuildingOccupancyUtil.transBuildingOccupancyExcel(response);
		}
	}

	// 导出每日收款报表
	@Override
	public void exportEverydayReceiptExcel(CommunityStatisticsReq req, HttpServletResponse response) {
		if (StringUtils.isNotEmpty(req.getStartDate())) {
			List<EverydayReceiptVo> voList = getEverydayReceiptList(
					EverydayReceiptUtil.getEverdayReceiptSql(req.getStartDate()));
			if (voList.size() > 0) {
				EverydayReceiptUtil.transEverydayReceiptExcel(voList, req.getStartDate(), response);
			}
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public void exportReceiptDjbExcel(CommunityStatisticsReq req, HttpServletResponse response) {
		if (StringUtils.isNotEmpty(req.getStartDate())) {
			LocalDate startDate = LocalDate.parse(req.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));

			// 根据请求的时间生成要新增的表格sheet名，以月份命名
			String newSheet = startDate.format(DateTimeFormatter.ofPattern("yyyy年MM月"));

			// 收据登记表以年为单位，一年一个登记表文件
//			File file = new File("/data/resource/communityFile/djb/" + newSheet.substring(0, newSheet.indexOf("年") + 1)
//					+ "收据登记表.xlsx");
			File file = new File("/data/resource/communityFile/djb/" + newSheet+ "收据登记表.xlsx");
			IOUtils.setByteArrayMaxOverride(1000000000);

			/*
			 * 判断服务器上是否存在当年的登记表文件，如果存在当年文件还需要判断登记表sheet名称是否为连续的月份，
			 * 例如服务器上登记表文件sheet最后为2023年08月，则本次newSheet的名字必须为2023年09月，如果此条件不
			 * 能满足，则直接返回服务器最近月份的收据登记表文件
			 */

			try {
				if (!file.exists()) {
					List<ReceiptDjbVo> voList = new LinkedList<>();

					List<Object[]> result = communityReceiptDao.createSQLQuery(
							ReceiptUtil.getReceiptDjbSql(startDate.with(TemporalAdjusters.firstDayOfMonth()).toString(),
									startDate.with(TemporalAdjusters.lastDayOfMonth()).toString()))
							.list();

					result.forEach(o -> {
						voList.add(ReceiptDjbVo.getReceiptDjbVo(o));
					});

					if (voList.size() > 0) {
						ReceiptUtil.transReceiptDjbExcel(newSheet, voList, response);
					}
				} else {
					XSSFWorkbook wb = new XSSFWorkbook(new FileInputStream(file));
					if (wb.getSheetIndex(newSheet) < 0 && LocalDate
							.parse(newSheet + "01日", DateTimeFormatter.ofPattern("yyyy年MM月dd日")).minusMonths(1)
							.isEqual(LocalDate.parse(wb.getSheetAt(wb.getNumberOfSheets() - 1).getSheetName() + "01日",
									DateTimeFormatter.ofPattern("yyyy年MM月dd日")))) {
						List<ReceiptDjbVo> voList = new LinkedList<>();

						List<Object[]> result = communityReceiptDao.createSQLQuery(ReceiptUtil.getReceiptDjbSql(
								startDate.with(TemporalAdjusters.firstDayOfMonth()).toString(),
								startDate.with(TemporalAdjusters.lastDayOfMonth()).toString())).list();

						result.forEach(o -> {
							voList.add(ReceiptDjbVo.getReceiptDjbVo(o));
						});

						if (voList.size() > 0) {
							ReceiptUtil.transReceiptDjbExcel(newSheet, voList, response);
						}
					} else {
						ExcelExportUtil.export(response, wb, file.getName());
					}

				}

			} catch (Exception ex) {
				ex.printStackTrace();
				log.error(ex.getMessage());
			}
		}

	}

	@SuppressWarnings({ "unchecked" })
	private List<CommunityNoReceivablesMeasuresVo> getCommunityNoReceivableMeasuresList(String sql) {
		List<CommunityNoReceivablesMeasuresVo> res = new LinkedList<>();
		List<Object[]> result = communityReceivablesDao.createSQLQuery(sql).list();
		result.forEach(o -> {
			res.add(CommunityNoReceivablesMeasuresVo.getCommunityNoReceivablesMeasuresVo(o));
		});
		return res;
	}

	@SuppressWarnings({ "unchecked" })
	private List<CommunityNoReceivablesSummaryVo> getCommunityNoReceivablesSummaryList(String sql) {
		List<CommunityNoReceivablesSummaryVo> res = new LinkedList<>();
		List<Object[]> result = communityReceivablesDao.createSQLQuery(sql).list();
		result.forEach(o -> {
			res.add(CommunityNoReceivablesSummaryVo.getCommunityNoReceivablesSummaryVo(o));
		});
		return res;
	}

	@SuppressWarnings({ "unchecked" })
	private List<MemberPropertyVo> getMemberPropertyList(String sql) {
		List<MemberPropertyVo> res = new LinkedList<>();
		List<Object[]> result = communityReceivablesDao.createSQLQuery(sql).list();
		result.forEach(o -> {
			res.add(MemberPropertyVo.getMemberPropertyVo(o));
		});
		return res;
	}

	@SuppressWarnings({ "unchecked" })
	private List<CommunityReceivablesChangesVo> getReceivalbesChangesList(String sql) {
		List<CommunityReceivablesChangesVo> res = new LinkedList<>();
		List<Object[]> result = communityReceivablesChangesDao.createSQLQuery(sql).list();
		result.forEach(o -> {
			res.add(CommunityReceivablesChangesVo.getCommunityReceivablesChangesVo(o));
		});
		return res;
	}

	private List<EverydayReceiptVo> getEverydayReceiptList(String sql) {
		List<EverydayReceiptVo> dataList = new LinkedList<>();

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {
				Statement st = conn.createStatement();
				// 构建报表数据
				ResultSet rs = st.executeQuery(sql);
				while (rs.next()) {
					String paymentMethod = rs.getString("paymentmethod");
					String subSql = rs.getString("subSql");
					ResultSet subrs = st.executeQuery(subSql);

					while (subrs.next()) {
						ResultSetMetaData rsmd = subrs.getMetaData();
						EverydayReceiptVo vo = new EverydayReceiptVo();
						vo.setPaymentMethod(paymentMethod);
						vo.setDistrictCode(subrs.getString("districtcode"));
						vo.setUnitCode(subrs.getString("unitcode"));
						vo.setReceiptCode(subrs.getString("receiptcode"));
						vo.setBuildingOrder(subrs.getInt("buildingorder"));
						Map<String, String> feeMap = new TreeMap<>();
						for (int i = 5; i <= rsmd.getColumnCount(); i++) {
							feeMap.put(rsmd.getColumnName(i), subrs.getString(i));
						}
						vo.setFeeMap(feeMap);
						dataList.add(vo);
					}
				}

			}
		});
		session.close();
		return dataList;
	}

	@Override
	public void downloadReceiptDjbFile(CommunityStatisticsReq req, HttpServletResponse response) {
		if (StringUtils.isNotEmpty(req.getFileName())) {
			try {
				IOUtils.setByteArrayMaxOverride(1000000000);
				XSSFWorkbook wb = new XSSFWorkbook("/data/resource/communityFile/djb/" + req.getFileName());
				ExcelExportUtil.export(response, wb, req.getFileName());
			} catch (Exception ex) {
				ex.printStackTrace();
			}
		}
	}
}
