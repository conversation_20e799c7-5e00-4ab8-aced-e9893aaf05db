package com.foshan.service.community.impl;

import static java.util.stream.Collectors.groupingBy;

import java.awt.Graphics;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DateTimeException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.hibernate.LockMode;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.Cache;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.foshan.dao.generic.Page;
import com.foshan.entity.DepartmentEntity;
import com.foshan.entity.DictionaryDataEntity;
import com.foshan.entity.WxParameterEntity;
import com.foshan.entity.community.CommunityBankDepositBatchEntity;
import com.foshan.entity.community.CommunityBankDepositRecordEntity;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityMemberEntity;
import com.foshan.entity.community.CommunityMemberPropertyEntity;
import com.foshan.entity.community.CommunityMeterRecordEntity;
import com.foshan.entity.community.CommunityPayItemsEntity;
import com.foshan.entity.community.CommunityPayItemsPriceEntity;
import com.foshan.entity.community.CommunityPropertyServiceEntity;
import com.foshan.entity.community.CommunityReceiptEntity;
import com.foshan.entity.community.CommunityReceivablesChangesEntity;
import com.foshan.entity.community.CommunityReceivablesEntity;
import com.foshan.entity.community.context.CommunityContext;
import com.foshan.entity.community.vo.CalBreachAmountChangeAmountHistoryResultVo;
import com.foshan.entity.community.vo.CancelBenefitAmountChangeDetailItem;
import com.foshan.entity.community.vo.CancelBenefitDetailItemVo;
import com.foshan.entity.community.vo.CancelBenefitDetailVo;
import com.foshan.entity.community.vo.EstateReceivablesVo;
import com.foshan.entity.community.vo.RentMeterAllocationVo;
import com.foshan.entity.community.vo.ReservedFieldVo;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityReceivablesChangesForm;
import com.foshan.form.community.CommunityReceivablesForm;
import com.foshan.form.community.ExportSendDiscForm;
import com.foshan.form.community.RentMeterAllocationForm;
import com.foshan.form.community.request.CommunityAddBreachReq;
import com.foshan.form.community.request.CommunityAddReceivablesForEstateReq;
import com.foshan.form.community.request.CommunityDeleteReceivablesByDetails;
import com.foshan.form.community.request.CommunityMeterAllocationReq;
import com.foshan.form.community.request.CommunityPreReceivablesReq;
import com.foshan.form.community.request.CommunityReceivablesReq;
import com.foshan.form.community.request.ExportExcelReq;
import com.foshan.form.community.response.communityReceivables.AddCommunityReceivablesRes;
import com.foshan.form.community.response.communityReceivables.CreatePayingInSlipPdfRes;
import com.foshan.form.community.response.communityReceivables.CreatePaymentQrCodeRes;
import com.foshan.form.community.response.communityReceivables.GetCommunityReceivablesInfoRes;
import com.foshan.form.community.response.communityReceivables.GetCommunityReceivablesListRes;
import com.foshan.form.community.response.communityReceivables.GetExportSendDiscListRes;
import com.foshan.form.community.response.communityReceivables.GetRentTrialMeterOperationListRes;
import com.foshan.form.community.response.communityReceivables.ModifyCommunityReceivablesRes;
import com.foshan.form.community.response.exportExcel.ExportExcelRes;
import com.foshan.form.request.WxParameterReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.IWeiXinApiService;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityReceivablesService;
import com.foshan.util.AlipayApiUtil;
import com.foshan.util.ChineseNumberUtil;
import com.foshan.util.CommonUtil;
import com.foshan.util.DateUtil;
import com.foshan.util.DigestUtil;
import com.foshan.util.ExcelExportUtil;
import com.foshan.util.FreemarkUtil;
import com.foshan.util.HttpClientUtil;
import com.foshan.util.JsonUtil;
import com.foshan.util.PicUtil;
import com.foshan.util.SpringHandler;
import com.foshan.util.WeiXinApiUtil;
import com.foshan.util.community.CommunityCache;
import com.foshan.util.community.CommunityDateUtil;
import com.foshan.util.community.CommunityPage;
import com.foshan.util.community.JepUtil;
import com.hazelcast.spring.cache.HazelcastCacheManager;
import com.itextpdf.text.BadElementException;
import com.itextpdf.text.Image;

@Transactional
@Service("communityReceivablesService")
public class CommunityReceivablesServiceImpl extends GenericCommunityService implements ICommunityReceivablesService {

	private final static Logger logger = LoggerFactory.getLogger(CommunityReceivablesServiceImpl.class);
	@Resource
	private HazelcastCacheManager cacheManager;
	protected static final Object gardenAndParkingManagementFees = null;
	public static final String RECEIVABLESNO_SN_TYPE = "RECEIVABLESNO";
	public static List<Map<String,Object>> rentMeterAllocationCache = new ArrayList<>();

	@Override
	@Audit(operate = "新增应收款")
	public IResponse addCommunityReceivables(CommunityReceivablesReq req) {
		AddCommunityReceivablesRes res = new AddCommunityReceivablesRes();

//		if (StringUtils.isEmpty(req.getComment())) {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "备注不能为空！");
//			return res;
//		}
		if (StringUtils.isEmpty(req.getReceivableAmount())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "应收金额不能为空！");
			return res;
		}
		if (StringUtils.isEmpty(req.getReceivableDate())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "应收日期不能为空！");
			return res;
		}
		if (null == req.getEstateId()) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "单元ID不能为空！");
			return res;
		}
		if (null == req.getPayItemId()) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "收费项目ID不能为空！");
			return res;
		}
		BigDecimal receivableAmount = BigDecimal.ZERO;
		try {
			receivableAmount = new BigDecimal(req.getReceivableAmount());
			if (receivableAmount.compareTo(BigDecimal.ZERO) == 0) {
				res.setRet(ResponseContext.RES_DATA_ERROR_INFO);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_CODE + "应收金额不能为0！");
				return res;
			}
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_DATA_ERROR_INFO);
			res.setRetInfo(ResponseContext.RES_DATA_ERROR_CODE + "应收金额格式有误，应为货币数值！");
			return res;
		}
		CommunityEstateEntity communityEstateEntity = communityEstateDao.get(req.getEstateId());
		if (communityEstateEntity == null) {
			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "找不到对应的单元信息！");
			return res;
		}

		CommunityPayItemsEntity communityPayItemsEntity = communityPayItemsDao.get(req.getPayItemId());
		if (communityPayItemsEntity == null) {
			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "找不到收费项目信息！");
			return res;
		}
		if (communityPayItemsEntity.getPayDate() == 0) {
			req.setChargeSource("其它收费");
		}
		CommunityReceivablesEntity communityReceivables = new CommunityReceivablesEntity();
		communityReceivables.setChargeCategory(
				CommunityPayItemsEntity.getChargeCategoryStr(communityPayItemsEntity.getChargeCategory()));

		communityReceivables.setChargeSource(req.getChargeSource());
		communityReceivables.setLockMark(0);
		communityReceivables.setState(1);
		communityReceivables.setComment(req.getComment());
		Date now = new Date();
		try {
			communityReceivables.setEndTime(
					StringUtils.isNotEmpty(req.getEndTime()) ? DateUtil.parseLongFormat(req.getEndTime()) : now);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		communityReceivables.setOldData(req.getOldData());
		communityReceivables.setOldId(req.getOldId());
		communityReceivables.setReceivablesNO(StringUtils.isNotEmpty(req.getReceivablesNO()) ? req.getReceivablesNO()
				: generateSnLastValue(RECEIVABLESNO_SN_TYPE).toString());
		communityReceivables.setPayItemsName(communityPayItemsEntity.getItemsName());

		try {
			communityReceivables.setPaymentPeriod(
					StringUtils.isNotEmpty(req.getPaymentPeriod()) ? DateUtil.parseLongFormat(req.getPaymentPeriod())
							: now);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		communityReceivables.setReceivableAmount(receivableAmount);
		try {
			communityReceivables.setReceivableDate(StringUtils.isNotEmpty(req.getReceivableDate())
					? (req.getReceivableDate().trim().length() > 10 ? DateUtil.parseLongFormat(req.getReceivableDate())
							: DateUtil.parseLongFormat(req.getReceivableDate() + " 00:00:00"))
					: now);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		communityReceivables.setReceivedAmount(
				StringUtils.isNotEmpty(req.getReceivedAmount()) ? new BigDecimal(req.getReceivedAmount())
						: BigDecimal.ZERO);
		communityReceivables.setSourceNotes(req.getSourceNotes());
		try {
			communityReceivables.setStartTime(
					StringUtils.isNotEmpty(req.getStartTime()) ? DateUtil.parseLongFormat(req.getStartTime()) : now);
		} catch (ParseException e) {
			e.printStackTrace();
		}

		communityReceivables.setEstate(communityEstateEntity);
		communityReceivables.setPayItem(communityPayItemsEntity);
		communityReceivablesDao.save(communityReceivables);

		CommunityCache.putReceivableCache(communityReceivables, CommunityCache.receivableCalEstateList);

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		return res;
	}

	@Override
	@Audit(operate = "修改应收款")
	public IResponse modifyCommunityReceivables(CommunityReceivablesReq req) {
		ModifyCommunityReceivablesRes res = new ModifyCommunityReceivablesRes();
		if (StringUtils.isNotEmpty(req.getCommunityReceivablesIdList())) {
			String[] receivablesIds = req.getCommunityReceivablesIdList().split(",");
//			String[] unlockIds = StringUtils.isNotEmpty(req.getUnlockList()) ? req.getUnlockList().split(",") : null;
//			String[] lockIds = StringUtils.isNotEmpty(req.getLockList()) ? req.getLockList().split(",") : null;
			for (String receivablesId : receivablesIds) {
				CommunityReceivablesEntity communityReceivables = communityReceivablesDao
						.get(Integer.valueOf(receivablesId));
				if (null != communityReceivables) {
//					if (communityReceivables.getLockMark() == 1
//							|| communityReceivables.getLockMark() == 2 && req.getLockMark() != 0) {
//						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
//						res.setRetInfo("对不起，锁盘的数据不能修改！");
//						return res;
//					}
					if (StringUtils.isNotEmpty(communityReceivables.getChargeSource())
							&& communityReceivables.getChargeSource().contains("BX")) {// 临时加有偿服务部分
						CommunityPropertyServiceEntity propertyService = communityPropertyServiceDao
								.findUnique("select a from " + "CommunityPropertyServiceEntity a where a.eventsCode='"
										+ communityReceivables.getChargeSource() + "'", "");
						if (null != propertyService) {
							res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
							res.setRetInfo("对不起，此数据为有偿服务应收款，不能修改！");
							return res;
						}
					}
					if(null!=req.getLockList() && req.getLockList().contains(receivablesId)) {
						communityReceivables.setLockMark(communityReceivables.getLockMark() != 1 ?
								2 :communityReceivables.getLockMark());
					}else if(null!=req.getUnlockList() && req.getUnlockList().contains(receivablesId)){
						communityReceivables.setLockMark(communityReceivables.getLockMark() != 1 ? 0
								:communityReceivables.getLockMark());
					}else {
						communityReceivables
							.setLockMark(null != req.getLockMark() && req.getLockMark() != 1
							&&communityReceivables.getLockMark() != 1 ? req.getLockMark()
								: communityReceivables.getLockMark());
					}

					communityReceivables
							.setChargeCategory(StringUtils.isNotEmpty(req.getChargeCategory()) ? req.getChargeCategory()
									: communityReceivables.getChargeCategory());
					communityReceivables
							.setChargeSource(StringUtils.isNotEmpty(req.getChargeSource()) ? req.getChargeSource()
									: communityReceivables.getChargeSource());
					communityReceivables.setComment(StringUtils.isNotEmpty(req.getComment()) ? req.getComment()
							: communityReceivables.getComment());
					try {
						communityReceivables.setEndTime(
								StringUtils.isNotEmpty(req.getEndTime()) ? DateUtil.parseLongFormat(req.getEndTime())
										: communityReceivables.getEndTime());
					} catch (ParseException e) {
						e.printStackTrace();
					}

					communityReceivables
							.setPayItemsName(StringUtils.isNotEmpty(req.getPayItemsName()) ? req.getPayItemsName()
									: communityReceivables.getPayItemsName());
					try {
						communityReceivables.setPaymentPeriod(StringUtils.isNotEmpty(req.getPaymentPeriod())
								? DateUtil.parseLongFormat(req.getPaymentPeriod())
								: communityReceivables.getPaymentPeriod());
					} catch (ParseException e) {
						e.printStackTrace();
					}
					if (StringUtils.isNotEmpty(req.getReceivableAmount())
							&& communityReceivables.getReceivedAmount().compareTo(new BigDecimal("0")) == 0) {
						communityReceivables.setReceivableAmount(StringUtils.isNotEmpty(req.getReceivableAmount())
								? new BigDecimal(req.getReceivableAmount())
								: communityReceivables.getReceivableAmount());
					}

					try {
						communityReceivables.setReceivableDate(StringUtils.isNotEmpty(req.getReceivableDate())
								? DateUtil.parseLongFormat(req.getReceivableDate())
								: communityReceivables.getReceivableDate());
					} catch (ParseException e) {
						e.printStackTrace();
					}
//					communityReceivables.setReceivedAmount(
//							StringUtils.isNotEmpty(req.getReceivedAmount()) ? new BigDecimal(req.getReceivedAmount())
//									: null);
					communityReceivables
							.setSourceNotes(StringUtils.isNotEmpty(req.getSourceNotes()) ? req.getSourceNotes()
									: communityReceivables.getSourceNotes());
					try {
						communityReceivables.setStartTime(StringUtils.isNotEmpty(req.getStartTime())
								? DateUtil.parseLongFormat(req.getStartTime())
								: communityReceivables.getStartTime());
					} catch (ParseException e) {
						e.printStackTrace();
					}
					communityReceivables
							.setReceivablesNO(StringUtils.isNotEmpty(req.getReceivablesNO()) ? req.getReceivablesNO()
									: communityReceivables.getReceivablesNO());
					communityReceivables.setLastModifyTime(new Timestamp(System.currentTimeMillis()));
					communityReceivablesDao.update(communityReceivables);

					// res.setCommunityReceivablesId(communityReceivables.getId());
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} else {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				}

			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "删除应收款")
	public IResponse deleteCommunityReceivables(CommunityReceivablesReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityReceivablesId()) {
			CommunityReceivablesEntity communityReceivables = communityReceivablesDao
					.get(req.getCommunityReceivablesId());
			if (null != communityReceivables
					&& (communityReceivables.getReceivedAmount().compareTo(new BigDecimal("0")) != 1)
					&& communityReceivables.getLockMark() == 0) {
				List<CommunityReceivablesChangesEntity>  receivablesChangesList = new ArrayList<>();
				for (CommunityReceivablesChangesEntity o : communityReceivables.getReceivablesChangesList()) {
					if(o.getState() == EntityContext.RECORD_STATE_VALID) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo("对不起，删除失败！该应收款存在减免记录，请重核对减免记录。");
						return res;
					}
					receivablesChangesList.add(o);
				}

				CommunityCache.removeReceivableCache(communityReceivables, CommunityCache.receivableCalEstateList);
				communityReceivables.setEstate(null);
				communityReceivables.setPayItem(null);

				if (StringUtils.isNotEmpty(communityReceivables.getChargeSource())
						&& communityReceivables.getChargeSource().contains("BX")) {// 临时加有偿服务部分
					CommunityPropertyServiceEntity propertyService = communityPropertyServiceDao
							.findUnique("select a from " + "CommunityPropertyServiceEntity a where a.eventsCode='"
									+ communityReceivables.getChargeSource() + "'", "");
					if (null != propertyService) {
						propertyService.setIsGeneratedBills(0);
					}
				}	
				Iterator<CommunityReceivablesChangesEntity> iterator = receivablesChangesList.iterator();
				while (iterator.hasNext()) {
					CommunityReceivablesChangesEntity receivablesChanges = iterator.next();
					receivablesChanges.setEstate(null);
					receivablesChanges.setReceivables(null);
					communityReceivablesChangesDao.delete(receivablesChanges);
				}
				communityReceivablesChangesDao.flush();

				communityReceivables.setReceivablesChangesList(null);
				communityReceivablesDao.clear();
				communityReceivablesDao.delete(communityReceivables);

				//				CommunityCache.removeReceivableCache(communityReceivables, CommunityCache.receivableQueryEstateList);
				// communityReceivables.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityReceivablesInfo(CommunityReceivablesReq req) {
		GetCommunityReceivablesInfoRes res = new GetCommunityReceivablesInfoRes();

		Object userObj = getPrincipal(true);
		if (null == userObj) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}

		if (null != req.getCommunityReceivablesId()) {
			CommunityReceivablesEntity communityReceivables = communityReceivablesDao
					.get(req.getCommunityReceivablesId());
			if (null != communityReceivables) {
				if (userObj instanceof CommunityMemberEntity) {
					CommunityMemberEntity member = (CommunityMemberEntity) userObj;
					Integer estateId = communityReceivables.getEstate() == null ? null
							: communityReceivables.getEstate().getId();
					if (!canManageBillOfEstate(member.getId(), estateId)) {
						res.setRet(ResponseContext.RES_PERM_BEYOND_AUTHORITY_CODE);
						res.setRetInfo(ResponseContext.RES_PERM_BEYOND_AUTHORITY_INFO);
						return res;
					}
				}
				CommunityReceivablesForm communityReceivablesForm = new CommunityReceivablesForm();
				communityReceivablesForm.setCommunityReceivablesId(communityReceivables.getId());
				communityReceivablesForm.setChargeCategory(communityReceivables.getChargeCategory());
				communityReceivablesForm.setChargeSource(communityReceivables.getChargeSource());
				communityReceivablesForm.setComment(communityReceivables.getComment());
				communityReceivablesForm.setReceivablesNO(communityReceivables.getReceivablesNO());
				communityReceivablesForm.setEndTime(null != communityReceivables.getEndTime()
						? DateUtil.formatLongFormat(communityReceivables.getEndTime())
						: "");
				communityReceivablesForm.setPayItemsName(communityReceivables.getPayItemsName());
				communityReceivablesForm.setPaymentPeriod(null != communityReceivables.getPaymentPeriod()
						? DateUtil.formatLongFormat(communityReceivables.getPaymentPeriod())
						: "");
				communityReceivablesForm.setQuantity(communityReceivables.getQuantity());
				communityReceivablesForm.setReceivableAmount(null != communityReceivables.getReceivableAmount()
						? communityReceivables.getReceivableAmount().toString()
						: "");
				communityReceivablesForm.setReceivableDate(null != communityReceivables.getReceivableDate()
						? DateUtil.formatLongFormat(communityReceivables.getReceivableDate())
						: "");
				communityReceivablesForm.setReceivedAmount(null != communityReceivables.getReceivedAmount()
						? communityReceivables.getReceivedAmount().toString()
						: "");
				communityReceivablesForm.setLockMark(communityReceivables.getLockMark());
				;
				communityReceivablesForm
						.setNotReceivedAmount(CommonUtil.subtractAmount(communityReceivables.getReceivableAmount(),
								communityReceivables.getReceivedAmount()).toString());
				communityReceivablesForm.setSourceNotes(communityReceivables.getSourceNotes());
				communityReceivablesForm.setStartTime(null != communityReceivables.getStartTime()
						? DateUtil.formatLongFormat(communityReceivables.getStartTime())
						: "");
				for (CommunityReceivablesChangesEntity receivablesChanges : communityReceivables
						.getReceivablesChangesList()) {
					CommunityReceivablesChangesForm form = new CommunityReceivablesChangesForm();
					form.setCommunityReceivablesChangesId(receivablesChanges.getId());
					form.setChangeAmount(receivablesChanges.getChangeAmount().toString());
					form.setChangeDate(DateUtil.format(receivablesChanges.getChangeDate(), 1));
					form.setComment(receivablesChanges.getComment());
					communityReceivablesForm.getReceivablesChangesList().add(form);
				}
				communityReceivablesForm.setBenefitValue(StringUtils.isNotEmpty(communityReceivables.getBenefitValue()) ?
						communityReceivables.getBenefitValue():"");
				res.setCommunityReceivablesForm(communityReceivablesForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@SuppressWarnings("unchecked")
	public Map<Integer, Map<String, Object>> getReceivableData(CommunityReceivablesReq req,
			CommunityBankDepositBatchEntity bankDepositBatch) {

		String bankNameSql = "";
		if (StringUtils.isNotEmpty(req.getBankNameList())) {
			String[] bankNameList = req.getBankNameList().split(",");
			if (bankNameList.length > 0) {
				StringBuilder bankName = new StringBuilder();
				bankName.append("(");
				for (int i = 0; i < bankNameList.length; i++) {
					bankName.append(" c.bankName like'%" + bankNameList[i] + "%' or");
				}
				bankNameSql = bankName.subSequence(0, bankName.length() - 2).toString() + ")";
			}
		}

		StringBuilder payItemsIds = new StringBuilder();
		bankDepositBatch.getPayItemsList().forEach(o -> {
			payItemsIds.append(o.getId() + ",");
		});

		Map<Integer, Map<String, Object>> maps = new HashMap<Integer, Map<String, Object>>();
		StringBuilder basicSql = new StringBuilder(
				"  FROM `t_community_receivables` a INNER JOIN t_community_property b ON b.id=a.estateId "
						+ "INNER JOIN t_community_payment_account c ON c.id=b.paymentAccountId  WHERE "
						+ " a.receivableAmount>a.receivedAmount AND a.bankDepositRecordId IS NULL AND a.lockMark=0 AND a.id NOT IN(");
		// 排除所有不划扣应该收款
		basicSql.append(
				"SELECT DISTINCT a1.id FROM t_community_receivables a1 inner join `t_community_bankdeposit_estate_payitems` "
						+ "b1 WHERE b1.payItemsId=a1.payItemId AND b1.estateId=a1.estateId  and b1.depositType=0 "
						+ "and a1.receivableAmount>a1.receivedAmount AND a1.bankDepositRecordId IS NULL AND a1.lockMark=0)");
		if (StringUtils.isNotEmpty(bankNameSql)) {
			basicSql.append(" AND " + bankNameSql);
		}
		basicSql.append(StringUtils.isNotEmpty(payItemsIds.toString()) ? " and a.payItemId in("
				+ payItemsIds.toString().substring(0, payItemsIds.toString().length() - 1) + ")" : "")
				.append(null != bankDepositBatch.getStartTime()
						? " and a.receivableDate>='" + DateUtil.formatLongFormat(bankDepositBatch.getStartTime()) + "'"
						: "")
				.append(null != bankDepositBatch.getEndTime()
						? " and a.receivableDate<='" + DateUtil.formatLongFormat(bankDepositBatch.getEndTime()) + "'"
						: "");

		StringBuilder sql = new StringBuilder();
		sql.append("SELECT DISTINCT a.* " + basicSql)
				// 加上违约金
				.append(" UNION SELECT DISTINCT t.* FROM `t_community_receivables` t where t.breachReceivablesId in(SELECT DISTINCT a.id "
						+ basicSql + ")")
				.append(" and t.receivableAmount>t.receivedAmount AND t.bankDepositRecordId IS NULL AND t.lockMark=0 ")
				.append(null != bankDepositBatch.getStartTime()
						? " and t.receivableDate>='" + DateUtil.formatLongFormat(bankDepositBatch.getStartTime()) + "'"
						: "")
				.append(null != bankDepositBatch.getEndTime()
						? " and t.receivableDate<='" + DateUtil.formatLongFormat(bankDepositBatch.getEndTime()) + "'"
						: "");
		// 排除不划扣新违约金：
		sql.append(" and t.id not in(SELECT w.id FROM `t_community_receivables` w INNER JOIN t_community_receivables r"
				+ " ON w.`breachReceivablesId` = r.id INNER JOIN `t_community_bankdeposit_estate_payitems` b ON (r.`estateId` = b.`estateId` "
				+ " AND r.`payItemId` = b.`payItemsId`) WHERE w.receivableAmount > w.receivedAmount AND w.lockMark = 0 AND w.breachReceivablesId IS NOT NULL ")
				.append(null != bankDepositBatch.getStartTime()
						? " and w.receivableDate>='" + DateUtil.formatLongFormat(bankDepositBatch.getStartTime()) + "'"
						: "")
				.append(null != bankDepositBatch.getEndTime()
						? " and w.receivableDate<='" + DateUtil.formatLongFormat(bankDepositBatch.getEndTime()) + "'"
						: "")
				.append(" UNION ")
				// 排除不划扣旧违约金
				.append("SELECT DISTINCT w.id FROM`t_community_receivables` w INNER JOIN `t_community_bankdeposit_estate_payitems` b "
						+ " ON w.`estateId` = b.`estateId` INNER JOIN `t_community_pay_items` t  ON t.`id` = b.`payItemsId` WHERE t.`isBreach` = 1 AND t.breachName IS NOT NULL"
						+ " AND w.receivableAmount > w.receivedAmount AND w.lockMark = 0 AND w.breachReceivablesId IS NULL AND w.`payItemsName`=t.breachName")
				.append(null != bankDepositBatch.getStartTime()
						? " and w.receivableDate>='" + DateUtil.formatLongFormat(bankDepositBatch.getStartTime()) + "'"
						: "")
				.append(null != bankDepositBatch.getEndTime()
						? " and w.receivableDate<='" + DateUtil.formatLongFormat(bankDepositBatch.getEndTime()) + "'"
						: "")
				.append(")");

		List<CommunityReceivablesEntity> receivablesList = communityReceivablesDao.getListBySql(sql.toString(), "");
		if (null != receivablesList) {
			Map<CommunityEstateEntity, List<CommunityReceivablesEntity>> collect1 = (Map<CommunityEstateEntity, List<CommunityReceivablesEntity>>) receivablesList
					.parallelStream().collect(groupingBy(CommunityReceivablesEntity::getEstate));
			for (CommunityEstateEntity estate : collect1.keySet()) {
				BigDecimal receivableAmountTotal = new BigDecimal(0);

				Map<String, Object> map = new HashMap<String, Object>();

				map.put("unitCode", estate.getUnitCode());
				if (estate.getPaymentAccount().getBankName().contains("（")) {
					map.put("bankName", estate.getPaymentAccount().getBankName().split("（")[0]);
				} else {
					map.put("bankName", estate.getPaymentAccount().getBankName());
				}
				map.put("bankAccount", estate.getPaymentAccount().getBankAccount());
				map.put("accountName", estate.getPaymentAccount().getAccountName());
				map.put("unitCode", estate.getUnitCode());
				ReservedFieldVo reservedField = null;
				if (StringUtils.isNotEmpty(estate.getReservedField())) {
					try {
						reservedField = mapper.readValue(estate.getReservedField(), ReservedFieldVo.class);
					} catch (JsonMappingException e) {
						e.printStackTrace();
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}
				}
				if (null != reservedField) {
					map.put("accountCustomerId", reservedField.getAccountCustomerId());
					map.put("accountContractNo", reservedField.getAccountContractNo());
				} else {
					map.put("accountCustomerId", "");
					map.put("accountContractNo", "");
				}
				Map<String, String> bankNoMap = null;
				try {
					bankNoMap = mapper.readValue(communityContextInfo.bankNo, Map.class);
				} catch (JsonMappingException e1) {
					e1.printStackTrace();
				} catch (JsonProcessingException e1) {
					e1.printStackTrace();
				}
				map.put("bankNo",
						bankNoMap.containsKey(estate.getPaymentAccount().getBankName())
								? bankNoMap.get(estate.getPaymentAccount().getBankName())
								: "");

				for (CommunityReceivablesEntity o : collect1.get(estate)) {
					BigDecimal receivableAmount = o.getReceivableAmount().subtract(o.getReceivedAmount());
					receivableAmountTotal = receivableAmountTotal.add(receivableAmount);
				}
				map.put("receivablesList", collect1.get(estate));
				map.put("receivableAmountTotal", receivableAmountTotal);
				maps.put(estate.getId(), map);
			}
		}
		return maps;
	}

	public CommunityBankDepositRecordEntity saveBankDepositBatch(CommunityBankDepositBatchEntity bankDepositBatch,
			CommunityReceivablesEntity receivables, String bankAccount, BigDecimal outstandingAmount,
			BigDecimal depositAmount, int i) {
		CommunityBankDepositRecordEntity depositRecord = new CommunityBankDepositRecordEntity();
		depositRecord.setBankAccount(bankAccount);
		depositRecord.setState(EntityContext.RECORD_STATE_VALID);
		depositRecord.setDepositAmount(depositAmount);
		depositRecord.setOutstandingAmount(outstandingAmount);
		depositRecord.setIsDeposited(0);
		depositRecord.setEstate(receivables.getEstate());
		depositRecord.getReceivablesList().add(receivables);
		depositRecord.setLastModifyTime(new Timestamp(new Date().getTime()));
		depositRecord.setBankDepositBatch(bankDepositBatch);
		depositRecord.setDepositComment(receivables.getEstate().getUnitCode());
		communityBankDepositRecordDao.save(depositRecord);
		bankDepositBatch.getBankDepositRecordList().add(depositRecord);
		refreshBankDepositRecord(i, false, depositRecord);
		return depositRecord;
	}

	public void updateCommunityReceivables(CommunityReceivablesEntity receivables,
			CommunityBankDepositRecordEntity depositRecord, int i) {
		receivables.setLockMark(1);
		receivables.setBankDepositRecord(depositRecord);
		receivables.setLastModifyTime(new Timestamp(System.currentTimeMillis()));
		refreshReceivables(i, false, receivables);
		depositRecord.getReceivablesList().add(receivables);
	}

	public void refreshBankDepositRecord(int i, boolean refresh, CommunityBankDepositRecordEntity depositRecord) {
		if (i % 50 == 0 || refresh) {
			communityBankDepositRecordDao.flush();
			communityBankDepositRecordDao.refresh(depositRecord, LockMode.PESSIMISTIC_WRITE);
		}
	}

	public void refreshReceivables(int i, boolean refresh, CommunityReceivablesEntity receivables) {
		if (i % 50 == 0 || refresh) {
			communityReceivablesDao.flush();
			communityReceivablesDao.refresh(receivables, LockMode.PESSIMISTIC_WRITE);
		}
	}

	public String getSerialNumber(String num, int places) {
		StringBuilder newStr = new StringBuilder();
		if (places > num.length()) {
			for (int i = 0; i < (places - num.length()); i++) {
				newStr.append("0");
			}
		}
		newStr.append(num);
		return newStr.toString();
	}

	/**
	 * 手机端会员获取应收列表
	 */
	@Override
	public IResponse getCommunityReceivablesList(CommunityReceivablesReq req) {
		GetCommunityReceivablesListRes res = new GetCommunityReceivablesListRes();
		CommunityMemberEntity member = null;
		Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> result = new TreeMap<>();

		if (null != req.getFetchAll() && (null != req.getEstateId() || StringUtils.isNotEmpty(req.getUnitCode()))) {
			Object userObj = getPrincipal(true);
			if (null != userObj && (userObj instanceof CommunityMemberEntity)) {
				member = (CommunityMemberEntity) userObj;
			} else {
				res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO + "会员未登录！");
				return res;
			}

			Integer estateId = req.getEstateId();
			if (StringUtils.isNotEmpty(req.getUnitCode())) {
				CommunityEstateEntity estate = communityEstateDao
						.getUniqueByHql("from CommunityEstateEntity a where a.unitCode='" + req.getUnitCode() + "'");
				if (null != estate) {
					estateId = estate.getId();
				} else {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
					return res;
				}
			}

			// 当前用户没有权限查阅该账单
			if (!canManageBillOfEstate(member.getId(), estateId)) {
				res.setRet(ResponseContext.RES_PERM_BEYOND_AUTHORITY_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_BEYOND_AUTHORITY_INFO);
				return res;
			}

			// 获取该用户历史收费记录
			StringBuilder sql = new StringBuilder(
					"select a from CommunityReceivablesEntity a inner join a.estate b inner join a.payItem c where b.id="
							+ estateId);
			if (req.getFetchAll().equals(0)) {
				sql.append(" and a.receivableAmount-a.receivedAmount>0");
			} else if (req.getFetchAll().equals(2)) {
				sql.append(" and a.receivableAmount-a.receivedAmount=0");
			}
			
			if(req.getFeeType() != null && req.getFeeType().equals(1)) {
				sql.append(" and c.payDate = 0");
			}
			else if(req.getFeeType() != null && req.getFeeType().equals(0)) {
				sql.append(" and c.payDate > 0");
			}

			sql.append(StringUtils.isNotEmpty(req.getPayItemIds()) ? " and c.id in (" + req.getPayItemIds() + ")" : "");
			sql.append(StringUtils.isNotEmpty(req.getChargeCategoryIds())
					? " and c.chargeCategory in (" + req.getChargeCategoryIds() + ")"
					: "");

			List<CommunityReceivablesEntity> receivablesList = communityReceivablesDao.getListByHql(sql.toString());

			for (CommunityReceivablesEntity o : receivablesList) {
				CommunityCache.putReceivableCache(o, result);
			}

//			if (!req.getFetchAll().equals(2)) {
//				// 开始计算预收金额，从请求当月起至系统默认配置预收时间截止
//				LocalDate preStart = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
//				LocalDate perEnd = preStart.plus(communityContextInfo.advanceReceiptsMonth, ChronoUnit.MONTHS)
//						.with(TemporalAdjusters.lastDayOfMonth());
//				Map<String, String> cycleMonth = CommunityDateUtil.cycleMonth(preStart.toString(), perEnd.toString());
//				for (String o : cycleMonth.keySet()) {
//					calManagerFee(LocalDate.parse(o), LocalDate.parse(cycleMonth.get(o)), estateId, req.getUnitCode(),
//							req.getPayItemIds(), req.getChargeCategoryIds(), 0, pre);
//				}
//				result = CommunityCache.addReceivableCache(result, pre);
//			}

			// 把生成的应收存在缓存中
			Cache cache = cacheManager.getCache("prePaymentReceivableCache");
			result.values().forEach(o -> {
				o.values().forEach(k -> {
					k.values().forEach(i -> {
						i.forEach(r -> {
							logger.info(r.getId() + "加入缓存");
							cache.put(r.getId(), r);
						});
					});
				});
			});

			if (null != req.getResultType() && req.getResultType().equals(2)) {
				result = CommunityCache.parseReceivableEstateList2Name(result);
				if (result.containsKey(estateId)) {
					Map<String, Set<CommunityReceivablesForm>> parseReceivablesFormMap = parseReceivablesToNameFormMap(
							result.get(estateId));
					res.getReceivablesToNameList().put(estateId, parseReceivablesFormMap);
					CommunityPage<Map<String, Set<CommunityReceivablesForm>>> page = CommunityCache
							.getPage(parseReceivablesFormMap, req.getPageSize(), req.getRequestPage());
					res.setCurrentPage(page.getCurrentPage());
					res.setPageSize(page.getPageSize());
					res.setTotal(page.getTotalPage());
					res.setTotalResult(page.getTotalCount());
					res.getReceivablesToNameList().put(estateId, page.getResultList());
				}
			} else {
				// 处理预收列表
				if (result.containsKey(estateId)) {
					res.getReceivablesList().put(estateId, parseReceivablesFormMap(result.get(estateId), null));
					CommunityPage<Map<String, Map<String, Set<CommunityReceivablesForm>>>> page = CommunityCache
							.getPage(parseReceivablesFormMap(result.get(estateId)), req.getPageSize(),
									req.getRequestPage());
					res.setCurrentPage(page.getCurrentPage());
					res.setPageSize(page.getPageSize());
					res.setTotal(page.getTotalPage());
					res.setTotalResult(page.getTotalCount());
					res.getReceivablesList().put(estateId, page.getResultList());
				}
			}

			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	/**
	 * 手机端获取催缴费账单
	 */
	@Override
	public IResponse getCommunityReceivablesBill(WxParameterReq req) {
		GetCommunityReceivablesListRes res = new GetCommunityReceivablesListRes();
		if (StringUtils.isEmpty(req.getKeyName())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "keyName不能为空！");
			return res;
		}
		WxParameterEntity wxParameter = wxParameterDao
				.findUnique("select a from WxParameterEntity a where a.keyName='" + req.getKeyName() + "'", "");
		if (null != wxParameter) {
			ObjectMapper mapper = new ObjectMapper();
			try {
				CommunityReceivablesReq reqParameter = mapper.readValue(wxParameter.getDataValue(),
						CommunityReceivablesReq.class);
				Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> result = new TreeMap<>();

				if (null != reqParameter.getFetchAll()
						&& (null != reqParameter.getEstateId() || StringUtils.isNotEmpty(reqParameter.getUnitCode()))) {
					CommunityEstateEntity estate = null;
					if (reqParameter.getEstateId() != null) {
						estate = communityEstateDao.getUniqueByHql(
								"from CommunityEstateEntity a where a.id= " + reqParameter.getEstateId() + "");
					} else if (StringUtils.isNotEmpty(reqParameter.getUnitCode())) {
						estate = communityEstateDao.getUniqueByHql(
								"from CommunityEstateEntity a where a.unitCode='" + reqParameter.getUnitCode() + "'");

					}
					if (null == estate) {
						res.setRet(ResponseContext.RES_DATA_NULL_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "找不到对应的单元");
						return res;
					}

					res.setUnitAddress(estate.getBuilding().getDistrict().getDistrictName()
							+ estate.getBuilding().getBuildingName() + estate.getFloor() + estate.getRoomNumber());
					res.setChargingArea(null != estate.getChargingArea() ? estate.getChargingArea().toString() : "");

					String userName = "";
					for (CommunityMemberPropertyEntity memberProperty : estate.getMemberPropertyList()) {
						if (memberProperty.getIsCurrentMember() == 1 && memberProperty.getMemberType() == 0) {
							userName = memberProperty.getMember().getUserName();
							break;
						}
					}
					res.setAccountName(userName);
					res.setUnitCode(estate.getUnitCode());
					res.setDistrictName(estate.getBuilding().getDistrict().getDistrictName());
					/*
					 * // 获取该用户历史收费记录 StringBuilder sql = new StringBuilder(
					 * "select a from CommunityReceivablesEntity a inner join a.estate b inner join a.payItem c where b.id="
					 * + estate.getId()); if (reqParameter.getFetchAll().equals(0)) {
					 * sql.append(" and a.receivableAmount-a.receivedAmount>0"); } else if
					 * (reqParameter.getFetchAll().equals(2)) {
					 * sql.append(" and a.receivableAmount-a.receivedAmount=0"); }
					 * 
					 * sql.append(StringUtils.isNotEmpty(reqParameter.getPayItemIds()) ?
					 * " and c.id in (" + reqParameter.getPayItemIds() + ")" : "");
					 * sql.append(StringUtils.isNotEmpty(reqParameter.getChargeCategoryIds()) ?
					 * " and c.chargeCategory in (" + reqParameter.getChargeCategoryIds() + ")" :
					 * "");
					 */

					LocalDate startDate = null;

					if (StringUtils.isNotEmpty(reqParameter.getStartTime())) {
						startDate = LocalDate.parse(reqParameter.getStartTime().substring(0, 10))
								.with(TemporalAdjusters.firstDayOfMonth());
					} else {
						startDate = LocalDate.of(1990, 12, 31);
					}

					LocalDate endDate = null;
					if (StringUtils.isNotEmpty(reqParameter.getEndTime())) {
						endDate = LocalDate.parse(reqParameter.getEndTime().substring(0, 10))
								.with(TemporalAdjusters.lastDayOfMonth());
					} else {
						endDate = LocalDate.of(2099, 12, 31);
					}
					List<CommunityReceivablesEntity> receivablesList = communityReceivablesDao
							.getListByHql(queryHql(reqParameter, estate.getId(), startDate, endDate));
					
					Collections.sort(receivablesList, (p1, p2) -> ((CommunityReceivablesEntity) p1).getPayItem().getChargeCategory()
							.compareTo(((CommunityReceivablesEntity) p2).getPayItem().getChargeCategory()));

					for (CommunityReceivablesEntity o : receivablesList) {
						CommunityCache.putReceivableCache(o, result);
					}

					/*
					 * if (!reqParameter.getFetchAll().equals(2)) { // 开始计算预收金额，从请求当月起至系统默认配置预收时间截止
					 * LocalDate preStart =
					 * LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()); LocalDate perEnd =
					 * preStart.plus(communityContextInfo.advanceReceiptsMonth, ChronoUnit.MONTHS)
					 * .with(TemporalAdjusters.lastDayOfMonth()); Map<String, String> cycleMonth =
					 * CommunityDateUtil.cycleMonth(preStart.toString(), perEnd.toString()); for
					 * (String o : cycleMonth.keySet()) { calManagerFee(LocalDate.parse(o),
					 * LocalDate.parse(cycleMonth.get(o)), estate.getId(),
					 * reqParameter.getUnitCode(), reqParameter.getPayItemIds(),
					 * reqParameter.getChargeCategoryIds(), 0, pre); } result =
					 * CommunityCache.addReceivableCache(result, pre); }
					 */
					// 把生成的应收存在缓存中
					Cache cache = cacheManager.getCache("prePaymentReceivableCache");
					result.values().forEach(o -> {
						o.values().forEach(k -> {
							k.values().forEach(i -> {
								i.forEach(r -> {
									logger.info(r.getId() + "加入缓存");
									cache.put(r.getId(), r);
								});
							});
						});
					});

					if (null != reqParameter.getResultType() && reqParameter.getResultType().equals(2)) {
						result = CommunityCache.parseReceivableEstateList2Name(result);
						if (result.containsKey(estate.getId())) {
							Map<String, Set<CommunityReceivablesForm>> parseReceivablesFormMap = parseReceivablesToNameFormMap(
									result.get(estate.getId()));
							res.getReceivablesToNameList().put(estate.getId(), parseReceivablesFormMap);
							CommunityPage<Map<String, Set<CommunityReceivablesForm>>> page = CommunityCache
									.getPage(parseReceivablesFormMap, req.getPageSize(), req.getRequestPage());
							res.setCurrentPage(page.getCurrentPage());
							res.setPageSize(page.getPageSize());
							res.setTotal(page.getTotalPage());
							res.setTotalResult(page.getTotalCount());
							res.getReceivablesToNameList().put(estate.getId(), page.getResultList());
						}
					} else {
						if (result.containsKey(estate.getId())) {
							res.getReceivablesList().put(estate.getId(),
									parseReceivablesFormMap(result.get(estate.getId()), null));
							CommunityPage<Map<String, Map<String, Set<CommunityReceivablesForm>>>> page = CommunityCache
									.getPage(parseReceivablesFormMap(result.get(estate.getId())), req.getPageSize(),
											req.getRequestPage());
							res.setCurrentPage(page.getCurrentPage());
							res.setPageSize(page.getPageSize());
							res.setTotal(page.getTotalPage());
							res.setTotalResult(page.getTotalCount());
							res.getReceivablesList().put(estate.getId(), page.getResultList());
						}
					}
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} else {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				}
			} catch (Exception e) {
				e.printStackTrace();
				res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO + e.getMessage());
			}
		} else {
			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "找不到对应的缴费清单！");
		}

		return res;
	}

	/**
	 * 管理端获取应收列表
	 */
	@Override
	public IResponse getAdminCommunityReceivablesList(CommunityReceivablesReq req) {
		// TODO Auto-generated method stub
		GetCommunityReceivablesListRes res = new GetCommunityReceivablesListRes();
		Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> result = new TreeMap<>();

		if (null != req.getFetchAll()) {
			LocalDate startDate = null;

			if (StringUtils.isNotEmpty(req.getStartTime())) {
				startDate = LocalDate.parse(req.getStartTime().substring(0, 10))
						.with(TemporalAdjusters.firstDayOfMonth());
			} else {
				startDate = LocalDate.of(1990, 12, 31);
			}

			LocalDate endDate = null;
			if (StringUtils.isNotEmpty(req.getEndTime())) {
				endDate = LocalDate.parse(req.getEndTime().substring(0, 10)).with(TemporalAdjusters.lastDayOfMonth());
			} else {
				endDate = LocalDate.of(2099, 12, 31);
			}

			if (null != req.getEstateId() || StringUtils.isNotEmpty(req.getUnitCode())) {
				Integer estateId = req.getEstateId();
				if (null == req.getEstateId() && StringUtils.isNotEmpty(req.getUnitCode())) {
					CommunityEstateEntity estate = communityEstateDao.getUniqueByHql(
							"from CommunityEstateEntity a where a.unitCode='" + req.getUnitCode() + "'");
					if (null != estate) {
						estateId = estate.getId();
					} else {
						res.setRet(ResponseContext.RES_DATA_NULL_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
						return res;
					}
				}else {
					estateId = req.getEstateId();
				}

				List<CommunityReceivablesEntity> receivablesList = communityReceivablesDao
						.getListByHql(queryHql(req, estateId, startDate, endDate));
				for (CommunityReceivablesEntity o : receivablesList) {
					CommunityCache.putReceivableCache(o, result);
				}

				if (null != req.getResultType() && req.getResultType().equals(2)) {
					result = CommunityCache.parseReceivableEstateList2Name(result);
					if (result.containsKey(estateId)) {
						Map<String, Set<CommunityReceivablesForm>> parseReceivablesFormMap = parseReceivablesToNameFormMap(
								result.get(estateId), estateId);
						res.getReceivablesToNameList().put(estateId, parseReceivablesFormMap);
//						CommunityPage<Map<String, Set<CommunityReceivablesForm>>> page = CommunityCache
//								.getPage(parseReceivablesFormMap, req.getPageSize(), req.getRequestPage());
//						res.setCurrentPage(page.getCurrentPage());
//						res.setPageSize(page.getPageSize());
//						res.setTotal(page.getTotalPage());
//						res.setTotalResult(page.getTotalCount());
//						res.getReceivablesToNameList().put(estateId, page.getResultList());

						parseReceivablesFormMap.keySet().forEach(p -> {
							Set<CommunityReceivablesForm> set = parseReceivablesFormMap.get(p);
							for (CommunityReceivablesForm receivableForm : set) {
								BigDecimal receivaedAmount = null == receivableForm.getReceivedAmount()
										? BigDecimal.ZERO
										: new BigDecimal(receivableForm.getReceivedAmount());
								BigDecimal receivableAmount = null == receivableForm.getReceivableAmount()
										? BigDecimal.ZERO
										: new BigDecimal(receivableForm.getReceivableAmount());
								BigDecimal notReceivedAmount = null == receivableForm.getNotReceivedAmount()
										? BigDecimal.ZERO
										: new BigDecimal(receivableForm.getNotReceivedAmount());
								res.setTotalReceivedAmount(
										CommonUtil.addAmount(res.getTotalReceivedAmount(), receivaedAmount));
								res.setTotalReceivableAmount(
										CommonUtil.addAmount(res.getTotalReceivableAmount(), receivableAmount));
								res.setTotalNotReceivedAmount(
										CommonUtil.addAmount(res.getTotalNotReceivedAmount(), notReceivedAmount));
							}
						});
					}
				} else {
					if (result.containsKey(estateId)) {
						res.getReceivablesList().put(estateId, parseReceivablesFormMap(result.get(estateId), estateId));
						CommunityPage<Map<String, Map<String, Set<CommunityReceivablesForm>>>> page = CommunityCache
								.getPage(parseReceivablesFormMap(result.get(estateId), estateId), req.getPageSize(),
										req.getRequestPage());
						res.setCurrentPage(page.getCurrentPage());
						res.setPageSize(page.getPageSize());
						res.setTotal(page.getTotalPage());
						res.setTotalResult(page.getTotalCount());
						res.getReceivablesList().put(estateId, page.getResultList());
					}
				}

			} else {
				Page<CommunityReceivablesEntity> page = new Page<CommunityReceivablesEntity>();
				page.setPageSize(req.getPageSize());
				page.setBeginCount((req.getRequestPage() - 1) * req.getPageSize());
				page.setCurrentPage(req.getRequestPage());

				StringBuilder sql = new StringBuilder(
						"select a from CommunityReceivablesEntity a inner join a.payItem c");

				// 设置应收条件
				if (req.getFetchAll().equals(2)) {
					sql.append(" where a.receivableAmount-a.receivedAmount=0 and a.receivableDate>='"
							+ startDate.toString() + " 00:00:00' and a.receivableDate<='" + endDate.toString()
							+ " 00:00:00'");
				} else if (req.getFetchAll().equals(0)) {
					sql.append(" where a.receivableAmount-a.receivedAmount>0");
				} else {
					sql.append(" where a.receivableDate>='" + startDate.toString() + " 00:00:00' and a.receivableDate<'"
							+ endDate.toString() + " 00:00:00'");
				}
				// 设置收费类型
				if (null != req.getFeeType()) {
					sql.append(req.getFeeType().equals(0) ? " and c.payDate > 0" : " and c.payDate = 0");
				}
				// 设置其它相关参数
				sql.append(StringUtils.isNotEmpty(req.getChargeCategory())
						? " and a.chargeCategory='" + req.getChargeCategory() + "'"
						: "")
						.append(StringUtils.isNotEmpty(req.getPayItemsName())
								? " and a.payItemsName like '%" + req.getPayItemsName() + "%'"
								: "")
						.append(StringUtils.isNotEmpty(req.getComment())
								? " and a.comment like '%" + req.getComment() + "%'"
								: "");
				page = communityReceivablesDao.queryPage(page, sql.toString());
				res.setPageSize(req.getPageSize());
				res.setCurrentPage(req.getRequestPage());
				res.setTotal(page.getTotalPage());
				res.setTotalResult(page.getTotalCount());
				for (CommunityReceivablesEntity o : page.getResultList()) {
					CommunityCache.putReceivableCache(o, result);
				}

				if (null != req.getResultType() && req.getResultType().equals(2)) {
					result = CommunityCache.parseReceivableEstateList2Name(result);
				}

				for (Integer id : result.keySet()) {
					Map<String, Map<String, Set<CommunityReceivablesEntity>>> temp = result.get(id);
					res.getReceivablesList().put(id, parseReceivablesFormMap(temp, id));
				}

			}

			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	public String queryHql(CommunityReceivablesReq req, Integer estateId, LocalDate startDate, LocalDate endDate) {
		// 获取该用户历史收费记录
		StringBuilder sql = new StringBuilder(
				"select a from CommunityReceivablesEntity a inner join a.estate b inner join a.payItem c where b.id="
						+ estateId);

		// 设置应收条件
		if (req.getFetchAll().equals(2)) {
			sql.append(" and a.receivableAmount-a.receivedAmount=0 and a.receivableDate>='" + startDate.toString()
					+ " 00:00:00' and a.receivableDate<='" + endDate.toString() + " 00:00:00'");
		} else if (req.getFetchAll().equals(0)) {
			sql.append(" and a.receivableAmount-a.receivedAmount>0 and a.receivableDate>='" + startDate.toString()
					+ " 00:00:00' and a.receivableDate<='" + endDate.toString() + " 00:00:00'");
		} else {
			sql.append(" and a.receivableDate>='" + startDate.toString() + " 00:00:00' and a.receivableDate<='"
					+ endDate.toString() + " 00:00:00'");
		}
		// 设置收费类型
		if (null != req.getFeeType()) {
			sql.append(req.getFeeType().equals(0) ? " and c.payDate > 0" : " and c.payDate = 0");
		}
		// 设置其它相关参数
		sql.append(StringUtils.isNotEmpty(req.getChargeCategory())
				? " and a.chargeCategory='" + req.getChargeCategory() + "'"
				: "")
				.append(StringUtils.isNotEmpty(req.getChargeCategoryIds())
						? " and c.chargeCategory in (" + req.getChargeCategoryIds() + ")"
						: "")
				.append(StringUtils.isNotEmpty(req.getPayItemsName())
						? " and a.payItemsName like '%" + req.getPayItemsName() + "%'"
						: "")
				.append(StringUtils.isNotEmpty(req.getPayItemIds()) ? " and c.id in (" + req.getPayItemIds() + ")" : "")
				// .append(null != req.getPayItemId() ? " and c.id = " + req.getPayItemId() :
				// "")
				.append(StringUtils.isNotEmpty(req.getComment()) ? " and a.comment like '%" + req.getComment() + "%'"
						: "");
		sql.append(" order by a.receivableDate desc");

		return sql.toString();
	}

	public IResponse exportSendDisc(CommunityReceivablesReq req, HttpServletResponse response) {
		GenericResponse res = new GenericResponse();
		if (null != req.getBankDepositBatchId() && null != req.getTemplateType()) {
			CommunityBankDepositBatchEntity bankDepositBatch = communityBankDepositBatchDao
					.get(req.getBankDepositBatchId());
			bankDepositBatch.setTemplateType(req.getTemplateType());
			if (null != bankDepositBatch) {

				HSSFWorkbook wb = new HSSFWorkbook();
				HSSFSheet wbSheet = wb.createSheet("Sheet1");
				HSSFCellStyle style = wb.createCellStyle();
				// 设置字体
				HSSFFont font = wb.createFont();
				font.setFontName("宋体");
				font.setFontHeightInPoints((short) 9);
				HSSFCell cellHead = null;
				int i = 1;
				BigDecimal total = new BigDecimal(0);
				HSSFRow row = null;
				StringBuilder hql = new StringBuilder(
						"select distinct a from CommunityBankDepositRecordEntity a inner join a.estate b "
								+ "where  a.bankDepositBatch.id=" + bankDepositBatch.getId())
										.append(" ORDER BY b.unitCode asc");
				List<CommunityBankDepositRecordEntity> bankDepositRecordList = communityBankDepositRecordDao
						.getListByHql(hql.toString(), "");
				if (null == bankDepositRecordList) {
					bankDepositRecordList = new ArrayList<>();
				}
				switch (bankDepositBatch.getTemplateType()) {
				case 0:// 工行
					wbSheet.setColumnWidth(0, 5000);
					wbSheet.setColumnWidth(1, 6000);
					wbSheet.setColumnWidth(2, 5000);
					wbSheet.setColumnWidth(3, 5000);
					wbSheet.setColumnWidth(4, 5000);
					wbSheet.setColumnWidth(5, 5000);
					row = wbSheet.createRow(0);
					row.setHeight((short) 420);
					cellHead = row.createCell(0);
					cellHead.setCellValue("姓名");
					cellHead.setCellStyle(style);
					cellHead = row.createCell(1);
					cellHead.setCellValue("卡号");
					cellHead = row.createCell(2);
					cellHead.setCellValue("应处理金额");
					cellHead = row.createCell(3);
					cellHead.setCellValue("备注");
					cellHead = row.createCell(4);
					cellHead.setCellValue("实处理金额");
					cellHead = row.createCell(5);
					cellHead.setCellValue("处理标志");
					i = 1;
					for (CommunityBankDepositRecordEntity record : bankDepositRecordList) {
						BigDecimal receivableAmountTotal = record.getOutstandingAmount();
						CommunityEstateEntity estate = record.getEstate();
						row = wbSheet.createRow(i);
						row.setHeight((short) 420);
						cellHead = row.createCell(0);
						cellHead.setCellValue(estate.getPaymentAccount().getAccountName());
						cellHead = row.createCell(1);
						cellHead.setCellValue(estate.getPaymentAccount().getBankAccount());
						cellHead = row.createCell(2);
						// cellHead.setCellValue(receivableAmountTotal.doubleValue());
						if (receivableAmountTotal.toString().contains(".")) {// 要求带小数点后两位，没有就补0
							String[] radixPointStr = receivableAmountTotal.toString().split("\\.");
							if (radixPointStr[1].length() == 1) {
								cellHead.setCellValue(receivableAmountTotal.toString() + "0");
							} else {
								cellHead.setCellValue(receivableAmountTotal.toString());
							}
						} else {
							cellHead.setCellValue(receivableAmountTotal.toString() + ".00");
						}

						cellHead = row.createCell(3);
						cellHead.setCellValue(estate.getUnitCode());
						cellHead = row.createCell(4);
						cellHead.setCellValue("");
						cellHead = row.createCell(5);
						cellHead.setCellValue("");
						i++;
					}
					break;
				case 1:// 农业银行
					wbSheet.setColumnWidth(13, 5000);
					wbSheet.setColumnWidth(4, 6000);
					i = 1;
					total = new BigDecimal(0);
					for (CommunityBankDepositRecordEntity record : bankDepositRecordList) {
						BigDecimal receivableAmountTotal = record.getOutstandingAmount();
						CommunityEstateEntity estate = record.getEstate();
						row = wbSheet.createRow(i);
						row.setHeight((short) 420);
						cellHead = row.createCell(0);
						cellHead.setCellValue(getSerialNumber(i + "", 6));
						cellHead = row.createCell(4);
						cellHead.setCellValue(estate.getPaymentAccount().getBankAccount());
						cellHead = row.createCell(5);
						cellHead.setCellValue(estate.getPaymentAccount().getAccountName());
						cellHead = row.createCell(10);
						double doubleVal = (receivableAmountTotal.multiply(new BigDecimal(100)).setScale(0,
								BigDecimal.ROUND_DOWN)).doubleValue();
						cellHead.setCellValue(doubleVal);
						cellHead = row.createCell(11);
						cellHead.setCellValue("CNY");
						cellHead = row.createCell(13);
						cellHead.setCellValue(estate.getUnitCode());
						total = total.add((receivableAmountTotal)
								.multiply(new BigDecimal(100).setScale(0, BigDecimal.ROUND_DOWN)));
						i++;
					}
					row = wbSheet.createRow(0);
					cellHead = row.createCell(0);
					cellHead.setCellValue("S");
					cellHead = row.createCell(2);
					cellHead.setCellValue(DateUtil.format(new Date(), 0).replaceAll("-", ""));
					cellHead = row.createCell(3);
					cellHead.setCellValue(i - 1);
					cellHead = row.createCell(4);
					cellHead.setCellValue(total.doubleValue());
					break;
				case 2:// 农村信用社
					wbSheet.setColumnWidth(1, 5000);
					wbSheet.setColumnWidth(2, 6000);
					wbSheet.setColumnWidth(3, 5000);
					i = 2;
					total = new BigDecimal(0);
					for (CommunityBankDepositRecordEntity record : bankDepositRecordList) {
						CommunityEstateEntity estate = record.getEstate();
						BigDecimal receivableAmountTotal = record.getOutstandingAmount();
						row = wbSheet.createRow(i);
						row.setHeight((short) 420);
						cellHead = row.createCell(0);
						cellHead.setCellValue((i - 1) + "");
						cellHead = row.createCell(1);
						cellHead.setCellValue(estate.getPaymentAccount().getAccountName());
						cellHead = row.createCell(2);
						cellHead.setCellValue(estate.getPaymentAccount().getBankAccount());
						cellHead = row.createCell(3);
						double doubleVal = receivableAmountTotal.doubleValue();
						cellHead.setCellValue(doubleVal);
						cellHead = row.createCell(4);
						cellHead.setCellValue(estate.getUnitCode());
						total = total.add((receivableAmountTotal));
						i++;
					}
					row = wbSheet.createRow(0);
					cellHead = row.createCell(0);
					cellHead.setCellValue("序号");
					cellHead = row.createCell(1);
					cellHead.setCellValue("账号名称");
					cellHead = row.createCell(2);
					cellHead.setCellValue("账号/卡号");
					cellHead = row.createCell(3);
					cellHead.setCellValue("金额");
					cellHead = row.createCell(4);
					cellHead.setCellValue("房号");
					row = wbSheet.createRow(1);
					cellHead = row.createCell(0);
					cellHead.setCellValue((i - 2) + "");
					cellHead = row.createCell(1);
					double totalVal = total.doubleValue();
					cellHead.setCellValue(totalVal);
					break;
				case 3:// 建设银行
					wbSheet.setColumnWidth(0, 3000);
					wbSheet.setColumnWidth(1, 6000);
					wbSheet.setColumnWidth(2, 3000);
					wbSheet.setColumnWidth(3, 3000);
					wbSheet.setColumnWidth(4, 6000);
					wbSheet.setColumnWidth(5, 6000);
					wbSheet.setColumnWidth(6, 6000);
					wbSheet.setColumnWidth(7, 6000);
					wbSheet.setColumnWidth(8, 3000);
					wbSheet.setColumnWidth(9, 6000);
					wbSheet.setColumnWidth(10, 4000);
					row = wbSheet.createRow(0);
					row.setHeight((short) 420);
					cellHead = row.createCell(0);
					cellHead.setCellValue("序号（必填）");
					cellHead = row.createCell(1);
					cellHead.setCellValue("账号（必填）");
					cellHead = row.createCell(2);
					cellHead.setCellValue("户名（必填）");
					cellHead = row.createCell(3);
					cellHead.setCellValue("金额（必填）");
					cellHead = row.createCell(4);
					cellHead.setCellValue("跨行标识（选填 建行填0 他行填1）");
					cellHead = row.createCell(5);
					cellHead.setCellValue("行名（跨行业务与联行行号不能同时为空）");
					cellHead = row.createCell(6);
					cellHead.setCellValue("联行行号（跨行业务与行名不能同时为空）");
					cellHead = row.createCell(7);
					cellHead.setCellValue("多方协议号（跨行代收必填）");
					cellHead = row.createCell(8);
					cellHead.setCellValue("标识号（选填）");
					cellHead = row.createCell(9);
					cellHead.setCellValue("摘要（选填 显示在收款账户流水明细中）");
					cellHead = row.createCell(10);
					cellHead.setCellValue("备注（选填）");
					i = 1;
					for (CommunityBankDepositRecordEntity record : bankDepositRecordList) {
						CommunityEstateEntity estate = record.getEstate();
						BigDecimal receivableAmountTotal = record.getOutstandingAmount();
						row = wbSheet.createRow(i);
						row.setHeight((short) 420);
						cellHead = row.createCell(0);
						cellHead.setCellValue(i);
						cellHead = row.createCell(1);
						cellHead.setCellValue(estate.getPaymentAccount().getBankAccount());
						cellHead = row.createCell(2);
						cellHead.setCellValue(estate.getPaymentAccount().getAccountName());
						cellHead = row.createCell(3);
						cellHead.setCellValue(receivableAmountTotal.doubleValue());
						cellHead = row.createCell(4);
						cellHead.setCellValue("");
						cellHead = row.createCell(5);
						cellHead.setCellValue("");
						cellHead = row.createCell(9);
						cellHead.setCellValue(estate.getUnitCode());
						i++;
					}
					break;
				case 4:// 全国平台
					wbSheet.setColumnWidth(0, 4000);
					wbSheet.setColumnWidth(1, 4000);
					wbSheet.setColumnWidth(2, 4000);
					wbSheet.setColumnWidth(3, 4000);
					wbSheet.setColumnWidth(4, 4000);
					wbSheet.setColumnWidth(5, 6000);
					wbSheet.setColumnWidth(6, 4000);
					wbSheet.setColumnWidth(7, 6000);
					row = wbSheet.createRow(0);
					row.setHeight((short) 420);
					cellHead = row.createCell(0);
					cellHead.setCellValue("委托日期");
					cellHead = row.createCell(1);
					cellHead.setCellValue("客户编号");
					cellHead = row.createCell(2);
					cellHead.setCellValue("付款人开户行行号");
					cellHead = row.createCell(3);
					cellHead.setCellValue("付款人账号");
					cellHead = row.createCell(4);
					cellHead.setCellValue("付款人名称");
					cellHead = row.createCell(5);
					cellHead.setCellValue("付款协议号");
					cellHead = row.createCell(6);
					cellHead.setCellValue("金额");
					cellHead = row.createCell(7);
					cellHead.setCellValue("附言（可选）");
					i = 1;
					String time = DateUtil.format(new Date(), 0).replaceAll("-", "");
					Map<String, String> bankNoMap = new HashMap<>();

					List<DictionaryDataEntity> dictionaryDataList = dictionaryDataDao
							.getListByHql("select distinct a from DictionaryDataEntity a "
									+ " inner join a.dictionary  b where b.directoryCode='bankNo' and a.state=1", "");
					if (null != dictionaryDataList && dictionaryDataList.size() > 0) {
						dictionaryDataList.forEach(o -> {
							bankNoMap.put(o.getDataName(), o.getDataKey());
						});
					} else {
						res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
						res.setRetInfo("请在数据字典里配置“付款人开户行行号”！");
					}

					for (CommunityBankDepositRecordEntity record : bankDepositRecordList) {
						CommunityEstateEntity estate = record.getEstate();
						BigDecimal receivableAmountTotal = record.getOutstandingAmount();
						ReservedFieldVo reservedField = null;
						row = wbSheet.createRow(i);
						if (StringUtils.isNotEmpty(estate.getReservedField())) {
							try {
								reservedField = mapper.readValue(estate.getReservedField(), ReservedFieldVo.class);
							} catch (JsonMappingException e) {
								e.printStackTrace();
							} catch (JsonProcessingException e) {
								e.printStackTrace();
							}
						}
						if (null != reservedField) {
							cellHead = row.createCell(1);
							cellHead.setCellValue(reservedField.getAccountCustomerId());
							cellHead = row.createCell(5);
							cellHead.setCellValue(reservedField.getAccountContractNo());
						} else {
							cellHead = row.createCell(1);
							cellHead.setCellValue("");
							cellHead = row.createCell(5);
							cellHead.setCellValue("");
						}
						row.setHeight((short) 420);
						cellHead = row.createCell(0);
						cellHead.setCellValue(time);
						cellHead = row.createCell(2);
						cellHead.setCellValue(bankNoMap.containsKey(estate.getPaymentAccount().getBankName())
								? bankNoMap.get(estate.getPaymentAccount().getBankName())
								: "");
						cellHead = row.createCell(3);
						cellHead.setCellValue(estate.getPaymentAccount().getBankAccount());
						cellHead = row.createCell(4);
						cellHead.setCellValue(estate.getPaymentAccount().getAccountName());
						cellHead = row.createCell(6);
						cellHead.setCellValue(receivableAmountTotal.doubleValue());
						cellHead = row.createCell(7);
						cellHead.setCellValue(estate.getUnitCode());
						i++;
					}
					break;
				default:
					break;
				}
				try {
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
					ExcelExportUtil.export(response, wb, "");
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	public IResponse getExportSendDiscList(CommunityReceivablesReq req) {
		GetExportSendDiscListRes res = new GetExportSendDiscListRes();
		if (null != req.getBankDepositBatchId()) {
			/*
			 * CommunityBankDepositBatchEntity bankDepositBatch =
			 * communityBankDepositBatchDao .get(req.getBankDepositBatchId()); if (null !=
			 * bankDepositBatch) { res.setPageSize(null != req.getPageSize() ?
			 * req.getPageSize() : 10); res.setCurrentPage(null != req.getRequestPage() ?
			 * req.getRequestPage() : 1);
			 * 
			 * Map<Integer, Map<String, Object>> dataMap = null; dataMap =
			 * getReceivableData(req, bankDepositBatch); int totalCount = 0; if (null !=
			 * dataMap) { int pageSize = 0; int size = req.getPageSize() *
			 * (req.getRequestPage() - 1); for (Integer key : dataMap.keySet()) { if (size
			 * <= 0 && pageSize < req.getPageSize()) { pageSize++; ExportSendDiscForm
			 * exportSendDiscForm = new ExportSendDiscForm(); Map<String, Object> map =
			 * dataMap.get(key); exportSendDiscForm.setBankName((String)
			 * map.get("bankName"));
			 * exportSendDiscForm.setBankAccount(map.get("bankAccount").toString());
			 * exportSendDiscForm.setAccountName(map.get("accountName").toString());
			 * exportSendDiscForm.setEstateId(key);
			 * exportSendDiscForm.setUnitCode(map.get("unitCode").toString());
			 * exportSendDiscForm.setReceivableAmount(map.get("receivableAmountTotal").
			 * toString());
			 * 
			 * res.getExportSendDiscList().add(exportSendDiscForm); } else if (pageSize >=
			 * req.getPageSize()) { break; } size--; } totalCount = dataMap.size(); }
			 * res.setPageSize(req.getPageSize()); res.setCurrentPage(req.getRequestPage());
			 * res.setTotal(totalCount == 0 ? 0 : (totalCount - 1) / req.getPageSize() + 1);
			 * res.setTotalResult(totalCount); res.setRet(ResponseContext.RES_SUCCESS_CODE);
			 * res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO); } else {
			 * res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
			 * res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO); }
			 */
			Page<CommunityBankDepositRecordEntity> page = new Page<CommunityBankDepositRecordEntity>();
			page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
			page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
			page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
			StringBuilder hql = new StringBuilder(
					"select distinct a from CommunityBankDepositRecordEntity a where 1=1 ");
			hql.append(null != req.getBankDepositBatchId() ? " and a.bankDepositBatch.id=" + req.getBankDepositBatchId()
					: "");
			hql.append(" ORDER BY a.id desc");
			page = communityBankDepositRecordDao.queryPage(page, hql.toString());

			res.setTotalResult(page.getTotalCount());
			res.setPageSize(page.getPageSize());
			res.setCurrentPage(page.getCurrentPage());
			res.setTotal(page.getTotalPage());
			page.getResultList().forEach(o -> {
				ExportSendDiscForm exportSendDiscForm = new ExportSendDiscForm();
				exportSendDiscForm.setBankName(o.getEstate().getPaymentAccount().getBankName());
				exportSendDiscForm.setBankAccount(o.getEstate().getPaymentAccount().getBankAccount());
				exportSendDiscForm.setAccountName(o.getEstate().getPaymentAccount().getAccountName());
				exportSendDiscForm.setEstateId(o.getEstate().getId());
				exportSendDiscForm.setUnitCode(o.getEstate().getUnitCode());
				exportSendDiscForm.setReceivableAmount(o.getDepositAmount().toString());

				res.getExportSendDiscList().add(exportSendDiscForm);
			});
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	/**
	 * 生成预收
	 */
	@Audit(operate = "生成预收")
	@Override
	public IResponse addPreReceivables(CommunityPreReceivablesReq req) {
		GetCommunityReceivablesListRes res = new GetCommunityReceivablesListRes();
		Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> result = new TreeMap<>();
		Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> pre = new TreeMap<>();
		DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		if (null == req.getPreMonths() && StringUtils.isEmpty(req.getPreEndMonth())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "预收月份数量和预收结束月份不能同时为空！");
			return res;
		}

		LocalDate preStart = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
		LocalDate perEnd = null;
		if (null == req.getPreMonths()) {
			if (StringUtils.isNotEmpty(req.getPreBeginMonth())) {
				preStart = LocalDate.parse(req.getPreBeginMonth(), dtf).with(TemporalAdjusters.firstDayOfMonth());
			}
			perEnd = LocalDate.parse(req.getPreEndMonth(), dtf).with(TemporalAdjusters.lastDayOfMonth());
			;
		} else {
			if (req.getPreMonths().intValue() < 1) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "预收月份数量不能少于1！");
				return res;
			} else {
				req.setPreMonths(req.getPreMonths() - 1);
			}
			perEnd = preStart
					.plus(null != req.getPreMonths() ? req.getPreMonths() : communityContextInfo.advanceReceiptsMonth,
							ChronoUnit.MONTHS)
					.with(TemporalAdjusters.lastDayOfMonth());
		}

		if (null == req.getEstateId() && StringUtils.isEmpty(req.getUnitCode())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "单元编号或单元ID不能同时为空！");
			return res;
		}
		Integer estateId = req.getEstateId();

		if (StringUtils.isNotEmpty(req.getUnitCode())) {
			CommunityEstateEntity estate = communityEstateDao
					.getUniqueByHql("from CommunityEstateEntity a where a.unitCode='" + req.getUnitCode() + "'");
			if (null != estate) {
				estateId = estate.getId();
			}
		}

		// 开始计算预收金额，从请求当月起至系统默认配置预收时间截止
		Map<String, String> cycleMonth = CommunityDateUtil.cycleMonth(preStart.toString(), perEnd.toString());

		for (String o : cycleMonth.keySet()) {
			calManagerFee(LocalDate.parse(o), LocalDate.parse(cycleMonth.get(o)), estateId, req.getUnitCode(),
					req.getPayItemIds(), req.getChargeCategoryIds(), 3, pre);
		}
		result = CommunityCache.addReceivableCache(result, pre);
		if (req.getReturnNeedPay() == 1) {

			// 获取该用户历史收费记录
			StringBuilder sql = new StringBuilder(
					"select a from CommunityReceivablesEntity a inner join a.estate b inner join a.payItem c where b.id="
							+ estateId);

			sql.append(" and a.receivableAmount - a.receivedAmount > 0");
			sql.append(" and c.payDate > 0");
			// 设置其它相关参数
			// sql.append(StringUtils.isNotEmpty(req.getPayItemIds()) ? " and c.id in (" +
			// req.getPayItemIds() +")" : "");
			sql.append(" order by a.receivableDate desc");

			List<CommunityReceivablesEntity> receivablesList = communityReceivablesDao.getListByHql(sql.toString());
			for (CommunityReceivablesEntity o : receivablesList) {
				CommunityCache.putReceivableCache(o, result);
			}
		}

		// 把生成的应收存在缓存中
		Cache cache = cacheManager.getCache("prePaymentReceivableCache");
		result.values().forEach(o -> {
			o.values().forEach(k -> {
				k.values().forEach(i -> {
					i.forEach(r -> {
						logger.info(r.getId() + "加入缓存");
						cache.put(r.getId(), r);
					});
				});
			});
		});
		if (null != req.getResultType() && req.getResultType().equals(2)) {
			result = CommunityCache.parseReceivableEstateList2Name(result);
			if (result.containsKey(estateId)) {
				Map<String, Set<CommunityReceivablesForm>> parseReceivablesFormMap = parseReceivablesToNameFormMap(
						result.get(estateId), estateId);
				res.getReceivablesToNameList().put(estateId, parseReceivablesFormMap);
				CommunityPage<Map<String, Set<CommunityReceivablesForm>>> page = CommunityCache
						.getPage(parseReceivablesFormMap, req.getPageSize(), req.getRequestPage());
				res.setCurrentPage(page.getCurrentPage());
				res.setPageSize(page.getPageSize());
				res.setTotal(page.getTotalPage());
				res.setTotalResult(page.getTotalCount());
				res.getReceivablesToNameList().put(estateId, page.getResultList());

				page.getResultList().keySet().forEach(p -> {
					Set<CommunityReceivablesForm> set = page.getResultList().get(p);
					for (CommunityReceivablesForm receivableForm : set) {
						BigDecimal receivaedAmount = null == receivableForm.getReceivedAmount() ? BigDecimal.ZERO
								: new BigDecimal(receivableForm.getReceivedAmount());
						BigDecimal receivableAmount = null == receivableForm.getReceivableAmount() ? BigDecimal.ZERO
								: new BigDecimal(receivableForm.getReceivableAmount());
						BigDecimal notReceivedAmount = null == receivableForm.getNotReceivedAmount() ? BigDecimal.ZERO
								: new BigDecimal(receivableForm.getNotReceivedAmount());
						res.setTotalReceivedAmount(CommonUtil.addAmount(res.getTotalReceivedAmount(), receivaedAmount));
						res.setTotalReceivableAmount(
								CommonUtil.addAmount(res.getTotalReceivableAmount(), receivableAmount));
						res.setTotalNotReceivedAmount(
								CommonUtil.addAmount(res.getTotalNotReceivedAmount(), notReceivedAmount));

					}
				});
			}
		} else {
			if (result.containsKey(estateId)) {
				res.getReceivablesList().put(estateId, parseReceivablesFormMap(result.get(estateId), estateId));
				CommunityPage<Map<String, Map<String, Set<CommunityReceivablesForm>>>> page = CommunityCache.getPage(
						parseReceivablesFormMap(result.get(estateId), estateId), req.getPageSize(),
						req.getRequestPage());
				res.setCurrentPage(page.getCurrentPage());
				res.setPageSize(page.getPageSize());
				res.setTotal(page.getTotalPage());
				res.setTotalResult(page.getTotalCount());
				res.getReceivablesList().put(estateId, page.getResultList());
			}
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	/**
	 * 生成违约金
	 */
	@Audit(operate = "生成违约金")
	@Override
	public IResponse addBreachReceivables(CommunityAddBreachReq req) {
		GenericResponse res = new GenericResponse();
		Date breanchDate = null;
		if (StringUtils.isNotEmpty(req.getBreachEndDate())) {
			try {
				breanchDate = DateUtil.parse(req.getBreachEndDate(), 0);
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		if (null == breanchDate) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "截止日期不能为空，或格式不正确！");
			return res;
		}

		// 如果手工生成违约金的截止日期早于当前月，需要删除截止日期后的月份已经生成的违约金，已经发生了收款的违约金不删除。
		// 仅支持按单元回退到本月之前的违约金
		if (breanchDate.before(DateUtil.getMonthDay(DateUtil.resetStartOfDay(new Date()), 1, 0))) {
			if (StringUtils.isNotEmpty(req.getEstateIds()) && StringUtils.isEmpty(req.getDistrictIds())
					&& StringUtils.isEmpty(req.getBuildingIds())) {
				StringBuilder sbDel = new StringBuilder(
						"SELECT r FROM CommunityReceivablesEntity r INNER JOIN r.estate e  WHERE "
								+ "	r.breachReceivablesId IS NOT NULL" + "	AND r.lockMark <> 1"
								+ "	AND r.receivedAmount = 0" + "  AND r.startTime > '"
								+ DateUtil.formatLongFormat(breanchDate) + "'" + "	AND e.id IN (" + req.getEstateIds()
								+ ")");
				List<CommunityReceivablesEntity> deleteList = communityReceivablesDao.getListByHql(sbDel.toString());
				deleteList.forEach(o -> {
					if (StringUtils.isNotEmpty(req.getPayItemIds())) {
						// 只删除指定的收费项目生成的违约金
						CommunityReceivablesEntity manageReceivables = communityReceivablesDao
								.get(o.getBreachReceivablesId());
						String payItemId = manageReceivables.getPayItem().getId() + "";
						ArrayList<String> payItemIdList = new ArrayList<String>();
						Collections.addAll(payItemIdList, req.getPayItemIds().split(","));
						if (payItemIdList.contains(payItemId)) {
							CommunityCache.removeReceivableCache(o, CommunityCache.receivableCalEstateList);
							o.setEstate(null);
							o.setPayItem(null);
							o.setBankDepositRecord(null);
							communityReceivablesDao.delete(o);
						}
					} else {
						// 不指定收费项目
						CommunityCache.removeReceivableCache(o, CommunityCache.receivableCalEstateList);
						o.setEstate(null);
						o.setPayItem(null);
						o.setBankDepositRecord(null);
						communityReceivablesDao.delete(o);
					}
				});
			} else {
				res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
				res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "暂时只支持指定单元回退到上个月的违约金！");
				return res;
			}
		}

		List<CommunityReceivablesEntity> newBreachReceivablesEntityList = generateBreachReceivablesByEstate(
				req.getDistrictIds(), req.getBuildingIds(), req.getEstateIds(), req.getPayItemIds(), breanchDate);

		// 更新用户违约金，如果该单元不存在违约金记录则插入，否则更新，已经发生过收款的违约金不受影响，数量较多时需要每100条记录批量提交
		for (int i = 0; i < newBreachReceivablesEntityList.size(); i++) {
			CommunityReceivablesEntity receivable = newBreachReceivablesEntityList.get(i);
			if (receivable.getId() != null && receivable.getReceivableAmount().compareTo(BigDecimal.ZERO) == 0
					&& receivable.getReceivedAmount().compareTo(BigDecimal.ZERO) == 0
					&& receivable.getLockMark() == 0) {
				// 删除违约金额为0，且未发生过收款的应收
				receivable.setEstate(null);
				receivable.setPayItem(null);
				communityReceivablesDao.delete(receivable);
			} else {
				communityReceivablesDao.saveOrUpdate(receivable);
			}
			if ((i+1)% 300 == 0) {
				communityReceivablesDao.flush();
				communityReceivablesDao.clear();
			}
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	/**
	 * 根据小区、楼栋、单元Id，收费项目ID生成当月生成的管理费，影片单元结果为前三个条件字段的并集，发生过收款的应收不受影响
	 */
	@Audit(operate = "生成单元当月管理费应收")
	@Override
	public IResponse addManagerReceivables(CommunityAddReceivablesForEstateReq req) {
		GenericResponse res = new GenericResponse();
		if (StringUtils.isEmpty(req.getEstateIds()) && StringUtils.isEmpty(req.getBuildingIds())
				&& StringUtils.isEmpty(req.getDistrictIds())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "小区id、楼栋id或单元id，不能全为空！");
			return res;
		}
		if (req.getStartDate() == null) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "开始时间不能为空！");
			return res;
		}
		if (req.getEndDate() == null) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "结束时间不能为空！");
			return res;
		}

		LocalDate localStartDate = CommunityDateUtil.dateToLocalDate(req.getStartDate()).with(TemporalAdjusters.firstDayOfMonth());
		LocalDate localEndDate = CommunityDateUtil.dateToLocalDate(req.getEndDate()).with(TemporalAdjusters.lastDayOfMonth());
		Map<String, String> cycleMonth = CommunityDateUtil.cycleMonth(localStartDate.toString(),
				localEndDate.toString());

		StringBuilder conditionHql = new StringBuilder();
		conditionHql.append(StringUtils.isNotEmpty(req.getEstateIds()) ? "e.id in (" + req.getEstateIds() + ")" : "");
		conditionHql.append(
				StringUtils.isNotEmpty(req.getBuildingIds()) ? "or b.id in (" + req.getBuildingIds() + ")" : "");
		conditionHql.append(
				StringUtils.isNotEmpty(req.getDistrictIds()) ? "or d.id in (" + req.getDistrictIds() + ")" : "");
		String conditonStr = conditionHql.toString();
		if (conditonStr.startsWith("or ")) {
			conditonStr = conditonStr.substring(3);
		}
		StringBuilder selHql = new StringBuilder(
				"select e from CommunityEstateEntity e inner join e.building b inner join b.district d");
		selHql.append(StringUtils.isNotEmpty(selHql) ? (" where " + conditonStr) : "");
		List<CommunityEstateEntity> estateList = communityEstateDao.getListByHql(selHql.toString());
		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();
		// 逐个单元生成管理费和违约金
		for (int i = 0; i < estateList.size(); i++) {
			CommunityEstateEntity estateEntity = estateList.get(i);
			// 生成管理费
			List<CommunityReceivablesEntity> totalManagerReceivablesList = new ArrayList<CommunityReceivablesEntity>();
			for (String o : cycleMonth.keySet()) {
				totalManagerReceivablesList.addAll(calManageFeeByEstate(estateEntity.getId(), req.getPayItemIds(),
						LocalDate.parse(o), LocalDate.parse(cycleMonth.get(o)), session));
			}
			for (CommunityReceivablesEntity receivables : totalManagerReceivablesList) {
				// 已经发生成收款的应收不能重新生成
				if (receivables.getReceivedAmount().compareTo(BigDecimal.ZERO) == 0) {
					communityReceivablesDao.merge(receivables);
					CommunityCache.putReceivableCache(receivables, CommunityCache.receivableCalEstateList);
				}
			}

			if (i % 50 == 0) {
				communityReceivablesDao.flush();
				communityReceivablesDao.clear();
			}
		}
		if (null != session) {
			session.close();
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	/**
	 * 根据小区、楼栋、单元Id，收费项目ID生成当月生成租金费用，影片单元结果为前三个条件字段的并集，发生过收款的应收不受影响
	 */
	@Audit(operate = "生成租金应收")
	@Override
	public IResponse addRentReceivables(CommunityAddReceivablesForEstateReq req) {
		GenericResponse res = new GenericResponse();
		if (StringUtils.isEmpty(req.getEstateIds()) && StringUtils.isEmpty(req.getBuildingIds())
				&& StringUtils.isEmpty(req.getDistrictIds())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "小区id、楼栋id或单元id，不能全为空！");
			return res;
		}
		if (req.getStartDate() == null) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "开始时间不能为空！");
			return res;
		}
		if (req.getEndDate() == null) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "结束时间不能为空！");
			return res;
		}

		StringBuilder conditionHql = new StringBuilder();
		conditionHql.append(StringUtils.isNotEmpty(req.getEstateIds()) ? "e.id in (" + req.getEstateIds() + ")" : "");
		conditionHql.append(
				StringUtils.isNotEmpty(req.getBuildingIds()) ? "or b.id in (" + req.getBuildingIds() + ")" : "");
		conditionHql.append(
				StringUtils.isNotEmpty(req.getDistrictIds()) ? "or d.id in (" + req.getDistrictIds() + ")" : "");
		String conditonStr = conditionHql.toString();
		if (conditonStr.startsWith("or ")) {
			conditonStr = conditonStr.substring(3);
		}
		StringBuilder selHql = new StringBuilder(
				"select e from CommunityEstateEntity e inner join e.building b inner join b.district d");
		selHql.append(StringUtils.isNotEmpty(conditonStr) ? (" where e.rentState != 0 and " + conditonStr)
				: " where e.rentState != 0");
		List<CommunityEstateEntity> estateList = communityEstateDao.getListByHql(selHql.toString());
		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();
		// 逐个单元生成租金
		for (int i = 0; i < estateList.size(); i++) {
			CommunityEstateEntity estateEntity = estateList.get(i);
			// 判断当前合同是长租还是短租
			String memberPropertySql =  "SELECT mp FROM CommunityMemberPropertyEntity mp WHERE mp.isCurrentOwner = 1 and mp.isCurrentMember = 1 and mp.property.id = " + estateEntity.getId();
			CommunityMemberPropertyEntity currentMemberProperty = communityMemberPropertyDao.getUniqueByHql(memberPropertySql);
			// 生成租金
			List<CommunityReceivablesEntity> totalManagerReceivablesList = new ArrayList<CommunityReceivablesEntity>();
			if(currentMemberProperty != null && currentMemberProperty.getRentType() != null) {
				if(currentMemberProperty.getRentType() == 1) {
					// 如果是短租，按每天生成
					LocalDate localStartDate = CommunityDateUtil.dateToLocalDate( req.getStartDate());
					LocalDate localEndDate = CommunityDateUtil.dateToLocalDate( req.getEndDate());
		
					List<LocalDate> interSection = CommunityDateUtil.getInterSection(CommunityDateUtil.dateToLocalDate(currentMemberProperty.getBillingDate()), CommunityDateUtil.dateToLocalDate(currentMemberProperty.getEndDate()), localStartDate, localEndDate);
					if(interSection.size()>0) {
						for (LocalDate day = interSection.get(0); day.isBefore(interSection.get(1)) || day.isEqual(interSection.get(1));) {
							totalManagerReceivablesList.addAll(calRentByEstate(estateEntity.getId(), req.getPayItemIds(),
									day, day, CommunityContext.ESTATE_RENT_TYPE_SHORT, session));
							day = day.plus(1, ChronoUnit.DAYS);
						}
					}
				} 
				else {
					// 如果是长租，
					LocalDate localStartDate = CommunityDateUtil.dateToLocalDate( req.getStartDate()).with(TemporalAdjusters.firstDayOfMonth());
					LocalDate localEndDate = CommunityDateUtil.dateToLocalDate( req.getEndDate()).with(TemporalAdjusters.lastDayOfMonth());
					Map<String, String> cycleMonth = CommunityDateUtil.cycleMonth(localStartDate.toString(),
							localEndDate.toString());
					for (String o : cycleMonth.keySet()) {
						totalManagerReceivablesList.addAll(calRentByEstate(estateEntity.getId(), req.getPayItemIds(),
								LocalDate.parse(o), LocalDate.parse(cycleMonth.get(o)), CommunityContext.ESTATE_RENT_TYPE_LONG, session));
					}
				}
			} 
			else if(StringUtils.isNotEmpty(req.getEstateIds()) ){
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "请检查合同信息！");
				return res;
			}

			for (CommunityReceivablesEntity receivables : totalManagerReceivablesList) {
				// 已经发生成收款的应收不能重新生成
				if (receivables.getReceivedAmount().compareTo(BigDecimal.ZERO) == 0) {
					communityReceivablesDao.merge(receivables);
					CommunityCache.putReceivableCache(receivables, CommunityCache.receivableCalEstateList);
				}
			}

			if (i % 50 == 0) {
				communityReceivablesDao.flush();
				communityReceivablesDao.clear();
			}
		}
		if (null != session) {
			session.close();
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	private Collection<? extends CommunityReceivablesEntity> calRentByEstate(Integer estateId, String payItemIds,
			LocalDate startDate, LocalDate endDate, Integer rentType , Session session) {
		List<CommunityReceivablesEntity> result = new ArrayList<CommunityReceivablesEntity>();

		if (startDate == null) {
			startDate = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
		}
		if (endDate == null) {
			endDate = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
		}

		// 生成出租公寓租金及管理费
		StringBuilder baseSql = new StringBuilder(
				"SELECT a.id,a.unitcode,a.additionalArea,a.chargingArea,d.parentPropertyId,a.paymentaccountId,"
						+ "b.payItemsId,c.chargeCategory,c.comment,c.endtime,c.feecaltype,c.itemsname,c.price,c.priceunit,c.starttime,"
						+ "a.acceptanceDate,d.billingDate,c.payDate,d.isCurrentMember,d.iscurrentowner,d.memberid,d.terminationDate,d.endDate,a.rentState,d.id AS memberPropertyId "
						+ "FROM t_community_property a LEFT JOIN t_community_member_property d ON a.id = d.propertyid, "
						+ "t_community_property_pay_items b,t_community_pay_items c"
						+ " WHERE a.id = b.propertyid AND b.payitemsid = c.id AND c.state = 1"
						+ " AND c.chargeCategory = 9 AND d.iscurrentowner = 1 AND d.isCurrentMember = 1"
						+ " AND d.memberType = 2"
						+ " AND d.rentType = " + rentType
						+ " AND a.rentState IN (1,2)"
						+ " AND a.specialFeeFlag IN (0,1,2,3) AND c.starttime <= '"
						+ startDate.toString() + " 00:00:00' AND c.endTime>='" + endDate.toString() + " 00:00:00'");
		baseSql.append(StringUtils.isNotEmpty(payItemIds) ? (" AND c.id IN (" + payItemIds + ")") : "");
		StringBuilder estateObjectSql = new StringBuilder(baseSql.toString());
		estateObjectSql.append(null != estateId ? " AND a.id = " + estateId : "");

		// 把车位收费也加进来
		estateObjectSql.append(" union ");
		estateObjectSql.append(baseSql.toString());
		estateObjectSql.append(null != estateId ? " AND d.parentPropertyId = " + estateId : "");

		@SuppressWarnings("unchecked")
		List<Object[]> resultList = session.createNativeQuery(estateObjectSql.toString()).list();
		// 互斥组
		Set<String> groupFeeSet = new HashSet<String>(); 
		for (Object[] temp : resultList) {
			EstateReceivablesVo o = EstateReceivablesVo.getEstateReceivablesVo(temp);
			if ((null == o.getParentPropertyId() || (null != o.getParentPropertyId()
					&& (null == o.getEndDate() || o.getEndDate().isAfter(endDate) || o.getEndDate().isEqual(endDate))))
					&& (((int) (o.getBillingDate().toEpochDay() - startDate.toEpochDay()) >= 0
							&& (int) (endDate.toEpochDay() - o.getBillingDate().toEpochDay()) >= 0)
							|| ((int) (o.getBillingDate().toEpochDay() - startDate.toEpochDay()) <= 0))) {
				CommunityReceivablesEntity receivables = new CommunityReceivablesEntity();
				CommunityEstateEntity estate = getEstate(
						null != o.getParentPropertyId() ? o.getParentPropertyId() : o.getId());
				if (estate.getSpecialFeeFlag().equals(0) || estate.getSpecialFeeFlag().equals(2)
						|| estate.getSpecialFeeFlag().equals(3)) {
					receivables.setEstate(estate);
					receivables.setPayItem(getPayItems(o.getPayItemsId()));
					receivables.setPayItemsName(o.getItemsName());
					receivables.setChargeCategory(CommunityPayItemsEntity.getChargeCategoryStr(o.getChargeCategory()));
					receivables.setChargeSource("收费设定");
//					receivables.setComment(o.getUnitCode() + "：" + startDate.format(DateTimeFormatter.ofPattern("yyyy年MM月"))
//							+ (null != o.getParentPropertyId() ? "车位 " : "管理费"));
					receivables.setSubEstateId(null != o.getParentPropertyId() ? o.getId() : null);
					receivables.setState(1);
					BigDecimal receivableAmount = new BigDecimal("0");
					/*
					 * 管理费根据单元的计费日期开始计算管理费 车位管理费根据车位租用时间开始计算
					 */
					LocalDate billingDate = o.getBillingDate().with(TemporalAdjusters.firstDayOfMonth())
							.isEqual(startDate) ? o.getBillingDate() : startDate;

					//根据租赁合同获取价格列表。
					CommunityMemberPropertyEntity memberProperty = communityMemberPropertyDao.get(o.getMemberPropertyId());
					List<CommunityPayItemsPriceEntity> payItemsPriceList = memberProperty.getPayItemsPriceList().stream().filter(s->s.getPayItems().getId().equals(o.getPayItemsId())).collect(Collectors.toList());
					StringBuilder strMemoBuilder = new  StringBuilder();
					if(payItemsPriceList.size() > 0) {
						// 如果存在租赁合同价格，就按租赁合同价格计算
						for(CommunityPayItemsPriceEntity payItemPrice : payItemsPriceList) {
							// 同一个合同存在多个不同时段的价格,根据时间段分段计算价格.
							BigDecimal subReceivableAmount = new BigDecimal("0");
							// 按当前价格，计费的开始时间
							LocalDate startDate2 =  CommunityDateUtil.dateToLocalDate(payItemPrice.getStartTime());
							// 按当前价格，计费的结束时间
							LocalDate endDate2 =  CommunityDateUtil.dateToLocalDate(payItemPrice.getEndTime());
							// 要求生成时间段和计费时间段的交集
							List<LocalDate> interSection = CommunityDateUtil.getInterSection(billingDate, endDate, startDate2, endDate2);
							if(interSection.size() < 2) {
								continue;
							}
							if(payItemPrice.getPeriod().equals(CommunityContext.COMMUNITY_PAY_ITEMS_PRICE_PERIOD_TYPE_MONTH)) {
								// 月长租
								if (o.getFeeCalType().equals(CommunityContext.COMMUNITY_PAY_ITEMS_CAL_TYPE_CHARGE)) {
									subReceivableAmount = JepUtil.cal("单价*收费面积/本月天数*计费天数", 4, payItemPrice.getPrice(), o.getChargingArea(),
											CommunityDateUtil.monthDays(interSection.get(1).toString()),
											CommunityDateUtil.subDate(interSection.get(0).toString(), interSection.get(1).toString()));
								} else if (o.getFeeCalType().equals(CommunityContext.COMMUNITY_PAY_ITEMS_CAL_TYPE_ADD)) {
									subReceivableAmount = JepUtil.cal("单价*附加面积/本月天数*计费天数", 4, payItemPrice.getPrice(), o.getAdditionalArea(),
											CommunityDateUtil.monthDays(interSection.get(1).toString()),
											CommunityDateUtil.subDate(interSection.get(0).toString(), interSection.get(1).toString()));
								} else if (o.getFeeCalType().equals(CommunityContext.COMMUNITY_PAY_ITEMS_CAL_TYPE_ONCE)) {
									subReceivableAmount = JepUtil.cal("单价/本月天数*计费天数", 4, payItemPrice.getPrice(),
											CommunityDateUtil.monthDays(interSection.get(1).toString()),
											CommunityDateUtil.subDate(interSection.get(0).toString(), interSection.get(1).toString()));
								}
							}
							else if(payItemPrice.getPeriod().equals(CommunityContext.COMMUNITY_PAY_ITEMS_PRICE_PERIOD_TYPE_DAY)) {
								// 日短租
								subReceivableAmount = payItemPrice.getPrice();
							}

							String strMemo = interSection.get(0).format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")) + "至" + interSection.get(1).format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")) + "按单价：" + payItemPrice.getPrice().setScale(2, RoundingMode.HALF_UP) + "结算,金额:" + subReceivableAmount.setScale(2, RoundingMode.HALF_UP) + ";"; 
							strMemoBuilder.append(strMemo);
							receivableAmount = receivableAmount.add(subReceivableAmount);
						}
					}
					else {
//						if (o.getFeeCalType().equals(CommunityContext.COMMUNITY_PAY_ITEMS_CAL_TYPE_CHARGE)) {
//							receivableAmount = JepUtil.cal("单价*收费面积/本月天数*计费天数", 4, o.getPrice(), o.getChargingArea(),
//									CommunityDateUtil.monthDays(endDate.toString()),
//									CommunityDateUtil.subDate(billingDate.toString(), endDate.toString()));
//						} else if (o.getFeeCalType().equals(CommunityContext.COMMUNITY_PAY_ITEMS_CAL_TYPE_ADD)) {
//							receivableAmount = JepUtil.cal("单价*附加面积/本月天数*计费天数", 4, o.getPrice(), o.getAdditionalArea(),
//									CommunityDateUtil.monthDays(endDate.toString()),
//									CommunityDateUtil.subDate(billingDate.toString(), endDate.toString()));
//						} else {
//							receivableAmount = JepUtil.cal("单价/本月天数*计费天数", 4, o.getPrice(),
//									CommunityDateUtil.monthDays(endDate.toString()),
//									CommunityDateUtil.subDate(billingDate.toString(), endDate.toString()));
//						}
						continue;
					}
					
					receivables.setComment(strMemoBuilder.toString());
					receivables.setReceivableAmount(receivableAmount.setScale(2, RoundingMode.HALF_UP));
					receivables.setReceivedAmount(new BigDecimal("0"));
					receivables.setSourceNotes(o.getItemsName() + "(" + o.getUnitCode() + ")");
					receivables.setLockMark(0);
					if (o.getPayDate() != null && o.getPayDate().intValue() > 0 && o.getPayDate().intValue() < 32) {
						// paydate[1,28]按生成应收日期,但当>28时需要判断闰收及大小月，后面有需求再优化。
						try {
							receivables.setReceivableDate(Date
									.from(LocalDate.of(startDate.getYear(), startDate.getMonthValue(), o.getPayDate())
											.atStartOfDay(ZoneId.systemDefault()).toInstant()));
						} catch (DateTimeException e) {
							// 如果paydate不正确，划超出当月范围，应收日期为月未最后一日
							receivables.setReceivableDate(
									Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
						}
					} else {
						// paydate > 31 应收日期为月未最后一日 ,未实现生成下个月2日起逻辑
						receivables
								.setReceivableDate(Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
					}
					if ((int) (o.getBillingDate().toEpochDay() - startDate.toEpochDay()) >= 0
							&& (int) (endDate.toEpochDay() - o.getBillingDate().toEpochDay()) >= 0) {
						receivables
								.setStartTime(Date.from(billingDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
					} else {
						receivables.setStartTime(Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
					}

					receivables.setEndTime(Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
					receivables.setPaymentPeriod(receivables.getReceivableDate());

					// 如查应表中存在已经生成的互斥收费项目的应收，则跳过不生成该应收。
					Boolean hasSameGroupFee = hasSameGroupFee(receivables);
					if(hasSameGroupFee) {
						continue;
					}
					// 如查应表中存在已经生成相同应收，则通过修改方式修改应收记录
					StringBuilder dbSql = new StringBuilder(
							"select a.* from t_community_receivables a where a.estateid="
									+ (null != o.getParentPropertyId() ? o.getParentPropertyId() : o.getId())
									+ " and a.payItemId = " + receivables.getPayItem().getId()
									+ " and a.receivableDate='" + DateUtil.format(receivables.getReceivableDate(), 0)
									+ "'")
							.append(null != receivables.getSubEstateId() && !receivables.getSubEstateId().equals(0)
									? " and a.subEstateId=" + receivables.getSubEstateId()
									: "");
					// logger.info("----1");
					CommunityReceivablesEntity dbReceivables = (CommunityReceivablesEntity) session
							.createNativeQuery(dbSql.toString()).addEntity(CommunityReceivablesEntity.class)
							.uniqueResult();
					// logger.info("----2");
					if (receivables.getReceivableAmount().doubleValue() > 0) {
						if (null != dbReceivables) {
							receivables.setId(dbReceivables.getId());
							receivables.setReceivablesNO(dbReceivables.getReceivablesNO());
							receivables.setCreateTime(dbReceivables.getCreateTime());
							receivables.setReceivedAmount(dbReceivables.getReceivedAmount());
							receivables.setLastModifyTime(new Timestamp(System.currentTimeMillis()));
						} else {
							// 新增的要生成编号
							receivables.setReceivablesNO(generateSnLastValue(RECEIVABLESNO_SN_TYPE).toString());
							// 已经生成的互斥收费项目的应收，则跳过不生成该应收。
							// 判断标准相同单元、相同应收日期、相同子单元、互斥ID的项目
							if(receivables.getPayItem().getGroupFee() != null) {
								String identify = receivables.getEstate().getId().toString() 
										  + "_" + DateUtil.format(receivables.getReceivableDate(), 0)
										  + "_" + ((null != receivables.getSubEstateId() && !receivables.getSubEstateId().equals(0)) ? receivables.getSubEstateId().toString(): "")
										  + "_" +  receivables.getPayItem().getGroupFee().toString();
										 
								if(groupFeeSet.contains(identify)) {
									continue;
								}
								else {
									groupFeeSet.add(identify);
								}
							}
						}
						result.add(receivables);
					}
				}
			}
		}
		// 生成公寓出租水电费应收
//		if(rentType == CommunityContext.ESTATE_RENT_TYPE_LONG) {
//			result.add(getRentElectricityAndWatherReceivables(estateId, startDate, endDate, 1));
//			result.add(getRentElectricityAndWatherReceivables(estateId, startDate, endDate, 2));
//		}
		return result;
	}

	private Boolean hasSameGroupFee(CommunityReceivablesEntity receivables) {
		if(receivables.getPayItem().getGroupFee() != null) {
			StringBuilder sameGroupSql = new StringBuilder("select a.* from t_community_receivables a inner join t_community_pay_items i on a.payItemId = i.id where a.estateid=" + (receivables.getEstate().getId()) 
					+ " and a.receivableDate='" + DateUtil.format(receivables.getReceivableDate(), 0) + "'"
					+ " and i.groupFee = " + receivables.getPayItem().getGroupFee())
					.append(null != receivables.getSubEstateId() ? " and a.subEstateId=" + receivables.getSubEstateId()
							: "");
			CommunityReceivablesEntity sameGroupReceivables = communityReceivablesDao.getUniqueBySql(sameGroupSql.toString());
			return sameGroupReceivables != null;
		}
		else {
			return false;
		}
	}

	/**
	 * 根据小区、楼栋、单元Id，试运算水电表操作
	 */
	@Audit(operate = "根据小区、楼栋、单元Id，试运算水电表操作")
	@Override
	public IResponse rentTrialMeterOperation(CommunityAddReceivablesForEstateReq req) {
		GenericResponse res = new GenericResponse();
		if(StringUtils.isEmpty(req.getCategoryList())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "类别不能为空！");
			return res;
		}
		if (StringUtils.isEmpty(req.getEstateIds()) && StringUtils.isEmpty(req.getBuildingIds())
				&& StringUtils.isEmpty(req.getDistrictIds())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "小区id、楼栋id或单元id，不能全为空！");
			return res;
		}
		if (req.getStartDate() == null) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "开始时间不能为空！");
			return res;
		}
		if (req.getEndDate() == null) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "结束时间不能为空！");
			return res;
		}

//		Instant instant = req.getStartDate().toInstant();
//		ZoneId zoneId = ZoneId.systemDefault();
//		LocalDate localStartDate = instant.atZone(zoneId).toLocalDate().with(TemporalAdjusters.firstDayOfMonth());
		LocalDate localStartDate =	LocalDate.parse(DateUtil.format(req.getStartDate(),0), DateTimeFormatter.ofPattern("yyyy-MM-dd"));

//		instant = req.getEndDate().toInstant();
//		LocalDate localEndDate = instant.atZone(zoneId).toLocalDate().with(TemporalAdjusters.lastDayOfMonth());
		LocalDate localEndDate =	LocalDate.parse(DateUtil.format(req.getEndDate(),0), DateTimeFormatter.ofPattern("yyyy-MM-dd"));

		StringBuilder conditionHql = new StringBuilder();
		conditionHql.append(StringUtils.isNotEmpty(req.getEstateIds()) ? "e.id in (" + req.getEstateIds() + ")" : "");
		conditionHql.append(
				StringUtils.isNotEmpty(req.getBuildingIds()) ? "or b.id in (" + req.getBuildingIds() + ")" : "");
		conditionHql.append(
				StringUtils.isNotEmpty(req.getDistrictIds()) ? "or d.id in (" + req.getDistrictIds() + ")" : "");
		String conditonStr = conditionHql.toString();
		if (conditonStr.startsWith("or ")) {
			conditonStr = conditonStr.substring(3);
		}
		StringBuilder selHql = new StringBuilder(
				"select e from CommunityEstateEntity e inner join e.building b inner join b.district d");
		selHql.append(StringUtils.isNotEmpty(conditonStr) ? (" where e.rentState = 2 and " + conditonStr)
				: " where e.rentState =2");
		List<CommunityEstateEntity> estateList = communityEstateDao.getListByHql(selHql.toString());
		// 逐个单元生成租金
		rentMeterAllocationCache.clear();
		for (CommunityEstateEntity estateEntity : estateList) {
			getRentElectricityAndWatherReceivables(estateEntity, localStartDate,  localEndDate, req.getCategoryList());
		}

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	
	/**
	 * 生成公寓出租水电费应收
	 * 没有个性化需求，不需要同一合同多时段计费，计费单价从表属性中读取。
     * 每间公寓有只有水表和电表各一只，暂不支持互斥组。
	 * @param estateId  单元ID
	 * @param startDate 起始时间
	 * @param endDate  结束时间
	 * @param category 表类型，1、电表；2、水表
	 */
	private void getRentElectricityAndWatherReceivables(CommunityEstateEntity estate, 
			LocalDate startDate, LocalDate endDate, String category) {
		
        String strMeterRecord ="select distinct mr.* from  t_community_meter_record mr  INNER JOIN t_community_meter m ON m.id = mr.meterId "
        		+ "inner join t_community_meter_property mp  on m.id = mp.meterId and  mp.propertyId = " + estate.getId()+ " and m.isCommon = 0 and m.state = 1 "
        		+ "inner join t_community_meter_attributes ma on ma.id = m.meterAttributesId and ma.category in (" + category + ") "
        		+ "where  mr.state=1 AND mr.recordDate <= '" + endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 00:00:00" + "' order by mr.recordDate desc LIMIT 1";
        List<CommunityMeterRecordEntity> list = communityMeterRecordDao.getListBySql(strMeterRecord);
        for(CommunityMeterRecordEntity watherMeterRecord : list) {
            if(null!=watherMeterRecord) {
            	// 单价
                BigDecimal unitPrice = watherMeterRecord.getCommunityMeter().getMeterAttributes().getUnitPrice();
                unitPrice = unitPrice == null ? BigDecimal.ZERO : unitPrice;

            	// 上次读数
            	CommunityMeterRecordEntity lastMeterRecordEntity = null!=watherMeterRecord.getLastRecordId() ? communityMeterRecordDao.get(watherMeterRecord.getLastRecordId()) :null;
            	BigDecimal lastRecordNum = (lastMeterRecordEntity == null || lastMeterRecordEntity.getRecordNum() == null) ? BigDecimal.ZERO : lastMeterRecordEntity.getRecordNum();
            	// Date lastRecordDate = lastMeterRecordEntity.getRecordDate();
            	// 用量 = 本次读数 - 上次读数
            	BigDecimal usedAmount = watherMeterRecord.getRecordNum().subtract(lastRecordNum);
            	// 金额 = 用量 * 单价
            	BigDecimal amountPrice = usedAmount.multiply(unitPrice).setScale(2, RoundingMode.HALF_UP);
            	if(amountPrice.compareTo(BigDecimal.ZERO) == 1) {
            		CommunityPayItemsEntity payItems = watherMeterRecord.getCommunityMeter().getPayItems();
                    //CommunityEstateEntity estate = getEstate(estateId);
                    
                    RentMeterAllocationVo rentMeterAllocationVo = new RentMeterAllocationVo();
                    rentMeterAllocationVo.setEstateId(estate.getId());
                    rentMeterAllocationVo.setPayItemsId(payItems!=null ? payItems.getId() : null);
                    rentMeterAllocationVo.setUnitCode(estate.getUnitCode());
                    rentMeterAllocationVo.setMeterId(watherMeterRecord.getCommunityMeter().getId());
                    rentMeterAllocationVo.setPayItemsName(null!=payItems ? payItems.getItemsName() :"");
                    rentMeterAllocationVo.setAllocationNum(usedAmount);
                    rentMeterAllocationVo.setAllocationAmount(amountPrice);
                    rentMeterAllocationVo.setMeterId(watherMeterRecord.getCommunityMeter().getId());
                    rentMeterAllocationVo.setRecordNum(watherMeterRecord.getRecordNum());
                    rentMeterAllocationVo.setLastNum(lastRecordNum);    
                    rentMeterAllocationVo.setUnitPrice(unitPrice);
                    rentMeterAllocationVo.setRecordDate(null != watherMeterRecord.getRecordDate() ? 
                    		DateUtil.formatShortFormat(watherMeterRecord.getRecordDate()) : "");
                    rentMeterAllocationVo.setReceivableAmount(amountPrice.setScale(2, RoundingMode.HALF_UP));
                    rentMeterAllocationVo.setCategory(watherMeterRecord.getCommunityMeter().getMeterAttributes().getCategory());
                    rentMeterAllocationVo.setEndDate(endDate);
                    rentMeterAllocationVo.setStartDate(startDate);
                    rentMeterAllocationVo.setMeterName(watherMeterRecord.getCommunityMeter().getMeterName());
                    rentMeterAllocationVo.setMeterCode(watherMeterRecord.getCommunityMeter().getMeterCode());
                    
                    Map<String,Object> map = new HashMap<>();
                    map.put("rentMeterAllocationVo", rentMeterAllocationVo);

            		// 如查应表中存在已经生成相同应收，则通过修改方式修改应收记录
            		StringBuilder dbSql = new StringBuilder(
            				"select a.* from t_community_receivables a where a.estateid="
            						+ (null != estate.getParentProperty() ? estate.getParentProperty().getId(): estate.getId())
            						+ " and a.payItemId = " + payItems.getId()
            						+ " and a.receivableDate='" + DateUtil.format(CommunityDateUtil.localDateToDate(endDate), 0)
            						+ "'")
            				.append(null != estate.getParentProperty() 
            						? " and a.subEstateId=" + estate.getId()
            						: "");
            		CommunityReceivablesEntity receivables = (CommunityReceivablesEntity) communityReceivablesDao
            				.createSQLQuery(dbSql.toString()).addEntity(CommunityReceivablesEntity.class)
            				.uniqueResult();
            		//if (receivables.getReceivableAmount().doubleValue() > 0) {
            			if (null != receivables) {
            				//receivables.setId(dbReceivables.getId());
            				//receivables.setReceivablesNO(dbReceivables.getReceivablesNO());
            				//receivables.setCreateTime(dbReceivables.getCreateTime());
            				//receivables.setReceivedAmount(dbReceivables.getReceivedAmount());
            				receivables.setLastModifyTime(new Timestamp(System.currentTimeMillis()));
            			} else {
                            receivables = new CommunityReceivablesEntity();
                            receivables.setEstate(estate);
                            receivables.setPayItem(payItems);
                            receivables.setPayItemsName(payItems.getItemsName());
                    		receivables.setChargeCategory(CommunityPayItemsEntity.getChargeCategoryStr(payItems.getChargeCategory()));
                    		receivables.setSubEstateId(null != estate.getParentProperty() ? estate.getId() : null);
                    		receivables.setState(1);
                    		receivables.setReceivedAmount(new BigDecimal("0"));
                    		receivables.setLockMark(0);
            				// 新增的要生成编号
            				receivables.setReceivablesNO(generateSnLastValue(RECEIVABLESNO_SN_TYPE).toString());
            			}
            		//}
        			receivables.setChargeSource("收费设定");
            		receivables.setComment("上次:" + lastRecordNum + ",本次:" +  watherMeterRecord.getRecordNum() + ",用量:" + usedAmount + ",单价:" + unitPrice);
            		receivables.setSourceNotes(watherMeterRecord.getCommunityMeter().getMeterName() + "(" + watherMeterRecord.getCommunityMeter().getMeterCode()+ ")");
            		receivables.setReceivableAmount(amountPrice.setScale(2, RoundingMode.HALF_UP));
            		receivables.setReceivableDate(CommunityDateUtil.localDateToDate(endDate));
            		receivables.setStartTime(CommunityDateUtil.localDateToDate(startDate));
            		receivables.setEndTime(CommunityDateUtil.localDateToDate(endDate));
            		receivables.setPaymentPeriod(receivables.getReceivableDate());
            		map.put("receivables", receivables);
            		rentMeterAllocationCache.add(map);
            	}
            }
        }
	}
	
	@Override
	@Audit(operate = "获取租赁单元试运算水电表列表")
	public IResponse getRentTrialMeterOperationList(CommunityMeterAllocationReq req) {
		// TODO Auto-generated method stub
		GetRentTrialMeterOperationListRes res = new GetRentTrialMeterOperationListRes();
		CommunityPage<Set<Map<String,Object>>> page = CommunityCache.getPage(new HashSet<>(rentMeterAllocationCache),
				req.getPageSize(), req.getRequestPage());
		res.setCurrentPage(page.getCurrentPage());
		res.setPageSize(page.getPageSize());
		res.setTotal(page.getTotalPage());
		res.setTotalResult(page.getTotalCount());
		page.getResultList().forEach(o -> {
			RentMeterAllocationVo rentMeterAllocationVo = (RentMeterAllocationVo) o.get("rentMeterAllocationVo");
			RentMeterAllocationForm rentMeterAllocationForm = new RentMeterAllocationForm();
			rentMeterAllocationForm.setUnitCode(rentMeterAllocationVo.getUnitCode());
			rentMeterAllocationForm.setMeterId(rentMeterAllocationVo.getMeterId());
			rentMeterAllocationForm.setPayItemsName(rentMeterAllocationVo.getPayItemsName());
			rentMeterAllocationForm.setAllocationNum(rentMeterAllocationVo.getAllocationNum().toString());
			rentMeterAllocationForm.setAllocationAmount(rentMeterAllocationVo.getAllocationAmount().toString());
			rentMeterAllocationForm.setMeterId(rentMeterAllocationVo.getMeterId());
			rentMeterAllocationForm.setRecordNum(rentMeterAllocationVo.getRecordNum().toString());
			rentMeterAllocationForm.setLastNum(rentMeterAllocationVo.getLastNum().toString());    
			rentMeterAllocationForm.setUnitPrice(rentMeterAllocationVo.getUnitPrice().toString());
			rentMeterAllocationForm.setRecordDate(rentMeterAllocationVo.getRecordDate());
			rentMeterAllocationForm.setReceivableAmount(rentMeterAllocationVo.getReceivableAmount().toString());
			rentMeterAllocationForm.setCategory(rentMeterAllocationVo.getCategory());
			rentMeterAllocationForm.setMeterName(rentMeterAllocationVo.getMeterName());
			rentMeterAllocationForm.setMeterCode(rentMeterAllocationVo.getMeterCode());		
			//rentMeterAllocationForm.setEndDate(o.getEndDate());
			//rentMeterAllocationForm.setStartDate(o.getStartDate());
			res.getRentMeterAllocationList().add(rentMeterAllocationForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	public IResponse exportRentTrialMeterOperation(ExportExcelReq req, HttpServletResponse response) throws ParseException {
		ExportExcelRes res = new ExportExcelRes();
		Map<String, String> heardMap = new LinkedHashMap<String, String>();
		heardMap.put("unitCode", "单元编号");
//		heardMap.put("meterName", "表名称");
		heardMap.put("meterCode", "表编号");
		heardMap.put("category", "分类");
		heardMap.put("payItemsName", "项目");
		heardMap.put("unitPrice", "单价");
		heardMap.put("recordNum", "本次读数");
		heardMap.put("lastNum", "上次读数");
//		heardMap.put("additionalAmount", "额外用量");
//		heardMap.put("additionalInstructions", "额外用量说明");
		heardMap.put("allocationNum", "用量");
		heardMap.put("recordDate", "应收日期");
		heardMap.put("receivableAmount", "应收金额");
		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		//Map<Integer, MeterRecordVo> resultList = new TreeMap<>();
		for (int i = 0; i < rentMeterAllocationCache.size(); i++) {
			RentMeterAllocationVo rentMeterAllocationVo = (RentMeterAllocationVo) rentMeterAllocationCache.get(i).
					get("rentMeterAllocationVo");
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("unitCode", rentMeterAllocationVo.getUnitCode());
//				map.put("meterName", rentMeterAllocationVo.getMeterName());
				map.put("meterCode", rentMeterAllocationVo.getMeterCode());
				switch(rentMeterAllocationVo.getCategory()){
				case 1:map.put("category", "电表");break;
				case 2:map.put("category", "水表");break;
				case 3:map.put("category", "临时表");break;
				case 4:map.put("category", "代收水表");break;
				case 5:map.put("category", "代收电表");break;
				default : map.put("category", "其他");break;
				}
				
				map.put("payItemsName", rentMeterAllocationVo.getPayItemsName());
				map.put("unitPrice", rentMeterAllocationVo.getUnitPrice().setScale(2, RoundingMode.HALF_UP).toString());
				map.put("recordNum", rentMeterAllocationVo.getRecordNum());
				map.put("lastNum", rentMeterAllocationVo.getLastNum());
//				map.put("additionalAmount", null!=rentMeterAllocationVo.getAdditionalAmount() ? 
//						rentMeterAllocationVo.getAdditionalAmount().setScale(2, RoundingMode.HALF_UP).toString() : "0");
//				map.put("additionalInstructions", StringUtils.isNotEmpty(rentMeterAllocationVo.getAdditionalInstructions()) ?
//						rentMeterAllocationVo.getAdditionalInstructions() : "");
				map.put("allocationNum", rentMeterAllocationVo.getAllocationNum());
				map.put("recordDate",rentMeterAllocationVo.getRecordDate());
				map.put("receivableAmount", rentMeterAllocationVo.getReceivableAmount().setScale(2, RoundingMode.HALF_UP).toString());
				dataList.add(map);
		}

		try {
			ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("300"), null, "", "",
					"", response);
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}

		return res;
	}
	
	public IResponse  submitRentMeterAllocation() {
		GenericResponse res = new GenericResponse();
		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();
		// 逐个单元生成租金
		if(null!=rentMeterAllocationCache && rentMeterAllocationCache.size()>0) {
			for (int i = 0; i < rentMeterAllocationCache.size(); i++) {
				CommunityReceivablesEntity receivables = (CommunityReceivablesEntity) 
						rentMeterAllocationCache.get(i).get("receivables");
				communityReceivablesDao.merge(receivables);
				CommunityCache.putReceivableCache(receivables, CommunityCache.receivableCalEstateList);
				if (i % 50 == 0) {
					communityReceivablesDao.flush();
					communityReceivablesDao.clear();
				}
			}
			if (null != session) {
				session.close();
			}
			rentMeterAllocationCache.clear();
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		}else {
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("对不起，请先核对数据！");
		}
		return res;
	}
	
	/**
	 * 按小区、楼栋、单元Id撤消当月生成的管理费已经发生过收款的应收不会被撤消
	 * 
	 * @param CommunityDeleteReceivablesByDetails
	 * @return
	 */
	@Audit(operate = "撤消单元当月管理费应收")
	@Override
	public IResponse deleteManagerReceivablesByDetail(CommunityDeleteReceivablesByDetails req) {
		GenericResponse res = new GenericResponse();

		LocalDate startDate = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
		if(req.getEndDate() != null) {
			startDate = CommunityDateUtil.dateToLocalDate(req.getStartDate());
			if(startDate.isBefore( LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()))) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "开始日期不能在当月之前！");
			}
		}
		LocalDate endDate = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
		if(req.getEndDate() != null) {
			endDate = CommunityDateUtil.dateToLocalDate(req.getEndDate());
		}
		StringBuilder conditionHql = new StringBuilder();
		conditionHql.append(StringUtils.isNotEmpty(req.getEstateIds()) ? "e.id in (" + req.getEstateIds() + ")" : "");
		conditionHql.append(
				StringUtils.isNotEmpty(req.getBuildingIds()) ? "or e.building.id in (" + req.getBuildingIds() + ")"
						: "");
		conditionHql.append(StringUtils.isNotEmpty(req.getDistrictIds())
				? "or e.building.district.id in (" + req.getDistrictIds() + ")"
				: "");
		String conditonStr = conditionHql.toString();
		if (conditonStr.startsWith("or ")) {
			conditonStr = conditonStr.substring(3);
		}
		StringBuilder baseHql = new StringBuilder(
				"select r from CommunityReceivablesEntity r inner join r.estate e inner join r.payItem i "
						+ "where i.chargeCategory in (1,2) and i.payDate > 0 and r.receivedAmount = 0 and r.receivableDate >='"
						+ startDate.toString() + " 00:00:00' and r.lockMark = 0 and r.receivableDate <='"
						+ endDate.toString() + " 00:00:00'")
								.append(StringUtils.isNotEmpty(conditonStr) ? " and (" + conditonStr + ")" : "");

		List<CommunityReceivablesEntity> receivablesList = communityReceivablesDao.getListByHql(baseHql.toString());

		for (int i = 0; i < receivablesList.size(); i++) {
			CommunityReceivablesEntity receivable = receivablesList.get(i);
			receivable.setEstate(null);
			receivable.setPayItem(null);
			communityReceivablesDao.delete(receivable);
			if (i % 200 == 0) {
				communityReceivablesDao.flush();
				communityReceivablesDao.clear();
			}
			CommunityCache.removeReceivableCache(receivable, CommunityCache.receivableCalEstateList);
		}

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	/**
	 * 根据单元Id、收费项目及计算日期生成违约金 ，目前是按（计算基准时间-1）与最后一次计算违约金的时间之间有没发生动账计算
	 * 
	 * @param districtIds
	 * @param buildingIds
	 * @param estateIds
	 * @param payItemIds
	 * @param breachEndDate
	 * @return
	 */
	private List<CommunityReceivablesEntity> generateBreachReceivablesByEstate(String districtIds, String buildingIds,
			String estateIds, String payItemIds, Date breachEndDate) {
		// 如果没有要求计算时间默认当前系统时间
		breachEndDate = breachEndDate == null ? new Date() : breachEndDate;
//		// 查询需要计算违约金应收记录
		StringBuilder queryHql = new StringBuilder(
				"SELECT r FROM CommunityReceivablesEntity r INNER JOIN r.payItem i INNER JOIN r.estate e ");

		if (StringUtils.isNotEmpty(buildingIds) || StringUtils.isNotEmpty(districtIds)) {
			queryHql.append("INNER JOIN e.building b ");
		}
		queryHql.append(StringUtils.isNotEmpty(districtIds) ? "INNER JOIN b.district d " : "");

		queryHql.append("WHERE ");

		// 收费项目中设定了生成违约金的应收
		queryHql.append("i.isBreach = 1 AND i.breachRatio > 0 ");
		// 单元不在排除名单中
		queryHql.append("AND e.specialFeeFlag <> 3 AND e.specialFeeFlag <> 4 ");

		// 只生成某个单元的违约金
		if (StringUtils.isNotEmpty(payItemIds)) {
			queryHql.append(" AND i.id in (" + payItemIds + ") ");
		}
		// 当前时间已经达到应收日期下个月的第1日,即从出账的下个月1号开始收违约金
		queryHql.append("AND ((r.receivableDate <= '" + DateUtil.format(DateUtil.getMonthDay(breachEndDate, 1, 0), 0)
		+ " 00:00:00') OR r.benefitValue IS NOT NULL) ");
		StringBuilder conditon = new StringBuilder("");
		conditon.append(StringUtils.isNotEmpty(estateIds) ? "OR e.id in (" + estateIds + ") " : "");
		conditon.append(StringUtils.isNotEmpty(buildingIds) ? "OR b.id in (" + buildingIds + ") " : "");
		conditon.append(StringUtils.isNotEmpty(districtIds) ? "OR d.id in (" + districtIds + ") " : "");
		String conditonStr = conditon.toString();
		if (conditonStr.startsWith("OR ")) {
			conditonStr = "AND (" + conditonStr.substring(3) + ")";
		}

        // 不含补收优惠差额的管理费
		List<CommunityReceivablesEntity> receivablesList = communityReceivablesDao
				.getListByHql(queryHql.toString()  + conditonStr.toString());

		//logger.info("the number of process breach receivables records:{}", receivablesList.size());
		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		// 按单元分组,
		Map<Integer, List<CommunityReceivablesEntity>> estateReceivableMap = new HashMap<Integer, List<CommunityReceivablesEntity>>();
		receivablesList.forEach(r -> {
			List<CommunityReceivablesEntity> estateReceivablesList = null;
			if (estateReceivableMap.get(r.getEstate().getId()) == null) {
				estateReceivablesList = new ArrayList<CommunityReceivablesEntity>();
				estateReceivablesList.add(r);
				estateReceivableMap.put(r.getEstate().getId(), estateReceivablesList);
			} else {
				estateReceivableMap.get(r.getEstate().getId()).add(r);
			}
		});

		// 如果处理的数据较多,先把违约金的应收加载到内存
		Map<Integer, CommunityReceivablesEntity> manageIdBreachReceivableMap = new HashMap<Integer, CommunityReceivablesEntity>();
		if (receivablesList.size() > 36) {
			// 可以不用全量加载到内存，只加载查询条件中涉及的单元对应的违约金到内存，后续再优化！！！
			List<CommunityReceivablesEntity> breachReceivablesList = communityReceivablesDao
					.getListByHql("SELECT m FROM CommunityReceivablesEntity m WHERE m.breachReceivablesId IS NOT NULL");
			logger.info("load breach receivables in memory!");
			breachReceivablesList.forEach(m -> {
				manageIdBreachReceivableMap.put(m.getBreachReceivablesId(), m);
			});
		}

		List<CommunityReceivablesEntity> newBreachReceivablesEntityList = new ArrayList<CommunityReceivablesEntity>();
		CommunityPayItemsEntity breachPayItem = getBreachPayItem();
		// 按每个单元计算，生成违约金

		final Date endDate = breachEndDate;
		estateReceivableMap.keySet().forEach(o -> {
			List<CommunityReceivablesEntity> estateReceivablesEntityList = new ArrayList<CommunityReceivablesEntity>();
			try {
				estateReceivablesEntityList = calBreachFee(o, estateReceivableMap.get(o), endDate, breachPayItem,
						receivablesList.size() > 36 ? manageIdBreachReceivableMap : null, session);
				newBreachReceivablesEntityList.addAll(estateReceivablesEntityList);
				logger.info("the number of processed breach receivables:{}", newBreachReceivablesEntityList.size());
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
		});
		if (null != session) {
			session.close();
		}
		return newBreachReceivablesEntityList;
	}

	/**
	 * 根据单元的应收记录计算违约金 发生过收款的违约金、手工不受影响,已经锁盘的违约金暂不更新，手工锁定的不会影响
	 * 
	 * @param estateId        单元ID
	 * @param receivablesList 单元ID的对应需要计算违约金的list
	 * @param breachEndDate   计算基准时间
	 * @return CommunityReceivablesEntity 生成的违约金应收
	 * @throws JsonProcessingException
	 * @throws JsonMappingException
	 */
	private List<CommunityReceivablesEntity> calBreachFee(Integer estateId,
			List<CommunityReceivablesEntity> receivablesList, Date breachEndDate, CommunityPayItemsEntity breachPayItem,
			Map<Integer, CommunityReceivablesEntity> manageIdBreachReceivableMap, Session querySession)
			throws JsonMappingException, JsonProcessingException {
		List<CommunityReceivablesEntity> result = new ArrayList<CommunityReceivablesEntity>();
		for (CommunityReceivablesEntity receivable : receivablesList) {
			if(receivable.getBenefitValue() == null) {
				// 查出对应管理费应收是否已经生成违约金
				CommunityReceivablesEntity currentBreachReceivablesEntity = calSingleBreachAmount(estateId,receivable,breachEndDate,breachPayItem,manageIdBreachReceivableMap,querySession);
				if(currentBreachReceivablesEntity == null) {
					continue;
				}
				else {
					result.add(currentBreachReceivablesEntity);
				}
				
			}
			else {
				StringBuilder detailComment = new StringBuilder();
				Date startTime = null;
				Date endTime =  null;
//				String districtAddress  = receivable.getEstate().getBuilding().getDistrict().getAddress();
//				districtAddress = districtAddress == null ? "" : districtAddress;
				try {
					 startTime = DateUtil.parse("3023-12-31 00:00:00", 1);
					 endTime = DateUtil.parse("1900-12-31 23:59:59", 1);
				} catch (ParseException e) {
					e.printStackTrace();
				}
				CancelBenefitDetailVo benefitVo  = JsonUtil.jsonToObject(receivable.getBenefitValue(), CancelBenefitDetailVo.class);
				//计算当月管理费产生的违续金
				receivable.setReceivableAmount(receivable.getReceivableAmount().subtract(benefitVo.getTotalSubManageAmount()));
				CommunityReceivablesEntity currentBreachReceivablesEntity = calSingleBreachAmount(estateId,receivable,breachEndDate,breachPayItem,manageIdBreachReceivableMap,querySession);
				if(currentBreachReceivablesEntity == null) {
					//还原应收金额
					receivable.setReceivableAmount(receivable.getReceivableAmount().add(benefitVo.getTotalSubManageAmount()));
					continue;
				}
				if(currentBreachReceivablesEntity.getReceivableAmount().compareTo(BigDecimal.ZERO) > 0 ) {
					startTime = currentBreachReceivablesEntity.getStartTime().before(startTime) ? currentBreachReceivablesEntity.getStartTime() : startTime;
					endTime = currentBreachReceivablesEntity.getEndTime().after(endTime) ? currentBreachReceivablesEntity.getEndTime() : endTime;
				}
				detailComment.append("。其中").append(DateUtil.formatByStyle(receivable.getReceivableDate(), "yyyy年MM月") + "产生违约金" + currentBreachReceivablesEntity.getReceivableAmount().setScale(2, RoundingMode.HALF_UP) + "元");
				//是否需要取消优惠后的违约金额差额

				BigDecimal totalSubBreachAmount =  BigDecimal.ZERO;
				//取消优惠后计算违约金额是多少
				for(CancelBenefitDetailItemVo itemVo : benefitVo.getItems()) {
					 CommunityReceivablesEntity cancelBenefitReceivable = communityReceivablesDao.get(itemVo.getReceivablesId());
					 if(null != cancelBenefitReceivable) {
						 // 获取优惠计算情况管理费产生的违约金额
						 BigDecimal originalBreachAmount = BigDecimal.ZERO;
						 CommunityReceivablesEntity originalBreachReceivablesEntity = calSingleBreachAmount(estateId,cancelBenefitReceivable,breachEndDate,breachPayItem,manageIdBreachReceivableMap,querySession);
						 if(originalBreachReceivablesEntity != null) {
							 originalBreachAmount = originalBreachReceivablesEntity.getReceivableAmount();
						 }
						 
						 // 管理费取消优惠后产生的违约金额
						 communityReceivablesDao.evict(cancelBenefitReceivable);
						 cancelBenefitReceivable.setReceivableAmount(cancelBenefitReceivable.getReceivableAmount().add(itemVo.getSubManageAmount()));
						 CommunityReceivablesEntity currentBenefitBreachReceivablesEntity = calSingleBreachAmount(estateId,cancelBenefitReceivable,breachEndDate,breachPayItem,manageIdBreachReceivableMap,querySession,itemVo.getChangeAmountDetail());
						 if(currentBenefitBreachReceivablesEntity != null) {
							 startTime = currentBenefitBreachReceivablesEntity.getStartTime().before(startTime) ? currentBenefitBreachReceivablesEntity.getStartTime() : startTime;
						     endTime = currentBenefitBreachReceivablesEntity.getEndTime().after(endTime) ? currentBenefitBreachReceivablesEntity.getEndTime() : endTime;
							 
							 // 计算取消优惠后的违约金的差额
							 itemVo.setSubBreachAmount(currentBenefitBreachReceivablesEntity.getReceivableAmount().subtract(originalBreachAmount)); 
							 totalSubBreachAmount = totalSubBreachAmount.add(itemVo.getSubBreachAmount());
						 }
					 }
				}	
				benefitVo.setTotalSubBreachAmount(totalSubBreachAmount);

				String districtAddressSql = "SELECT t.address,p.unitCode FROM `t_community_district` t INNER JOIN `t_community_building` b ON b.`districtId` =  t.id INNER JOIN `t_community_property` p ON p.`buildingId` = b.id WHERE p.id = " + estateId;
				List<Object[]> estateDetailList = querySession.createNativeQuery(districtAddressSql).list();

				String districtAddress = "" ;
				String unitCode = "";
				if(estateDetailList.size() > 0) {
					districtAddress = estateDetailList.get(0)[0] == null ? "" : estateDetailList.get(0)[0].toString();
					unitCode = estateDetailList.get(0)[1] == null ? "" : estateDetailList.get(0)[1].toString();
				}
				
				//更新json
				detailComment.append("，由于不符合" + districtAddress + "区的优惠政策，补收此前月份违约金差额" + benefitVo.getTotalSubBreachAmount().setScale(2, RoundingMode.HALF_UP)+ "元!");
				String strBenefitJson = JsonUtil.objectToJSON(benefitVo);
				currentBreachReceivablesEntity.setEndTime(endTime);
				currentBreachReceivablesEntity.setStartTime(startTime);
				currentBreachReceivablesEntity.setPaymentPeriod(breachEndDate.before(startTime) ? startTime : breachEndDate);
				currentBreachReceivablesEntity.setReceivableDate(breachEndDate.before(startTime) ? startTime : breachEndDate);
				
				currentBreachReceivablesEntity.setBenefitValue(strBenefitJson);
				currentBreachReceivablesEntity.setReceivableAmount(currentBreachReceivablesEntity.getReceivableAmount().add(benefitVo.getTotalSubBreachAmount()));
				
				currentBreachReceivablesEntity.setComment(unitCode + "："
						+ DateUtil.formatByStyle(receivable.getReceivableDate(), "yyyy年MM月") + receivable.getPayItem().getItemsName()
						+ "违约金"+ detailComment.toString());
				//还原管理费应收金额
				receivable.setReceivableAmount(receivable.getReceivableAmount().add(benefitVo.getTotalSubManageAmount()));
				receivable.setBenefitValue(strBenefitJson);
				if((currentBreachReceivablesEntity.getReceivableAmount().compareTo(BigDecimal.ZERO) == 0
						|| currentBreachReceivablesEntity.getReceivableAmount().setScale(2, RoundingMode.HALF_UP).compareTo(BigDecimal.ZERO) == 0)
						&& currentBreachReceivablesEntity.getId() == null) {
					continue;
				}
				result.add(currentBreachReceivablesEntity);
		   }	
		}
		return result;
	}

	/**
	 * 生成单个管理费应收的违约金
	 * @param estateId                        单元ID
	 * @param receivable                      产生违约金的应收ID
	 * @param breachEndDate                   计算违约金的结束日期
	 * @param breachPayItem                   违约金的payItemId
	 * @param manageIdBreachReceivableMap     违约金缓存Map<key=管理费应收ID，value=对应的违约金>
	 * @param querySession                    hibernate会话
	 * @return 生成对应的违约金应收对象
	 */
	private CommunityReceivablesEntity calSingleBreachAmount(Integer estateId,
			CommunityReceivablesEntity receivable, Date breachEndDate, CommunityPayItemsEntity breachPayItem,
			Map<Integer, CommunityReceivablesEntity> manageIdBreachReceivableMap, Session querySession) {
		return calSingleBreachAmount(estateId,receivable,breachEndDate,breachPayItem,manageIdBreachReceivableMap,querySession, null);
	}

	/**
	 * 生成单个管理费应收的违约金
	 * @param estateId                        单元ID
	 * @param receivable                      产生违约金的应收ID
	 * @param breachEndDate                   计算违约金的结束日期
	 * @param breachPayItem                   违约金的payItemId
	 * @param manageIdBreachReceivableMap     违约金缓存Map<key=管理费应收ID，value=对应的违约金>
	 * @param querySession                    hibernate会话
	 * @param changeList                      List<CancelBenefitAmountChangeDetailItem> 取消优惠后，补收管理费差额部分的动账记录List
	 * @return 生成对应的违约金应收对象
	 */
	private CommunityReceivablesEntity calSingleBreachAmount(Integer estateId,
			CommunityReceivablesEntity receivable, Date breachEndDate, CommunityPayItemsEntity breachPayItem,
			Map<Integer, CommunityReceivablesEntity> manageIdBreachReceivableMap, Session querySession, List<CancelBenefitAmountChangeDetailItem> changeList) {
		CommunityReceivablesEntity currentBreachReceivablesEntity = (manageIdBreachReceivableMap == null)
				? (communityReceivablesDao
						.getUniqueByHql("select r from CommunityReceivablesEntity r where r.breachReceivablesId = "
								+ receivable.getId()))
				: manageIdBreachReceivableMap.get(receivable.getId());

		Date startCaculateDate = DateUtil.getNextMonthDay(receivable.getReceivableDate(), 1);
		startCaculateDate = startCaculateDate.before(communityContextInfo.cutoverDate) ? communityContextInfo.cutoverDate
				: startCaculateDate;
		startCaculateDate = DateUtil.resetStartOfDay(startCaculateDate);
		CommunityPayItemsEntity payItem = receivable.getPayItem();
		// 目前是按（计算基准时间-1）与最后一次计算违约金的时间之间有没发生动账计算
		if (currentBreachReceivablesEntity == null || changeList != null) {  // evict不生效，只对新建对象处理
			currentBreachReceivablesEntity = new CommunityReceivablesEntity();
			currentBreachReceivablesEntity.setSubEstateId(null);
			currentBreachReceivablesEntity.setBreachReceivablesId(receivable.getId());
			currentBreachReceivablesEntity.setEstate(receivable.getEstate());

			currentBreachReceivablesEntity.setChargeCategory(
					CommunityPayItemsEntity.getChargeCategoryStr(breachPayItem.getChargeCategory()));
			currentBreachReceivablesEntity.setChargeSource("系统违约金");
			currentBreachReceivablesEntity.setSourceNotes("系统违约金");
			currentBreachReceivablesEntity.setLockMark(0);
			currentBreachReceivablesEntity.setComment(receivable.getEstate().getUnitCode() + "："
					+ DateUtil.formatByStyle(receivable.getReceivableDate(), "yyyy年MM月") + payItem.getItemsName()
					+ "违约金");
			currentBreachReceivablesEntity
					.setPayItemsName(StringUtils.isNotEmpty(payItem.getBreachName()) ? payItem.getBreachName()
							: (payItem.getItemsName() + "违约金"));

			currentBreachReceivablesEntity.setStartTime(startCaculateDate);
			currentBreachReceivablesEntity.setReceivedAmount(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
			currentBreachReceivablesEntity
					.setReceivableAmount(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
			currentBreachReceivablesEntity.setPayItem(breachPayItem);
			//communityReceivablesDao.save(currentBreachReceivablesEntity);
		} else {
//			if(changeList != null) {
//				communityReceivablesDao.evict(currentBreachReceivablesEntity);  // 不生效，只对新建对象处理
//			}
			// 发生过收款的违约金，手工不受影响,已经锁盘的违约金暂不更新，手工锁定的不受影响
			if (currentBreachReceivablesEntity.getLockMark().intValue() == 1
					|| currentBreachReceivablesEntity.getReceivedAmount().compareTo(BigDecimal.ZERO) > 0) {
				return null;
			}
			currentBreachReceivablesEntity.setSubEstateId(null);
			currentBreachReceivablesEntity.setLastModifyTime(new Timestamp(System.currentTimeMillis()));
		}
		CalBreachAmountChangeAmountHistoryResultVo calBreachAmountResult = new CalBreachAmountChangeAmountHistoryResultVo(currentBreachReceivablesEntity.getStartTime(),BigDecimal.ZERO);
//		if (breachEndDate.before(currentBreachReceivablesEntity.getStartTime())) {
//			breachEndDate = currentBreachReceivablesEntity.getStartTime();
//		}
		if (!breachEndDate.before(currentBreachReceivablesEntity.getStartTime())) {
			currentBreachReceivablesEntity.setState(1);
			String receiveHistorySql = "SELECT * FROM (SELECT r.receiptDate, rr.currentAmount, rr.receivedAmount FROM t_community_receipt_receivables rr "
					+ " INNER JOIN t_community_receipt r ON rr.receiptId =  r.id" + " WHERE rr.receivablesId = "
					+ receivable.getId() + " AND r.receiptDate <= '"
					+ DateUtil.formatByStyle(breachEndDate, "yyyy-MM-dd 23:59:59") + "'" 
					+ " UNION"
					+ " SELECT k.changeDate as receiptDate, (0-k.changeAmount) as currentAmount, k.receivedAmount as receivedAmount FROM t_community_receivables_changes k "
					+ " WHERE k.receivablesId = " + receivable.getId() + " AND k.changeDate <= '" 
					+ DateUtil.formatByStyle(breachEndDate, "yyyy-MM-dd 23:59:59") + "'" 
					+ " AND k.state = 1) s ORDER BY s.receiptDate ASC";
			// logger.info("1--");
			@SuppressWarnings("unchecked")
			List<Object[]> objList = querySession.createNativeQuery(receiveHistorySql).list();
			if(changeList != null) {
				for(CancelBenefitAmountChangeDetailItem  changeItem : changeList) {
					objList.add(new Object[] { changeItem.getChangeDate(), changeItem.getChangeAmount(), BigDecimal.ZERO } );
				}
				Collections.sort(objList,new Comparator<Object[]>() {
					@Override
					public int compare(Object[] o1, Object[] o2) {
						Date date1 = (Date) o1[0];
						Date date2 = (Date) o2[0];
						return date1.compareTo(date2);
					}
					
				});
			}
			// logger.info("2--");
			// 预收收款日期早于应收日期，如果也产生违约金会有问题。
			Object[] head = new Object[] { receivable.getReceivableDate(), BigDecimal.ZERO, BigDecimal.ZERO };
			Object[] tail = new Object[] { breachEndDate, BigDecimal.ZERO, receivable.getReceivedAmount() };
			objList.add(0, head);
			
			objList.add(objList.size(), tail);
			// 把日期清零
			objList.forEach(o -> {
				o[0] = DateUtil.resetStartOfDay((Date) o[0]);
			});
			// 根据动账记录分段计算违约金，只计算系统割接后的违约金
			// 原始应收未收金额
			calBreachAmountResult = calBreachAmountChangeAmountHistory(receivable,objList,startCaculateDate,breachEndDate);
		}
		
		if ((calBreachAmountResult.getTotalBreachAmount().compareTo(BigDecimal.ZERO) == 0
				|| calBreachAmountResult.getTotalBreachAmount().setScale(2, RoundingMode.HALF_UP).compareTo(BigDecimal.ZERO) == 0)
				&& currentBreachReceivablesEntity.getId() == null && receivable.getBenefitValue() == null) {
			// 没有产生违约金，不生成违约金应收
			return null;
		}

		String endTimeStr = DateUtil.formatByStyle(calBreachAmountResult.getDisplayDate(), "yyyy-MM-dd 23:59:59");
		String receivableDateStr = DateUtil.formatByStyle(calBreachAmountResult.getDisplayDate(), "yyyy-MM-dd 00:00:00");
		try {
			currentBreachReceivablesEntity.setEndTime(DateUtil.parse(endTimeStr, 1));
			currentBreachReceivablesEntity.setPaymentPeriod(DateUtil.parse(receivableDateStr, 1));
			currentBreachReceivablesEntity.setReceivableDate(DateUtil.parse(receivableDateStr, 1));
		} catch (ParseException e) {
			e.printStackTrace();
		}
		BigDecimal orgnalBreachReceivableAmount = currentBreachReceivablesEntity.getReceivableAmount();
		currentBreachReceivablesEntity.setReceivableAmount(calBreachAmountResult.getTotalBreachAmount().setScale(2, RoundingMode.HALF_UP));
		if (currentBreachReceivablesEntity.getId() == null && changeList == null) {
			// 新增的应收，生成编号
			currentBreachReceivablesEntity.setReceivablesNO(generateSnLastValue(RECEIVABLESNO_SN_TYPE).toString());
		}
		String logPrefix = currentBreachReceivablesEntity.getId() == null ? "生成违约金:"
				: ("修改违约金[" + currentBreachReceivablesEntity.getId() + "]:");
		logger.debug(logPrefix + "违约金额[{}]=>[{}]元", orgnalBreachReceivableAmount,
				currentBreachReceivablesEntity.getReceivableAmount().setScale(2, RoundingMode.HALF_UP));
		return currentBreachReceivablesEntity;
	}
	
	/**
	 * 根据动账历史，计算管理费的违约金
	 * @param receivable		管理费应收对象
	 * @param objList 			动账历史记录Object[0]=动账时间,Date类型；Object[1]=当次动账金额，收款/减免为正，加收为负数,BigDecimal类型；
	 * @param startCaculateDate 开始计算时间
	 * @param breachEndDate     结束计算时间
	 * @return CalBreachAmountChangeAmountHistoryResultVo 对象， displayDate 实际结束计算日期；totalBreachAmount 对应的违约金额
	 */
	private CalBreachAmountChangeAmountHistoryResultVo calBreachAmountChangeAmountHistory(CommunityReceivablesEntity receivable, List<Object[]> objList, Date startCaculateDate, Date breachEndDate) {
		CalBreachAmountChangeAmountHistoryResultVo result = new CalBreachAmountChangeAmountHistoryResultVo(startCaculateDate, null);
		Date displayDate = breachEndDate;
		BigDecimal totalBreachAmount = BigDecimal.ZERO;
		BigDecimal currentAmount = receivable.getReceivableAmount();
		int segementCount = 0;
		BigDecimal currentNeedPayAmount = currentAmount;
		for (int i = 1; i < objList.size(); i++) {
			Date currentDate = (Date) objList.get(i)[0];
			currentNeedPayAmount = currentNeedPayAmount.subtract((BigDecimal) objList.get(i - 1)[1]);
			if (!currentDate.before(startCaculateDate)) {
				Date lastChangeAmountTime = (Date) objList.get(i - 1)[0];
				Date satrtTime = lastChangeAmountTime.before(startCaculateDate) ? startCaculateDate
						: lastChangeAmountTime;
				long subDate = DateUtil.subDate(currentDate, satrtTime);
				if (i == objList.size() - 1) {
					subDate = subDate + 1;
				} else {
					satrtTime = DateUtil.getOffsetDateByDay(satrtTime, 1); // 为了正确显示时间
				}
				// 未到计算日期已经收完本金，在satrtTime收完
				if (currentNeedPayAmount.compareTo(BigDecimal.ZERO) == 0) {
					displayDate = DateUtil.getOffsetDateByDay(satrtTime, -1);
					if (displayDate.before(communityContextInfo.cutoverDate)) {
						displayDate = communityContextInfo.cutoverDate;
					}
					break;
				}
				BigDecimal dist = BigDecimal.valueOf(subDate);
				BigDecimal incrementalAmount = currentNeedPayAmount.multiply(receivable.getPayItem().getBreachRatio())
						.multiply(dist);
				totalBreachAmount = totalBreachAmount.add(incrementalAmount);

				logger.debug("第[{}]段，起始时间[{}]，结束时间[{}],共[{}]天，本金[{}],产生违约金[{}]", ++segementCount,
						DateUtil.format(satrtTime, 1), DateUtil.format(currentDate, 1), subDate,
						currentNeedPayAmount, totalBreachAmount);
			}
		}
		result.setDisplayDate(displayDate);
		// 基于假设用户同一天把该应收的所有欠款都交完，临时处理方案
		result.setTotalBreachAmount(totalBreachAmount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : totalBreachAmount);
		//result.setTotalBreachAmount(totalBreachAmount);
		return result;
	}

	private List<CommunityReceivablesEntity> calManageFeeByEstate(Integer estateId, String payItemIds,
			LocalDate startDate, LocalDate endDate, Session session) {
		List<CommunityReceivablesEntity> result = new ArrayList<CommunityReceivablesEntity>();

		if (startDate == null) {
			startDate = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
		}
		if (endDate == null) {
			endDate = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
		}
		Integer rentFlag = 0; // 都不生成租金收费
		StringBuilder baseSql = new StringBuilder(
				"SELECT a.id,a.unitcode,a.additionalArea,a.chargingArea,d.parentPropertyId,a.paymentaccountId,"
						+ "b.payItemsId,c.chargeCategory,c.comment,c.endtime,c.feecaltype,c.itemsname,c.price,c.priceunit,c.starttime,"
						+ "a.acceptanceDate,d.billingDate,c.payDate,d.isCurrentMember,d.iscurrentowner,d.memberid,d.terminationDate,d.endDate,a.rentState, d.id AS memberPropertyId "
						+ "FROM t_community_property a LEFT JOIN t_community_member_property d ON a.id = d.propertyid, "
						+ "t_community_property_pay_items b,t_community_pay_items c "
						+ "WHERE a.id = b.propertyid AND b.payitemsid = c.id AND c.state = 1 "
						+ "AND c.chargeCategory != 4 AND c.chargeCategory != 5 AND c.chargeCategory != 9 AND d.iscurrentowner = 1 AND d.isCurrentMember = 1 "
						+ "AND a.specialFeeFlag IN (0,2,3,5) AND c.starttime <= '" + startDate.toString()
						+ " 00:00:00' AND c.endTime>='" + endDate.toString() + " 00:00:00'");
		baseSql.append(StringUtils.isNotEmpty(payItemIds) ? (" AND c.id IN (" + payItemIds + ")") : "");
		StringBuilder estateObjectSql = new StringBuilder(baseSql.toString());
		estateObjectSql.append((rentFlag.intValue() == 0 || rentFlag.intValue() == 3) ? " AND c.payDate != 1 " : "");
		estateObjectSql.append(null != estateId ? " AND a.id = " + estateId : "");

		// 把车位收费也加进来
		estateObjectSql.append(" union ");
		estateObjectSql.append(baseSql.toString());
		estateObjectSql.append((rentFlag.intValue() == 0 || rentFlag.intValue() == 2) ? " AND c.payDate != 1 " : "");
		estateObjectSql.append(null != estateId ? " AND d.parentPropertyId = " + estateId : "");

		@SuppressWarnings("unchecked")
		List<Object[]> resultList = session.createNativeQuery(estateObjectSql.toString()).list();
		// 互斥组
		Set<String> groupFeeSet = new HashSet<String>();
		for (Object[] temp : resultList) {
			EstateReceivablesVo o = EstateReceivablesVo.getEstateReceivablesVo(temp);
			if ((null == o.getParentPropertyId() || (null != o.getParentPropertyId()
					&& (null == o.getEndDate() || o.getEndDate().isAfter(endDate) || o.getEndDate().isEqual(endDate))))
					&& (((int) (o.getBillingDate().toEpochDay() - startDate.toEpochDay()) >= 0
							&& (int) (endDate.toEpochDay() - o.getBillingDate().toEpochDay()) >= 0)
							|| ((int) (o.getBillingDate().toEpochDay() - startDate.toEpochDay()) <= 0))) {
				CommunityReceivablesEntity receivables = new CommunityReceivablesEntity();
				receivables.setPayItemsName(o.getItemsName());
				receivables.setChargeCategory(CommunityPayItemsEntity.getChargeCategoryStr(o.getChargeCategory()));
				receivables.setChargeSource("收费设定");
				receivables.setComment(o.getUnitCode() + "：" + startDate.format(DateTimeFormatter.ofPattern("yyyy年MM月"))
						+ (null != o.getParentPropertyId() ? "车位费" : "管理费"));
				CommunityEstateEntity estate = getEstate(
						null != o.getParentPropertyId() ? o.getParentPropertyId() : o.getId());
				if (estate.getSpecialFeeFlag().equals(0) || estate.getSpecialFeeFlag().equals(2)
						|| estate.getSpecialFeeFlag().equals(3)) {

					receivables.setEstate(estate);
					receivables.setSubEstateId(null != o.getParentPropertyId() ? o.getId() : null);
					receivables.setPayItem(getPayItems(o.getPayItemsId()));
					receivables.setState(1);
					BigDecimal receivableAmount = new BigDecimal("0");
					/*
					 * 管理费根据单元的计费日期开始计算管理费 车位管理费根据车位租用时间开始计算
					 */
					LocalDate billingDate = o.getBillingDate().with(TemporalAdjusters.firstDayOfMonth())
							.isEqual(startDate) ? o.getBillingDate() : startDate;
					
					if (o.getFeeCalType().equals(CommunityContext.COMMUNITY_PAY_ITEMS_CAL_TYPE_CHARGE)) {
						receivableAmount = JepUtil.cal("单价*收费面积/本月天数*计费天数", 4, o.getPrice(), o.getChargingArea(),
								CommunityDateUtil.monthDays(endDate.toString()),
								CommunityDateUtil.subDate(billingDate.toString(), endDate.toString()));
					} else if (o.getFeeCalType().equals(CommunityContext.COMMUNITY_PAY_ITEMS_CAL_TYPE_ADD)) {
						receivableAmount = JepUtil.cal("单价*附加面积/本月天数*计费天数", 4, o.getPrice(), o.getAdditionalArea(),
								CommunityDateUtil.monthDays(endDate.toString()),
								CommunityDateUtil.subDate(billingDate.toString(), endDate.toString()));
					} else {
						receivableAmount = JepUtil.cal("单价/本月天数*计费天数", 4, o.getPrice(),
								CommunityDateUtil.monthDays(endDate.toString()),
								CommunityDateUtil.subDate(billingDate.toString(), endDate.toString()));
					}

					receivables.setReceivableAmount(receivableAmount.setScale(2, RoundingMode.HALF_UP));
					receivables.setReceivedAmount(new BigDecimal("0"));
					receivables.setSourceNotes(o.getItemsName() + "(" + o.getUnitCode() + ")");
					receivables.setLockMark(0);
					if (o.getPayDate() != null && o.getPayDate().intValue() > 0 && o.getPayDate().intValue() < 32) {
						// paydate[1,28]按生成应收日期,但当>28时需要判断闰收及大小月，后面有需求再优化。
						try {
							receivables.setReceivableDate(Date
									.from(LocalDate.of(startDate.getYear(), startDate.getMonthValue(), o.getPayDate())
											.atStartOfDay(ZoneId.systemDefault()).toInstant()));
						} catch (DateTimeException e) {
							// 如果paydate不正确，划超出当月范围，应收日期为月未最后一日
							receivables.setReceivableDate(
									Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
						}
					} else {
						// paydate > 31 应收日期为月未最后一日 ,未实现生成下个月2日起逻辑
						receivables
								.setReceivableDate(Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
					}
					if ((int) (o.getBillingDate().toEpochDay() - startDate.toEpochDay()) >= 0
							&& (int) (endDate.toEpochDay() - o.getBillingDate().toEpochDay()) >= 0) {
						receivables
								.setStartTime(Date.from(billingDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
					} else {
						receivables.setStartTime(Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
					}

					receivables.setEndTime(Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
					receivables.setPaymentPeriod(receivables.getReceivableDate());

					// 如查应表中存在已经生成相同应收，则通过修改方式修改应收记录
					StringBuilder dbSql = new StringBuilder(
							"select a.* from t_community_receivables a where a.estateid="
									+ (null != o.getParentPropertyId() ? o.getParentPropertyId() : o.getId())
									+ " and a.payItemId = " + receivables.getPayItem().getId()
									+ " and a.receivableDate='" + DateUtil.format(receivables.getReceivableDate(), 0)
									+ "'").append(
											null != receivables.getSubEstateId()
													&& !receivables.getSubEstateId().equals(0)
															? " and a.subEstateId=" + receivables.getSubEstateId()
															: "");
					// logger.info("----1");
					CommunityReceivablesEntity dbReceivables = (CommunityReceivablesEntity) session
							.createNativeQuery(dbSql.toString()).addEntity(CommunityReceivablesEntity.class)
							.uniqueResult();
					// logger.info("----2");
					if (receivables.getReceivableAmount().doubleValue() > 0) {
						if (null != dbReceivables) {
							receivables.setId(dbReceivables.getId());
							receivables.setReceivablesNO(dbReceivables.getReceivablesNO());
							receivables.setCreateTime(dbReceivables.getCreateTime());
							receivables.setReceivedAmount(dbReceivables.getReceivedAmount());
							receivables.setLastModifyTime(new Timestamp(System.currentTimeMillis()));
						} else {
							// 如查应表中存在已经生成的互斥收费项目的应收，则跳过不生成该应收。
							if (receivables.getPayItem().getGroupFee() != null) {
								StringBuilder sameGroupSql = new StringBuilder(
										"select a.* from t_community_receivables a inner join t_community_pay_items i on a.payItemId = i.id where a.estateid="
												+ (receivables.getEstate().getId()) + " and a.receivableDate='"
												+ DateUtil.format(receivables.getReceivableDate(), 0) + "'"
												+ " and i.groupFee = " + receivables.getPayItem().getGroupFee())
														.append(null != receivables.getSubEstateId()
																? " and a.subEstateId=" + receivables.getSubEstateId()
																: "");
								CommunityReceivablesEntity sameGroupReceivables = communityReceivablesDao
										.getUniqueBySql(sameGroupSql.toString());
								if (sameGroupReceivables != null) {
									continue;
								}
							}

							// 新增的要生成编号
							receivables.setReceivablesNO(generateSnLastValue(RECEIVABLESNO_SN_TYPE).toString());
							// 已经生成的互斥收费项目的应收，则跳过不生成该应收。
							// 判断标准相同单元、相同应收日期、相同子单元、互斥ID的项目
							if (receivables.getPayItem().getGroupFee() != null) {
								String identify = receivables.getEstate().getId().toString() + "_"
										+ DateUtil.format(receivables.getReceivableDate(), 0) + "_"
										+ ((null != receivables.getSubEstateId()
												&& !receivables.getSubEstateId().equals(0))
														? receivables.getSubEstateId().toString()
														: "")
										+ "_" + receivables.getPayItem().getGroupFee().toString();

								if (groupFeeSet.contains(identify)) {
									continue;
								} else {
									groupFeeSet.add(identify);
								}
							}
						}
						// 管理费优惠计算
						if(receivables.getPayItem().getEnableBenefitPolicy().intValue() == 1) {
							BigDecimal orginAmount = JepUtil.cal("单价*收费面积/本月天数*计费天数", 4, receivables.getPayItem().getBenefitOrginPrice(), o.getChargingArea(),
									CommunityDateUtil.monthDays(endDate.toString()),
									CommunityDateUtil.subDate(billingDate.toString(), endDate.toString()));
							calBenefitReceivables(receivables, orginAmount.setScale(2, RoundingMode.HALF_UP));
						}
						
						result.add(receivables);
					}
				}
			}
		}
		return result;
	}
	
	@Override
	public IResponse createPayingInSlipPdf(CommunityReceivablesReq req) {
		CreatePayingInSlipPdfRes res = new CreatePayingInSlipPdfRes();
		if ((null != req.getEstateId() || StringUtils.isNotEmpty(req.getUnitCode())) && null != req.getFetchAll()) {
			if (null != req.getEstateId() || StringUtils.isNotEmpty(req.getUnitCode())) {
				Integer estateId = req.getEstateId();
				CommunityEstateEntity estate = communityEstateDao
						.getUniqueByHql("from CommunityEstateEntity a where a.unitCode='" + req.getUnitCode()
								+ "' or id=" + req.getEstateId());
				if (null != estate) {
					estateId = estate.getId();
				} else {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
					return res;
				}
				
				String wxPagePath="";
				List<DictionaryDataEntity> wxDictionaryDataList = dictionaryDataDao
						.getListByHql("select distinct a from DictionaryDataEntity a "
								+ " inner join a.dictionary b where b.directoryCode='appletPagePath' and a.dataName='wxPagePath' and a.state=1", "");
				if (null != wxDictionaryDataList && wxDictionaryDataList.size() > 0) {
					wxPagePath = wxDictionaryDataList.get(0).getDataKey();
				} else {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo("请在数据字典里配置“微信交款通知单页面路径”,directoryCode为\"appletPagePath\"！");
					return res;
				}
				
				String alipayPagePath="";
				List<DictionaryDataEntity> alipayDictionaryDataList = dictionaryDataDao
						.getListByHql("select distinct a from DictionaryDataEntity a "
								+ " inner join a.dictionary  b where b.directoryCode='appletPagePath' and a.dataName='alipayPagePath' and a.state=1", "");
				if (null != alipayDictionaryDataList && alipayDictionaryDataList.size() > 0) {
					alipayPagePath = alipayDictionaryDataList.get(0).getDataKey();
				} 
				
				LocalDate startDate = null;

				if (StringUtils.isNotEmpty(req.getStartTime())) {
					startDate = LocalDate.parse(req.getStartTime().substring(0, 10))
							.with(TemporalAdjusters.firstDayOfMonth());
				} else {
					startDate = LocalDate.of(1990, 12, 31);
				}

				LocalDate endDate = null;
				if (StringUtils.isNotEmpty(req.getEndTime())) {
					endDate = LocalDate.parse(req.getEndTime().substring(0, 10))
							.with(TemporalAdjusters.lastDayOfMonth());
				} else {
					endDate = LocalDate.of(2099, 12, 31);
				}
				// GetCommunityReceivablesListRes receivablesListRes =
				// (GetCommunityReceivablesListRes) getAdminCommunityReceivablesList(req);
				List<CommunityReceivablesEntity> receivablesList = communityReceivablesDao
						.getListByHql(queryHql(req, estateId, startDate, endDate));
				Map<String, Object> params = new HashMap<String, Object>();
				Date date = new Date();
				String userName = "";
				for (CommunityMemberPropertyEntity memberProperty : estate.getMemberPropertyList()) {
					if (memberProperty.getIsCurrentMember() == 1 && memberProperty.getMemberType() == 0) {
						userName = memberProperty.getMember().getUserName();
						break;
					}
				}
				params.put("userName", userName);
				params.put("date", DateUtil.format(date, 0));
				params.put("estateAddress", estate.getBuilding().getDistrict().getDistrictName()
						+ estate.getBuilding().getBuildingName() + estate.getFloor() + estate.getRoomNumber());
				params.put("buildingArea", estate.getBuildingArea().toString());
				List<Map<String, String>> list = new ArrayList<Map<String, String>>();
				//Map<Integer, Map<String, Object>> compoundMap = getCompoundReceivables(receivablesList);
				Map<String, Map<String, Object>> compoundMap = getCompoundReceivables(receivablesList);
				BigDecimal total = new BigDecimal(0);
				//for (Integer id : compoundMap.keySet()) {
				for (String payItemsName : compoundMap.keySet()) {
					Map<String, String> map = new HashMap<String, String>();
					Map<String, Object> data = compoundMap.get(payItemsName);
					if (((Date) data.get("endReceivableDate")).compareTo((Date) data.get("startReceivableDate")) == 0) {
						map.put("period", DateUtil.formatShortFormat((Date) data.get("startReceivableDate"))
								.substring(0, 7).replaceAll("-", "."));
					} else {
						map.put("period",
								DateUtil.formatShortFormat((Date) data.get("startReceivableDate")).substring(0, 7)
										.replaceAll("-", ".") + "-"
										+ DateUtil.formatShortFormat((Date) data.get("endReceivableDate"))
												.substring(0, 7).replaceAll("-", "."));
					}
					map.put("payItemsName", (String) data.get("payItemsName"));
					BigDecimal dosage = (BigDecimal) data.get("dosage");
					map.put("dosage", dosage.compareTo(new BigDecimal("0"))==0 ? "":dosage.toString());
					map.put("unitPrice", (String) data.get("unitPrice"));
					BigDecimal amount = (BigDecimal) data.get("amount");
					map.put("amount", amount.toString());
					map.put("chargeCategory", data.get("chargeCategory").toString());
					total = total.add(amount);
					list.add(map);
				}

				Collections.sort(list, (p1, p2) -> ((Map<String,String>) p1).get("chargeCategory")
						.compareTo(((Map<String,String>) p2).get("chargeCategory")));
				params.put("receivablesList", list);
				params.put("total", total.toString() + "元");
				params.put("capitalization", ChineseNumberUtil.convert(total.toString()));

				SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
				String today = sdf.format(date);
				String dir = contextInfo.getAssetFilePath() + File.separator + "receivables" + File.separator + today
						+ File.separator;
				createMultilayerFile(dir);
				File templateFile = new File(
						contextInfo.getAssetFilePath() + File.separator + "payingInSlipTemplate.ftl");
				if (!templateFile.exists()) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("模板不存在，请重新上传模板！");
					return res;
				}
				String fileName = estate.getUnitCode();
				List<Image> imageList = new ArrayList<>();
				WxParameterEntity wxParameter = new WxParameterEntity();
				String key="";
				try {
					wxParameter.setDataValue(new ObjectMapper().writeValueAsString(req));
					wxParameter.setScene(0);
					wxParameterDao.save(wxParameter);
					key = DigestUtil.sm3Digest(System.currentTimeMillis() + wxParameter.getId().toString()).substring(0,32);
				} catch (Exception e1) {
					e1.printStackTrace();
				}
				wxParameter.setKeyName(key);
				
				// 生成微信小程序 
				if(StringUtils.isNotEmpty(wxPagePath)) {
					BufferedImage image = null;
					try {
						// 从缓存中获取小程序accessToken
						IWeiXinApiService weiXinApiService = (IWeiXinApiService) SpringHandler.getBean("weiXinApiService");
						//String accessToken = weiXinApiService.getAccessToken(1);
						String accessToken = weiXinApiService.getAccessToken(1,"phmj");
						// 生成小程序码，sence和page参数需要与小程序前端研发对接
						byte[] result = WeiXinApiUtil.getWxaCodeUnlimit(key, wxPagePath,
								accessToken);
						if (result.length < 1024) {
							String errmsg = new String(result);
							logger.error("获取微信小程序码失败！{}", errmsg);
							String retMsg = null;
							if(errmsg.contains("40001")) {
								logger.error("accessToken已经失效，重新访问微信服务器刷新aceessToken!");
								weiXinApiService.getAccessToken(1,"phmj", true);
								retMsg = "微信服务器凭证失效，系统已经尝试恢复，请重新操作一次！";
							}
							else {
								retMsg = "获取微信小程序码失败，请检查微信相关配置或稍后再试！";
							}
							wxParameterDao.delete(wxParameter);
							res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
							res.setRetInfo(retMsg);
							return res;
						} else {
							ByteArrayInputStream bais = new ByteArrayInputStream(result);
							image = ImageIO.read(bais);
						}

						Image img = Image.getInstance(image, null, false);
						img.setAlignment(Image.LEFT | Image.TEXTWRAP);
						img.scaleToFit(60, 60);// 大小
						img.setAbsolutePosition(485, 781);
						imageList.add(img);
					} catch (Exception e1) {

						e1.printStackTrace();
					}

					File logofile = new File(contextInfo.getAssetFilePath() + File.separator + "logo.png");
					if (!logofile.exists()) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo("LOGO不存，请上传LOGO图片！");
						return res;
					}
					BufferedImage logoImage = null;
					try {
						logoImage = ImageIO.read(logofile);
					} catch (IOException e) {
						e.printStackTrace();
					}
					Image img = null;
					try {
						img = Image.getInstance(logoImage, null, false);
					} catch (BadElementException e) {
						e.printStackTrace();
					} catch (IOException e) {
						e.printStackTrace();
					}
					if (null != img) {
						img.setAlignment(Image.LEFT | Image.TEXTWRAP);
						img.scaleToFit(25, 25);// 大小
						img.setAbsolutePosition(503, 797);
						imageList.add(img);
					}
				}
			
				// 生成支付宝小程码
				if(StringUtils.isNotEmpty(alipayPagePath)) {
					try {
						
						//String departmentCode = req.getDepartmentCode();  //如果系统同事使用不同的物业公司支付和微信，需要请求时把departmentCode传过来
						String departmentCode = "root";
						String alipayQrCodeUrl = "";
						DepartmentEntity department = getDepartmentByCode(departmentCode);
						if(department == null){
							res.setRet(ResponseContext.RES_DATA_NULL_CODE);
							res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "找不到Department[DepartmentCode=" + departmentCode + "]对应的数据");
							return res;
						}
						
//						WxParameterEntity alipayParameter = new WxParameterEntity();
//						alipayParameter.setDataValue(new ObjectMapper().writeValueAsString(req));
//						alipayParameter.setScene(0);
//						wxParameterDao.save(alipayParameter);
//						String key = DigestUtil.sm3Digest(System.currentTimeMillis() + alipayParameter.getId().toString()).substring(0,32);
//						alipayParameter.setKeyName(key);
						
						if(StringUtils.isNotEmpty(department.getAlipayMiniProgramAppId()) 
								&& StringUtils.isNotEmpty(department.getAlipayApplicationPrivateKey())  
								&& StringUtils.isNotEmpty(department.getAlipayPulbicKey()) ) {
							alipayQrCodeUrl = AlipayApiUtil.getAlipayQrCodeUnlimit("scene="+key, 
									alipayPagePath, department.getAlipayMiniProgramAppId(), 
									department.getAlipayApplicationPrivateKey(), department.getAlipayPulbicKey(), 1);
						}
						else {
							res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
							res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "Department[" + departmentCode + "]表中没有配置对应的支付宝小程序参数");
							//wxParameterDao.delete(alipayParameter);
							return res;
						}
						
						if(StringUtils.isNotEmpty(alipayQrCodeUrl)) {
							java.awt.Image alipayImage = PicUtil.loadImage(alipayQrCodeUrl);
							Image alipayImg = Image.getInstance(alipayImage, null, false);
							alipayImg.setAlignment(Image.LEFT | Image.TEXTWRAP);
							alipayImg.scaleToFit(80, 80);// 大小
							alipayImg.setAbsolutePosition(405, 768);
							imageList.add(alipayImg);
						}else {
							logger.error("获取支付宝小程序码失败！");
							//wxParameterDao.delete(alipayParameter);
						}
					
					}
					catch (IOException | BadElementException e) {
						e.printStackTrace();
					}
				}

				// 合成PDF
				if (FreemarkUtil.generatePdf(contextInfo.getAssetFilePath(), "payingInSlipTemplate.ftl", params,
						dir + fileName + ".pdf", imageList)) {
					res.setPdfUrl(contextInfo.getAssetFileUrl() + "/" + "receivables" + "/" + today + "/" + "/"
							+ fileName + ".pdf");

					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} else {
					res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
				}

				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {

			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	
	@Override
	public IResponse createPayingInSlipPdfTest(CommunityReceivablesReq req) {
		CreatePayingInSlipPdfRes res = new CreatePayingInSlipPdfRes();
		if ((null != req.getEstateId() || StringUtils.isNotEmpty(req.getUnitCode())) && null != req.getFetchAll()) {
			if (null != req.getEstateId() || StringUtils.isNotEmpty(req.getUnitCode())) {
				Integer estateId = req.getEstateId();
				CommunityEstateEntity estate = communityEstateDao
						.getUniqueByHql("from CommunityEstateEntity a where a.unitCode='" + req.getUnitCode()
								+ "' or id=" + req.getEstateId());
				if (null != estate) {
					estateId = estate.getId();
				} else {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
					return res;
				}
				
				
				LocalDate startDate = null;

				if (StringUtils.isNotEmpty(req.getStartTime())) {
					startDate = LocalDate.parse(req.getStartTime().substring(0, 10))
							.with(TemporalAdjusters.firstDayOfMonth());
				} else {
					startDate = LocalDate.of(1990, 12, 31);
				}

				LocalDate endDate = null;
				if (StringUtils.isNotEmpty(req.getEndTime())) {
					endDate = LocalDate.parse(req.getEndTime().substring(0, 10))
							.with(TemporalAdjusters.lastDayOfMonth());
				} else {
					endDate = LocalDate.of(2099, 12, 31);
				}
				// GetCommunityReceivablesListRes receivablesListRes =
				// (GetCommunityReceivablesListRes) getAdminCommunityReceivablesList(req);
				List<CommunityReceivablesEntity> receivablesList = communityReceivablesDao
						.getListByHql(queryHql(req, estateId, startDate, endDate));
				Map<String, Object> params = new HashMap<String, Object>();
				Date date = new Date();
				String userName = "";
				for (CommunityMemberPropertyEntity memberProperty : estate.getMemberPropertyList()) {
					if (memberProperty.getIsCurrentMember() == 1 && memberProperty.getMemberType() == 0) {
						userName = memberProperty.getMember().getUserName();
						break;
					}
				}
				params.put("userName", userName);
				params.put("date", DateUtil.format(date, 0));
				params.put("estateAddress", estate.getBuilding().getDistrict().getDistrictName()
						+ estate.getBuilding().getBuildingName() + estate.getFloor() + estate.getRoomNumber());
				params.put("buildingArea", estate.getBuildingArea().toString());
				List<Map<String, String>> list = new ArrayList<Map<String, String>>();
				//Map<Integer, Map<String, Object>> compoundMap = getCompoundReceivables(receivablesList);
				Map<String, Map<String, Object>> compoundMap = getCompoundReceivables(receivablesList);
				BigDecimal total = new BigDecimal(0);
				//for (Integer id : compoundMap.keySet()) {
				for (String payItemsName : compoundMap.keySet()) {
					Map<String, String> map = new HashMap<String, String>();
					Map<String, Object> data = compoundMap.get(payItemsName);
					if (((Date) data.get("endReceivableDate")).compareTo((Date) data.get("startReceivableDate")) == 0) {
						map.put("period", DateUtil.formatShortFormat((Date) data.get("startReceivableDate"))
								.substring(0, 7).replaceAll("-", "."));
					} else {
						map.put("period",
								DateUtil.formatShortFormat((Date) data.get("startReceivableDate")).substring(0, 7)
										.replaceAll("-", ".") + "-"
										+ DateUtil.formatShortFormat((Date) data.get("endReceivableDate"))
												.substring(0, 7).replaceAll("-", "."));
					}
					map.put("payItemsName", (String) data.get("payItemsName"));
					BigDecimal dosage = (BigDecimal) data.get("dosage");
					map.put("dosage", dosage.compareTo(new BigDecimal("0"))==0 ? "":dosage.toString());
					map.put("unitPrice", (String) data.get("unitPrice"));
					BigDecimal amount = (BigDecimal) data.get("amount");
					map.put("amount", amount.toString());
					map.put("chargeCategory", data.get("chargeCategory").toString());
					total = total.add(amount);
					list.add(map);
				}

				Collections.sort(list, (p1, p2) -> ((Map<String,String>) p1).get("chargeCategory")
						.compareTo(((Map<String,String>) p2).get("chargeCategory")));
				params.put("receivablesList", list);
				params.put("total", total.toString() + "元");
				params.put("capitalization", ChineseNumberUtil.convert(total.toString()));

				SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
				String today = sdf.format(date);
				String dir = contextInfo.getAssetFilePath() + File.separator + "receivables" + File.separator + today
						+ File.separator;
				createMultilayerFile(dir);
				File templateFile = new File(
						contextInfo.getAssetFilePath() + File.separator + "payingInSlipTemplate.ftl");
				if (!templateFile.exists()) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("模板不存在，请重新上传模板！");
					return res;
				}
				String fileName = estate.getUnitCode();
				List<Image> imageList = new ArrayList<>();

			
				// 生成微信小程序 

					BufferedImage image = null;
					try {
						// 从缓存中获取小程序accessToken
						IWeiXinApiService weiXinApiService = (IWeiXinApiService) SpringHandler.getBean("weiXinApiService");
						//String accessToken = weiXinApiService.getAccessToken(1);
						String accessToken = weiXinApiService.getAccessToken(1,"phmj");
						// 生成小程序码，sence和page参数需要与小程序前端研发对接
						byte[] result = WeiXinApiUtil.getWxaCodeUnlimit("b6aa85544941d233c67e029e4d34387e", "pages/testMpCode/testMpCode",
								accessToken);
						if (result.length < 1024) {
							String errmsg = new String(result);
							logger.error("获取微信小程序码失败！{}", errmsg);
							String retMsg = null;
							if(errmsg.contains("40001")) {
								logger.error("accessToken已经失效，重新访问微信服务器刷新aceessToken!");
								weiXinApiService.getAccessToken(1,"phmj", true);
								retMsg = "微信服务器凭证失效，系统已经尝试恢复，请重新操作一次！";
							}
							else {
								retMsg = "获取微信小程序码失败，请检查微信相关配置或稍后再试！";
							}
							res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
							res.setRetInfo(retMsg);
							return res;
						} else {
							ByteArrayInputStream bais = new ByteArrayInputStream(result);
							image = ImageIO.read(bais);
						}

						Image img = Image.getInstance(image, null, false);
						img.setAlignment(Image.LEFT | Image.TEXTWRAP);
						img.scaleToFit(100, 100);// 大小
						img.setAbsolutePosition(285, 581);
						imageList.add(img);
					} catch (Exception e1) {

						e1.printStackTrace();
					}

					File logofile = new File(contextInfo.getAssetFilePath() + File.separator + "logo.png");
					if (!logofile.exists()) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo("LOGO不存，请上传LOGO图片！");
						return res;
					}
					BufferedImage logoImage = null;
					try {
						logoImage = ImageIO.read(logofile);
					} catch (IOException e) {
						e.printStackTrace();
					}
					Image img = null;
					try {
						img = Image.getInstance(logoImage, null, false);
					} catch (BadElementException e) {
						e.printStackTrace();
					} catch (IOException e) {
						e.printStackTrace();
					}
					if (null != img) {
						img.setAlignment(Image.LEFT | Image.TEXTWRAP);
						img.scaleToFit(45, 45);// 大小
						img.setAbsolutePosition(313, 607);
						imageList.add(img);
					}
				
			
				// 合成PDF
				if (FreemarkUtil.generatePdf(contextInfo.getAssetFilePath(), "payingInSlipTemplate.ftl", params,
						dir + fileName + ".pdf", imageList)) {
					res.setPdfUrl(contextInfo.getAssetFileUrl() + "/" + "receivables" + "/" + today + "/" + "/"
							+ fileName + "_test.pdf");

					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} else {
					res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
				}

				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {

			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	
	
	
	// TV端生成微信催缴费二维码
	@Override
	public IResponse createPaymentQrCode(CommunityReceivablesReq req) {
		CreatePaymentQrCodeRes res = new CreatePaymentQrCodeRes();
		CommunityMemberEntity member = null;
		Object userObj = getPrincipal(true);
		if (null != userObj && (userObj instanceof CommunityMemberEntity)) {
			member = (CommunityMemberEntity) userObj;
		} else {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO + "会员未登录！");
			return res;
		}

		Integer estateId = req.getEstateId();

		// 当前用户没有权限查阅该账单
		if (!canManageBillOfEstate(member.getId(), estateId)) {
			res.setRet(ResponseContext.RES_PERM_BEYOND_AUTHORITY_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_BEYOND_AUTHORITY_INFO);
			return res;
		}

		if ((null != req.getEstateId() || StringUtils.isNotEmpty(req.getUnitCode())) && null != req.getFetchAll()) {
			if (null != req.getEstateId() || StringUtils.isNotEmpty(req.getUnitCode())) {
				CommunityEstateEntity estate = communityEstateDao
						.getUniqueByHql("from CommunityEstateEntity a where a.unitCode='" + req.getUnitCode()
								+ "' or id=" + req.getEstateId());
				if (null != estate) {
					estateId = estate.getId();
				} else {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
					return res;
				}

//				String page = "";
//				List<DictionaryDataEntity> dictionaryDataList = dictionaryDataDao.getListByHql(
//						"select distinct a from DictionaryDataEntity a "
//								+ " inner join a.dictionary  b where b.directoryCode='wXAppletPagePath' and a.state=1",
//						"");
//				if (null != dictionaryDataList && dictionaryDataList.size() > 0) {
//					page = dictionaryDataList.get(0).getDataKey();
//				} else {
//					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//					res.setRetInfo("请在数据字典里配置“微信交款通知单页面路径”,directoryCode为\"wXAppletPagePath\"！");
//					return res;
//				}
				String page="";
				List<DictionaryDataEntity> wxDictionaryDataList = dictionaryDataDao
						.getListByHql("select distinct a from DictionaryDataEntity a "
								+ " inner join a.dictionary b where b.directoryCode='appletPagePath' and a.dataName='wxPagePath' and a.state=1", "");
				if (null != wxDictionaryDataList && wxDictionaryDataList.size() > 0) {
					page = wxDictionaryDataList.get(0).getDataKey();
				} else {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo("请在数据字典里配置“微信交款通知单页面路径”,directoryCode为\"appletPagePath\",详情配置项dataName为\"wxPagePath\"！");
					return res;
				}
				
				LocalDate startDate = null;

				if (StringUtils.isNotEmpty(req.getStartTime())) {
					startDate = LocalDate.parse(req.getStartTime().substring(0, 10))
							.with(TemporalAdjusters.firstDayOfMonth());
				} else {
					startDate = LocalDate.of(1990, 12, 31);
				}

				LocalDate endDate = null;
				if (StringUtils.isNotEmpty(req.getEndTime())) {
					endDate = LocalDate.parse(req.getEndTime().substring(0, 10))
							.with(TemporalAdjusters.lastDayOfMonth());
				} else {
					endDate = LocalDate.of(2099, 12, 31);
				}
				// GetCommunityReceivablesListRes receivablesListRes =
				// (GetCommunityReceivablesListRes) getAdminCommunityReceivablesList(req);

				String fileName = estate.getUnitCode();
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
				String today = sdf.format(new Date());
				String dir = contextInfo.getAssetFilePath() + File.separator + "receivables" + File.separator + today
						+ File.separator;
				createMultilayerFile(dir);
				try {
					File file = new File(dir + fileName + "_tv" + ".png");
					if (!file.exists()) {
						// 当天未生成过交款二维码,生生成
						BufferedImage image = null;
						WxParameterEntity wxParameter = new WxParameterEntity();
						wxParameter.setDataValue(new ObjectMapper().writeValueAsString(req));
						wxParameter.setScene(0);
						wxParameterDao.save(wxParameter);
						String key = DigestUtil.sm3Digest(System.currentTimeMillis() + wxParameter.getId().toString())
								.substring(0, 32);
						wxParameter.setKeyName(key);
						// 从缓存中获取小程序accessToken
						IWeiXinApiService weiXinApiService = (IWeiXinApiService) SpringHandler
								.getBean("weiXinApiService");
						// String accessToken = weiXinApiService.getAccessToken(1);
						String accessToken = weiXinApiService.getAccessToken(1, "phmj");
						// 生成小程序码，sence和page参数需要与小程序前端研发对接
						byte[] result = WeiXinApiUtil.getWxaCodeUnlimit(key, page, accessToken);
						if (result.length < 1024) {
							String errmsg = new String(result);
							logger.error("获取微信小程序码失败！{}", errmsg);
							String retMsg = null;
							if (errmsg.contains("40001")) {
								logger.error("accessToken已经失效，重新访问微信服务器刷新aceessToken!");
								weiXinApiService.getAccessToken(1, "phmj", true);
								retMsg = "微信服务器凭证失效，系统已经尝试恢复，请重新操作一次！";
							} else {
								retMsg = "获取微信小程序码失败，请检查微信相关配置或稍后再试！";
							}
							wxParameterDao.delete(wxParameter);
							res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
							res.setRetInfo(retMsg);
							return res;
						} else {
							ByteArrayInputStream bais = new ByteArrayInputStream(result);
							image = ImageIO.read(bais);
						}

						File logofile = new File(contextInfo.getAssetFilePath() + File.separator + "logo.png");
						if (!logofile.exists()) {
							res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
							res.setRetInfo("LOGO不存，请上传LOGO图片！");
							return res;
						}
						BufferedImage logoImage = ImageIO.read(logofile);
						Graphics g = image.getGraphics();
						g.drawImage(logoImage, 110, 110, 208, 208, null);
						ImageIO.write(image, "png", new File(dir + fileName + "_tv" + ".png"));

					}
					res.setImgUrl(contextInfo.getAssetFileUrl() + "/" + "receivables" + "/" + today + "/" 
							+ fileName + "_tv" + ".png");
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} catch (Exception e1) {
					e1.printStackTrace();
					res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
				}

			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	public Map<String, Map<String, Object>> getCompoundReceivables(List<CommunityReceivablesEntity> receivablesList) {
		//Map<Integer, Map<String, Object>> map = new HashMap<Integer, Map<String, Object>>();
		Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();
		Map<String, List<CommunityReceivablesEntity>> collect1 = (Map<String, List<CommunityReceivablesEntity>>) receivablesList
				.parallelStream().collect(groupingBy(CommunityReceivablesEntity::getPayItemsName));
		for (String payItemsName : collect1.keySet()) {
			Map<String, Object> data = new HashMap<String, Object>();
			boolean state = true;
			for(CommunityReceivablesEntity o : collect1.get(payItemsName)) {
				if(state) {
					state = false;
					data.put("payItemsName", o.getPayItemsName());
					data.put("startReceivableDate", o.getReceivableDate());
					data.put("endReceivableDate", o.getReceivableDate());
					data.put("chargeCategory", o.getPayItem().getChargeCategory());
					BigDecimal sourceNotes = new BigDecimal("0");
					if (null != o.getPayItem()) {
						if (null != o.getPayItem()) {
							if (o.getPayItem().getPayDate() != 0 && o.getPayItem()
									.getChargeCategory() != CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_ALLOCATION
									&& o.getPayItem()
											.getChargeCategory() != CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_BREAK
									&& o.getPayItem()
											.getChargeCategory() != CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_DEPOSIT) {
								if(o.getComment().contains("不符合") && o.getPayItem()
										.getChargeCategory() == CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_MANAGEMENT){
									data.put("unitPrice",
											"3.18"+ (StringUtils.isNotEmpty(o.getPayItem().getPriceUnit())
															? o.getPayItem().getPriceUnit()
															: ""));
								}else {
									data.put("unitPrice",
											o.getPayItem().getPrice().toString()
													+ (StringUtils.isNotEmpty(o.getPayItem().getPriceUnit())
															? o.getPayItem().getPriceUnit()
															: ""));
								}

							} else {
								data.put("unitPrice", "");
							}
							if(o.getPayItem()
									.getChargeCategory() == CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_ALLOCATION) {
								sourceNotes = StringUtils.isNotEmpty(o.getSourceNotes())&&!judgeContainsStr(o.getSourceNotes()) ?
										new BigDecimal(o.getSourceNotes()) : new BigDecimal("0");
							}
						}
					}
					data.put("dosage", sourceNotes);
					data.put("amount", o.getReceivableAmount().subtract(o.getReceivedAmount()));
				}else {
					data.put("amount",
							o.getReceivableAmount().subtract(o.getReceivedAmount()).add((BigDecimal) data.get("amount")));
					if (o.getReceivableDate().before((Date) data.get("startReceivableDate"))) {
						data.put("startReceivableDate", o.getReceivableDate());
					} else if (o.getReceivableDate().after((Date) data.get("endReceivableDate"))) {
						data.put("endReceivableDate", o.getReceivableDate());
					}
					BigDecimal sourceNotes = new BigDecimal("0");
					if(o.getPayItem()
							.getChargeCategory() == CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_ALLOCATION) {
						sourceNotes = StringUtils.isNotEmpty(o.getSourceNotes())&&!judgeContainsStr(o.getSourceNotes()) ?
								new BigDecimal(o.getSourceNotes()) : new BigDecimal("0");
						sourceNotes = sourceNotes.add((BigDecimal) data.get("dosage"));
					}
					data.put("dosage", sourceNotes);
				}
			}
			map.put(payItemsName, data);
		}
		
		/*
		for (CommunityReceivablesEntity o : receivablesList) {
			Map<String, Object> data = null;
			// 预防收费项目为空的情况
			Integer payItemsId = null != o.getPayItem() ? o.getPayItem().getId() : o.getId() * 300;
			if (map.containsKey(payItemsId)) {
				data = map.get(payItemsId);
				data.put("amount",
						o.getReceivableAmount().subtract(o.getReceivedAmount()).add((BigDecimal) data.get("amount")));
				if (o.getReceivableDate().before((Date) data.get("startReceivableDate"))) {
					data.put("startReceivableDate", o.getReceivableDate());
				} else if (o.getReceivableDate().after((Date) data.get("endReceivableDate"))) {
					data.put("endReceivableDate", o.getReceivableDate());
				}
				BigDecimal sourceNotes = new BigDecimal("0");
				if(o.getPayItem()
						.getChargeCategory() == CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_ALLOCATION) {
					sourceNotes = StringUtils.isNotEmpty(o.getSourceNotes())&&!judgeContainsStr(o.getSourceNotes()) ?
							new BigDecimal(o.getSourceNotes()) : new BigDecimal("0");
					sourceNotes = sourceNotes.add((BigDecimal) data.get("dosage"));
				}
				data.put("dosage", sourceNotes);
				map.put(payItemsId, data);
			} else {
				data = new HashMap<String, Object>();
				data.put("payItemsName", o.getPayItemsName());
				data.put("startReceivableDate", o.getReceivableDate());
				data.put("endReceivableDate", o.getReceivableDate());
				BigDecimal sourceNotes = new BigDecimal("0");
				if (null != o.getPayItem()) {
					if (null != o.getPayItem()) {
						if (o.getPayItem().getPayDate() != 0 && o.getPayItem()
								.getChargeCategory() != CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_ALLOCATION
								&& o.getPayItem()
										.getChargeCategory() != CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_BREAK
								&& o.getPayItem()
										.getChargeCategory() != CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_DEPOSIT) {
							if(o.getComment().contains("不符合十六区优惠政策") && o.getPayItem()
									.getChargeCategory() == CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_MANAGEMENT){
								data.put("unitPrice",
										"3.18"+ (StringUtils.isNotEmpty(o.getPayItem().getPriceUnit())
														? o.getPayItem().getPriceUnit()
														: ""));
							}else {
								data.put("unitPrice",
										o.getPayItem().getPrice().toString()
												+ (StringUtils.isNotEmpty(o.getPayItem().getPriceUnit())
														? o.getPayItem().getPriceUnit()
														: ""));
							}

						} else {
							data.put("unitPrice", "");
						}
						if(o.getPayItem()
								.getChargeCategory() == CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_ALLOCATION) {
							sourceNotes = StringUtils.isNotEmpty(o.getSourceNotes())&&!judgeContainsStr(o.getSourceNotes()) ?
									new BigDecimal(o.getSourceNotes()) : new BigDecimal("0");
						}
					}
				}
				data.put("dosage", sourceNotes);
				data.put("amount", o.getReceivableAmount().subtract(o.getReceivedAmount()));
				map.put(payItemsId, data);
			}
		}*/

		return map;
	}
	
	public  IResponse sendReceivablesSms(CommunityReceivablesReq req) {
		GenericResponse res = new GenericResponse();
		StringBuilder sql = new StringBuilder(
				"SELECT a.estateId,b.unitCode,(SUM(a.receivableAmount)- SUM(a.receivedAmount))AS amount ,"
				+ "d.homePhone ,b.paymentAccountId FROM t_community_receivables a INNER JOIN `t_community_property` b ON b.id=a.estateId "
				+ "INNER JOIN t_community_member_property c ON c.propertyId=b.id   "
				+ "INNER JOIN t_account d ON d.id=c.memberId INNER JOIN `t_community_building` e ON e.id=b.buildingId "
				+ " INNER JOIN  t_community_district f on f.id=e.districtId  INNER JOIN `t_community_pay_items` g ON g.id= a.payItemId "
				+ "WHERE a.receivableAmount!=a.receivedAmount AND c.memberType=0 "
				+ "AND c.isCurrentMember=1 AND e.buildingType!=2 AND e.buildingType!=3 ");
		sql.append(StringUtils.isNotEmpty(req.getEstateIdList()) ? "  and a.estateId in("+req.getEstateIdList()+")" :"")
			.append(StringUtils.isNotEmpty(req.getBuildingIdList()) ? "  and e.id in("+req.getBuildingIdList()+")" :"")
			.append(StringUtils.isNotEmpty(req.getDistrictIdList()) ? "  and f.id in("+req.getDistrictIdList()+")" :"")
			.append(StringUtils.isNotEmpty(req.getEndTime()) ? " AND a.receivableDate<='"+req.getEndTime()+"'" 
					:" AND a.receivableDate<=DATE_FORMAT(LAST_DAY(NOW()),'%Y-%m-%d')")
			.append(null!=req.getSmsStatus() && 0==req.getSmsStatus() ? "" 
					: "  and (IFNULL (REPLACE (JSON_EXTRACT (`b`.`reservedField`,'$.isSendMessage'),'\"',''),1))=1 ")
			.append(" AND payDate!=0 GROUP BY a.estateId");
		String day = "";
		if(StringUtils.isEmpty(req.getDay())) {
			String d = DateUtil.formatShortFormat(new Date()).substring(8, 10);
			if(d.startsWith("0")){
				day = d.substring(1, 2);
			}else {
				day=d;
			}
		}else {
			day = req.getDay();
		}
//		LocalDate today = LocalDate.now(); 
//	    LocalDate lastDayOfMonth = today.with(TemporalAdjusters.lastDayOfMonth()); 
	    String today = DateUtil.formatShortFormat(new Date());

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();
		@SuppressWarnings("unchecked")
		List<Object[]> resultList = session.createNativeQuery(sql.toString()).list();
		for (Object[] temp : resultList) {
			String unitCode = (null != temp[1] ? temp[1].toString() : "");
			String amount = null != temp[2] ? temp[2].toString() : "";
			String homePhone = (null != temp[3] ? temp[3].toString() : "");
			String paymentAccountId = (null != temp[4] ? temp[4].toString() : "");
			String receivableAmount = (new BigDecimal(amount)).setScale(2, RoundingMode.HALF_UP).toString();
			if(StringUtils.isNotEmpty(homePhone)) {
//				if(StringUtils.isNotEmpty(paymentAccountId)){
//					String verifyCode = "尊敬的"+unitCode+"业户，您有"+receivableAmount+"元未结，本月"+
//							day+"日银行扣费，为避免逾期违约，请确保余额充足，祝生活愉快。";
//					String templateData = "{\"uniCode\":\""+unitCode+"\",\"amount\":\""+receivableAmount
//							+"\",\"date\":\""+day+"\"}";
//					sendSms(homePhone.replaceAll("、", ",").replaceAll("/", ",")
//							.replaceAll("，", ",").replaceAll(" ", ",")
//							.replaceAll("\\\\", ",")
//							,templateData,"SMS_463615875",verifyCode);
//				}else {
//					String verifyCode = "尊敬的"+unitCode+"业户，您有"+receivableAmount+
//							"元未结，为避免逾期违约，请您与客服中心或管家联系缴费，祝您生活愉快。";
//					String templateData = "{\"uniCode\":\""+unitCode+"\",\"amount\":\""+receivableAmount+"\"}";
//					sendSms(homePhone.replaceAll("、", ",").replaceAll("/", ",")
//							.replaceAll("，", ",").replaceAll(" ", ",")
//							.replaceAll("\\\\", ",")
//							,templateData,"SMS_463130146",verifyCode);
//				}
				String verifyCode = "尊敬的"+unitCode+"业户，截至"+today+"您有"+receivableAmount
						+"元未缴，逾期违约，请尽快缴清，祝生活愉快!";
				String templateData = "{\"unitCode\":\""+unitCode+"\",\"amount\":\""+receivableAmount
						+"\",\"date\":\""+today+"\"}";
				sendSms(homePhone.replaceAll("、", ",").replaceAll("/", ",")
				.replaceAll("，", ",").replaceAll(" ", ",")
				.replaceAll("\\\\", ",")
				,templateData,"SMS_465725956",verifyCode);
			}
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	/*public IResponse updateReservedField() {
		GenericResponse res = new GenericResponse();
		StringBuilder hql = new StringBuilder("select a from CommunityEstateEntity a  "
				+ "inner join a.building c inner join c.district d where a.reservedField NOT LIKE'%isSendMessage%'");
		List<CommunityEstateEntity> list = communityEstateDao.getListByHql(hql.toString(), "");
		if (null!=list && list.size() > 0) {
			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
			Session session = sessionFactory.openSession();

			session.doWork(new Work() {
				@Override
				public void execute(Connection conn) throws SQLException {
					StringBuilder sql = new StringBuilder(
							"update t_community_property set reservedField=? where id=?");
					Integer transCount = 0;
					PreparedStatement stmt = conn.prepareStatement(sql.toString());
					conn.setAutoCommit(false);
					ObjectMapper mapper = new ObjectMapper();
					for (CommunityEstateEntity o : list) {
						ReservedFieldForm reservedField= null;
						if(StringUtils.isNotEmpty(o.getReservedField())) {
							try {
								reservedField =  mapper.readValue(o.getReservedField(), ReservedFieldForm.class);
							} catch (JsonMappingException e) {
								e.printStackTrace();
							} catch (JsonProcessingException e) {
								e.printStackTrace();
							}
						}else {
							reservedField = new ReservedFieldForm();
							reservedField.setAccountContractNo("");
							reservedField.setElectricMeterBase("");
							reservedField.setAccountCustomerId("");
							reservedField.setPropertyParkingNum("");
							reservedField.setPropertyParkingNum("");
							reservedField.setDefenceParkingArea("");
							reservedField.setDefenceParkingNum("");
							reservedField.setWaterMeterBase("");
							reservedField.setAccountContractTime("");
							reservedField.setAccountContractComment("");
							reservedField.setAccountCustomerId("1");
							reservedField.setIsSendMessage("1");
						}
						if(StringUtils.isEmpty(reservedField.getIsSendMessage()) ||
								(StringUtils.isNotEmpty(reservedField.getIsSendMessage())&& 
								reservedField.getIsSendMessage().equals(""))) {
							reservedField.setIsSendMessage("1");
						}
						String json="";
						try {
							json = mapper.writeValueAsString(reservedField);
						} catch (JsonProcessingException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
						stmt.setString(1, json);
						stmt.setInt(2, o.getId());
						stmt.addBatch();
						transCount++;
						if (transCount % 400 == 0) {
							stmt.executeBatch();
							conn.commit();
						}
					}
					stmt.executeBatch();
					conn.commit();
				}
			});
			session.close();
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}*/
	
//	public  void sendSms(String phone,String templateData,String templateCode,String verifyCode) {
//		ObjectMapper objectMapper = new ObjectMapper();
//		ObjectNode json = objectMapper.createObjectNode();
//		//String verifyCode = "尊敬的御江南"+unitCode+"业户，你已欠费"+amount+"元，本月将在"+day+"日银行划扣，为避免逾期违约，请您尽快缴清。";
//		// 发送短信
//		json.put("messageContent", verifyCode);
//		json.put("userNumber", phone);
//		json.put("scheduleTime", DateUtil.format(new Date(), 1));
//		json.put("f", "1");
//		json.put("templateCode", templateCode);
//		json.put("templateData", templateData);
//
//		Map<String, String> result = null;
//		try {
//			objectMapper.setSerializationInclusion(Include.NON_NULL);
////			log.info("--------------------------------------调用短信接口地址：" + communityContextInfo.smsInterfaceUrl + ";   参数："
////					+ json.toString() + "-------------------------");
//			result = HttpClientUtil.post(communityContextInfo.smsInterfaceUrl, objectMapper.writeValueAsString(json), "utf-8");
////			log.info("--------------------------------------调用短信接口返回：" + result
////					+ "----------------------------------------");
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}

	public boolean judgeContainsStr(String str) {

		String regex=".*[a-zA-Z]+.*";
		Matcher matcher = Pattern.compile(regex).matcher(str);
		return matcher.matches();

		}
	private static boolean createMultilayerFile(String dir) {
		try {
			File dirPath = new File(dir);
			if (!dirPath.exists()) {
				dirPath.mkdirs();
			}
		} catch (Exception e) {
			System.out.println("创建多层目录操作出错: " + e.getMessage());
			e.printStackTrace();
			return false;
		}
		return true;
	}
}