package com.foshan.service.community.impl;


import static java.util.Comparator.comparingInt;
import static java.util.stream.Collectors.groupingBy;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.dao.generic.Page;
import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityEventCategoryEntity;
import com.foshan.entity.community.CommunityEventCategoryItemsEntity;
import com.foshan.entity.community.CommunityEventsEntity;
import com.foshan.entity.community.CommunityInspectionRecordEntity;
import com.foshan.entity.community.CommunityMemberEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityEventCategoryForm;
import com.foshan.form.community.CommunityEventCategoryItemsForm;
import com.foshan.form.community.CommunityInspectionRecordForm;
import com.foshan.form.community.request.CommunityInspectionRecordReq;
import com.foshan.form.community.response.communityInspectionRecord.GetCommunityInspectionRecordInfoRes;
import com.foshan.form.community.response.communityInspectionRecord.GetCommunityInspectionRecordListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.model.permssion.PrincipalModel;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityInspectionRecordService;
import com.foshan.util.DateUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional
@Service("communityInspectionRecordService")
public class CommunityInspectionRecordServiceImpl extends GenericCommunityService implements ICommunityInspectionRecordService {

	@SuppressWarnings("unchecked")
	@Override
	public IResponse getCommunityInspectionRecordList(CommunityInspectionRecordReq req) {
		GetCommunityInspectionRecordListRes res = new GetCommunityInspectionRecordListRes();
		Page<CommunityInspectionRecordEntity> page = new Page<CommunityInspectionRecordEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		
		StringBuilder hql = new StringBuilder("select distinct a from CommunityInspectionRecordEntity a inner join a.events b"
				+ " inner join b.property c where " );
		if(StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime())) {
			hql.append(" a.createTime >='"+
					req.getStartTime()+"'"+" and a.createTime <='"+req.getEndTime()+"'");
		}else {
			hql.append(StringUtils.isNotEmpty(req.getSignInDate()) ? " a.createTime like'%"+
					req.getSignInDate()+"%'":" a.createTime like'%"+DateUtil.formatShortFormat(new Date())+"%'");
		}
		if(StringUtils.isNotEmpty(req.getUserCode())) {
			CommunityEstateEntity estate = communityEstateDao.findUnique("select a "
					+ "from CommunityEstateEntity a where a.unitCode ='"+req.getUserCode()+"'");
			if(null!=estate) {
				hql.append(" and c.id="+estate.getId());
			}else {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo("对不起，此单元编号不存在！");
				return res;
			}
			
		}
		hql.append(null!=req.getSignIn() ? " and signIn="+req.getSignIn() :"")
			.append(null!=req.getEventId() ? " and a.events.id="+req.getEventId():"");
		page = communityInspectionRecordDao.queryPage(page, hql.toString());
		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityInspectionRecordForm inspectionRecord = new CommunityInspectionRecordForm();
			inspectionRecord.setCentent(o.getEvents().getCentent());
			inspectionRecord.setCreateTime(null != o.getCreateTime()? DateUtil.formatShortFormat(o.getCreateTime()) : "");
			inspectionRecord.setInspectionRecordId(o.getId());
			inspectionRecord.setLastModifyTime(null != o.getLastModifyTime()? DateUtil.formatLongFormat(o.getLastModifyTime()) : "");
			inspectionRecord.setOrderCode(o.getEvents().getOrderCode());
			inspectionRecord.setRecorder(o.getRecorder());
			inspectionRecord.setSignIn(o.getSignIn());
			CommunityEstateEntity estate = (CommunityEstateEntity) o.getEvents().getProperty();
			inspectionRecord.setUnitCode(estate.getUnitCode());
			inspectionRecord.setUserName(o.getEvents().getMember().getUserName());
			inspectionRecord.setEstateId(estate.getId());
			inspectionRecord.setEventId(o.getEvents().getId());
			inspectionRecord.setItemsDetail(StringUtils.isNotEmpty(o.getItemsDetail()) ? o.getItemsDetail() :"");
			
			res.getInspectionRecordFormList().add(inspectionRecord);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	
	@Override
	@Audit(operate = "新增巡查记录")
	public IResponse addCommunityInspectionRecord(CommunityInspectionRecordReq req) {
		GenericResponse res = new GenericResponse();
		CommunityEventCategoryEntity eventCategory = communityEventCategoryDao.getUniqueByHql("select distinct a from CommunityEventCategoryEntity a where a.categoryType=2 and a.state="
		+EntityContext.RECORD_STATE_VALID);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityEventsEntity a   " 
				+ "where a.eventState=8 and a.eventType=1 and  a.startTime<='"+DateUtil.formatShortFormat(new Date())+" 00:00:00' and a.endTime>='"+
				DateUtil.formatShortFormat(new Date())+" 23:59:59' and a.id not in(select distinct b.events.id from CommunityInspectionRecordEntity b where b.createTime  like'%"+DateUtil.formatShortFormat(new Date())+"%')");
		List<CommunityEventsEntity> list = communityEventsDao.getListByHql(hql.toString(), null);
		list.forEach(o->{
			CommunityInspectionRecordEntity inspectionRecord = new CommunityInspectionRecordEntity(); 
			inspectionRecord.setEvents(o);
			inspectionRecord.setSignIn(0);
			if(null != eventCategory) {
				inspectionRecord.setEventCategory(eventCategory);
				try {
					inspectionRecord.setItemsDetail(mapper.writeValueAsString(getEventCategoryList
							(eventCategory.getId())));
				} catch (JsonProcessingException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}

			communityInspectionRecordDao.save(inspectionRecord);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	
	@SuppressWarnings("unchecked")
	public List<CommunityEventCategoryForm> getEventCategoryList(Integer eventCategoryId) {
		List<CommunityEventCategoryForm> eventCategoryList = new ArrayList<CommunityEventCategoryForm>();
		//Map<String,String> map = new HashMap<>();
		StringBuilder hql = new StringBuilder("select distinct a from CommunityEventCategoryEntity a ");
		hql.append(" where  a.state="+EntityContext.RECORD_STATE_VALID)
			.append(" and a.id="+eventCategoryId)
			.append(" and a.categoryType=2");
		List<CommunityEventCategoryEntity> list = communityEventCategoryDao.getListByHql(hql.toString(),null);
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
		ObjectMapper mapper = new ObjectMapper();
		list.forEach(o -> {
			CommunityEventCategoryForm eventCategoryForm = new CommunityEventCategoryForm(o.getId(),
					o.getCategoryName(),o.getCategoryLevel(),o.getIsDispatching(),
					sdf.format(o.getCreateTime()),sdf.format(o.getLastModifyTime()),o.getIcon());
			eventCategoryForm.setOrders(o.getOrders());
			o.getEventCategoryItemsList().forEach(p->{
				List<Object> optionslList = new ArrayList<Object>();
				try {
					if(StringUtils.isNoneEmpty(p.getOptions())) {
						optionslList =mapper.readValue(p.getOptions(), ArrayList.class);
					}
				} catch (JsonParseException e) {
					e.printStackTrace();
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JSONException e) {
					e.printStackTrace();
				} catch (IOException e) {
					e.printStackTrace();
				}
				CommunityEventCategoryItemsForm eventCategoryItemsForm = new CommunityEventCategoryItemsForm(p.getId(),
						p.getItemName(),p.getItemkey(),p.getOrders(),p.getDataType(),optionslList,p.getIsRequiredng()) ;
				eventCategoryItemsForm.setReflectionField(p.getReflectionField());
				eventCategoryItemsForm.setSubEventCategoryItemsList(getSubEventCategoryItemsList(p.getSubEventCategoryItemsList()));
				eventCategoryItemsForm.setIsCascade(p.getIsCascade());
				eventCategoryItemsForm.setCascadeValue(StringUtils.isNotEmpty(p.getCascadeValue()) ? p.getCascadeValue():"");
				eventCategoryForm.getEventCategoryItemList().add(eventCategoryItemsForm);
			});
			Map<Integer, List<CommunityEventsEntity>> collect1 = (Map<Integer, List<CommunityEventsEntity>>) o.getCommunityEventsList()
			    .parallelStream().collect(groupingBy(CommunityEventsEntity::getEventState));
			eventCategoryForm.setPendingCount(collect1.containsKey(1) ? collect1.get(1).size() : 0);
			eventCategoryForm.setCompleteCount(collect1.containsKey(5) ? collect1.get(5).size() : 0);
			eventCategoryForm.setCategoryType(eventCategoryForm.getCategoryType());
			eventCategoryForm.getEventCategoryItemList().sort(comparingInt(CommunityEventCategoryItemsForm::getOrders));
			eventCategoryForm.setCategoryType(o.getCategoryType());
			
			if(null != o.getParentEventCategory()) {
				CommunityEventCategoryForm parentEventCategoryForm = new CommunityEventCategoryForm(
					o.getParentEventCategory().getId(),o.getParentEventCategory().getCategoryName(),
					o.getParentEventCategory().getCategoryLevel(),o.getParentEventCategory().getIsDispatching(),
					sdf.format(o.getParentEventCategory().getCreateTime()),
					sdf.format(o.getParentEventCategory().getLastModifyTime()),o.getParentEventCategory().getIcon());
				eventCategoryForm.setParentEventCategoryForm(parentEventCategoryForm);
			}

			eventCategoryForm.setSubEventCategoryList(getSubEventCategoryList(o.getSubEventCategoryList(),10));
			eventCategoryForm.setIsSend(o.getIsSend());
			eventCategoryForm.setDefaultParameter(o.getDefaultParameter());
			eventCategoryForm.getSubEventCategoryList().sort(comparingInt(CommunityEventCategoryForm::getOrders));
			eventCategoryList.add(eventCategoryForm);
		});
		return eventCategoryList;
	}
	
	@SuppressWarnings("unchecked")
	public List<CommunityEventCategoryForm> getSubEventCategoryList(List<CommunityEventCategoryEntity> list,Integer depth){
		List<CommunityEventCategoryForm> subEventCategoryList = new ArrayList<CommunityEventCategoryForm>();
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
		depth--;
		for(CommunityEventCategoryEntity o : list){
			CommunityEventCategoryForm eventCategoryForm = new CommunityEventCategoryForm(o.getId(),
					o.getCategoryName(),o.getCategoryLevel(),o.getIsDispatching(),
					sdf.format(o.getCreateTime()),sdf.format(o.getLastModifyTime()),o.getIcon());
			eventCategoryForm.setOrders(o.getOrders());
			Map<Integer, List<CommunityEventsEntity>> collect1 = (Map<Integer, List<CommunityEventsEntity>>) o.getCommunityEventsList()
				    .parallelStream().collect(groupingBy(CommunityEventsEntity::getEventState));
			eventCategoryForm.setPendingCount(collect1.containsKey(1) ? collect1.get(1).size() : 0);
			o.getEventCategoryItemsList().forEach(p->{
				List<Object> optionslList = new ArrayList<Object>();
				try {
					if(StringUtils.isNoneEmpty(p.getOptions())) {
						optionslList =mapper.readValue(p.getOptions(), ArrayList.class);
					}
				} catch (JsonParseException e) {
					e.printStackTrace();
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JSONException e) {
					e.printStackTrace();
				} catch (IOException e) {
					e.printStackTrace();
				}
				CommunityEventCategoryItemsForm eventCategoryItemsForm = new CommunityEventCategoryItemsForm(p.getId(),
						p.getItemName(),p.getItemkey(),p.getOrders(),p.getDataType(),optionslList,p.getIsRequiredng()) ;
				eventCategoryItemsForm.setReflectionField(p.getReflectionField());
				eventCategoryItemsForm.setSubEventCategoryItemsList(getSubEventCategoryItemsList(p.getSubEventCategoryItemsList()));
				eventCategoryItemsForm.setIsCascade(p.getIsCascade());
				eventCategoryItemsForm.setCascadeValue(StringUtils.isNotEmpty(p.getCascadeValue()) ? p.getCascadeValue():"");
				eventCategoryForm.getEventCategoryItemList().add(eventCategoryItemsForm);
			});
			eventCategoryForm.setCategoryType(depth);
			eventCategoryForm.setCategoryType(o.getCategoryType());
			eventCategoryForm.setIsSend(o.getIsSend());
			eventCategoryForm.setDefaultParameter(o.getDefaultParameter());
			eventCategoryForm.getEventCategoryItemList().sort(comparingInt(CommunityEventCategoryItemsForm::getOrders));
			eventCategoryForm.getSubEventCategoryList().sort(comparingInt(CommunityEventCategoryForm::getOrders));
			if(null != depth && depth>=1) {
				eventCategoryForm.setSubEventCategoryList(getSubEventCategoryList(o.getSubEventCategoryList(),depth));
			}
			subEventCategoryList.add(eventCategoryForm);
		}
		
		return subEventCategoryList;
	}
	
	@SuppressWarnings("unchecked")
	public List<CommunityEventCategoryItemsForm> getSubEventCategoryItemsList(List<CommunityEventCategoryItemsEntity> list){
		List<CommunityEventCategoryItemsForm> subItemsList = new ArrayList<CommunityEventCategoryItemsForm>();
		for(CommunityEventCategoryItemsEntity o : list){
			List<Object> optionslList = new ArrayList<Object>();
			try {
				if(StringUtils.isNoneEmpty(o.getOptions())) {
					optionslList =mapper.readValue(o.getOptions(), ArrayList.class);
				}
			} catch (JsonParseException e) {
				e.printStackTrace();
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (JSONException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}
			CommunityEventCategoryItemsForm eventCategoryItemsForm = new CommunityEventCategoryItemsForm(o.getId(),
					o.getItemName(),o.getItemkey(),o.getOrders(),o.getDataType(),optionslList,o.getIsRequiredng());
			eventCategoryItemsForm.setItemTag(StringUtils.isNotEmpty(o.getItemTag()) ? o.getItemTag() :"");
			eventCategoryItemsForm.setItemTag(StringUtils.isNotEmpty(o.getItemTag()) ? o.getItemTag() :"");
			eventCategoryItemsForm.setReflectionField(o.getReflectionField());
			eventCategoryItemsForm.setIsCascade(o.getIsCascade());
			eventCategoryItemsForm.setCascadeValue(StringUtils.isNotEmpty(o.getCascadeValue()) ? o.getCascadeValue() :"");
			subItemsList.add(eventCategoryItemsForm);
		}
		return subItemsList;
	}
	
	@Override
	@Audit(operate = "修改巡查记录")
	public IResponse modifyCommunityInspectionRecord(CommunityInspectionRecordReq req) {
		GenericResponse res = new GenericResponse();
		if (null!=req.getInspectionRecordId()) {
			CommunityInspectionRecordEntity inspectionRecord = communityInspectionRecordDao.get(req.getInspectionRecordId()) ;
			if(null != inspectionRecord){
    			inspectionRecord.setLastModifyTime(new Timestamp(new Date().getTime()));
    			inspectionRecord.setOpinion(StringUtils.isNotEmpty(req.getOpinion()) ? req.getOpinion() :"");
    			inspectionRecord.setImage(null!=req.getImageId() ? assetDao.get(req.getImageId()) :null);
    			inspectionRecord.setSignIn(null!=req.getSignIn() ? req.getSignIn() :0);
    			inspectionRecord.setItemsDetail(StringUtils.isNotEmpty(req.getItemsDetail()) ? 
    					req.getItemsDetail() :inspectionRecord.getItemsDetail());
    			Object userObj = getPrincipal(true);
    			if(null!=userObj && userObj instanceof CommunityMemberEntity) {
    				CommunityMemberEntity member = (CommunityMemberEntity)userObj;
    				inspectionRecord.setRecorder(member.getUserName());
    			}else if(null!=userObj && userObj instanceof PlatformUserEntity) {
    				PlatformUserEntity user = (PlatformUserEntity)userObj;
    				inspectionRecord.setRecorder(user.getUserName());
    			}else if(null!=userObj && userObj instanceof PrincipalModel){
    				PrincipalModel user = (PrincipalModel)userObj;
    				inspectionRecord.setRecorder(user.getUserName());
    			}else {
    				inspectionRecord.setRecorder(StringUtils.isNotEmpty(req.getRecorder()) ? req.getRecorder() :"");
    			}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	
	
	@Override
	public IResponse getCommunityInspectionRecordInfo(CommunityInspectionRecordReq req) {
		GetCommunityInspectionRecordInfoRes res = new GetCommunityInspectionRecordInfoRes();
		if (null!=req.getInspectionRecordId()) {
			CommunityInspectionRecordEntity inspectionRecord = communityInspectionRecordDao.get(req.getInspectionRecordId()) ;
			if(null != inspectionRecord){
				res.setCentent(inspectionRecord.getEvents().getCentent());
				res.setCreateTime(null != inspectionRecord.getCreateTime()? DateUtil.formatShortFormat(inspectionRecord.getCreateTime()) : "");
				res.setInspectionRecordId(inspectionRecord.getId());
				res.setLastModifyTime(null != inspectionRecord.getLastModifyTime()? DateUtil.formatLongFormat(inspectionRecord.getLastModifyTime()) : "");
				res.setOrderCode(inspectionRecord.getEvents().getOrderCode());
				res.setRecorder(inspectionRecord.getRecorder());
				res.setSignIn(inspectionRecord.getSignIn());
				CommunityEstateEntity estate = (CommunityEstateEntity) inspectionRecord.getEvents().getProperty();
				res.setUnitCode(estate.getUnitCode());
				res.setEstateId(estate.getId());
				res.setOpinion(StringUtils.isNotEmpty(inspectionRecord.getOpinion()) ? inspectionRecord.getOpinion() :"");
				res.setUserName(inspectionRecord.getEvents().getMember().getUserName());
				res.setAssetForm(null!=inspectionRecord.getImage() ? getAsset(inspectionRecord.getImage()) : null);
				res.setEventId(inspectionRecord.getEvents().getId());
				res.setItemsDetail(StringUtils.isNotEmpty(inspectionRecord.getItemsDetail()) ? inspectionRecord.getItemsDetail() :"");
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
			
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	
	
}
