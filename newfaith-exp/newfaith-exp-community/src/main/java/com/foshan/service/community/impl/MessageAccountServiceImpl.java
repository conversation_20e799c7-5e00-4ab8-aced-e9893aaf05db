package com.foshan.service.community.impl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.community.MessageAccountEntity;
import com.foshan.form.community.MessageAccountForm;
import com.foshan.form.community.request.MessageAccountReq;
import com.foshan.form.community.response.messageAccount.AddMessageAccountRes;
import com.foshan.form.community.response.messageAccount.GetMessageAccountInfoRes;
import com.foshan.form.community.response.messageAccount.GetMessageAccountListRes;
import com.foshan.form.community.response.messageAccount.ModifyMessageAccountRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.community.IMessageAccountService;

@Transactional
@Service("messageAccountService")
public class MessageAccountServiceImpl extends GenericCommunityService implements IMessageAccountService{

	@Override
	public IResponse getMessageAccountList(MessageAccountReq req) {
		GetMessageAccountListRes res = new GetMessageAccountListRes();
		Page<MessageAccountEntity> page = new Page<MessageAccountEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from MessageAccountEntity a where 1=1 ");
		hql.append("");
		hql.append(" ORDER BY a.id desc");
		page = messageAccountDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			MessageAccountForm messageAccountForm = new MessageAccountForm();
			messageAccountForm.setMessageAccountId(o.getId());
            messageAccountForm.setAccountName(o.getAccountName());
            messageAccountForm.setSecretKey(o.getSecretKey());
			res.getMessageAccountList().add(messageAccountForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public IResponse addMessageAccount(MessageAccountReq req) {
		AddMessageAccountRes res = new AddMessageAccountRes();
		//if () {
			MessageAccountEntity messageAccount = new MessageAccountEntity();
			
            messageAccount.setAccountName(req.getAccountName());
            messageAccount.setSecretKey(req.getSecretKey());
			messageAccountDao.save(messageAccount);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		} else {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//		}
		return res;
	}

	@Override
	public IResponse modifyMessageAccount(MessageAccountReq req) {
		ModifyMessageAccountRes res = new ModifyMessageAccountRes();
		if (null!=req.getMessageAccountId() ) {
			MessageAccountEntity messageAccount = messageAccountDao.get(req.getMessageAccountId()) ;
			if(null != messageAccount){
                messageAccount.setAccountName(req.getAccountName());
                messageAccount.setSecretKey(req.getSecretKey());
				res.setMessageAccountId(messageAccount.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse deleteMessageAccount(MessageAccountReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getMessageAccountId()) {
		MessageAccountEntity messageAccount = messageAccountDao.get(req.getMessageAccountId());
			if (null != messageAccount) {
				messageAccountDao.deleteById(req.getMessageAccountId());
				//messageAccount.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getMessageAccountInfo(MessageAccountReq req) {
		GetMessageAccountInfoRes res = new GetMessageAccountInfoRes();
		if (null != req.getMessageAccountId()) {
			MessageAccountEntity messageAccount = messageAccountDao.get(req.getMessageAccountId());
			if (null != messageAccount) {
				MessageAccountForm messageAccountForm = new MessageAccountForm();
				messageAccountForm.setMessageAccountId(messageAccount.getId());
                messageAccountForm.setAccountName(messageAccount.getAccountName());
                messageAccountForm.setSecretKey(messageAccount.getSecretKey());
				res.setMessageAccountForm(messageAccountForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}