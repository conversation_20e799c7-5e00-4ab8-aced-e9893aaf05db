package com.foshan.service.community;

import com.foshan.form.community.request.CommunityPaymentAccountReq;
import com.foshan.form.response.IResponse;

public interface ICommunityPaymentAccountService {
    public IResponse getCommunityPaymentAccountList(CommunityPaymentAccountReq req);
	public IResponse addCommunityPaymentAccount(CommunityPaymentAccountReq req);
	public IResponse modifyCommunityPaymentAccount(CommunityPaymentAccountReq req);
	public IResponse deleteCommunityPaymentAccount(CommunityPaymentAccountReq req);
	public IResponse getCommunityPaymentAccountInfo(CommunityPaymentAccountReq req);
}

