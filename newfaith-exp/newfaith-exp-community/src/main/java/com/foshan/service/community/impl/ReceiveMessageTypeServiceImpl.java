package com.foshan.service.community.impl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.community.ReceiveMessageTypeEntity;
import com.foshan.form.community.ReceiveMessageTypeForm;
import com.foshan.form.community.request.ReceiveMessageTypeReq;
import com.foshan.form.community.response.receiveMessageType.AddReceiveMessageTypeRes;
import com.foshan.form.community.response.receiveMessageType.GetReceiveMessageTypeInfoRes;
import com.foshan.form.community.response.receiveMessageType.GetReceiveMessageTypeListRes;
import com.foshan.form.community.response.receiveMessageType.ModifyReceiveMessageTypeRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.community.IReceiveMessageTypeService;

@Transactional
@Service("receiveMessageTypeService")
public class ReceiveMessageTypeServiceImpl extends GenericCommunityService implements IReceiveMessageTypeService{

	@Override
	public IResponse getReceiveMessageTypeList(ReceiveMessageTypeReq req) {
		GetReceiveMessageTypeListRes res = new GetReceiveMessageTypeListRes();
		Page<ReceiveMessageTypeEntity> page = new Page<ReceiveMessageTypeEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from ReceiveMessageTypeEntity a where 1=1 ");
		hql.append("");
		hql.append(" ORDER BY a.id desc");
		page = receiveMessageTypeDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			ReceiveMessageTypeForm receiveMessageTypeForm = new ReceiveMessageTypeForm();
			receiveMessageTypeForm.setReceiveMessageTypeId(o.getId());
            receiveMessageTypeForm.setObjectName(o.getObjectName());
            receiveMessageTypeForm.setTypeValue(o.getTypeValue());
			res.getReceiveMessageTypeList().add(receiveMessageTypeForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public IResponse addReceiveMessageType(ReceiveMessageTypeReq req) {
		AddReceiveMessageTypeRes res = new AddReceiveMessageTypeRes();
		//if () {
			ReceiveMessageTypeEntity receiveMessageType = new ReceiveMessageTypeEntity();
			
            receiveMessageType.setObjectName(req.getObjectName());
            receiveMessageType.setTypeValue(req.getTypeValue());
			receiveMessageTypeDao.save(receiveMessageType);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		} else {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//		}
		return res;
	}

	@Override
	public IResponse modifyReceiveMessageType(ReceiveMessageTypeReq req) {
		ModifyReceiveMessageTypeRes res = new ModifyReceiveMessageTypeRes();
		if (null!=req.getReceiveMessageTypeId() ) {
			ReceiveMessageTypeEntity receiveMessageType = receiveMessageTypeDao.get(req.getReceiveMessageTypeId()) ;
			if(null != receiveMessageType){
                receiveMessageType.setObjectName(req.getObjectName());
                receiveMessageType.setTypeValue(req.getTypeValue());
				res.setReceiveMessageTypeId(receiveMessageType.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse deleteReceiveMessageType(ReceiveMessageTypeReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getReceiveMessageTypeId()) {
		ReceiveMessageTypeEntity receiveMessageType = receiveMessageTypeDao.get(req.getReceiveMessageTypeId());
			if (null != receiveMessageType) {
				receiveMessageTypeDao.deleteById(req.getReceiveMessageTypeId());
				//receiveMessageType.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getReceiveMessageTypeInfo(ReceiveMessageTypeReq req) {
		GetReceiveMessageTypeInfoRes res = new GetReceiveMessageTypeInfoRes();
		if (null != req.getReceiveMessageTypeId()) {
			ReceiveMessageTypeEntity receiveMessageType = receiveMessageTypeDao.get(req.getReceiveMessageTypeId());
			if (null != receiveMessageType) {
				ReceiveMessageTypeForm receiveMessageTypeForm = new ReceiveMessageTypeForm();
				receiveMessageTypeForm.setReceiveMessageTypeId(receiveMessageType.getId());
                receiveMessageTypeForm.setObjectName(receiveMessageType.getObjectName());
                receiveMessageTypeForm.setTypeValue(receiveMessageType.getTypeValue());
				res.setReceiveMessageTypeForm(receiveMessageTypeForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}