package com.foshan.service.community.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.foshan.dao.generic.Page;
import com.foshan.entity.DictionaryDataEntity;
import com.foshan.entity.community.CommunityMemberEntity;
import com.foshan.entity.community.DeviceCorrelationEntity;
import com.foshan.entity.community.DeviceEntity;
import com.foshan.entity.community.MessageAccountEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.DeviceForm;
import com.foshan.form.community.MessageAccountForm;
import com.foshan.form.community.request.DeviceReq;
import com.foshan.form.community.response.device.AddDeviceRes;
import com.foshan.form.community.response.device.GetDeviceInfoRes;
import com.foshan.form.community.response.device.GetDeviceListRes;
import com.foshan.form.community.response.device.ModifyDeviceRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.IDeviceService;
import com.foshan.util.DateUtil;
import com.foshan.util.HttpClientUtil;

@Transactional
@Service("deviceService")
public class DeviceServiceImpl extends GenericCommunityService implements IDeviceService{

	@Override
	public IResponse getDeviceList(DeviceReq req) {
		GetDeviceListRes res = new GetDeviceListRes();
		Page<DeviceEntity> page = new Page<DeviceEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from DeviceEntity a ");
		hql.append(StringUtils.isNotEmpty(req.getMemberIdList()) ? " inner join deviceCorrelationList dc "
				+ " join dc.member m where m.id in("+req.getMemberIdList()+")":" where 1=1 ")
			.append(StringUtils.isNotEmpty(req.getDeviceName()) ? " and a.deviceName like'%"+req.getDeviceName()+"%'":"")
			.append(StringUtils.isNotEmpty(req.getDeviceCode()) ? " and a.deviceCode like'%"+req.getDeviceCode()+"%'":"")
			.append(null!=req.getDeviceType() ? " and a.deviceType="+req.getDeviceType():"");
		hql.append(" ORDER BY a.orders desc");
		page = deviceDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			DeviceForm deviceForm = new DeviceForm();
			deviceForm.setDeviceId(o.getId());
			deviceForm.setDeviceType(o.getDeviceType());
            deviceForm.setComment(o.getComment());
            deviceForm.setCreateTime(null != o.getCreateTime()? DateUtil.formatLongFormat(o.getCreateTime()) : "");
            deviceForm.setDeviceAddress(o.getDeviceAddress());
            deviceForm.setDeviceCode(o.getDeviceCode());
            deviceForm.setDeviceName(o.getDeviceName());
            deviceForm.setLastModifyTime(null != o.getLastModifyTime()? DateUtil.formatLongFormat(o.getLastModifyTime()) : "");
            deviceForm.setState(o.getState());
            deviceForm.setContentMsg(o.getContentMsg());
            deviceForm.setTitleMsg(o.getTitleMsg());
            deviceForm.setRunTime(o.getRunTime());
            deviceForm.setParameterJson(o.getParameterJson());
            deviceForm.setOrders(o.getOrders());
//            deviceForm.setMessageAccountId(o.getMessageAccountId());
            if(null!=o.getMessageAccount()) {
            	MessageAccountForm messageAccount = new MessageAccountForm();
            	messageAccount.setMessageAccountId(o.getMessageAccount().getId());
            	messageAccount.setAccountName(o.getMessageAccount().getAccountName());
            	deviceForm.setMessageAccount(messageAccount);
            }
			res.getDeviceList().add(deviceForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public IResponse addDevice(DeviceReq req) {
		AddDeviceRes res = new AddDeviceRes();
		if (StringUtils.isNotEmpty(req.getDeviceCode()) && null!=req.getMessageAccountId()) {
			MessageAccountEntity messageAccount = messageAccountDao.get(req.getMessageAccountId());
			if(null == messageAccount) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			DeviceEntity device = new DeviceEntity();
            device.setComment(req.getComment());
            device.setOrders(null!=req.getOrders() ? req.getOrders() : 1);
            device.setDeviceAddress(req.getDeviceAddress());
            device.setDeviceCode(req.getDeviceCode());
            device.setDeviceName(req.getDeviceName());
            device.setDeviceType(req.getDeviceType());
            device.setLastModifyTime(new Timestamp(new Date().getTime()));
            device.setState(EntityContext.RECORD_STATE_VALID);
            device.setContentMsg(StringUtils.isNotEmpty(req.getContentMsg()) ? req.getContentMsg() : "");
            device.setTitleMsg(StringUtils.isNotEmpty(req.getTitleMsg()) ? req.getTitleMsg() : "");
            device.setRunTime(null!=req.getRunTime() ? req.getRunTime() :0);
            device.setParameterJson(StringUtils.isNotEmpty(req.getParameterJson()) ? req.getParameterJson() : "");
            device.setMessageAccount(messageAccount);
			deviceDao.save(device);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	
	@Override
	public IResponse modifyDevice(DeviceReq req) {
		ModifyDeviceRes res = new ModifyDeviceRes();
		if (null!=req.getDeviceId() ) {
			DeviceEntity device = deviceDao.get(req.getDeviceId()) ;
			if(null != device){
                device.setComment(req.getComment());
                device.setDeviceAddress(req.getDeviceAddress());
                device.setDeviceCode(req.getDeviceCode());
                device.setDeviceName(req.getDeviceName());
                device.setOrders(null!=req.getOrders() ? req.getOrders() : device.getOrders());
                device.setLastModifyTime(new Timestamp(new Date().getTime()));
                device.setDeviceType(null!=req.getDeviceType() ? req.getDeviceType():device.getDeviceType());
//                device.setState(req.getState());
//                device.setMessageAccountId(req.getMessageAccountId());
                device.setContentMsg(StringUtils.isNotEmpty(req.getContentMsg()) ? req.getContentMsg() : device.getContentMsg());
                device.setTitleMsg(StringUtils.isNotEmpty(req.getTitleMsg()) ? req.getTitleMsg() : device.getTitleMsg());
                device.setRunTime(null!=req.getRunTime() ? req.getRunTime() :device.getRunTime());
                device.setParameterJson(StringUtils.isNotEmpty(req.getParameterJson()) ? req.getParameterJson() : device.getParameterJson());
				res.setDeviceId(device.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse deleteDevice(DeviceReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getDeviceId()) {
		DeviceEntity device = deviceDao.get(req.getDeviceId());
			if (null != device) {
				//deviceDao.deleteById(req.getDeviceId());
				device.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getDeviceInfo(DeviceReq req) {
		GetDeviceInfoRes res = new GetDeviceInfoRes();
		if (null != req.getDeviceId()) {
			DeviceEntity device = deviceDao.get(req.getDeviceId());
			if (null != device) {
				DeviceForm deviceForm = new DeviceForm();
				deviceForm.setDeviceId(device.getId());
                deviceForm.setComment(device.getComment());
                deviceForm.setDeviceType(device.getDeviceType());
                deviceForm.setCreateTime(null != device.getCreateTime()? DateUtil.formatLongFormat(device.getCreateTime()) : "");
                deviceForm.setDeviceAddress(device.getDeviceAddress());
                deviceForm.setDeviceCode(device.getDeviceCode());
                deviceForm.setDeviceName(device.getDeviceName());
                deviceForm.setLastModifyTime(null != device.getLastModifyTime()? DateUtil.formatLongFormat(device.getLastModifyTime()) : "");
                deviceForm.setState(device.getState());
                deviceForm.setContentMsg(device.getContentMsg());
                deviceForm.setTitleMsg(device.getTitleMsg());
                deviceForm.setRunTime(device.getRunTime());
                deviceForm.setOrders(device.getOrders());
                deviceForm.setParameterJson(device.getParameterJson());
//                deviceForm.setMessageAccountId(device.getMessageAccountId());
                if(null!=device.getMessageAccount()) {
                	MessageAccountForm messageAccount = new MessageAccountForm();
                	messageAccount.setMessageAccountId(device.getMessageAccount().getId());
                	messageAccount.setAccountName(device.getMessageAccount().getAccountName());
                	deviceForm.setMessageAccount(messageAccount);
                }
				res.setDeviceForm(deviceForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public IResponse updateCameraDevice() {
		GenericResponse res = new GenericResponse();
		String caCard = "";
		String getCustomerIPCAreaUrl = "";
		String getCustomerIPCRelByAreaUrl = "";
		List<DictionaryDataEntity> dictionaryDataList = dictionaryDataDao
				.getListByHql("select distinct a from DictionaryDataEntity a "
						+ " inner join a.dictionary  b where b.directoryCode='easydms' and a.state=1", "");
		if (null != dictionaryDataList && dictionaryDataList.size() > 0) {
			for(DictionaryDataEntity d : dictionaryDataList) {
				if(d.getDataName().equals("caCard")) {
					caCard = d.getDataKey();
				}else if(d.getDataName().equals("getCustomerIPCAreaUrl")){
					getCustomerIPCAreaUrl = d.getDataKey();
				}else if(d.getDataName().equals("getCustomerIPCRelByAreaUrl")) {
					getCustomerIPCRelByAreaUrl = d.getDataKey();
				}
			}
			if(StringUtils.isEmpty(caCard) || StringUtils.isEmpty(getCustomerIPCAreaUrl)
					|| StringUtils.isEmpty(getCustomerIPCRelByAreaUrl)) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo("请在数据字典里配置“整合视频接口相关参数（caCard、getCustomerIPCAreaUrl和getCustomerIPCAreaUrl）”,directoryCode为\"easydms\"！");
				return res;
			}
		}
		String areaResult = HttpClientUtil.get(getCustomerIPCAreaUrl+"&caCard="+caCard);
		 try {
			 Map<String, Object> areaData  =  mapper.readValue(areaResult, Map.class);
			 if(areaData.containsKey("Area")) {
				 List<Map<String,Object>> areaList = (List<Map<String, Object>>) areaData.get("Area");
				 for(Map<String,Object> area : areaList) {
					 if(area.containsKey("FRegionCode")) {
						 String regionCode = (String) area.get("FRegionCode");
						 String result = HttpClientUtil.get(getCustomerIPCRelByAreaUrl+"&caCard="+caCard+"&FRegionCode="+regionCode);
						 Map<String, Object> data  =  mapper.readValue(result, Map.class);
						 if(data.containsKey("IPC")) {
							 List<Map<String,Object>> list = (List<Map<String, Object>>) data.get("IPC");
							 for(Map<String,Object> m : list) {
								 if(m.containsKey("FGUID")) {
									 Map<String,String> map = new HashMap<>();
									 String deviceCode = (String) m.get("FGUID");
									 DeviceEntity device = deviceDao.findUnique("select a from DeviceEntity a where deviceCode='"+deviceCode+"'", "");
									 if(null == device) {
										 device = new DeviceEntity();
										 device.setDeviceCode(deviceCode);
										 device.setRunTime(0);
										 device.setState(EntityContext.RECORD_STATE_VALID);
										 device.setDeviceType(2);
										 device.setOrders(1);
									 }
									 device.setDeviceName(m.containsKey("FName") ? (String)m.get("FName") : "");
									 map.put("FSsrcURL", m.containsKey("FSsrcURL") ? (String)m.get("FSsrcURL") :"");
									 map.put("FSsrcURL_sub", m.containsKey("FSsrcURL_sub") ? (String)m.get("FSsrcURL_sub"):"");
									 map.put("liveHttpPort", m.containsKey("liveHttpPort") ? (String)m.get("liveHttpPort"):"");
									 map.put("liveRtspPort", m.containsKey("liveRtspPort") ? (String)m.get("liveRtspPort"):"");
									 map.put("liveIP", m.containsKey("liveIP") ? (String)m.get("liveIP"):"");
									 device.setParameterJson(mapper.writeValueAsString(map));
									 deviceDao.saveOrUpdate(device);
								 }
							 }
						 }
					 }
				 }
			 }
		 } catch (JsonMappingException e) {
			 e.printStackTrace();
		 } catch (JsonProcessingException e) {
			 e.printStackTrace();
		 }
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
	return res;
	}
	
	
	@Override
	@Audit(operate = "设备绑定会员")
	public IResponse bindingDeviceAndMember(DeviceReq req) {
		GenericResponse res = new GenericResponse();
		if (StringUtils.isNotEmpty(req.getDeviceIdList())
				&& (StringUtils.isNotEmpty(req.getBuildingIdList()) || StringUtils.isNotEmpty(req.getDistrictIdList())
						|| StringUtils.isNotEmpty(req.getPropertyIdList()))) {
			List<DeviceEntity> deviceList = deviceDao.getListByHql(
					"select a from DeviceEntity a where a.id in(" + req.getDeviceIdList() + ") and a.deviceType=2");
			if (null != deviceList) {
				StringBuilder hql = new StringBuilder("select distinct a from CommunityMemberEntity a "
						+ "inner join a.memberPropertyList mp inner join mp.property p inner join p.building b inner join b.district d where "
						+ "mp.isCurrentMember=1 AND mp.memberType=0");
				hql.append(StringUtils.isNotEmpty(req.getBuildingIdList()) ? " and b.id in("+req.getBuildingIdList()+")":"")
					.append(StringUtils.isNotEmpty(req.getDistrictIdList()) ? " and d.id in("+req.getDistrictIdList()+")":"")
					.append(StringUtils.isNotEmpty(req.getPropertyIdList()) ? " and p.id in("+req.getPropertyIdList()+")":"");
				List<CommunityMemberEntity> memberList = communityMemberDao.getListByHql(hql.toString());
				deviceList.forEach(d->{
					List<DeviceCorrelationEntity> deviceCorrelationList = d.getDeviceCorrelationList();
					List<CommunityMemberEntity> cmList = new ArrayList<>();
					deviceCorrelationList.forEach(o->{
						cmList.add(o.getMember());
					});
					memberList.forEach(m->{
						if(!cmList.contains(m)) {
							DeviceCorrelationEntity deviceCorrelation = new DeviceCorrelationEntity();
							deviceCorrelation.setMember(m);
							deviceCorrelation.setDevice(d);
							deviceCorrelationDao.save(deviceCorrelation);
						}
					});
					
				});

				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	@Override
	@Audit(operate = "设备解绑绑定会员")
	public IResponse unbindingDeviceAndMember(DeviceReq req) {
		GenericResponse res = new GenericResponse();
		if (StringUtils.isNotEmpty(req.getDeviceIdList())
				&& (StringUtils.isNotEmpty(req.getBuildingIdList()) || StringUtils.isNotEmpty(req.getDistrictIdList())
						|| StringUtils.isNotEmpty(req.getPropertyIdList()))) {
			StringBuilder hql = new StringBuilder("select distinct dc from DeviceCorrelationEntity dc inner join  dc.member a "
					+ "inner join a.memberPropertyList mp inner join mp.property p inner join p.building b inner join b.district d"
					+ " inner join dc.device de where mp.isCurrentMember=1 AND mp.memberType=0 and de.id in("+req.getDeviceIdList()+")");
			hql.append(StringUtils.isNotEmpty(req.getBuildingIdList()) ? " and b.id in("+req.getBuildingIdList()+")":"")
				.append(StringUtils.isNotEmpty(req.getDistrictIdList()) ? " and d.id in("+req.getDistrictIdList()+")":"")
				.append(StringUtils.isNotEmpty(req.getPropertyIdList()) ? " and p.id in("+req.getPropertyIdList()+")":"");
			List<DeviceCorrelationEntity> deviceCorrelationList = deviceCorrelationDao.getListByHql(hql.toString());
			deviceCorrelationList.forEach(d->{
				d.setDevice(null);
				d.setReceiveMessageType(null);
				d.setMember(null);
				deviceCorrelationDao.delete(d);
			});

			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}