package com.foshan.service.community.impl;

import java.math.BigDecimal;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityReceiptReceivablesEntity;
import com.foshan.form.community.CommunityReceiptReceivablesForm;
import com.foshan.form.community.CommunityReceiptForm;
import com.foshan.form.community.CommunityReceivablesForm;
import com.foshan.form.community.request.CommunityReceiptReceivablesReq;
import com.foshan.form.community.response.communityReceiptReceivables.AddCommunityReceiptReceivablesRes;
import com.foshan.form.community.response.communityReceiptReceivables.GetCommunityReceiptReceivablesInfoRes;
import com.foshan.form.community.response.communityReceiptReceivables.GetCommunityReceiptReceivablesListRes;
import com.foshan.form.community.response.communityReceiptReceivables.ModifyCommunityReceiptReceivablesRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityReceiptReceivablesService;

@Transactional
@Service("communityReceiptReceivablesService")
public class CommunityReceiptReceivablesServiceImpl extends GenericCommunityService implements ICommunityReceiptReceivablesService{

	@Override
	public IResponse getCommunityReceiptReceivablesList(CommunityReceiptReceivablesReq req) {
		GetCommunityReceiptReceivablesListRes res = new GetCommunityReceiptReceivablesListRes();
		Page<CommunityReceiptReceivablesEntity> page = new Page<CommunityReceiptReceivablesEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityReceiptReceivablesEntity a where 1=1 ");
		hql.append("");
		hql.append(" ORDER BY a.id desc");
		page = communityReceiptReceivablesDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityReceiptReceivablesForm communityReceiptReceivablesForm = new CommunityReceiptReceivablesForm();
			communityReceiptReceivablesForm.setCommunityReceiptReceivablesId(o.getId());
            communityReceiptReceivablesForm.setCurrentAmount(null!=o.getCurrentAmount() ? o.getCurrentAmount().toString() : "");
            communityReceiptReceivablesForm.setReceivedAmount(null!=o.getReceivedAmount() ? o.getReceivedAmount().toString() : "");
            if(null != o.getReceipt()) {
            	CommunityReceiptForm communityReceiptForm = new CommunityReceiptForm();
    			communityReceiptForm.setCommunityReceiptId(o.getReceipt().getId());
                communityReceiptForm.setPayerName(o.getReceipt().getPayerName());
                communityReceiptReceivablesForm.setReceiptForm(communityReceiptForm);
            }
            if(null != o.getReceivables()) {
    			CommunityReceivablesForm communityReceivablesForm = new CommunityReceivablesForm();
    			communityReceivablesForm.setCommunityReceivablesId(o.getReceivables().getId());
                communityReceivablesForm.setChargeCategory(o.getReceivables().getChargeCategory());
                communityReceivablesForm.setChargeSource(o.getReceivables().getChargeSource());
                communityReceivablesForm.setPayItemsName(o.getReceivables().getPayItemsName());
                communityReceiptReceivablesForm.setReceivablesForm(communityReceivablesForm);
            }

			res.getCommunityReceiptReceivablesList().add(communityReceiptReceivablesForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	@Audit(operate = "新增实收款")
	public IResponse addCommunityReceiptReceivables(CommunityReceiptReceivablesReq req) {
		AddCommunityReceiptReceivablesRes res = new AddCommunityReceiptReceivablesRes();
		//if () {
			CommunityReceiptReceivablesEntity communityReceiptReceivables = new CommunityReceiptReceivablesEntity();
			
            communityReceiptReceivables.setCurrentAmount(StringUtils.isNotEmpty(req.getCurrentAmount()) ?  new BigDecimal(req.getCurrentAmount()) : null);
            communityReceiptReceivables.setReceivedAmount(StringUtils.isNotEmpty(req.getReceivedAmount()) ?  new BigDecimal(req.getReceivedAmount()) : null);
            communityReceiptReceivables.setReceivables(null!=req.getReceiptId() ? communityReceivablesDao.get(req.getReceiptId()) : null);
            communityReceiptReceivables.setReceipt(null!=req.getReceivablesId() ? communityReceiptDao.get(req.getReceivablesId()) : null);
            communityReceiptReceivables.setInvoiceState(0);
            communityReceiptReceivablesDao.save(communityReceiptReceivables);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		} else {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//		}
		return res;
	}

	@Override
	@Audit(operate = "修改实收款")
	public IResponse modifyCommunityReceiptReceivables(CommunityReceiptReceivablesReq req) {
		ModifyCommunityReceiptReceivablesRes res = new ModifyCommunityReceiptReceivablesRes();
		if (null!=req.getCommunityReceiptReceivablesId() ) {
			CommunityReceiptReceivablesEntity communityReceiptReceivables = communityReceiptReceivablesDao.get(req.getCommunityReceiptReceivablesId()) ;
			if(null != communityReceiptReceivables){
                communityReceiptReceivables.setCurrentAmount(StringUtils.isNotEmpty(req.getCurrentAmount()) ?  new BigDecimal(req.getCurrentAmount()) : null);
                communityReceiptReceivables.setReceivedAmount(StringUtils.isNotEmpty(req.getReceivedAmount()) ?  new BigDecimal(req.getReceivedAmount()) : null);
                communityReceiptReceivables.setReceivables(null!=req.getReceiptId() ? communityReceivablesDao.get(req.getReceiptId()) : null);
                communityReceiptReceivables.setReceipt(null!=req.getReceivablesId() ? communityReceiptDao.get(req.getReceivablesId()) : null);
				res.setCommunityReceiptReceivablesId(communityReceiptReceivables.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "删除实收款")
	public IResponse deleteCommunityReceiptReceivables(CommunityReceiptReceivablesReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityReceiptReceivablesId()) {
		CommunityReceiptReceivablesEntity communityReceiptReceivables = communityReceiptReceivablesDao.get(req.getCommunityReceiptReceivablesId());
			if (null != communityReceiptReceivables) {
				communityReceiptReceivablesDao.deleteById(req.getCommunityReceiptReceivablesId());
				//communityReceipReceivables.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityReceiptReceivablesInfo(CommunityReceiptReceivablesReq req) {
		GetCommunityReceiptReceivablesInfoRes res = new GetCommunityReceiptReceivablesInfoRes();
		if (null != req.getCommunityReceiptReceivablesId()) {
			CommunityReceiptReceivablesEntity communityReceiptReceivables = communityReceiptReceivablesDao.get(req.getCommunityReceiptReceivablesId());
			if (null != communityReceiptReceivables) {
				CommunityReceiptReceivablesForm communityReceiptReceivablesForm = new CommunityReceiptReceivablesForm();
				communityReceiptReceivablesForm.setCommunityReceiptReceivablesId(communityReceiptReceivables.getId());
                communityReceiptReceivablesForm.setCurrentAmount(null!=communityReceiptReceivables.getCurrentAmount() ? communityReceiptReceivables.getCurrentAmount().toString() : "");
                communityReceiptReceivablesForm.setReceivedAmount(null!=communityReceiptReceivables.getReceivedAmount() ? communityReceiptReceivables.getReceivedAmount().toString() : "");

                if(null != communityReceiptReceivables.getReceipt()) {
                	CommunityReceiptForm communityReceiptForm = new CommunityReceiptForm();
        			communityReceiptForm.setCommunityReceiptId(communityReceiptReceivables.getReceipt().getId());
                    communityReceiptForm.setPayerName(communityReceiptReceivables.getReceipt().getPayerName());
                    communityReceiptReceivablesForm.setReceiptForm(communityReceiptForm);
                }
                if(null != communityReceiptReceivables.getReceivables()) {
        			CommunityReceivablesForm communityReceivablesForm = new CommunityReceivablesForm();
        			communityReceivablesForm.setCommunityReceivablesId(communityReceiptReceivables.getReceivables().getId());
                    communityReceivablesForm.setChargeCategory(communityReceiptReceivables.getReceivables().getChargeCategory());
                    communityReceivablesForm.setChargeSource(communityReceiptReceivables.getReceivables().getChargeSource());
                    communityReceivablesForm.setPayItemsName(communityReceiptReceivables.getReceivables().getPayItemsName());
                    communityReceiptReceivablesForm.setReceivablesForm(communityReceivablesForm);
                }
				res.setCommunityReceiptReceivablesForm(communityReceiptReceivablesForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}