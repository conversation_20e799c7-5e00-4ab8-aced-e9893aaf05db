package com.foshan.service.community.impl;


import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.form.community.request.CommunityReceivableFeeReq;
import com.foshan.form.response.IResponse;
import com.foshan.service.community.ICommunityFeeService;

@Transactional
@Service("communityFeeService")
public class CommunityFeeServiceImpl extends GenericCommunityService implements ICommunityFeeService {


	@Override
	public IResponse calManagementFee(CommunityReceivableFeeReq req) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public IResponse calAlloctionFee(CommunityReceivableFeeReq req) {
		// TODO Auto-generated method stub
		return null;
	}
	

	@Override
	public IResponse calReceivableFee(CommunityReceivableFeeReq req) {
		// TODO Auto-generated method stub
		return null;
	}

}
