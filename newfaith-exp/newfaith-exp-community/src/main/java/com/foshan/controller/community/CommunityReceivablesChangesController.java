package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityReceivablesChangesReq;
import com.foshan.form.community.response.communityReceivablesChanges.AddCommunityReceivablesChangesRes;
import com.foshan.form.community.response.communityReceivablesChanges.GetCommunityReceivablesChangesInfoRes;
import com.foshan.form.community.response.communityReceivablesChanges.GetCommunityReceivablesChangesListRes;
import com.foshan.form.community.response.communityReceivablesChanges.ModifyCommunityReceivablesChangesRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "应收款变更（减加）模块")
@RestController
public class CommunityReceivablesChangesController extends BaseCommunityController {

	// 获取应收款变更（减加）列表
	@ApiOperation(value = "获取应收款变更（减加）列表(getCommunityReceivablesChangesList)", httpMethod = "POST", notes = "获取应收款变更（减加）列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityReceivablesChangesList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityReceivablesChangesListRes getCommunityReceivablesChangesList(@RequestBody CommunityReceivablesChangesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityReceivablesChangesListRes res = (GetCommunityReceivablesChangesListRes) communityReceivablesChangesService.getCommunityReceivablesChangesList(req);
		return res;
	}
	
	// 新增应收款变更（减加）
	@ApiOperation(value = "新增事件类型项目(addCommunityReceivablesChanges)", httpMethod = "POST", notes = "新增应收款变更（减加）")
	@ResponseBody
	@RequestMapping(value = "/addCommunityReceivablesChanges", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityReceivablesChangesRes addCommunityReceivablesChanges(@RequestBody CommunityReceivablesChangesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityReceivablesChangesRes res = (AddCommunityReceivablesChangesRes) communityReceivablesChangesService.addCommunityReceivablesChanges(req);
		return res;
	}
	
	// 修改应收款变更（减加）
	@ApiOperation(value = "修改应收款变更（减加）(modifyCommunityReceivablesChanges)", httpMethod = "POST", notes = "修改应收款变更（减加），communityReceivablesChangesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityReceivablesChanges", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityReceivablesChangesRes modifyCommunityReceivablesChanges(@RequestBody CommunityReceivablesChangesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityReceivablesChangesRes res = (ModifyCommunityReceivablesChangesRes) communityReceivablesChangesService.modifyCommunityReceivablesChanges(req);
		return res;
	}
	
	// 删除应收款变更（减加）
	@ApiOperation(value = "删除事件类型项目(deleteCommunityReceivablesChanges)", httpMethod = "POST", notes = "删除应收款变更（减加），communityReceivablesChangesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityReceivablesChanges", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityReceivablesChanges(@RequestBody CommunityReceivablesChangesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReceivablesChangesService.deleteCommunityReceivablesChanges(req);
		return res;
	}
	
	// 获取应收款变更（减加）详情
	@ApiOperation(value = "获取应收款变更（减加）详情(getCommunityReceivablesChangesInfo)", httpMethod = "POST", notes = "获取应收款变更（减加）详情，communityReceivablesChangesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityReceivablesChangesInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityReceivablesChangesInfoRes getCommunityReceivablesChangesInfo(@RequestBody CommunityReceivablesChangesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityReceivablesChangesInfoRes res = (GetCommunityReceivablesChangesInfoRes) communityReceivablesChangesService.getCommunityReceivablesChangesInfo(req);
		return res;
	}
	
	// 取消一次性收费退款
	@ApiOperation(value = "取消一次性收费退款(cancelDisposableRefund)", httpMethod = "POST", notes = "取消一次性收费退款，communityReceivablesChangesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/cancelDisposableRefund", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse cancelDisposableRefund(@RequestBody CommunityReceivablesChangesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReceivablesChangesService.cancelDisposableRefund(req);
		return res;
	}
   
}
