package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityPayItemsPriceReq;
import com.foshan.form.community.response.communityPayItemsPrice.GetCommunityPayItemsPriceListRes;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "收费项目设定模块")
@RestController
public class CommunityPayItemsPriceController extends BaseCommunityController {

	// 新增租金
	@ApiOperation(value = "新增租金(addCommunityPayItemsPrice)", httpMethod = "POST", notes = "新增租金，communityPayItemsId、communityMemberPropertyId和period不能这空！")
	@ResponseBody
	@RequestMapping(value = "/addCommunityPayItemsPrice", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addCommunityPayItemsPrice(@RequestBody CommunityPayItemsPriceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityPayItemsPriceService.addCommunityPayItemsPrice(req);
		return res;
	}
	
	// 修改租金
	@ApiOperation(value = "修改租金(modifyCommunityPayItemsPrice)", httpMethod = "POST", notes = "修改租金，communityPayItemsPriceId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityPayItemsPrice", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyCommunityPayItemsPrice(@RequestBody CommunityPayItemsPriceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityPayItemsPriceService.modifyCommunityPayItemsPrice(req);
		return res;
	}
	
	// 删除租金
	@ApiOperation(value = "删除租金(deleteCommunityPayItemsPrice)", httpMethod = "POST", notes = "删除租金，communityPayItemsPriceId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityPayItemsPrice", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityPayItemsPrice(@RequestBody CommunityPayItemsPriceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityPayItemsPriceService.deleteCommunityPayItemsPrice(req);
		return res;
	}
	
	// 获取列表
	@ApiOperation(value = "获取列表(getCommunityPayItemsPriceList)", httpMethod = "POST", notes = "获取列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityPayItemsPriceList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityPayItemsPriceListRes getCommunityPayItemsPriceList(@RequestBody CommunityPayItemsPriceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityPayItemsPriceListRes res = (GetCommunityPayItemsPriceListRes) communityPayItemsPriceService.getCommunityPayItemsPriceList(req);
		return res;
	}
	
	
	// 合同绑定收费价格
	@ApiOperation(value = "合同绑定收费价格(contractBindingPayItemsPrice)", httpMethod = "POST", notes = "合同绑定收费价格，PayItemsPriceIdList、ContractIdList不能为空；")
	@ResponseBody
	@RequestMapping(value = "/contractBindingPayItemsPrice", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse contractBindingPayItemsPrice(@RequestBody CommunityPayItemsPriceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityPayItemsPriceService.contractBindingPayItemsPrice(req);
		return res;
	}
	
	// 合同解绑收费价格
	@ApiOperation(value = "合同解绑收费价格(contractUnbindingPayItemsPrice)", httpMethod = "POST", notes = "合同解绑收费价格，PayItemsPriceIdList、ContractIdList不能为空；")
	@ResponseBody
	@RequestMapping(value = "/contractUnbindingPayItemsPrice", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse contractUnbindingPayItemsPrice(@RequestBody CommunityPayItemsPriceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityPayItemsPriceService.contractUnbindingPayItemsPrice(req);
		return res;
	}

}
