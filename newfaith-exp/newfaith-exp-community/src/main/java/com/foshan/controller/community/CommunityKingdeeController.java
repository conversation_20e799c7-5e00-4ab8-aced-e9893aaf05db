package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityKingdeeReq;
import com.foshan.form.community.response.kingdee.GetKingdeeImportHistoryRes;
import com.foshan.form.community.response.kingdee.GetToKingdeeDataListRes;
import com.foshan.form.response.GenericResponse;

@RestController
public class CommunityKingdeeController extends BaseCommunityController {

	/*
	 * 生成应收单
	 */

	@SuppressWarnings("rawtypes")
	@ResponseBody
	@PostMapping(value = "/getReceivablesOrder", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetToKingdeeDataListRes getReceivablesOrder(@RequestBody CommunityKingdeeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetToKingdeeDataListRes res = (GetToKingdeeDataListRes) communityKingdeeService.getReceivablesOrder(req);
		return res;
	}
	
	/*
	 * 生成应收单减免
	 */

	@SuppressWarnings("rawtypes")
	@ResponseBody
	@PostMapping(value = "/getReceivablesOrderChange", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetToKingdeeDataListRes getReceivablesOrderChange(@RequestBody CommunityKingdeeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetToKingdeeDataListRes res = (GetToKingdeeDataListRes) communityKingdeeService.getReceivablesOrderChange(req);
		return res;
	}

	/*
	 * 生成收据
	 */
	@SuppressWarnings("rawtypes")
	@ResponseBody
	@PostMapping(value = "/getReceiptOrder", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetToKingdeeDataListRes getRecepitOrder(@RequestBody CommunityKingdeeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetToKingdeeDataListRes res = (GetToKingdeeDataListRes) communityKingdeeService.getReceiptOrder(req);
		return res;
	}

	/*
	 * 生成基础数据
	 */
	@SuppressWarnings("rawtypes")
	@ResponseBody
	@PostMapping(value = "/getBaseData", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetToKingdeeDataListRes getBaseData(@RequestBody CommunityKingdeeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetToKingdeeDataListRes res = (GetToKingdeeDataListRes) communityKingdeeService.getBaseData(req);
		return res;
	}

	/*
	 * 导入应收单
	 */
	@ResponseBody
	@PostMapping(value = "/importReceivablesOrder", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse importReceivablesOrder(@RequestBody CommunityKingdeeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityKingdeeService.importReceivablesOrder(req);
		return res;
	}
	
	/*
	 * 导入应收单减免
	 */
	@ResponseBody
	@PostMapping(value = "/importReceivablesOrderChange", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse importReceivablesOrderChange(@RequestBody CommunityKingdeeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityKingdeeService.importReceivablesOrderChange(req);
		return res;
	}

	/*
	 * 导入收据
	 */
	@ResponseBody
	@PostMapping(value = "/importReceiptOrder", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse importReceiptOrder(@RequestBody CommunityKingdeeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityKingdeeService.importReceiptOrder(req);
		return res;
	}

	/*
	 * 导入基础数据
	 */
	@ResponseBody
	@PostMapping(value = "/importBaseData", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse importBaseData(@RequestBody CommunityKingdeeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityKingdeeService.importBaseData(req);
		return res;
	}

	/*
	 * 获取金蝶入账历史列表
	 */
	@ResponseBody
	@PostMapping(value = "/getKingdeeImportHistoryList", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetKingdeeImportHistoryRes getKingdeeImportHistoryList(@RequestBody CommunityKingdeeReq req,
			HttpServletRequest request) throws JsonProcessingException {
		GetKingdeeImportHistoryRes res = (GetKingdeeImportHistoryRes) communityKingdeeService
				.getKingdeeImportHistoryList(req);
		return res;
	}

	/*
	 * 删除金蝶入帐历史记录
	 */
	@ResponseBody
	@PostMapping(value = "/deleteKingdeeImportHistory", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetKingdeeImportHistoryRes deleteKingdeeImportHistory(@RequestBody CommunityKingdeeReq req,
			HttpServletRequest request) throws JsonProcessingException {
		GetKingdeeImportHistoryRes res = (GetKingdeeImportHistoryRes) communityKingdeeService
				.deleteKingdeeImportHistory(req);
		return res;
	}

	/*
	 * 下载金蝶入帐历史记录
	 */
	@ResponseBody
	@PostMapping(value = "/exportKingdeeImportFile", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportKingdeeImportFile(@RequestBody CommunityKingdeeReq req, HttpServletRequest request,
			HttpServletResponse response) {
		communityKingdeeService.exportKingdeeImportFile(req, response);
	}
}
