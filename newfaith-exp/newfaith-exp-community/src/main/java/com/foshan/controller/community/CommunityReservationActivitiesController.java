package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityReservationActivitiesReq;
import com.foshan.form.community.response.reservationActivities.GetReservationActivitiesInfo;
import com.foshan.form.community.response.reservationActivities.GetReservationActivitiesListRes;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "活动模块")
@RestController
public class CommunityReservationActivitiesController extends BaseCommunityController {
	// 获取活动列表
	@ApiOperation(value = "获取活动列表(getCommunityReservationActivitiesList)", httpMethod = "POST", notes = "获取活动列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityReservationActivitiesList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetReservationActivitiesListRes getCommunityReservationActivitiesList(@RequestBody CommunityReservationActivitiesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetReservationActivitiesListRes res = (GetReservationActivitiesListRes) communityReservationActivitiesService.getCommunityReservationActivitiesList(req);
		return res;
	}
	
	// 新增活动
	@ApiOperation(value = "新增活动(addCommunityReservationActivities)", httpMethod = "POST", notes = "新增活动，StrategyId、PublicAreaId、StartTime和EndTime不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addCommunityReservationActivities", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addCommunityReservationActivities(@RequestBody CommunityReservationActivitiesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReservationActivitiesService.addCommunityReservationActivities(req);
		return res;
	}
	
	// 修改活动
	@ApiOperation(value = "修改活动(modifyCommunityReservationActivities)", httpMethod = "POST", notes = "修改活动，ActivitiesId、StrategyId、PublicAreaId、StartTime和EndTime不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityReservationActivities", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyCommunityReservationActivities(@RequestBody CommunityReservationActivitiesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReservationActivitiesService.modifyCommunityReservationActivities(req);
		return res;
	}
	// 删除活动
	@ApiOperation(value = "删除活动(deleteCommunityReservationActivities)", httpMethod = "POST", notes = "删除活动，ActivitiesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityReservationActivities", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityReservationActivities(@RequestBody CommunityReservationActivitiesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReservationActivitiesService.deleteCommunityReservationActivities(req);
		return res;
	}
	
	// 获取活动详情
	@ApiOperation(value = "获取活动详情(getCommunityReservationActivitiesInfo)", httpMethod = "POST", notes = "获取活动详情，ActivitiesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityReservationActivitiesInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetReservationActivitiesInfo getCommunityReservationActivitiesInfo(@RequestBody CommunityReservationActivitiesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetReservationActivitiesInfo res = (GetReservationActivitiesInfo) communityReservationActivitiesService.getCommunityReservationActivitiesInfo(req);
		return res;
	}
}
