package com.foshan.controller.community;

import java.io.IOException;
import java.text.ParseException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityBankDepositBatchReq;
import com.foshan.form.community.response.communityBankDepositBatch.AddCommunityBankDepositBatchRes;
import com.foshan.form.community.response.communityBankDepositBatch.GetBankDepositEstatePayItemsListRes;
import com.foshan.form.community.response.communityBankDepositBatch.GetCommunityBankDepositBatchInfoRes;
import com.foshan.form.community.response.communityBankDepositBatch.GetCommunityBankDepositBatchListRes;
import com.foshan.form.community.response.communityBankDepositBatch.ModifyCommunityBankDepositBatchRes;
import com.foshan.form.community.response.exportExcel.ExportExcelRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "银行划账批次模块")
@RestController
public class CommunityBankDepositBatchController extends BaseCommunityController {

	// 获取银行划账批次列表
	@ApiOperation(value = "获取银行划账批次列表(getCommunityBankDepositBatchList)", httpMethod = "POST", notes = "获取银行划账批次列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityBankDepositBatchList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityBankDepositBatchListRes getCommunityBankDepositBatchList(@RequestBody CommunityBankDepositBatchReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityBankDepositBatchListRes res = (GetCommunityBankDepositBatchListRes) communityBankDepositBatchService.getCommunityBankDepositBatchList(req);
		return res;
	}
	
	// 新增银行划账批次
	@ApiOperation(value = "新增事件类型项目(addCommunityBankDepositBatch)", httpMethod = "POST", notes = "新增银行划账批次")
	@ResponseBody
	@RequestMapping(value = "/addCommunityBankDepositBatch", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityBankDepositBatchRes addCommunityBankDepositBatch(@RequestBody CommunityBankDepositBatchReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityBankDepositBatchRes res = (AddCommunityBankDepositBatchRes) communityBankDepositBatchService.addCommunityBankDepositBatch(req);
		return res;
	}
	
	// 修改银行划账批次
	@ApiOperation(value = "修改银行划账批次(modifyCommunityBankDepositBatch)", httpMethod = "POST", notes = "修改银行划账批次，communityBankDepositBatchId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityBankDepositBatch", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityBankDepositBatchRes modifyCommunityBankDepositBatch(@RequestBody CommunityBankDepositBatchReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityBankDepositBatchRes res = (ModifyCommunityBankDepositBatchRes) communityBankDepositBatchService.modifyCommunityBankDepositBatch(req);
		return res;
	}
	
	// 删除银行划账批次
	@ApiOperation(value = "删除事件类型项目(deleteCommunityBankDepositBatch)", httpMethod = "POST", notes = "删除银行划账批次，communityBankDepositBatchId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityBankDepositBatch", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityBankDepositBatch(@RequestBody CommunityBankDepositBatchReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityBankDepositBatchService.deleteCommunityBankDepositBatch(req);
		return res;
	}
	
	// 获取银行划账批次详情
	@ApiOperation(value = "获取银行划账批次详情(getCommunityBankDepositBatchInfo)", httpMethod = "POST", notes = "获取银行划账批次详情，communityBankDepositBatchId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityBankDepositBatchInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityBankDepositBatchInfoRes getCommunityBankDepositBatchInfo(@RequestBody CommunityBankDepositBatchReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityBankDepositBatchInfoRes res = (GetCommunityBankDepositBatchInfoRes) communityBankDepositBatchService.getCommunityBankDepositBatchInfo(req);
		return res;
	}
	
	
	// 新增筛选单元
	@ApiOperation(value = "新增筛选单元(addBankDepositEstatePayItems)", httpMethod = "POST", notes = "新增筛选单元，estatePayItemsIdList不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addBankDepositEstatePayItems", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addBankDepositEstatePayItems(@RequestBody CommunityBankDepositBatchReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityBankDepositBatchService.addBankDepositEstatePayItems(req);
		return res;
	}
	
	
	// 删除筛选单元
	@ApiOperation(value = "删除筛选单元(deleteBankDepositEstatePayItems)", httpMethod = "POST", notes = "删除筛选单元，estateId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteBankDepositEstatePayItems", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteBankDepositEstatePayItems(@RequestBody CommunityBankDepositBatchReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityBankDepositBatchService.deleteBankDepositEstatePayItems(req);
		return res;
	}
	
	// 修改筛选单元
	@ApiOperation(value = "修改筛选单元(modifyBankDepositEstatePayItems)", httpMethod = "POST", notes = "修改筛选单元，estateId、payItemsIdList不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyBankDepositEstatePayItems", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyBankDepositEstatePayItems(@RequestBody CommunityBankDepositBatchReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityBankDepositBatchService.modifyBankDepositEstatePayItems(req);
		return res;
	}
   
	
	// 特殊划扣单元项目列表
	@ApiOperation(value = "特殊划扣单元项目列表(getBankDepositEstatePayItemsList)", httpMethod = "POST", notes = "特殊划扣单元项目列表，unitCode选填；")
	@ResponseBody
	@RequestMapping(value = "/getBankDepositEstatePayItemsList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetBankDepositEstatePayItemsListRes getBankDepositEstatePayItemsList(@RequestBody CommunityBankDepositBatchReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetBankDepositEstatePayItemsListRes res = (GetBankDepositEstatePayItemsListRes) communityBankDepositBatchService.getBankDepositEstatePayItemsList(req);
		return res;
	}
	
	// 导出特殊划扣单元项目
	@ApiOperation(value = "导出特殊划扣单元项目(exportBankDepositEstatePayItems)", httpMethod = "POST", notes = "导出特殊划扣单元项目，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/exportBankDepositEstatePayItems", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ExportExcelRes exportBankDepositEstatePayItems(@RequestBody CommunityBankDepositBatchReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		ExportExcelRes  res = (ExportExcelRes) communityBankDepositBatchService.exportBankDepositEstatePayItems(req,response);
		return res;
	}
	
	// 记账
	@ApiOperation(value = "记账(setbatchState)", httpMethod = "POST", notes = "记账，communityBankDepositBatchId必填；")
	@ResponseBody
	@RequestMapping(value = "/setbatchState", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse setBatchState(@RequestBody CommunityBankDepositBatchReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityBankDepositBatchService.setBatchState(req);
		return res;
	}
	
	// 回滚记账
	@ApiOperation(value = "回滚记账(rollbackBatchState)", httpMethod = "POST", notes = "回滚记账，communityBankDepositBatchId必填；")
	@ResponseBody
	@RequestMapping(value = "/rollbackBatchState", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse rollbackBatchState(@RequestBody CommunityBankDepositBatchReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityBankDepositBatchService.rollbackBatchState(req);
		return res;
	}
}
