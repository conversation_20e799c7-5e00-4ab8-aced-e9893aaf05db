package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityMeterRecordReq;
import com.foshan.form.community.response.communityMeterRecord.AddCommunityMeterRecordRes;
import com.foshan.form.community.response.communityMeterRecord.GetCommunityMeterRecordInfoRes;
import com.foshan.form.community.response.communityMeterRecord.GetCommunityMeterRecordListRes;
import com.foshan.form.community.response.communityMeterRecord.ModifyCommunityMeterRecordRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "抄表模块")
@RestController
public class CommunityMeterRecordController extends BaseCommunityController {

	// 获取抄表列表
	@ApiOperation(value = "获取抄表列表(getCommunityMeterRecordList)", httpMethod = "POST", notes = "获取抄表列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityMeterRecordList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMeterRecordListRes getCommunityMeterRecordList(@RequestBody CommunityMeterRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityMeterRecordListRes res = (GetCommunityMeterRecordListRes) communityMeterRecordService.getCommunityMeterRecordList(req);
		return res;
	}
	
	// 获取租赁抄表列表
	@ApiOperation(value = "获取租赁抄表列表(getCommunityRentMeterRecordList)", httpMethod = "POST", notes = "获取租赁抄表列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityRentMeterRecordList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMeterRecordListRes getCommunityRentMeterRecordList(@RequestBody CommunityMeterRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		req.setIsRent(1);
		GetCommunityMeterRecordListRes res = (GetCommunityMeterRecordListRes) communityMeterRecordService.getCommunityMeterRecordList(req);
		return res;
	}
	
	// 新增抄表
	@ApiOperation(value = "新增抄表记录(addCommunityMeterRecord)", httpMethod = "POST", notes = "新增抄表")
	@ResponseBody
	@RequestMapping(value = "/addCommunityMeterRecord", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityMeterRecordRes addCommunityMeterRecord(@RequestBody CommunityMeterRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityMeterRecordRes res = (AddCommunityMeterRecordRes) communityMeterRecordService.addCommunityMeterRecord(req);
		return res;
	}
	
	
	// 新增租赁抄表
	@ApiOperation(value = "新增租赁抄表(addCommunityRentMeterRecord)", httpMethod = "POST", notes = "新增租赁抄表")
	@ResponseBody
	@RequestMapping(value = "/addCommunityRentMeterRecord", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityMeterRecordRes addCommunityRentMeterRecord(@RequestBody CommunityMeterRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityMeterRecordRes res = (AddCommunityMeterRecordRes) communityMeterRecordService.addCommunityMeterRecord(req);
		return res;
	}
	
	// 修改抄表
	@ApiOperation(value = "修改抄表(modifyCommunityMeterRecord)", httpMethod = "POST", notes = "修改抄表，communityMeterRecordId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityMeterRecord", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityMeterRecordRes modifyCommunityMeterRecord(@RequestBody CommunityMeterRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityMeterRecordRes res = (ModifyCommunityMeterRecordRes) communityMeterRecordService.modifyCommunityMeterRecord(req);
		return res;
	}
	
	// 修改租赁抄表
	@ApiOperation(value = "修改租赁抄表(modifyCommunityRentMeterRecord)", httpMethod = "POST", notes = "修改租赁抄表，communityMeterRecordId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityRentMeterRecord", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityMeterRecordRes modifyCommunityRentMeterRecord(@RequestBody CommunityMeterRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityMeterRecordRes res = (ModifyCommunityMeterRecordRes) communityMeterRecordService.modifyCommunityMeterRecord(req);
		return res;
	}
	
	// 删除抄表
	@ApiOperation(value = "删除抄表(deleteCommunityMeterRecord)", httpMethod = "POST", notes = "删除抄表，communityMeterRecordId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityMeterRecord", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityMeterRecord(@RequestBody CommunityMeterRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMeterRecordService.deleteCommunityMeterRecord(req);
		return res;
	}
	
	// 删除租赁抄表
	@ApiOperation(value = "删除租赁抄表记录(deleteCommunityRentMeterRecord)", httpMethod = "POST", notes = "删除租赁抄表，communityMeterRecordId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityRentMeterRecord", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityRentMeterRecord(@RequestBody CommunityMeterRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMeterRecordService.deleteCommunityMeterRecord(req);
		return res;
	}
	
	// 获取抄表详情
	@ApiOperation(value = "获取抄表详情(getCommunityMeterRecordInfo)", httpMethod = "POST", notes = "获取抄表详情，communityMeterRecordId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityMeterRecordInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMeterRecordInfoRes getCommunityMeterRecordInfo(@RequestBody CommunityMeterRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityMeterRecordInfoRes res = (GetCommunityMeterRecordInfoRes) communityMeterRecordService.getCommunityMeterRecordInfo(req);
		return res;
	}
	
	// 获取租赁抄表详情
	@ApiOperation(value = "获取租赁抄表详情(getCommunityRentMeterRecordInfo)", httpMethod = "POST", notes = "获取租赁抄表详情，communityMeterRecordId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityRentMeterRecordInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMeterRecordInfoRes getCommunityRentMeterRecordInfo(@RequestBody CommunityMeterRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		req.setIsRent(1);
		GetCommunityMeterRecordInfoRes res = (GetCommunityMeterRecordInfoRes) communityMeterRecordService.getCommunityMeterRecordInfo(req);
		return res;
	}
   
}
