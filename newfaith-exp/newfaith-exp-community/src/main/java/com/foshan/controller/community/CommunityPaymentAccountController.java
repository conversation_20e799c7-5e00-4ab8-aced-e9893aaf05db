package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityPaymentAccountReq;
import com.foshan.form.community.response.communityPaymentAccount.AddCommunityPaymentAccountRes;
import com.foshan.form.community.response.communityPaymentAccount.GetCommunityPaymentAccountInfoRes;
import com.foshan.form.community.response.communityPaymentAccount.GetCommunityPaymentAccountListRes;
import com.foshan.form.community.response.communityPaymentAccount.ModifyCommunityPaymentAccountRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "账户信息模块")
@RestController
public class CommunityPaymentAccountController extends BaseCommunityController {

	// 获取账户信息列表
	@ApiOperation(value = "获取账户信息列表(getCommunityPaymentAccountList)", httpMethod = "POST", notes = "获取账户信息列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityPaymentAccountList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityPaymentAccountListRes getCommunityPaymentAccountList(@RequestBody CommunityPaymentAccountReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityPaymentAccountListRes res = (GetCommunityPaymentAccountListRes) communityPaymentAccountService.getCommunityPaymentAccountList(req);
		return res;
	}
	
	// 新增账户信息
	@ApiOperation(value = "新增事件类型项目(addCommunityPaymentAccount)", httpMethod = "POST", notes = "新增账户信息")
	@ResponseBody
	@RequestMapping(value = "/addCommunityPaymentAccount", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityPaymentAccountRes addCommunityPaymentAccount(@RequestBody CommunityPaymentAccountReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityPaymentAccountRes res = (AddCommunityPaymentAccountRes) communityPaymentAccountService.addCommunityPaymentAccount(req);
		return res;
	}
	
	// 修改账户信息
	@ApiOperation(value = "修改账户信息(modifyCommunityPaymentAccount)", httpMethod = "POST", notes = "修改账户信息，communityPaymentAccountId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityPaymentAccount", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityPaymentAccountRes modifyCommunityPaymentAccount(@RequestBody CommunityPaymentAccountReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityPaymentAccountRes res = (ModifyCommunityPaymentAccountRes) communityPaymentAccountService.modifyCommunityPaymentAccount(req);
		return res;
	}
	
	// 删除账户信息
	@ApiOperation(value = "删除事件类型项目(deleteCommunityPaymentAccount)", httpMethod = "POST", notes = "删除账户信息，communityPaymentAccountId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityPaymentAccount", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityPaymentAccount(@RequestBody CommunityPaymentAccountReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityPaymentAccountService.deleteCommunityPaymentAccount(req);
		return res;
	}
	
	// 获取账户信息详情
	@ApiOperation(value = "获取账户信息详情(getCommunityPaymentAccountInfo)", httpMethod = "POST", notes = "获取账户信息详情，communityPaymentAccountId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityPaymentAccountInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityPaymentAccountInfoRes getCommunityPaymentAccountInfo(@RequestBody CommunityPaymentAccountReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityPaymentAccountInfoRes res = (GetCommunityPaymentAccountInfoRes) communityPaymentAccountService.getCommunityPaymentAccountInfo(req);
		return res;
	}
   
}
