package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityBuilderReq;
import com.foshan.form.community.response.communityBuilder.AddCommunityBuilderRes;
import com.foshan.form.community.response.communityBuilder.GetCommunityBuilderInfoRes;
import com.foshan.form.community.response.communityBuilder.GetCommunityBuilderListRes;
import com.foshan.form.community.response.communityBuilder.ModifyCommunityBuilderRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "开发商模块")
@RestController
public class CommunityBuilderController extends BaseCommunityController {

	// 获取开发商列表
	@ApiOperation(value = "获取开发商列表(getCommunityBuilderList)", httpMethod = "POST", notes = "获取开发商列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityBuilderList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityBuilderListRes getCommunityBuilderList(@RequestBody CommunityBuilderReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityBuilderListRes res = (GetCommunityBuilderListRes) communityBuilderService.getCommunityBuilderList(req);
		return res;
	}
	
	// 新增开发商
	@ApiOperation(value = "新增事件类型项目(addCommunityBuilder)", httpMethod = "POST", notes = "新增开发商")
	@ResponseBody
	@RequestMapping(value = "/addCommunityBuilder", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityBuilderRes addCommunityBuilder(@RequestBody CommunityBuilderReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityBuilderRes res = (AddCommunityBuilderRes) communityBuilderService.addCommunityBuilder(req);
		return res;
	}
	
	// 修改开发商
	@ApiOperation(value = "修改开发商(modifyCommunityBuilder)", httpMethod = "POST", notes = "修改开发商，communityBuilderId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityBuilder", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityBuilderRes modifyCommunityBuilder(@RequestBody CommunityBuilderReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityBuilderRes res = (ModifyCommunityBuilderRes) communityBuilderService.modifyCommunityBuilder(req);
		return res;
	}
	
	// 删除开发商
	@ApiOperation(value = "删除事件类型项目(deleteCommunityBuilder)", httpMethod = "POST", notes = "删除开发商，communityBuilderId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityBuilder", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityBuilder(@RequestBody CommunityBuilderReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityBuilderService.deleteCommunityBuilder(req);
		return res;
	}
	
	// 获取开发商详情
	@ApiOperation(value = "获取开发商详情(getCommunityBuilderInfo)", httpMethod = "POST", notes = "获取开发商详情，communityBuilderId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityBuilderInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityBuilderInfoRes getCommunityBuilderInfo(@RequestBody CommunityBuilderReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityBuilderInfoRes res = (GetCommunityBuilderInfoRes) communityBuilderService.getCommunityBuilderInfo(req);
		return res;
	}
   
}
