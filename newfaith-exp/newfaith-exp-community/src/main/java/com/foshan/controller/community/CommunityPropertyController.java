package com.foshan.controller.community;


import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityPropertyReq;
import com.foshan.form.community.response.communityProperty.GetCommunityPropertyListRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "资产模块")
@RestController
public class CommunityPropertyController extends BaseCommunityController {
	
	// 获取资产列表
	@ApiOperation(value = "获取资产列表(getCommunityPropertyList)", httpMethod = "POST", notes = "获取资产列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityPropertyList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityPropertyListRes getCommunityPropertyList(@RequestBody CommunityPropertyReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityPropertyListRes res = (GetCommunityPropertyListRes) communityPropertyService.getCommunityPropertyList(req);
		return res;
	}

}
