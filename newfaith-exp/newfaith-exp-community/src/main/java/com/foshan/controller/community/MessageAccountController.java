package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.MessageAccountReq;
import com.foshan.form.community.response.messageAccount.AddMessageAccountRes;
import com.foshan.form.community.response.messageAccount.GetMessageAccountInfoRes;
import com.foshan.form.community.response.messageAccount.GetMessageAccountListRes;
import com.foshan.form.community.response.messageAccount.ModifyMessageAccountRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "消息账号表模块")
@RestController
public class MessageAccountController extends BaseCommunityController {

	// 获取消息账号表列表
	@ApiOperation(value = "获取消息账号表列表(getMessageAccountList)", httpMethod = "POST", notes = "获取消息账号表列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getMessageAccountList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetMessageAccountListRes getMessageAccountList(@RequestBody MessageAccountReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetMessageAccountListRes res = (GetMessageAccountListRes) messageAccountService.getMessageAccountList(req);
		return res;
	}
	
	// 新增消息账号
	@ApiOperation(value = "新增消息账号(addMessageAccount)", httpMethod = "POST", notes = "新增消息账号")
	@ResponseBody
	@RequestMapping(value = "/addMessageAccount", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddMessageAccountRes addMessageAccount(@RequestBody MessageAccountReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddMessageAccountRes res = (AddMessageAccountRes) messageAccountService.addMessageAccount(req);
		return res;
	}
	
	// 修改消息账号表
	@ApiOperation(value = "修改消息账号表(modifyMessageAccount)", httpMethod = "POST", notes = "修改消息账号表，messageAccountId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyMessageAccount", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyMessageAccountRes modifyMessageAccount(@RequestBody MessageAccountReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyMessageAccountRes res = (ModifyMessageAccountRes) messageAccountService.modifyMessageAccount(req);
		return res;
	}
	
	// 删除消息账号表
	@ApiOperation(value = "删除事件类型项目(deleteMessageAccount)", httpMethod = "POST", notes = "删除消息账号表，messageAccountId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteMessageAccount", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteMessageAccount(@RequestBody MessageAccountReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) messageAccountService.deleteMessageAccount(req);
		return res;
	}
	
	// 获取消息账号表详情
	@ApiOperation(value = "获取消息账号表详情(getMessageAccountInfo)", httpMethod = "POST", notes = "获取消息账号表详情，messageAccountId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getMessageAccountInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetMessageAccountInfoRes getMessageAccountInfo(@RequestBody MessageAccountReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetMessageAccountInfoRes res = (GetMessageAccountInfoRes) messageAccountService.getMessageAccountInfo(req);
		return res;
	}
   
}
