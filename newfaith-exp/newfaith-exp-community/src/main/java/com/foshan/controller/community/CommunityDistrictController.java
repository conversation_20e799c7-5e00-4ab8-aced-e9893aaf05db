package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityDistrictReq;
import com.foshan.form.community.response.communityDistrict.AddCommunityDistrictRes;
import com.foshan.form.community.response.communityDistrict.GetCommunityDistrictInfoRes;
import com.foshan.form.community.response.communityDistrict.GetCommunityDistrictListRes;
import com.foshan.form.community.response.communityDistrict.ModifyCommunityDistrictRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "小区模块")
@RestController
public class CommunityDistrictController extends BaseCommunityController {

	// 获取小区列表
	@ApiOperation(value = "获取小区列表(getCommunityDistrictList)", httpMethod = "POST", notes = "获取小区列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityDistrictList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityDistrictListRes getCommunityDistrictList(@RequestBody CommunityDistrictReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityDistrictListRes res = (GetCommunityDistrictListRes) communityDistrictService.getCommunityDistrictList(req);
		return res;
	}
	
	// 新增小区
	@ApiOperation(value = "新增事件类型项目(addCommunityDistrict)", httpMethod = "POST", notes = "新增小区<p>communityId和name不能为空")
	@ResponseBody
	@RequestMapping(value = "/addCommunityDistrict", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityDistrictRes addCommunityDistrict(@RequestBody CommunityDistrictReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityDistrictRes res = (AddCommunityDistrictRes) communityDistrictService.addCommunityDistrict(req);
		return res;
	}
	
	// 修改小区
	@ApiOperation(value = "修改小区(modifyCommunityDistrict)", httpMethod = "POST", notes = "修改小区<p>communityDistrictId、communityId和name不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityDistrict", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityDistrictRes modifyCommunityDistrict(@RequestBody CommunityDistrictReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityDistrictRes res = (ModifyCommunityDistrictRes) communityDistrictService.modifyCommunityDistrict(req);
		return res;
	}
	
	// 删除小区
	@ApiOperation(value = "删除事件类型项目(deleteCommunityDistrict)", httpMethod = "POST", notes = "删除小区<p>communityDistrictId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityDistrict", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityDistrict(@RequestBody CommunityDistrictReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityDistrictService.deleteCommunityDistrict(req);
		return res;
	}
	
	// 获取小区详情
	@ApiOperation(value = "获取小区详情(getCommunityDistrictInfo)", httpMethod = "POST", notes = "获取小区详情 <p>communityDistrictId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityDistrictInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityDistrictInfoRes getCommunityDistrictInfo(@RequestBody CommunityDistrictReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityDistrictInfoRes res = (GetCommunityDistrictInfoRes) communityDistrictService.getCommunityDistrictInfo(req);
		return res;
	}
	
	// 获取小区树
	@ApiOperation(value = "获取小区树(getCommunityDistrictTree)", httpMethod = "POST", notes = "获取小区树")
	@ResponseBody
	@RequestMapping(value = "/getCommunityDistrictTree", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityDistrictListRes getCgetCommunityDistrictTreeommunityDistrictInfo(@RequestBody CommunityDistrictReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityDistrictListRes res = (GetCommunityDistrictListRes) communityDistrictService.getCommunityDistrictTree();
		return res;
	}
   
}
