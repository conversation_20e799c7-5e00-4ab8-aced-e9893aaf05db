package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityEventCategoryItemsReq;
import com.foshan.form.community.response.communityEventCategoryItems.AddCommunityEventCategoryItemsRes;
import com.foshan.form.community.response.communityEventCategoryItems.GetCommunityEventCategoryItemsInfo;
import com.foshan.form.community.response.communityEventCategoryItems.GetCommunityEventCategoryItemsListRes;
import com.foshan.form.community.response.communityEventCategoryItems.ModifyCommunityEventCategoryItemsRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


@Api(tags = "事件类型项目型模块")
@RestController
public class CommunityEventCategoryItemsController extends BaseCommunityController {

	// 获取事件类型项目列表
	@ApiOperation(value = "获取事件类型项目列表(getCommunityEventCategoryItemsList)", httpMethod = "POST", notes = "获取事件类型项目列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityEventCategoryItemsList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityEventCategoryItemsListRes getCommunityEventCategoryItemsList(@RequestBody CommunityEventCategoryItemsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityEventCategoryItemsListRes res = (GetCommunityEventCategoryItemsListRes) communityEventCategoryItemsService.getCommunityEventCategoryItemsList(req);
		return res;
	}
	
	// 新增事件类型项目
	@ApiOperation(value = "新增事件类型项目(addCommunityEventCategoryItems)", httpMethod = "POST", notes = "新增事件类型项目，eventCategoryId、orders、itemName和itemkey不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addCommunityEventCategoryItems", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityEventCategoryItemsRes addCommunityEventCategoryItems(@RequestBody CommunityEventCategoryItemsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityEventCategoryItemsRes res = (AddCommunityEventCategoryItemsRes) communityEventCategoryItemsService.addCommunityEventCategoryItems(req);
		return res;
	}
	
	// 修改事件类型项目
	@ApiOperation(value = "修改事件类型项目(modifyCommunityEventCategoryItems)", httpMethod = "POST", notes = "修改事件类型项目，eventCategoryId、eventCategoryItemsId、orders、itemName和itemkey不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityEventCategoryItems", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityEventCategoryItemsRes modifyCommunityEventCategoryItems(@RequestBody CommunityEventCategoryItemsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityEventCategoryItemsRes res = (ModifyCommunityEventCategoryItemsRes) communityEventCategoryItemsService.modifyCommunityEventCategoryItems(req);
		return res;
	}
	
	// 删除事件类型项目
	@ApiOperation(value = "删除事件类型项目(deleteCommunityEventCategoryItems)", httpMethod = "POST", notes = "删除事件类型项目，eventCategoryItemsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityEventCategoryItems", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityEventCategoryItems(@RequestBody CommunityEventCategoryItemsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityEventCategoryItemsService.deleteCommunityEventCategoryItems(req);
		return res;
	}
	
	// 获取事件类型项目详情
	@ApiOperation(value = "获取事件类型项目详情(getCommunityEventCategoryItemsInfo)", httpMethod = "POST", notes = "获取事件类型项目详情，eventCategoryItemsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityEventCategoryItemsInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityEventCategoryItemsInfo getCommunityEventCategoryItemsInfo(@RequestBody CommunityEventCategoryItemsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityEventCategoryItemsInfo res = (GetCommunityEventCategoryItemsInfo) communityEventCategoryItemsService.getCommunityEventCategoryItemsInfo(req);
		return res;
	}

}
