-- v_meter_estate_total_area source
CREATE OR REPLACE
ALGORITHM = UNDEFINED VIEW v_meter_estate_total_area AS (
SELECT
    aa.meterid,aa.metercount,aa.totalchargearea,aa.totaladdarea
FROM (
    SELECT  a.meterId,COUNT(1) AS metercount,SUM(b.chargingArea) AS totalchargearea, SUM(b.additionalArea) AS totaladdarea
     FROM t_community_meter_property a,t_community_property b
     WHERE a.propertyId = b.id  GROUP BY a.meterId
)  aa
WHERE aa.metercount > 1
);

-- v_meter_record source
CREATE OR REPLACE
ALGORITHM = UNDEFINED VIEW v_meter_record AS (
SELECT DISTINCT
aa.*,aa.unitprice*aa.allocationNum AS allocationAmount,bb.id AS allocationId
FROM (
SELECT a.*
FROM (
SELECT a.id AS meterId,a.meterCode,a.metername,a.allocationMethod,a.payItemsName,
c.additionalAmount,c.additionalInstructions,a.expirationDates,a.meterAttributesId,
a.formulaId,a.allocationPeriod,e.templeteId,b.rate,b.unitPrice,
e.dynamicParameter,e.formulaName,f.templeteInfo,f.templeteName,
c.lastnum,c.recordnum,c.recorddate,c.lastrecorddate,
YEAR(DATE_ADD(c.lastrecorddate, INTERVAL+1 MONTH)) AS allocationStartYear,
MONTH(DATE_ADD(c.lastrecorddate, INTERVAL+1 MONTH)) AS allocationStartMonth,
YEAR(c.recorddate) AS allocationEndYear,
MONTH(c.recorddate) AS allocationEndMonth,
IF(g.meterCount IS NULL,0,g.meterCount) AS estateCount,
IF(g.totalChargeArea IS NULL,0,g.totalChargeArea) AS totalChargeArea,
IF(g.totalAddArea IS NULL,0,g.totalAddArea) AS totalAddArea,
(c.recordNum - IF(c.lastNum IS NULL,IF(a.initialData IS NULL,0,a.initialData),c.lastNum))*b.rate + c.additionalAmount AS allocationNum,a.lastallocatedate,d.maxrecorddate
FROM t_community_meter a
INNER JOIN t_community_meter_attributes b ON a.meterattributesid = b.id
LEFT JOIN t_community_meter_formula e ON a.formulaid=e.id
LEFT JOIN t_community_formula_templete f ON e.templeteid=f.id
LEFT JOIN v_meter_estate_total_area g ON a.id=g.meterid
INNER JOIN (
SELECT b.meterid,b.additionalamount,b.additionalinstructions,b.id,
IF(c.recordnum IS NULL,0,c.recordnum) AS lastnum,
IF(a.lastallocatedate IS NULL OR b.lastRecordid IS NULL,LAST_DAY(DATE_ADD(b.recorddate,INTERVAL-a.allocationperiod MONTH)),c.recorddate) AS lastrecorddate,
b.recordnum,b.recorddate
FROM t_community_meter a 
INNER JOIN t_community_meter_record b ON a.id=b.meterId AND b.state=1
LEFT JOIN t_community_meter_record c ON a.id=c.meterid AND c.state=1
AND IF(
a.allocationPeriod=1,
b.lastrecordid=c.id,
c.recorddate=LAST_DAY(DATE_ADD(b.recorddate,INTERVAL-a.allocationperiod MONTH)))
LEFT JOIN t_community_meter_record d ON a.id=d.meterid AND d.state=1
AND IF(
a.allocationPeriod=1 AND b.lastrecordid IS NOT NULL,
b.lastrecordid=d.id,
d.recorddate=LAST_DAY(DATE_ADD(b.recorddate,INTERVAL-a.allocationperiod+1 MONTH)))
WHERE (b.recorddate>=DATE_ADD(NOW(), INTERVAL -2 MONTH)) AND d.recordnum IS NOT NULL
) c ON a.id=c.meterid
INNER JOIN (
SELECT meterid,MAX(recorddate) AS maxrecorddate
FROM t_community_meter_record
GROUP BY meterid
) d ON a.id=d.meterid 
WHERE a.allocationMethod IS NOT NULL
AND a.level=1 AND a.isApportioned=1 AND a.iscommon=1 AND a.isDisabled=0
)a
WHERE (a.lastallocatedate IS NULL 
OR DATE_FORMAT(a.recorddate,'%Y-%m-%d')=LAST_DAY(DATE_ADD(a.lastallocatedate,INTERVAL+a.allocationperiod MONTH))
OR (a.allocationperiod=1 AND LAST_DAY(DATE_ADD(a.lastrecorddate,INTERVAL+a.allocationperiod MONTH))=a.maxrecorddate))
) aa
LEFT JOIN t_community_meter_allocation bb ON aa.meterId=bb.meterid
AND aa.allocationEndyear=bb.allocationyear
AND aa.allocationEndmonth=bb.allocationmonth
 );




-- v_allocation_item source
CREATE OR REPLACE
ALGORITHM = UNDEFINED VIEW v_allocation_item AS (
SELECT
c.id,a.payItemsName,a.templeteId,a.unitPrice,a.dynamicParameter,a.templeteInfo,
a.allocationStartYear,a.allocationStartMonth,a.allocationEndYear,a.allocationEndMonth,a.totalChargeArea,a.allocationNum,a.allocationId,
c.chargingArea,c.usableArea,a.meterId,d.memberid,d.billingDate,
IF(LENGTH(REPLACE(JSON_EXTRACT(c.reservedField,'$.propertyParkingArea'),'"',''))=0,0,REPLACE(JSON_EXTRACT(c.reservedField,'$.propertyParkingArea'),'"','')) AS propertyParkingArea,
IF(LENGTH(REPLACE(JSON_EXTRACT(c.reservedField,'$.propertyParkingNum'),'"',''))=0,0,REPLACE(JSON_EXTRACT(c.reservedField,'$.propertyParkingNum'),'"','')) AS propertyParkingNum,
IF(LENGTH(REPLACE(JSON_EXTRACT(c.reservedField,'$.defenceParkingArea'),'"',''))=0,0,REPLACE(JSON_EXTRACT(c.reservedField,'$.defenceParkingArea'),'"','')) AS defenceParkingArea,
IF(LENGTH(REPLACE(JSON_EXTRACT(c.reservedField,'$.defenceParkingNum'),'"',''))=0,0,REPLACE(JSON_EXTRACT(c.reservedField,'$.defenceParkingNum'),'"','')) AS defenceParkingNum,
c.unitCode,a.allocationPeriod,c.specialallocationflag
FROM tmp_meter_record a,t_community_meter_property b,t_community_property c,t_community_member_property d
WHERE a.meterId = b.meterId AND c.id=d.propertyid AND d.iscurrentowner=1
AND c.specialFeeFlag IN(0,1,3,5)
AND b.propertyId = c.id
AND d.parentpropertyid IS NULL
AND a.allocationAmount <> 0
);


-- v_chewei_shijian source
CREATE OR REPLACE
ALGORITHM = UNDEFINED VIEW v_chewei_shijian AS (
SELECT b.parentpropertyid,b.propertyid,b.billingdate,b.terminationDate,a.chargingarea,a.estatetype,a.comment
FROM t_community_property a,t_community_member_property b
WHERE a.id=b.propertyid AND b.parentpropertyid IS NOT NULL
AND b.membertype<>2 AND (b.terminationDate IS NULL OR b.terminationDate>DATE_ADD(NOW(), INTERVAL-12 MONTH))
);


-- v_export_allocation_list source
CREATE OR REPLACE
ALGORITHM = UNDEFINED VIEW v_export_allocation_list AS (
SELECT a.districtName AS 楼盘名称,b.buildingName AS 楼阁,c.unitCode AS 单元编号,c.floor AS 楼层,c.roomnumber AS 房号,e.username AS 姓名,
ROUND(c.chargingarea,3) AS 收费面积,
ROUND(IFNULL(REPLACE(JSON_EXTRACT(c.reservedField,'$.propertyParkingArea'),'\"',''),0),3) AS 产权车位面积,
IFNULL(REPLACE(JSON_EXTRACT(c.reservedField,'$.propertyParkingNum'),'\"',''),0) AS 产权车位个数,
ROUND(IFNULL(REPLACE(JSON_EXTRACT(c.reservedField,'$.defenceParkingArea'),'\"',''),0),3) AS 人防车位面积,
IFNULL(REPLACE(JSON_EXTRACT(c.reservedField,'$.defenceParkingNum'),'\"',''),0) AS 人防车位个数,
DATE_FORMAT(d.recorddate,'%Y-%m-%d') AS 入住日期,f.二区车库公共电费,f.三区车库公共电费,f.公共电费分摊,f.景观大道路灯电费,
f.空中花园公共电费,
(f.车库公共电费1+f.车库公共电费2+f.车库公共电费3) AS 车库公共电费,
(f.车库公共水费分摊1+f.车库公共水费分摊2+f.车库公共水费分摊3) AS 车库公共水费分摊,
f.公共水费分摊,
ROUND((f.二区车库公共电费+f.三区车库公共电费+f.公共电费分摊+f.景观大道路灯电费+f.空中花园公共电费+
(f.车库公共电费1+f.车库公共电费2)+
(f.车库公共水费分摊1+f.车库公共水费分摊2)+f.公共水费分摊),2) AS 合计,f.allocationDate
,a.districtOrder AS districtOrder,b.buildingOrder AS buildingOrder,c.floor AS FLOOR, c.roomNumber AS roomNumber　
FROM (SELECT aa.estateid,aa.allocationdate,
    MAX(CASE payitemsname WHEN '二区车库公共电费1' THEN allocationAmount ELSE 0 END ) AS 二区车库公共电费,
    MAX(CASE payitemsname WHEN '三区车库公共电费1' THEN allocationAmount ELSE 0 END ) 三区车库公共电费,
    MAX(CASE payitemsname WHEN '公共电费分摊' THEN allocationAmount ELSE 0 END ) 公共电费分摊,
    MAX(CASE payitemsname WHEN '景观大道路灯电费' THEN allocationAmount ELSE 0 END ) 景观大道路灯电费,
    MAX(CASE payitemsname WHEN '空中花园公共电费' THEN allocationAmount ELSE 0 END ) 空中花园公共电费,
    MAX(CASE payitemsname WHEN '车库公共电费1' THEN allocationAmount ELSE 0 END ) 车库公共电费1,
    MAX(CASE payitemsname WHEN '车库公共电费2' THEN allocationAmount ELSE 0 END ) 车库公共电费2,
    MAX(CASE payitemsname WHEN '车库公共电费3' THEN allocationAmount ELSE 0 END ) 车库公共电费3,
    MAX(CASE payitemsname WHEN '车库公共水费分摊1' THEN allocationAmount ELSE 0 END ) 车库公共水费分摊1,
    MAX(CASE payitemsname WHEN '车库公共水费分摊2' THEN allocationAmount ELSE 0 END ) 车库公共水费分摊2,
    MAX(CASE payitemsname WHEN '车库公共水费分摊3' THEN allocationAmount ELSE 0 END ) 车库公共水费分摊3,
    MAX(CASE payitemsname WHEN '公共水费分摊' THEN allocationAmount ELSE 0 END ) 公共水费分摊 
    FROM (SELECT a.estateid,CONCAT(a.payitemsname,IFNULL(templeteid,'')) AS payitemsname,
    ROUND(SUM(a.allocationAmount),2) AS allocationAmount, CONCAT(a.allocationendyear,'-',a.allocationendmonth) AS allocationdate
    FROM t_community_meter_allocation_item a GROUP BY a.estateid,a.payitemsname,a.templeteid,CONCAT(a.allocationendyear,'-',a.allocationendmonth)) aa 
    GROUP BY aa.estateid,aa.allocationdate) f,t_community_district a,t_community_building b,t_community_property c,
    t_community_member_property d,t_account e WHERE f.estateid=c.id AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.propertyid 
    AND d.memberid=e.id AND d.iscurrentMember=1 AND d.iscurrentowner=1 ORDER BY a.districtName,b.buildingname,c.unitcode
 );


-- v_community_receivables source
CREATE OR REPLACE
ALGORITHM = UNDEFINED VIEW v_community_receivables AS (
SELECT aa.districtId,aa.districtName,aa.districtOrder,aa.districtCode,aa.buildingId as buildingId,aa.buildingName,aa.buildingorder,aa.unitCode,DATE_FORMAT(aa.receivableDate,'%Y-%m-%d') AS receivableDate,bb.username,
IF(bb.terminationdate='2099-12-31 00:00:00',
CASE bb.estateState WHEN 0 THEN '未收楼' WHEN 1 THEN '已入住' WHEN 2 THEN '装修中'
WHEN 3 THEN '出租' WHEN 4 THEN '离退' WHEN 5 THEN '自住' WHEN 6 THEN '空置' END,'离退') AS estatestate,aa.bankname,aa.bankaccount,aa.accountname,
aa.payItemsName AS payItemsName,aa.receivableAmount,aa.receivedAmount,aa.arrears,aa.buildingArea,DATE_FORMAT(bb.billingDate,'%Y-%m-%d') billingDate,DATE_FORMAT(bb.recordDate,'%Y-%m-%d') AS recordDate,aa.chargecategory,aa.comment
FROM(
SELECT c.id,a.id AS districtId,a.districtName,a.districtOrder,a.districtCode,b.id as buildingId,b.buildingName,b.buildingorder,c.unitCode,d.receivableDate,
d.payItemsName AS payItemsName,SUM(d.receivableAmount) AS receivableAmount,SUM(d.receivedAmount) AS receivedAmount,SUM(d.receivableAmount-d.receivedAmount) AS arrears,f.bankname,f.bankaccount,f.accountname,c.buildingArea,d.chargecategory,d.comment
FROM t_community_district a INNER JOIN t_community_building b ON a.id<>11 AND b.id NOT IN (17,20,21,126,127,327,328) AND a.id=b.districtId
INNER JOIN t_community_property c ON b.id=c.buildingId
INNER JOIN t_community_receivables d ON c.id=d.estateId
INNER JOIN t_community_pay_items e ON (e.chargeCategory IN(1,2,3,5) OR e.id IN (35,36,70,71,72,77,78,80,82,83,84,85,86,87,89,90,91,92,93,101,126,129,154,155,157,159,160,161,164,168,181,194,195)) AND d.payItemId=e.id
LEFT JOIN t_community_payment_account f ON c.paymentaccountid=f.id
GROUP BY c.unitCode,d.receivableDate,d.payItemsName) aa
INNER JOIN
(SELECT a.id,c.username,a.estatestate,b.billingdate,IFNULL(b.terminationdate,'2099-12-31 00:00:00') AS terminationdate,b.recordDate,b.iscurrentOwner
FROM t_community_property a,t_community_member_property b,t_account c
WHERE a.id=b.propertyid AND b.memberid=c.id) bb
ON aa.id=bb.id AND aa.receivabledate>=bb.billingdate
AND ((aa.receivabledate<=bb.terminationdate AND bb.iscurrentOwner=1)
OR (aa.receivabledate>=bb.billingdate AND aa.receivabledate<= bb.terminationdate)
OR (aa.receivabledate>bb.terminationdate AND YEAR(aa.receivabledate)=YEAR(bb.terminationdate) AND MONTH(aa.receivabledate)=MONTH(bb.terminationdate)))
ORDER BY aa.payitemsname,aa.receivabledate
);


-- v_no_receivables source
CREATE OR REPLACE
ALGORITHM = UNDEFINED VIEW v_no_receivables AS (
SELECT a.id AS districtid,a.districtName,a.districtOrder,a.districtCode,b.buildingName,b.buildingType,c.unitCode,g.username,g.idcard,g.homephone,c.buildingArea,d.chargecategory,
CASE c.estateState WHEN 0 THEN '未收楼' WHEN 1 THEN '已入住' WHEN 2 THEN '装修中' WHEN 3 THEN '出租' WHEN 4 THEN '离退' WHEN 5 THEN '自住' WHEN 6 THEN '空置' END AS estatestate,
h.bankname,h.bankaccount,h.accountname,d.payitemsname,d.receivabledate,
d.receivableamount,d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1,totalamount,amount3,amount36,amount6
FROM t_community_district a INNER JOIN t_community_building b ON a.id<>11 AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid
INNER JOIN t_community_property c ON b.id=c.buildingid
LEFT JOIN t_community_payment_account h ON c.paymentaccountid=h.id
INNER JOIN t_community_receivables d ON c.id=d.estateid AND d.receivableamount-d.receivedamount<>0 AND d.receivableDate<=LAST_DAY(CURDATE())
INNER JOIN t_community_pay_items e ON (e.paydate<>0 OR e.id IN(84))  AND d.payitemid=e.id
INNER JOIN t_community_member_property f ON c.id=f.propertyid AND f.iscurrentowner=1 AND f.iscurrentmember=1
INNER JOIN t_account g ON f.memberid=g.id
INNER JOIN
(SELECT aa.id,SUM(aa.amount1) AS totalAmount
FROM(SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1
FROM  t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e
WHERE a.id<>11 AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id
AND d.receivableamount-d.receivedamount<>0 AND d.receivableDate<=LAST_DAY(CURDATE())) aa GROUP BY aa.unitCode) a1 ON c.id=a1.id
LEFT JOIN(
SELECT aa.id,SUM(aa.amount1) AS amount3
FROM(SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1
FROM  t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e
WHERE a.id<>11 AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id  AND d.receivabledate>=DATE_ADD(LAST_DAY(CURDATE()), INTERVAL-3 MONTH)
AND d.receivableamount-d.receivedamount<>0 AND d.receivableDate<=LAST_DAY(CURDATE())) aa GROUP BY aa.unitCode) a2 ON a1.id=a2.id
LEFT JOIN(
SELECT aa.id,SUM(aa.amount1) AS amount36
FROM(SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1
FROM  t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e
WHERE a.id<>11 AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid AND(e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id AND d.receivabledate<DATE_ADD(LAST_DAY(CURDATE()), INTERVAL-3 MONTH) AND d.receivabledate>=DATE_ADD(LAST_DAY(CURDATE()), INTERVAL-6 MONTH)
AND d.receivableamount-d.receivedamount<>0 AND d.receivableDate<=LAST_DAY(CURDATE())) aa GROUP BY aa.unitCode) a3 ON a1.id=a3.id
LEFT JOIN(
SELECT aa.id,SUM(aa.amount1) AS amount6
FROM(SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1
FROM  t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e
WHERE a.id<>11 AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id AND d.receivabledate<DATE_ADD(LAST_DAY(CURDATE()), INTERVAL-6 MONTH)
AND d.receivableamount-d.receivedamount<>0 AND d.receivableDate<=LAST_DAY(CURDATE())) aa GROUP BY aa.unitCode) a4 ON a1.id=a4.id
);
    

-- v_yingshou source
CREATE OR REPLACE
ALGORITHM = UNDEFINED VIEW v_yingshou AS (
SELECT aa.receivablesno,aa.receivabledate,aa.districtname,aa.unitcode,bb.username,aa.receivablesid,aa.buildingname,aa.payitemsname,aa.receivableamount,aa.id,bb.memberid,aa.chargecategory
FROM(
SELECT c.id,d.receivablesno,DATE_FORMAT(d.receivabledate,'%Y-%m-%d') AS receivabledate,a.districtname,c.unitcode,IF(d.payitemsname IN('露天车位管理服务费','露天车位管理服务费2','露天车位管理服务费3','露天车位管理服务费4') ,'露天车位管理服务费',d.payitemsname) AS payitemsname,d.receivableamount,d.id AS receivablesid,b.buildingname,d.chargecategory
FROM t_community_district a
INNER JOIN t_community_building b ON a.id=b.districtid
INNER JOIN t_community_property c ON b.id=c.buildingid
INNER JOIN t_community_receivables d ON c.id=d.estateid
INNER JOIN t_community_pay_items e ON e.id=d.payItemId AND e.paydate=32 AND e.isreceivables=1) aa
INNER JOIN
(SELECT a.id,c.id AS memberid,c.username,a.estatestate,b.billingdate,IFNULL(b.terminationdate,'2099-12-31 00:00:00') AS terminationdate
FROM t_community_property a,t_community_member_property b,t_account c
WHERE a.id=b.propertyid AND b.memberid=c.id) bb
ON aa.id=bb.id AND aa.receivabledate>=bb.billingdate AND (aa.receivabledate<=bb.terminationdate OR (aa.receivabledate>bb.terminationdate AND YEAR(aa.receivabledate)=YEAR(bb.terminationdate) AND MONTH(aa.receivabledate)=MONTH(bb.terminationdate)))
);

-- v_shouju source
CREATE OR REPLACE
ALGORITHM = UNDEFINED VIEW v_shouju AS (
SELECT aa.code1,DATE_FORMAT(aa.receiptdate,'%Y-%m-%d') AS receiptdate,aa.agent,bb.username AS payername,aa.unitcode,aa.comment,
aa.paymentmethod,aa.address,aa.roomnumber,DATE_FORMAT(aa.receivabledate,'%Y-%m') AS paymentperiod,aa.payitemsname,aa.currentamount AS totalamount,aa.id AS estateid,bb.memberid
FROM(
SELECT f.receiptcode AS code1,f.receiptdate,d.receivabledate,f.payerName,a.address,
c.unitcode,f.paymentMethod,f.amount AS totalamount,d.payitemsname,e.currentAmount,f.agent,f.comment,c.roomnumber,c.id
FROM t_community_district a
INNER JOIN t_community_building b ON a.id<>11 AND a.id=b.districtid
INNER JOIN t_community_property c ON b.id=c.buildingId
INNER JOIN t_community_receivables d ON c.id=d.estateid
INNER JOIN t_community_receipt_receivables e ON d.id=e.receivablesid
INNER JOIN t_community_receipt f ON f.id=e.receiptid
UNION ALL
SELECT f.receiptcode,g.changedate,d.paymentPeriod,f.payerName,a.address,
c.unitcode,f.paymentMethod,0-g.changeAmount,d.payitemsname,0-g.changeAmount,f.agent,f.comment,c.roomnumber,c.id
FROM t_community_district a
INNER JOIN t_community_building b ON a.id<>11 AND a.id=b.districtid
INNER JOIN t_community_property c ON b.id=c.buildingId
INNER JOIN t_community_receivables d ON c.id=d.estateid
INNER JOIN t_community_receipt_receivables e ON d.id=e.receivablesid
INNER JOIN t_community_receipt f ON f.id=e.receiptid
INNER JOIN t_community_receivables_changes g ON f.id=g.receiptid
) aa INNER JOIN (
SELECT a.id,c.id AS memberid,c.username,a.estatestate,b.billingdate,IFNULL(b.terminationdate,'2099-12-31 00:00:00') AS terminationdate,d.propertycount
FROM t_community_property a,t_community_member_property b,t_account c,(SELECT propertyid,COUNT(1) AS propertycount FROM t_community_member_property WHERE membertype=0 AND parentpropertyid IS NULL GROUP BY propertyid) d
WHERE a.id=b.propertyid AND b.memberid=c.id AND a.id AND a.id=d.propertyid
) bb ON aa.id=bb.id
AND (aa.receiptdate>=bb.billingDate OR (DATEDIFF(aa.receiptdate,bb.billingDate)>=-30 AND DATEDIFF(aa.receiptdate,bb.billingDate)<=0 AND bb.propertycount=1))
AND (aa.receiptdate<=bb.terminationdate OR (aa.receiptdate>bb.terminationdate AND YEAR(aa.receiptdate)=YEAR(bb.terminationdate) AND MONTH(aa.receiptdate)=MONTH(bb.terminationdate)))
);


-- v_receipt source

CREATE OR REPLACE
ALGORITHM = UNDEFINED VIEW v_receipt AS (
SELECT a.id AS districtid,a.districtOrder,a.districtname,f.receiptcode,c.unitcode,b.buildingname,b.buildingOrder,b.id AS buildingId,
DATE_FORMAT(d.receivabledate,'%Y-%m-%d %H:%i:%s') AS receivableDate,
CONCAT(DATE_FORMAT(d.receivabledate,'%m'),'月') AS receivablemonth,DATE_FORMAT(f.receiptdate,'%Y-%m-%d %H:%i:%s') AS receiptDate,f.paymentmethod,
f.agent,d.payitemsname,e.currentamount,a.districtCode,d.chargecategory
FROM t_community_district a
INNER JOIN t_community_building b ON a.id =b.districtId
INNER JOIN t_community_property c ON b.id =c.buildingid
INNER JOIN t_community_receivables d ON c.id=d.estateid
INNER JOIN t_community_pay_items g ON d.payitemid=g.id 
INNER JOIN t_community_receipt_receivables e ON d.id=e.receivablesid AND e.currentamount>0
INNER JOIN t_community_receipt f ON e.receiptid=f.id
ORDER BY a.districtorder,b.buildingname,c.unitcode
);


-- v_everyday source
CREATE OR REPLACE
ALGORITHM = UNDEFINED VIEW v_everyday AS (
SELECT a.districtcode,a.districtname,b.buildingname,b.buildingorder,IF(c.unitcode='合作单位',CONCAT(c.unitcode,'-',d.payername),c.unitcode)AS unitcode,d.receiptcode,f.payitemsname,DATE_FORMAT(d.receiptdate,'%Y-%m-%d') AS receiptdate,
IF(d.paymentmethod='微信小程序支付','微信支付',d.paymentmethod) AS paymentmethod,SUM(e.currentAmount) AS amount
FROM t_community_district a
INNER JOIN t_community_building b ON a.id =b.districtid
INNER JOIN t_community_property c ON b.id = c.buildingid
INNER JOIN t_community_receipt d ON c.id=d.estateid AND d.receiptdate>=NOW() + INTERVAL -15 DAY AND d.paymentmethod IN('POS机','POS通','微信','微信小程序支付','微信支付','现金','支付宝','支付宝小程序支付')
INNER JOIN t_community_receipt_receivables e ON d.id=e.receiptId
INNER JOIN t_community_receivables f ON e.receivablesid=f.id
--GROUP BY d.receiptcode,f.payitemsname
GROUP BY d.id,f.payitemsname
);


-- v_shouju_djb source
CREATE OR REPLACE
ALGORITHM = UNDEFINED VIEW v_shouju_djb AS (
SELECT DATE_FORMAT(aa.receiptdate,'%Y-%m-%d') AS receiptdate,aa.receiptcode,aa.unitcode,aa.paymentmethod,aa.amountmethod,aa.amount,aa.payitemsname,SUM(aa.currentamount) AS receivedamount
FROM( 
  SELECT a.receiptdate,a.receiptcode,IF(d.unitcode REGEXP '^[0-9]',d.unitcode,a.payername) AS unitcode,a.paymentmethod,
  CASE a.paymentmethod
  WHEN 'POS通' THEN 'POS机'
  WHEN '微信' THEN 'POS机'
  WHEN '微信小程序支付' THEN '农村信用合作社'
  WHEN '微信支付' THEN '农村信用合作社'
  WHEN '建设银行' THEN '建行'
  WHEN '建行微信' THEN '建行'
  WHEN '三水农业银行' THEN '农行'
  ELSE a.paymentmethod END AS amountmethod,
  a.amount,
  CASE c.payitemsname
  WHEN 'IC卡工本费' THEN 'IC卡工本费\远程卡'
  WHEN '二区车库公共电费' THEN '公共开支分摊'
  WHEN '三区车库公共电费' THEN '公共开支分摊'
  WHEN '代收水费' THEN '水费'
  WHEN '代收电费' THEN '电费'
  WHEN '代收车位租金' THEN '出租车位租金'
  WHEN '儿童公园及精灵屋门票款' THEN '精灵屋门票'
  WHEN '公共水费分摊' THEN '公共开支分摊'
  WHEN '公共电费分摊' THEN '公共开支分摊'
  WHEN '公摊电费' THEN '公共开支分摊'
  WHEN '垃圾清运费' THEN '装修垃圾清运费'
  WHEN '宿舍物业管理费' THEN '物业管理费'
  WHEN '小区临时停车收费' THEN '临时停车收费'
  WHEN '废品款' THEN '往来款'
  WHEN '日常办公费' THEN '复印费'
  WHEN '景观大道路灯电费' THEN '公共开支分摊'
  WHEN '有偿清洁服务费' THEN '家政清洁费'
  WHEN '有偿绿化服务费' THEN '绿化服务费'
  WHEN '游泳池租金' THEN '租金'
  WHEN '空中花园公共电费' THEN '公共开支分摊'
  WHEN '花园及停车位管理费违约金' THEN '违约金'
  WHEN '证件工本费' THEN '工本费'
  WHEN '诉讼等相关费用' THEN '诉讼费'
  WHEN '车位管理费违约金' THEN '违约金'
  WHEN '车库公共水费分摊' THEN '公共开支分摊'
  WHEN '车库公共电费' THEN '公共开支分摊'
  WHEN '露天车位管理服务费2' THEN '露天车位管理服务费'
  WHEN '露天车位管理服务费2违约金' THEN '违约金'
  WHEN '露天车位管理服务费3' THEN '露天车位管理服务费'
  WHEN '露天车位管理服务费3违约金' THEN '违约金'
  WHEN '露天车位管理服务费4' THEN '露天车位管理服务费'
  WHEN '露天车位管理服务费4违约金' THEN '违约金'
  WHEN '露天车位管理服务费违约金' THEN '违约金'
  ELSE payitemsname END AS payitemsname,
  b.currentamount
  FROM t_community_receipt a
  INNER JOIN t_community_receipt_receivables b ON a.id=b.receiptid
  INNER JOIN t_community_receivables c ON b.receivablesid=c.id
  INNER JOIN t_community_property d ON d.id=a.estateid
  WHERE a.receiptdate>=DATE_ADD(NOW(), INTERVAL-3 MONTH)
  AND a.paymentmethod IN('POS机','POS通','微信','微信支付','现金','工商银行','农村信用合作社','微信小程序支付','建设银行','三水农业银行','建行微信','支付宝小程序支付','银行代收')
) aa GROUP BY aa.receiptcode,aa.payitemsname
ORDER BY aa.receiptdate,aa.receiptcode
);

