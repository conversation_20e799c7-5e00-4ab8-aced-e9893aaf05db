package com.foshan.form.education.response.educationMember;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="新增教育用户返回对象(AddEducationMemberRes)")
@JsonInclude(Include.NON_NULL)
public class AddEducationMemberRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 3353356567264373054L;

}
