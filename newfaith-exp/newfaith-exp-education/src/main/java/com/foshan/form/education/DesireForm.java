package com.foshan.form.education;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="愿望对象(DesireForm)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public  class DesireForm implements IForm {

    /**
     *
     */
    private static final long serialVersionUID = -1L;
    private Integer desireId;
    @ApiModelProperty(value = "愿望")
    private String desireContent;
    @ApiModelProperty(value = "审核状态 0-未审核 1-审核通过 2-审核不通过")
    private Integer checkState;
    @ApiModelProperty(value = "手机号")
    private String phone;
    @ApiModelProperty(value = "to手机号")
    private String toPhone;
    @ApiModelProperty(value = "智能卡号")
    private String smartCardId;
    @ApiModelProperty(value = "昵称")
    private String nickName;
    @ApiModelProperty(value = "手机尾号")
    private String lastPhoneNum;
    @ApiModelProperty(value = "接收人手机尾号")
    private String lastToPhoneNum;
    @ApiModelProperty(value = "接受者昵称")
    private String toNickName;



    @Override
    public int compareTo(Object o) {
        // TODO Auto-generated method stub
        return 0;
    }
}
