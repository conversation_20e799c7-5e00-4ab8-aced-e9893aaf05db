package com.foshan.util;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class CacheManagerUtil {
    private static Map<String, Map<String,Object>> caches = new ConcurrentHashMap<String, Map<String,Object>>();

    public static void putCache(String key, Map<String,Object> object) {
        caches.put(key, object);
    }
    
    public static Map<String,Object> getCacheByKey(String key) {
        if (isContains(key)) {
            return caches.get(key);
        }
        return null;
    }
        

    public static boolean isContains(String key) {
        return caches.containsKey(key);
    }

    
    public static void clearByKey(String key) {
        if (isContains(key)) {
            caches.remove(key);
        }
    }
}
