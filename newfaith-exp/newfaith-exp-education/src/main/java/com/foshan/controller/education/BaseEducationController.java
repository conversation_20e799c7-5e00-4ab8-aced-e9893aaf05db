package com.foshan.controller.education;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;

import com.foshan.controller.BaseController;
import com.foshan.service.education.*;
import com.foshan.util.ContextInfo;
import com.foshan.util.EducationContextInfo;

public class BaseEducationController extends BaseController {

//	@Resource(name = "educationEventService")
//	protected IEducationEventService educationEventService;
//
//	@Resource(name = "educationMemberService")
//	protected IEducationMemberService educationMemberService;

	@Resource(name = "desireService")
	protected IDesireService desireService;

	@Autowired
	protected ContextInfo contextInfo;
	@Autowired
	protected EducationContextInfo educationContextInfo;
}
