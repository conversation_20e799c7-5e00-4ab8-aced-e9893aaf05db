package com.foshan.entity.education;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AccountEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Entity
@DiscriminatorValue("E")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class EducationMemberEntity extends AccountEntity {
	/**
	 * 教育
	 */
	private static final long serialVersionUID = -6800153538413904774L;
	@Column(columnDefinition = "longtext comment '备注'")
	private String meno;
	@Column(columnDefinition = "int(10) comment '排序'")  
	private Integer orders;


	@ManyToMany(targetEntity = EducationRoleEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_member_role", joinColumns = @JoinColumn(name = "memberId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "roleId", referencedColumnName = "id"))
	private List<EducationRoleEntity> educationRoleList = new ArrayList<EducationRoleEntity>();
	

}
