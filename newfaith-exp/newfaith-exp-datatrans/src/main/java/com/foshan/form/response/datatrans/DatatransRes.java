package com.foshan.form.response.datatrans;

import java.util.HashMap;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import com.foshan.form.response.BasePageResponse;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class DatatransRes<T> extends BasePageResponse {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -5668286340030106667L;
	private HashMap<String, String> result = new HashMap<>();
	private T resultList;
	private Object o;

}
