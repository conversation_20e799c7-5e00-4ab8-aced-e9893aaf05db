package com.foshan.controller;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.request.datatrans.DataTransReq;
import com.foshan.form.response.datatrans.DatatransRes;

@RestController
@ResponseBody
public class LoupanController extends BaseTransController {
	/*
	 * 楼盘
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseLoupan", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseLoupan(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) testService.parseLoupan(req);
		return res;
	}

	/*
	 * 楼阁
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseLouge", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseLouge(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) testService.parseLouge(req);
		return res;
	}

	
	
	/*
	 * 楼梯
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseLouti", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseLouti(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) testService.parseLouti(req);
		return res;
	}
	
	/*
	 * 单元
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseDanyuan", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseDanyuan(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) testService.parseDanyuan(req);
		return res;
	}
	



}
