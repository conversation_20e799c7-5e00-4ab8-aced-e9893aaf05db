package com.foshan.controller;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.request.datatrans.DataTransReq;
import com.foshan.form.response.datatrans.DatatransRes;

@RestController
@ResponseBody
public class RepairController extends BaseTransController{
	/*
	 * 楼盘
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseRepair", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseRepair(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) repairService.parseRepair(req);
		return res;
	}
}
