package com.foshan.dao.generic.mssql;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.Criteria;
import org.hibernate.Hibernate;
import org.hibernate.LockOptions;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.CriteriaSpecification;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Disjunction;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Projection;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.hibernate.internal.CriteriaImpl;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.Query;
import org.hibernate.transform.ResultTransformer;
import org.hibernate.transform.Transformers;
import org.springframework.util.Assert;

import com.foshan.dao.generic.GenericDao;
import com.foshan.dao.generic.Page;
import com.foshan.dao.generic.PropertyFilter;
import com.foshan.dao.generic.ReflectionUtils;
import com.foshan.dao.generic.PropertyFilter.MatchType;
import com.foshan.util.SessionUtil;

import lombok.extern.slf4j.Slf4j;


//@Repository("hibernateDao")
@Slf4j
public class MssqlHibernateDao<T, PK extends Serializable> implements GenericDao<T, PK> {
    
	// 实体类对象
	protected Class<T> entityClass;
	
//	//方法一
//	@Autowired
//	private EntityManager entityManager;
//	
//	public Session getSession() {
//		Session session = entityManager.unwrap(Session.class)  ;
//		return session;
//	}

//	//方法二
//	@Autowired
//	private EntityManagerFactory entityManagerFactory;
//    
//	protected Session getSession() {
//        return entityManagerFactory.unwrap(SessionFactory.class).getCurrentSession();
//    }
	
	//方法三
	@Resource(name = "mssqlSessionFactory")
	private SessionFactory sessionFactory;
	
	protected Session getSession() {
		Session session = null;
		if(SessionUtil.session.containsKey("mssql")) {
			session=(Session)SessionUtil.session.get("mssql");
		}else {
			session= sessionFactory.openSession();
			SessionUtil.session.put("mssql", session);
		}
		
		return session;
	}
	
	
	public MssqlHibernateDao() {
		this.entityClass = ReflectionUtils.getSuperClassGenricType(getClass());
	}
	
	public void delete(T entity) {
		log.info("delete对象：" + entity.getClass());
		Assert.notNull(entity, "entity不能为空");
		getSession().delete(entity);
		log.info("delete entity: {}", entity.getClass().getName());
	}

	/**
	 * 
	 * Description : 获取实体对象
	 * 
	 * @param id 实体主键
	 * @return entity 实体对象
	 *
	 */
	public T get(Serializable id) {
		log.info("根据ID" + id + "获取"+entityClass+"实体对象");
		Assert.notNull(id, "id不能为空");
		log.info("get entity: {}", id);
		return (T) getSession().get(entityClass, id);
	}

	/**
	 * 
	 * Description : 保存实体对象
	 * 
	 * @param entity 实体对象
	 * @return
	 *
	 */
	public Serializable save(T entity) {
		Assert.notNull(entity, "entity不能为空");
		Serializable id = getSession().save(entity);
		log.info("save entity: {}", entity.getClass().getName());
		return id;
	}

	/**
	 * 
	 * Description : 保存或修改实体对象
	 * 
	 * @param entity 实体对象
	 * @return
	 *
	 */
	public void saveOrUpdate(T entity) {
		Assert.notNull(entity, "entity不能为空");
		getSession().saveOrUpdate(entity);
		log.info("saveOrUpdate entity: {}", entity.getClass().getName());
	}

	/**
	 * 
	 * Description : 修改实体对象
	 *
	 * @param entity 实体对象
	 * @return
	 *
	 */
	public void update(T entity) {
		Assert.notNull(entity, "entity不能为空");
		getSession().update(entity);
		log.info("update entity: {}", entity.getClass().getName());
	}

	/**
	 * 
	 * Description : 根据主键删除实体
	 * 
	 * @param id 主键
	 * @return
	 *
	 */
	public void deleteById(Serializable id) {
		T entity = this.get(id);
		getSession().delete(entity);
		log.info("delete entity: {}", id);
	}

	/**
	 * 
	 * Description : 根据SQL查询，取结果集合总数
	 * 
	 * @param sql    SQL查询语句
	 * @param values 查询值
	 * @return 结果集合总数
	 *
	 */
	@SuppressWarnings("rawtypes")
	public Integer countBySql(String sql, Object... values) {
		log.info("countBySql", sql);
		Query query = createSQLQuery(sql, values);
		return Integer.valueOf(query.uniqueResult().toString());
	}

	/**
	 * 根据属性查询，取结果集合中的第0个结果
	 * 
	 * @param pName
	 * @param pValue
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public T getUniqueByProperty(String pName, Object pValue) {
		log.info("根据属性查询，取结果集合中的第0个结果getUniqueByProperty", pName);
		String hql = "select model from " + entityClass.getName() + " as model where model." + pName + " = '" + pValue
				+ "'";
		List<T> objs = getSession().createQuery(hql).list();
		if (objs != null && objs.size() != 0) {
			return objs.get(0);
		} else {
			return null;
		}
	}

	/**
	 * 根据多个属性查询，取结果集合中的第0个结果
	 * 
	 * @param clazz
	 * @param strs
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public T getUniqueByNProperty(Object... strs) {
		if (strs != null && strs.length != 0 && 0 == strs.length % 2) {
			StringBuffer hql = new StringBuffer("select model from " + entityClass.getName() + " as model where ");
			for (int i = 0; i < strs.length; i += 2) {
				hql.append(" " + strs[i] + " = '" + strs[i + 1] + "' and");
			}
			int hqlLength = hql.length();
			hql.delete(hqlLength - 3, hqlLength);// 去掉最后的and
			List<T> objs = getSession().createQuery(hql.toString()).list();
			if (objs != null && objs.size() != 0) {
				return objs.get(0);
			} else {
				return null;
			}
		} else {
			return null;
		}
	}

	/**
	 * 根据HQL查询，取结果集合中的第0个结果
	 * 
	 * @param hql
	 * @return
	 */	
	public T getUniqueByHql(String hql, Object... values) {
		List<T> objs = this.query(hql, values);
		if (objs != null && objs.size() != 0) {
			return objs.get(0);
		} else {
			return null;
		}
	}

	/**
	 * 根据SQL查询，取结果集合中的第0个结果
	 * 
	 * @param sql
	 * @param clazz
	 * @return
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public T getUniqueBySql(String sql, Object... values) {
		NativeQuery query = createSQLQuery(sql, values).addEntity(entityClass);
		List<T> objs = query.list();
		if (objs != null && objs.size() != 0) {
			return objs.get(0);
		} else {
			return null;
		}
	}
	////////////////////// 查询单个完毕////////////////

	/**
	 * 查询所有
	 */
	@SuppressWarnings("unchecked")
	public List<T> getList() {
		String hql = "select model from " + entityClass.getName() + " as model ";
		List<T> list = getSession().createQuery(hql).list();
		return list;
	}

	/**
	 * 根据属性查询 全部
	 * 
	 * @param pName
	 * @param pValue
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<T> getListByProperty(String pName, Object pValue) {
		String hql = "select model from " + entityClass.getName() + " as model where model." + pName + " = '" + pValue
				+ "'";
		return getSession().createQuery(hql).list();
	}

	/**
	 * 根据属性和条件查询 全部
	 * 
	 * @param pName
	 * @param pValue
	 * @param condition
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<T> getListByProperty(String pName, Object pValue, String condition) {
		String hql = "select model from " + entityClass.getName() + " as model where model." + pName + " " + condition
				+ " '%" + pValue + "%'";
		List<T> list = getSession().createQuery(hql).list();
		return list;
	}

	/**
	 * 根据多个属性模糊查询
	 * 
	 * @param strs
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<T> getListByNProperty(Object... strs) {
		if (strs != null && strs.length != 0 && 0 == strs.length % 2) {
			StringBuffer hql = new StringBuffer("select model from " + entityClass.getName() + " as model where");
			for (int i = 0; i < strs.length; i += 2) {
				hql.append(" " + strs[i] + " = '" + strs[i + 1] + "' and");
			}
			int hqlLength = hql.length();
			hql.delete(hqlLength - 3, hqlLength);// 去掉最后的and
			List<T> objs = getSession().createQuery(hql.toString()).list();
			return objs;
		} else {
			return null;
		}
	}

	/**
	 * 根据HQL查询 全部
	 * 
	 * @param hql
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<T> getListByHql(String hql, Object... values) {
		return createQuery(hql, values).list();
	}

	/**
	 * 根据SQL查询全部
	 * 
	 * @param sql
	 * @param clazz
	 * @return
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public List<T> getListBySql(String sql, Object... values) {
		NativeQuery query = createSQLQuery(sql, values).addEntity(entityClass);
		return query.list();
	}

	@SuppressWarnings("unchecked")
	public <X> List<X> query(String hql, Object... values) {
		return createQuery(hql, values).list();
	}

	@SuppressWarnings("unchecked")
	public <X> List<X> query(String hql, Map<String, Object> values) {
		return createQuery(hql, values).list();
	}

	@SuppressWarnings("unchecked")
	public <X> X findUnique(String hql, Object... values) {
		return (X) createQuery(hql, values).uniqueResult();
	}

	@SuppressWarnings("unchecked")
	public <X> X findUnique(String hql, Map<String, Object> values) {
		return (X) createQuery(hql, values).uniqueResult();
	}

	@SuppressWarnings("unchecked")
	public T findUnique(final Criterion... criterions) {
		return (T) createCriteria(criterions).uniqueResult();
	}

	@SuppressWarnings("rawtypes")
	public Query createQuery(String queryString, Map<String, Object> values) {
		Assert.hasText(queryString, "queryString不能为空");
		Query query = getSession().createQuery(queryString);
		query.setCacheable(true);

		if (values != null) {
			query.setProperties(values);
		}

		return query;
	}

	@SuppressWarnings("rawtypes")
	public Query createQuery(String queryString, Object... values) {
		Assert.hasText(queryString, "queryString不能为空");
		Query query = getSession().createQuery(queryString);
		query.setCacheable(true);

		if (values != null) {
			for (int i = 1; i <= values.length; i++) {
				if (values[i-1] != null && StringUtils.isNotEmpty(values[i-1].toString()))
					query.setParameter(i, values[i-1]);
			}
		}

		return query;
	}

	@SuppressWarnings("rawtypes")
	public NativeQuery createSQLQuery(String queryString, Object... values) {
		Assert.hasText(queryString, "queryString不能为空");
		NativeQuery query = getSession().createSQLQuery(queryString);
		query.setCacheable(true);
		if (values != null) {
			for (int i = 0; i < values.length; i++) {
				if (values[i] != null && StringUtils.isNotEmpty(values[i].toString()))
					query.setParameter(i, values[i]);
			}
		}

		return query;
	}

	public Integer executeUpdate(final String hql, final Object... values) {

		return createQuery(hql, values).executeUpdate();
	}

	public Integer executeUpdate(final String hql, final Map<String, Object> values) {
		return createQuery(hql, values).executeUpdate();
	}

	public void initEntity(T entity) {
		Hibernate.initialize(entity);
	}

	public void initEntity(List<T> entityList) {
		for (T entity : entityList) {
			Hibernate.initialize(entity);
		}
	}

	/**
	 * 
	 * Description : 根据Criterion条件创建Criteria
	 * 
	 * @param criterions
	 * @return
	 * 
	 */
	@SuppressWarnings("deprecation")
	public Criteria createCriteria(final Criterion... criterions) {
		Criteria criteria = getSession().createCriteria(entityClass);
		for (Criterion c : criterions) {
			criteria.add(c);
		}
		return criteria;
	}

	@SuppressWarnings("rawtypes")
	public Integer countByHql(String hql, Object... values) {
		Query query = createQuery(hql, values);
		return Integer.valueOf(query.uniqueResult().toString());
	}

	@SuppressWarnings({ "unchecked", "rawtypes", "deprecation" })
	@Override
	public List<Map<String, Object>> queryListBysql(String queryString, Object... values) {
		Assert.hasText(queryString, "queryString不能为空");
		NativeQuery query = getSession().createSQLQuery(queryString);
		query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
		if (values != null) {
			for (int i = 0; i < values.length; i++) {
				if (values[i] != null && StringUtils.isNotEmpty(values[i].toString()))
					query.setParameter(i, values[i]);
			}
		}
		return query.list();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<T> getList(String ids) {

		if (ids == null || ids.length() == 0) {
			return getList();
		}

		String hql = "select model from " + entityClass.getName() + " as model where model.id in(" + ids + ")";

		List<T> list = getSession().createQuery(hql).list();

		return list;
	}
	
	/**
	 * 
	 * Description : HQL分页查询
	 * 
	 * @param Page
	 *            分页对象
	 * @param hql
	 *            HQL语句
	 * @param Object
	 *            参数值
	 * @return
	 * 
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Page<T> queryPage(Page<T> page, String hql, Object... values) {
		// TODO Auto-generated method stub
		Assert.notNull(page, "page不能为空");
		Query q = createQuery(hql, values);
		int totalCount = countHqlResult(hql, values);
		page.setTotalCount(totalCount);
		setPageParameter(q, page);
		List<T> result = q.list();
		page.setResultList(result);
		return page;
	}

	/**
	 * 
	 * Description : HQL分页查询
	 * 
	 * @param Page
	 *            分页对象
	 * @param hql
	 *            HQL语句
	 * @param values
	 *            map对象参数值
	 * @return
	 * 
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Page<T> queryPage(Page<T> page, String hql, Map<String, Object> values) {
		// TODO Auto-generated method stub
		Assert.notNull(page, "page不能为空");
		Query q = createQuery(hql, values);
		int totalCount = countHqlResult(hql, values);
		page.setTotalCount(totalCount);
		setPageParameter(q, page);
		List<T> result = q.list();
		page.setResultList(result);
		return page;
	}

	/**
	 * 
	 * Description : 按Criteria分页查询
	 *
	 * @param page
	 *            分页对象
	 * @param criterions
	 *            条件
	 * @return
	 *
	 */
	@SuppressWarnings("unchecked")
	public Page<T> queryPage(Page<T> page, Criterion... criterions) {
		// TODO Auto-generated method stub
		Assert.notNull(page, "page不能为空");
		Criteria c = createCriteria(criterions);
		int totalCount = countCriteriaResult(c);
		page.setTotalCount(totalCount);
		setPageParameter(c, page);
		List<T> result = c.list();
		page.setResultList(result);
		return page;
	}

	/**
	 * 
	 * Description : 按属性过滤条件列表分页查找对象
	 *
	 * @param page
	 *            分页对象
	 * @param filters
	 *            属性过滤条件
	 * @return
	 *
	 */
	public Page<T> queryPage(final Page<T> page, final List<PropertyFilter> filters) {
		Criterion[] criterions = buildPropertyFilterCriterions(filters);
		return queryPage(page, criterions);
	}

	/**
	 * 
	 * Description : 执行count查询获得本次Criteria查询所能获得的对象总数
	 *
	 * @param c
	 *            Criteria对象
	 * @return
	 *
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Integer countCriteriaResult(Criteria c) {
		// TODO Auto-generated method stub
		CriteriaImpl impl = (CriteriaImpl) c;
		// 先把Projection、ResultTransformer、OrderBy取出来,清空三者后再执行Count操作
		Projection projection = impl.getProjection();
		ResultTransformer transformer = impl.getResultTransformer();
		List<CriteriaImpl.OrderEntry> orderEntries = null;
		try {
			orderEntries = (List<CriteriaImpl.OrderEntry>) ReflectionUtils.getFieldValue(impl, "orderEntries");
			ReflectionUtils.setFieldValue(impl, "orderEntries", new ArrayList());
		} catch (Exception e) {
			log.error("不可能抛出的异常:{}", e.getMessage());
		}
		// 执行Count查询
		int totalCount = (Integer) c.setProjection(Projections.rowCount()).uniqueResult();
		// 将之前的Projection,ResultTransformer和OrderBy条件重新设回去
		c.setProjection(projection);
		if (projection == null) {
			c.setResultTransformer(CriteriaSpecification.ROOT_ENTITY);
		}
		if (transformer != null) {
			c.setResultTransformer(transformer);
		}
		try {
			ReflectionUtils.setFieldValue(impl, "orderEntries", orderEntries);
		} catch (Exception e) {
			log.error("不可能抛出的异常:{}", e.getMessage());
		}
		return totalCount;
	}

	/**
	 * 
	 * Description : 执行count查询获得本次Hql查询所能获得的对象总数
	 *
	 * @param hql
	 *            HQL语句
	 * @param values
	 *            查询值
	 * @return
	 *
	 */
	public Integer countHqlResult(String hql, Object... values) {
		String fromHql = hql;
		// select子句与order by子句会影响count查询,进行简单的排除.
		fromHql = "from " + StringUtils.substringAfter(fromHql, "from");
		fromHql = StringUtils.substringBefore(fromHql, "order by");
		String selectHql = "*";
		// 如果含有 distinct 描述符，进行简单处理
		if (StringUtils.indexOf(hql, "distinct") != -1) {
			selectHql = StringUtils.substringBetween(hql, "select", "from").trim();
		}
		String countHql = "select count(" + selectHql + ") " + fromHql;
		try {
			Long count = findUnique(countHql, values);
			return count.intValue();
		} catch (Exception e) {
			throw new RuntimeException("hql can't be auto count, hql is:" + countHql, e);
		}
	}

	/**
	 * 
	 * Description : 执行count查询获得本次Hql查询所能获得的对象总数
	 *
	 * @param hql
	 *            HQL语句
	 * @param values
	 *            map对象查询值
	 * @return
	 *
	 */
	public Integer countHqlResult(String hql, Map<String, Object> values) {
		String fromHql = hql;
		// select子句与order by子句会影响count查询,进行简单的排除.
		fromHql = "from " + StringUtils.substringAfter(fromHql, "from");
		fromHql = StringUtils.substringBefore(fromHql, "order by");
		String countHql = "select count(*) " + fromHql;
		try {
			Long count = findUnique(countHql, values);
			return count.intValue();
		} catch (Exception e) {
			throw new RuntimeException("hql can't be auto count, hql is:" + countHql, e);
		}
	}

	/**
	 * 
	 * Description : 设置分页参数到Query对象,辅助函数
	 * 
	 * @param q
	 * @param page
	 * @return
	 * 
	 */
	@SuppressWarnings({ "static-access", "rawtypes" })
	protected Query setPageParameter(final Query q, final Page<T> page) {
		// 如果每页显示的记录数 为-1，查询所有记录 ，不做分页
		if (page.getPageSize() != page.ALL_SIZE) {
			q.setFirstResult(page.getBeginCount());
			q.setMaxResults(page.getPageSize());
		}
		return q;
	}

	/**
	 * 
	 * Description : 设置分页参数到Criteria对象,辅助函数
	 * 
	 * @param c
	 * @param page
	 * @return
	 * 
	 */
	protected Criteria setPageParameter(final Criteria c, final Page<T> page) {
		c.setFirstResult(page.getCurrentPage() - 1);
		c.setMaxResults(page.getPageSize());
		// if (page.isOrderBySetted()) {
		// String[] orderByArray = StringUtils.split(page.getOrderBy(), ',');
		// String[] orderArray = StringUtils.split(page.getOrder(), ',');
		//
		// Assert.isTrue(orderByArray.length == orderArray.length,
		// "分页多重排序参数中,排序字段与排序方向的个数不相等");
		//
		// for (int i = 0; i < orderByArray.length; i++) {
		// if (Page.ASC.equals(orderArray[i])) {
		// c.addOrder(Order.asc(orderByArray[i]));
		// } else {
		// c.addOrder(Order.desc(orderByArray[i]));
		// }
		// }
		// }
		return c;
	}

	/**
	 * 
	 * Description : 按属性条件列表创建Criterion数组,辅助函数
	 * 
	 * @param filters
	 * @return
	 * 
	 */
	protected Criterion[] buildPropertyFilterCriterions(final List<PropertyFilter> filters) {
		List<Criterion> criterionList = new ArrayList<Criterion>();
		for (PropertyFilter filter : filters) {
			if (!filter.isMultiProperty()) { // 只有一个属性需要比较的情况.
				Criterion criterion = buildPropertyFilterCriterion(filter.getPropertyName(), filter.getPropertyValue(),
						filter.getMatchType());
				criterionList.add(criterion);
			} else {// 包含多个属性需要比较的情况,进行or处理.
				Disjunction disjunction = Restrictions.disjunction();
				for (String param : filter.getPropertyNames()) {
					Criterion criterion = buildPropertyFilterCriterion(param, filter.getPropertyValue(),
							filter.getMatchType());
					disjunction.add(criterion);
				}
				criterionList.add(disjunction);
			}
		}
		return criterionList.toArray(new Criterion[criterionList.size()]);
	}

	/**
	 * 
	 * Description : 按属性条件参数创建Criterion,辅助函数
	 * 
	 * @param propertyName
	 * @param propertyValue
	 * @param matchType
	 * @return
	 * 
	 */
	protected Criterion buildPropertyFilterCriterion(final String propertyName, final Object propertyValue,
			final MatchType matchType) {
		Assert.hasText(propertyName, "propertyName不能为空");
		Criterion criterion = null;
		try {
			// 根据MatchType构造criterion
			if (MatchType.EQ.equals(matchType)) {
				criterion = Restrictions.eq(propertyName, propertyValue);
			} else if (MatchType.LIKE.equals(matchType)) {
				criterion = Restrictions.like(propertyName, (String) propertyValue, MatchMode.ANYWHERE);
			} else if (MatchType.LE.equals(matchType)) {
				criterion = Restrictions.le(propertyName, propertyValue);
			} else if (MatchType.LT.equals(matchType)) {
				criterion = Restrictions.lt(propertyName, propertyValue);
			} else if (MatchType.GE.equals(matchType)) {
				criterion = Restrictions.ge(propertyName, propertyValue);
			} else if (MatchType.GT.equals(matchType)) {
				criterion = Restrictions.gt(propertyName, propertyValue);
			}
		} catch (Exception e) {
			throw ReflectionUtils.convertReflectionExceptionToUnchecked(e);
		}
		return criterion;
	}


	@Override
	public T get(Serializable id, LockOptions lockMode) {
		// TODO Auto-generated method stub
		return null;
	}
}
