package com.foshan.service;

import com.foshan.form.request.datatrans.DataTransReq;
import com.foshan.form.response.IResponse;

public interface FeeService {
	public IResponse parseShoufeiSheding(DataTransReq req);
	public IResponse parseOtherPayItem(DataTransReq req);
	public IResponse parseShoufeiFanwei(DataTransReq req);
	public IResponse parseGongyongFentanFanwei(DataTransReq req);
	public IResponse parseYingshoukuan(DataTransReq req);
	public IResponse parseYingshoukuanJianjia(DataTransReq req);
	public IResponse parseShouju(DataTransReq req);
	public IResponse parseShishoukuan(DataTransReq req);
	public IResponse parseOtherFee(DataTransReq req);
	public IResponse parseOtherFeeDeposit(DataTransReq req);
	public IResponse parseZongbiaoFentan(DataTransReq req);
	public IResponse parseDanyuanZongbiaoFentan(DataTransReq req);
	public IResponse parseYinhangHuazhangDanyuanShaixuan(DataTransReq req);
}
