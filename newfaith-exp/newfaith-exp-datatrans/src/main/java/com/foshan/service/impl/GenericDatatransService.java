package com.foshan.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.jdbc.Work;
import org.springframework.util.ResourceUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.dao.mssql.I业主Dao;
import com.foshan.dao.mssql.I住户成员Dao;
import com.foshan.dao.mssql.I住户管理出入证Dao;
import com.foshan.dao.mssql.I住户管理报修登记Dao;
import com.foshan.dao.mssql.I公用分摊范围Dao;
import com.foshan.dao.mssql.I分摊一级总表范围Dao;
import com.foshan.dao.mssql.I分摊分摊用量临时Dao;
import com.foshan.dao.mssql.I分摊单元分摊用量临时Dao;
import com.foshan.dao.mssql.I分摊设置自定义分摊公式Dao;
import com.foshan.dao.mssql.I单元Dao;
import com.foshan.dao.mssql.I实收款Dao;
import com.foshan.dao.mssql.I应收款Dao;
import com.foshan.dao.mssql.I应收款减加Dao;
import com.foshan.dao.mssql.I应预收款收据Dao;
import com.foshan.dao.mssql.I总表Dao;
import com.foshan.dao.mssql.I总表抄表Dao;
import com.foshan.dao.mssql.I户主Dao;
import com.foshan.dao.mssql.I收费范围Dao;
import com.foshan.dao.mssql.I收费设定Dao;
import com.foshan.dao.mssql.I收费项目其他收费Dao;
import com.foshan.dao.mssql.I收费项目经营项目Dao;
import com.foshan.dao.mssql.I楼梯Dao;
import com.foshan.dao.mssql.I楼盘Dao;
import com.foshan.dao.mssql.I楼阁Dao;
import com.foshan.dao.mssql.I用户表Dao;
import com.foshan.dao.mssql.I用户表抄表Dao;
import com.foshan.dao.mssql.I表属性Dao;
import com.foshan.dao.mssql.I财务其他收费Dao;
import com.foshan.dao.mssql.I车场管理停车证Dao;
import com.foshan.dao.mssql.I车场管理车辆Dao;
import com.foshan.dao.mssql.I银行划帐IdDao;
import com.foshan.dao.mssql.I银行划帐导入导出Dao;
import com.foshan.dao.mssql.I银行划账单元筛选Dao;
import com.foshan.entity.community.CommunityBuildingEntity;
import com.foshan.entity.community.CommunityDistrictEntity;
import com.foshan.entity.community.CommunityEntity;
import com.foshan.entity.community.CommunityMeterAttributesEntity;
import com.foshan.entity.community.CommunityMeterEntity;
import com.foshan.entity.community.CommunityPayItemsEntity;
import com.foshan.entity.community.CommunityReceiptEntity;
import com.foshan.entity.community.CommunityReceivablesEntity;
import com.foshan.entity.community.context.CommunityContext;
import com.foshan.entity.mssql.住户成员Entity;
import com.foshan.entity.mssql.住户管理报修登记Entity;
import com.foshan.entity.mssql.公用分摊范围Entity;
import com.foshan.entity.mssql.分摊分摊用量临时Entity;
import com.foshan.entity.mssql.分摊单元分摊用量临时Entity;
import com.foshan.entity.mssql.分摊设置自定义分摊公式Entity;
import com.foshan.entity.mssql.单元Entity;
import com.foshan.entity.mssql.实收款Entity;
import com.foshan.entity.mssql.应收款Entity;
import com.foshan.entity.mssql.应收款减加Entity;
import com.foshan.entity.mssql.应预收款收据Entity;
import com.foshan.entity.mssql.总表Entity;
import com.foshan.entity.mssql.总表抄表Entity;
import com.foshan.entity.mssql.户主Entity;
import com.foshan.entity.mssql.收费范围Entity;
import com.foshan.entity.mssql.收费设定Entity;
import com.foshan.entity.mssql.收费项目其他收费Entity;
import com.foshan.entity.mssql.楼盘Entity;
import com.foshan.entity.mssql.楼阁Entity;
import com.foshan.entity.mssql.用户表Entity;
import com.foshan.entity.mssql.用户表抄表Entity;
import com.foshan.entity.mssql.表属性Entity;
import com.foshan.entity.mssql.财务其他收费Entity;
import com.foshan.service.community.impl.GenericCommunityService;
import com.foshan.util.DateUtil;
import com.foshan.util.SpringHandler;
import com.foshan.util.community.CommunityCache;
import com.foshan.util.datatrans.Datatrans;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GenericDatatransService extends GenericCommunityService {

	@Resource(name = "表属性Dao")
	public I表属性Dao 表属性Dao;
	@Resource(name = "财务其他收费Dao")
	public I财务其他收费Dao 财务其他收费Dao;
	@Resource(name = "车场管理车辆Dao")
	public I车场管理车辆Dao 车场管理车辆Dao;
	@Resource(name = "车场管理停车证Dao")
	public I车场管理停车证Dao 车场管理停车证Dao;
	@Resource(name = "单元Dao")
	public I单元Dao 单元Dao;
	@Resource(name = "分摊单元分摊用量临时Dao")
	public I分摊单元分摊用量临时Dao 分摊单元分摊用量临时Dao;
	@Resource(name = "分摊分摊用量临时Dao")
	public I分摊分摊用量临时Dao 分摊分摊用量临时Dao;
	@Resource(name = "分摊设置自定义分摊公式Dao")
	public I分摊设置自定义分摊公式Dao 分摊设置自定义分摊公式Dao;
	@Resource(name = "分摊一级总表范围Dao")
	public I分摊一级总表范围Dao 分摊一级总表范围Dao;
	@Resource(name = "公用分摊范围Dao")
	public I公用分摊范围Dao 公用分摊范围Dao;
	@Resource(name = "户主Dao")
	public I户主Dao 户主Dao;
	@Resource(name = "楼阁Dao")
	public I楼阁Dao 楼阁Dao;
	@Resource(name = "楼盘Dao")
	public I楼盘Dao 楼盘Dao;
	@Resource(name = "楼梯Dao")
	public I楼梯Dao 楼梯Dao;
	@Resource(name = "实收款Dao")
	public I实收款Dao 实收款Dao;
	@Resource(name = "收费范围Dao")
	public I收费范围Dao 收费范围Dao;
	@Resource(name = "收费设定Dao")
	public I收费设定Dao 收费设定Dao;
	@Resource(name = "收费项目经营项目Dao")
	public I收费项目经营项目Dao 收费项目经营项目Dao;
	@Resource(name = "收费项目其他收费Dao")
	public I收费项目其他收费Dao 收费项目其他收费Dao;
	@Resource(name = "业主Dao")
	public I业主Dao 业主Dao;
	@Resource(name = "银行划帐IdDao")
	public I银行划帐IdDao 银行划帐IdDao;
	@Resource(name = "银行划帐导入导出Dao")
	public I银行划帐导入导出Dao 银行划帐导入导出Dao;
	@Resource(name = "银行划账单元筛选Dao")
	public I银行划账单元筛选Dao 银行划账单元筛选Dao;
	@Resource(name = "应收款Dao")
	public I应收款Dao 应收款Dao;
	@Resource(name = "应收款减加Dao")
	public I应收款减加Dao 应收款减加Dao;
	@Resource(name = "应预收款收据Dao")
	public I应预收款收据Dao 应预收款收据Dao;
	@Resource(name = "用户表Dao")
	public I用户表Dao 用户表Dao;
	@Resource(name = "用户表抄表Dao")
	public I用户表抄表Dao 用户表抄表Dao;
	@Resource(name = "住户成员Dao")
	public I住户成员Dao 住户成员Dao;
	@Resource(name = "住户管理报修登记Dao")
	public I住户管理报修登记Dao 住户管理报修登记Dao;
	@Resource(name = "住户管理出入证Dao")
	public I住户管理出入证Dao 住户管理出入证Dao;
	@Resource(name = "总表Dao")
	public I总表Dao 总表Dao;
	@Resource(name = "总表抄表Dao")
	public I总表抄表Dao 总表抄表Dao;

	// 楼盘导入

	protected HashMap<String, String> transLoupan(List<楼盘Entity> 楼盘List) {
		HashMap<String, String> result = new HashMap<>();

		CommunityEntity community = communityDao.getUniqueByHql("from CommunityEntity where communityName='御江南'");

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {

				StringBuilder sql = new StringBuilder(
						"insert into t_community_district(districtName,address,oldId,districtCode,state,communityId) values(?,?,?,?,?,?)");

				PreparedStatement stmt = conn.prepareStatement(sql.toString());
				conn.setAutoCommit(false);
				Integer transCount = 0;
				for (楼盘Entity o : 楼盘List) {
					stmt.setString(1, o.get楼盘名称());
					stmt.setString(2, o.get地址());
					stmt.setString(3, o.get楼盘ID());
					stmt.setString(4, o.get楼盘编号() + "");
					stmt.setInt(5, 1);
					stmt.setInt(6, community.getId());
					stmt.addBatch();
					transCount++;
					if (transCount % 100 == 0) {
						stmt.executeBatch();
						conn.commit();
					}
				}
				stmt.executeBatch();
				conn.commit();
				result.put("割接结果", "共有楼盘" + 楼盘List.size() + "个，本次割接" + transCount + "个");

			}
		});
		session.close();
		return result;
	}

	// 楼阁导入

	protected HashMap<String, String> transLouge(List<CommunityDistrictEntity> disList) {
		HashMap<String, String> result = new HashMap<>();

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {

			@Override
			public void execute(Connection conn) throws SQLException {

				StringBuilder sql = new StringBuilder(
						"insert into t_community_building(districtId,buildingName,buildingCode,buildingType,oldId) values(?,?,?,?,?)");

				PreparedStatement stmt = conn.prepareStatement(sql.toString());
				conn.setAutoCommit(false);

				for (CommunityDistrictEntity o : disList) {
					if (StringUtils.isNotEmpty(o.getOldId())) {
						Integer transLougeCount = 0;
						List<楼阁Entity> 楼阁List = 楼阁Dao.getListByHql("from 楼阁Entity where 楼盘Id='" + o.getOldId() + "'");
						for (楼阁Entity b : 楼阁List) {
							stmt.setInt(1, o.getId());
							stmt.setString(2, b.get楼阁名称());
							stmt.setString(3, b.get楼阁编号() + "");
							String temp = b.get功能();
							stmt.setInt(4,
									StringUtils.isNotEmpty(temp)
											? (temp.equals("住宅") ? 0
													: (temp.equals("别墅") ? 1
															: (temp.equals("商铺") ? 2
																	: (temp.equals("停车场") ? 3
																			: (temp.equals("商业")
																					|| temp.equals("合同终止/离退")) ? 2
																							: 4))))
											: b.get楼阁名称().equals("其它收费") || b.get楼阁名称().equals("公共区域") ? 4 : 0);
							stmt.setString(5, b.get楼阁ID());
							;
							stmt.addBatch();
							transLougeCount++;
							if (transLougeCount % 100 == 0) {
								stmt.executeBatch();
								conn.commit();
							}
						}
						stmt.executeBatch();
						conn.commit();
						result.put(o.getDistrictName() + "下楼阁割接结果：",
								"共有楼阁" + 楼阁List.size() + "栋，本次割接" + transLougeCount + "栋");
					}
				}
			}

		});
		session.close();
		return result;
	}

	// 楼梯导入
	protected HashMap<String, String> transLouti(List<Object[]> 楼梯List) {
		HashMap<String, String> result = new HashMap<>();

		getBuildingIdList();
		if (Datatrans.buildingIdList.size() > 0) {
			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
			Session session = sessionFactory.openSession();

			session.doWork(new Work() {

				@Override
				public void execute(Connection conn) throws SQLException {

					StringBuilder sql = new StringBuilder(
							"insert into t_community_property(buildingId,OldId,UnitCode,RoomNumber,BuildingArea,Orientation,Floor,UsableArea,ChargingArea,AdditionalArea,Decoration,Comment,OldData,state,estateType,propertyType,reservedField) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

					PreparedStatement stmt = conn.prepareStatement(sql.toString());
					conn.setAutoCommit(false);
					Integer transCount = 0;
					for (Object[] o : 楼梯List) {
						if (Datatrans.buildingIdList.containsKey(o[1].toString())) {

							stmt.setInt(1, Datatrans.buildingIdList.get(o[1].toString()));
							stmt.setString(2, o[0].toString());
							stmt.setString(3, null != o[3] ? o[3].toString() : "");
							stmt.setString(4, null != o[5] ? o[5].toString() : "");
							stmt.setBigDecimal(5, null != o[6] ? new BigDecimal(o[6].toString()) : new BigDecimal("0"));
							stmt.setString(6, null != o[9] ? o[9].toString() : "");
							stmt.setInt(7, Integer.valueOf(o[4].toString()));
							stmt.setBigDecimal(8, null != o[7] ? new BigDecimal(o[7].toString()) : new BigDecimal("0"));
							stmt.setBigDecimal(9, null != o[8] ? new BigDecimal(o[8].toString()) : new BigDecimal("0"));
							stmt.setBigDecimal(10,
									null != o[11] ? new BigDecimal(o[11].toString()) : new BigDecimal("0"));
							stmt.setString(11, "");
							stmt.setString(12, "楼梯");
							stmt.setString(13,
									(null != o[2] ? o[2].toString() : "") + "|"
											+ (null != o[12] ? o[12].toString() : "") + "|"
											+ (null != o[13] ? o[13].toString() : ""));
							stmt.setInt(14, 1);
							stmt.setString(15, null != o[10] ? o[10].toString() : "");
							stmt.setString(16, "ESTATE");
							stmt.setString(17,
									"{\"propertyParkingArea\":\"0\",\"propertyParkingNum\":\"0\",\"defenceParkingArea\":\"0\",\"defenceParkingNum\":\"0\",\"waterMeterBase\":\"\",\"electricMeterBase\":\"\",\"accountCustomerId\":\"\",\"accountContractNo\":\"\",\"accountContractTime\":\"\",\"accountContractComment\":\"\"}");
							stmt.addBatch();
							transCount++;
							if (transCount % 100 == 0) {
								stmt.executeBatch();
								conn.commit();
							}
						} else {
							log.info("割接异常====>>>楼梯导入：楼阁Id(" + o[1].toString() + ")割接不成功！！！");
						}
						stmt.executeBatch();
						conn.commit();
						result.put("割接结果", "共有楼梯" + 楼梯List.size() + "个，本次割接" + transCount + "个");

					}
				}

			});
			session.close();
		} else {
			log.info("buildingIdList不能为空！！！");
			result.put("割接失败", "buildingIdList不能为空！！！");
		}
		return result;
	}


	// 单元导入
	@SuppressWarnings("resource")
	protected HashMap<String, String> transDanyuan(List<CommunityBuildingEntity> buildingList) {
		HashMap<String, String> result = new HashMap<>();

		Map<String, Map<String, String>> temp = new HashMap<>();

		try {
			File file = ResourceUtils.getFile("classpath:全国平台扣费协议********.xls");
			InputStream inputStream = new FileInputStream(file);

			Workbook workBook = new HSSFWorkbook(inputStream);
			Sheet sheet = workBook.getSheetAt(workBook.getActiveSheetIndex());

			for (int i = 1; i <= sheet.getLastRowNum(); i++) {

				Row o = sheet.getRow(i);
				try {
					Map<String, String> tempInfo = new HashMap<>();
					tempInfo.put("accountCustomerId", o.getCell(2).getStringCellValue());
					tempInfo.put("accountContractNo", o.getCell(6).getStringCellValue());
					tempInfo.put("accountContractTime",
							o.getCell(7).getStringCellValue().substring(0, 4) + "-"
									+ o.getCell(7).getStringCellValue().substring(4, 6) + "-"
									+ o.getCell(7).getStringCellValue().substring(6));
					tempInfo.put("accountContractComment", o.getCell(8).getStringCellValue());
					tempInfo.put("propertyParkingArea", "0");
					tempInfo.put("propertyParkingNum", "0");
					tempInfo.put("defenceParkingArea", "0");
					tempInfo.put("defenceParkingNum", "0");
					tempInfo.put("waterMeterBase", "");
					tempInfo.put("electricMeterBase", "");
					temp.put(o.getCell(0).getStringCellValue(), tempInfo);
				} catch (IllegalStateException e) {
					System.out.println("=============>" + o.getCell(2).getStringCellValue());
				}
			}
		} catch (Exception ex) {
			System.out.println("=============>");
			ex.printStackTrace();
		}

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {

			@Override
			public void execute(Connection conn) throws SQLException {

				StringBuilder sql = new StringBuilder(
						"insert into t_community_property(buildingId,OldId,UnitCode,RoomNumber,BuildingArea,Orientation,Floor,UsableArea,ChargingArea,AdditionalArea,Decoration,Comment,OldData,state,estateType,specialFeeFlag,propertyType,estateState,reservedField) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

				PreparedStatement stmt = conn.prepareStatement(sql.toString());
				conn.setAutoCommit(false);
				Integer transCount = 0;
				for (CommunityBuildingEntity o : buildingList) {
					if (StringUtils.isNotEmpty(o.getOldId())) {
						List<单元Entity> 单元List = 单元Dao.getListByHql("from 单元Entity where 楼阁id='" + o.getOldId()
								+ "' and 单元id not in('E90DDA1D-AF41-495A-BEEE-419BFAAA9742')");
						Integer transDanyuanCount = 0;
						for (单元Entity b : 单元List) {
							stmt.setInt(1, o.getId());
							stmt.setString(2, b.get单元ID());
							stmt.setString(3, b.get单元编号());
							stmt.setString(4, b.get房号());
							stmt.setBigDecimal(5, b.get建筑面积());
							stmt.setString(6, b.get朝向());
							stmt.setInt(7, b.get楼层());
							stmt.setBigDecimal(8, b.get使用面积());
							stmt.setBigDecimal(9, b.get收费面积());
							stmt.setBigDecimal(10, null != b.get附加面积() ? b.get附加面积() : new BigDecimal("0"));
							stmt.setString(11, b.get装修标准());
							stmt.setString(12, b.get备注());
							stmt.setString(13, "");
							stmt.setInt(14, 1);
							if (o.getOldId().equals("778F9EDE-411D-4466-ADC1-1991BC13E714")
									|| o.getOldId().equals("8B87B91A-516C-411B-9627-913564E9D042")) {
								stmt.setString(15, "人防车位");
							} else if (o.getOldId().equals("6A3F9978-1128-4789-85C8-DC003D78C486")
									|| o.getOldId().equals("2F5A05EC-BA00-45EB-8C20-A39B54150057")
									|| o.getOldId().equals("9C35A38E-2E4A-4A2D-89D7-2018937A56C6")
									|| o.getOldId().equals("9DB42EAD-FD7D-4B24-A454-E9BEC46E8AED")
									|| o.getOldId().equals("B9638BD1-89D1-45D1-969B-F3F936056808")) {
								stmt.setString(15, "产权车位");
							} else {
								stmt.setString(15, b.get功能());
							}

							if (b.get单元ID().equals("DE58253A-AD7A-4422-A5A6-0F192E5F715E")) {
								stmt.setInt(16, 4);
							} else if (b.get单元ID().equals("CD1A80BB-9A0A-4AAB-80DA-488996203CCB")
									|| b.get单元ID().equals("915EE554-5CF6-4432-8BDE-93ECC1BA2A89")
									|| b.get单元ID().equals("335AE8FB-849A-4372-AA42-E2B603EE410A")
									|| b.get单元ID().equals("1F24F874-9FCC-4EB0-899D-F4D348C06218")
									|| b.get单元ID().equals("A04F4ED6-94E5-4EBD-8BFD-A6E736642B29")
									|| b.get单元ID().equals("D2F52A54-BB11-4E2B-ADB2-93DA6296956D")
									|| b.get单元ID().equals("2C000FEA-F4DA-465B-88D3-C6C00DB47DA0")
									|| b.get单元ID().equals("6B6F8F1C-78B9-405F-B708-42E1CF042C88")) {
								stmt.setInt(16, 3);
							} else {
								stmt.setInt(16, 0);
							}

							stmt.setString(17, "ESTATE");
							if (o.getBuildingType().equals(0) || o.getBuildingType().equals(1)) {
								stmt.setInt(18, 6);
							} else {
								stmt.setNull(18, Types.INTEGER);
							}

							if (temp.containsKey(b.get单元编号())) {
								try {
									stmt.setString(19, mapper.writeValueAsString(temp.get(b.get单元编号())));
								} catch (JsonProcessingException | SQLException e) {
									// TODO Auto-generated catch block
									e.printStackTrace();
								}
							} else {
								stmt.setString(19,
										"{\"propertyParkingArea\":\"0\",\"propertyParkingNum\":\"0\",\"defenceParkingArea\":\"0\",\"defenceParkingNum\":\"0\",\"waterMeterBase\":\"\",\"electricMeterBase\":\"\",\"accountCustomerId\":\"\",\"accountContractNo\":\"\",\"accountContractTime\":\"\",\"accountContractComment\":\"\"}");
							}
							stmt.addBatch();
							transDanyuanCount++;
							transCount++;
							if (transCount % 400 == 0) {
								stmt.executeBatch();
								conn.commit();
							}

						}
						stmt.executeBatch();
						conn.commit();
						result.put(o.getBuildingName() + "下单元割接结果：",
								"共有单元" + 单元List.size() + "套，本次割接" + transDanyuanCount + "套");
					}
				}
			}

		});
		session.close();
		return result;
	}

	/*
	 * 导入表属性
	 */

	protected HashMap<String, String> transBiaoshuxing(List<表属性Entity> 表属性List) {
		HashMap<String, String> result = new HashMap<>();

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {

				StringBuilder sql = new StringBuilder(
						"insert into t_community_meter_attributes(attributeName,category,comment,measureUnit,oldId,ranges,rate,unitPrice) values(?,?,?,?,?,?,?,?)");

				PreparedStatement stmt = conn.prepareStatement(sql.toString());
				conn.setAutoCommit(false);
				Integer transCount = 0;
				for (表属性Entity o : 表属性List) {
					stmt.setString(1, o.get编号());
					String category = o.get类别();
					stmt.setInt(2, category.equals("电表") ? 1
							: category.equals("水表") ? 2 : category.equals("临时表") ? 3 : category.equals("代收水表") ? 4 : 5);
					stmt.setString(3, o.get备注());
					stmt.setString(4, o.get单位());
					stmt.setString(5, o.get属性ID());
					stmt.setInt(6, o.get量程());
					stmt.setBigDecimal(7, o.get倍率());
					stmt.setBigDecimal(8, o.get单价().setScale(8, BigDecimal.ROUND_HALF_UP));
					stmt.addBatch();
					transCount++;
					if (transCount % 100 == 0) {
						stmt.executeBatch();
						conn.commit();
					}
				}
				stmt.executeBatch();
				conn.commit();
				result.put("割接结果", "共有表属性" + 表属性List.size() + "个，本次割接" + transCount + "个");

			}
		});
		session.close();
		return result;
	}

	/*
	 * 导入自定义分摊公式
	 */
	protected HashMap<String, String> transZidingyiGongshi(List<分摊设置自定义分摊公式Entity> 公式List) {
		HashMap<String, String> result = new HashMap<>();
		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {

				conn.setAutoCommit(false);
				StringBuilder sql_1 = new StringBuilder(
						"insert into t_community_formula_templete(state,templeteInfo,templeteName,dynamicParameter,id) values(?,?,?,?,?)");
				PreparedStatement stmt2 = conn.prepareStatement(sql_1.toString());
				stmt2.setInt(1, 1);
				stmt2.setString(2, "(总表用量+偏移量)/总面积*收费面积");
				stmt2.setString(3, "按用户使用面积分摊");
				Map<String, BigDecimal> para = new HashMap<>();
				para.put("偏移量", BigDecimal.ZERO);
				para.put("总面积", BigDecimal.ZERO);
				try {
					stmt2.setString(4, mapper.writeValueAsString(para));
				} catch (JsonProcessingException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				stmt2.setInt(5, 1);
				stmt2.addBatch();
				stmt2.setInt(1, 1);
				stmt2.setString(2, "(总表用量+偏移量)/分摊数量");
				stmt2.setString(3, "按数量分摊");
				Map<String, BigDecimal> para1 = new HashMap<>();
				para1.put("偏移量", BigDecimal.ZERO);
				para1.put("分摊数量", BigDecimal.ZERO);
				try {
					stmt2.setString(4, mapper.writeValueAsString(para1));
				} catch (JsonProcessingException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				stmt2.setInt(5, 2);
				stmt2.addBatch();

				stmt2.executeBatch();
				conn.commit();

				StringBuilder sql = new StringBuilder(
						"insert into t_community_meter_formula(formulaName,dynamicParameter,oldData,templeteId) values(?,?,?,?)");

				PreparedStatement stmt = conn.prepareStatement(sql.toString());
				Integer transCount = 0;
				for (分摊设置自定义分摊公式Entity o : 公式List) {
					if (!o.get公式名().equals("车库电费")) {
						stmt.setString(1, o.get公式名());
						try {
							String opNum = "";
							Pattern pattern = Pattern.compile("/[1-9]\\d*\\.?\\d*");
							Matcher matcher = pattern.matcher(o.get公式描述());
							while (matcher.find()) {
								opNum = matcher.group();
								break;
							}
							if (o.get公式描述().length() > 20) {
								para.put("总面积", new BigDecimal(opNum.substring(1)));
								stmt.setString(2, mapper.writeValueAsString(para));
							} else {
								para1.put("分摊数量", new BigDecimal(opNum.substring(1)));
								stmt.setString(2, mapper.writeValueAsString(para1));
							}
						} catch (JsonProcessingException | SQLException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
						stmt.setString(3, o.get公式描述());
						stmt.setInt(4, o.get公式描述().length() > 20 ? 1 : 2);
						stmt.addBatch();
						transCount++;
						if (transCount % 100 == 0) {
							stmt.executeBatch();
							conn.commit();
						}
					}
				}

				stmt.executeBatch();
				conn.commit();

				result.put("割接结果", "共有自定义公式" + 公式List.size() + "个，本次割接" + transCount + "个");

			}
		});
		session.close();
		return result;
	}

	/*
	 * 导入总表
	 */
	protected HashMap<String, String> transZongbiao(List<总表Entity> 总表List) {
		HashMap<String, String> result = new HashMap<>();
		getShuxingIdList();
		getGongshiIdList();

		if (Datatrans.shuxingIdList.size() > 0 && Datatrans.gongshiIdList.size() > 0) {

			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
			Session session = sessionFactory.openSession();

			session.doWork(new Work() {
				@Override
				public void execute(Connection conn) throws SQLException {

					StringBuilder sql = new StringBuilder(
							"insert into t_community_meter(allocationMethod,comment,expirationDates,isDisabled,initialData,installationsite,isCommon,level,meterCode,meterName,meterAttributesId,formulaId,parentMeterId,oldId,payitemsname,isApportioned) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

					PreparedStatement stmt = conn.prepareStatement(sql.toString());
					conn.setAutoCommit(false);
					Integer transCount = 0;
					for (总表Entity o : 总表List) {
						if (null != o.get分摊ID()) {
							stmt.setInt(1, !o.get分摊ID().equals(13) ? 1 : 2);
						} else {
							stmt.setNull(1, Types.INTEGER);
						}
						stmt.setString(2, o.get备注());
						if (null != o.get表坏日期()) {
							stmt.setDate(3, new Date(o.get表坏日期().getTime()));
							stmt.setInt(4, 1);
						} else {
							stmt.setNull(3, Types.DATE);
							stmt.setInt(4, 0);
						}

						stmt.setBigDecimal(5, o.get底数());
						stmt.setString(6, o.get安表地点());
						stmt.setInt(7, 1);
						stmt.setInt(8, o.get层次());
						if (StringUtils.isNotEmpty(
								getMeterCode(StringEscapeUtils.unescapeJava(o.get编号()).replaceAll("\r|\n", "")))) {
							stmt.setString(9,
									getMeterCode(StringEscapeUtils.unescapeJava(o.get编号()).replaceAll("\r|\n", "")));
						} else {
							System.out.println(o.get编号() + ":::::" + StringEscapeUtils.unescapeJava(o.get编号()));
							stmt.setString(9, o.get安表地点());
						}
						stmt.setString(10, StringEscapeUtils.unescapeJava(o.get编号()).replaceAll("\r|\n", ""));
						stmt.setInt(11, Datatrans.shuxingIdList.get(o.get属性ID()));

						if (null != o.get分摊ID() && o.get分摊ID().equals(13) && StringUtils.isNotEmpty(o.get公式名())
								&& !o.get公式名().equals("null")) {
							stmt.setInt(12, Datatrans.gongshiIdList.get(o.get公式名()));
						} else {
							stmt.setNull(12, Types.INTEGER);
						}

						if (o.get层次() > 1) {
							CommunityMeterEntity parent = communityMeterDao
									.getUniqueByHql("from CommunityMeterEntity where oldId='" + o.get上级表ID() + "'");
							stmt.setInt(13, parent.getId());
						} else {
							stmt.setNull(13, Types.INTEGER);
						}
						stmt.setString(14, o.get总表ID());
						stmt.setString(15, o.get分摊费用名称().equals("公共电分摊") ? "公共电费分摊" : o.get分摊费用名称());
						stmt.setInt(16, 1);
						stmt.addBatch();
						transCount++;
						if (transCount % 100 == 0) {
							stmt.executeBatch();
							conn.commit();
						}

					}

					stmt.executeBatch();

					StringBuilder sql1 = new StringBuilder(
							"UPDATE t_community_meter SET metername = REPLACE(REPLACE(REPLACE(metername,CHAR(10),''),CHAR(13),''),CHAR(9),'');");
					PreparedStatement stmt1 = conn.prepareStatement(sql1.toString());
					stmt1.addBatch();
					stmt1.executeBatch();

					conn.commit();
					result.put("割接结果", "共有总表" + 总表List.size() + "个，本次割接" + transCount + "个");

				}
			});
			session.close();
		} else {
			if (Datatrans.shuxingIdList.size() == 0) {
				log.info("shuxingIdList不能为空！！！");
				result.put("割接失败", "shuxingIdList不能为空！！！");
			} else if (Datatrans.gongshiIdList.size() == 0) {
				log.info("gongshiIdList不能为空！！！");
				result.put("割接失败", "gongshiIdList不能为空！！！");
			} else {
				log.info("shuxingIdList和gongshiIdList不能为空！！！");
				result.put("割接失败", "shuxingIdList和gongshiIdList不能为空！！！");
			}

		}
		return result;
	}

	/*
	 * 导入总表抄表
	 */

	protected HashMap<String, String> transZongbiaoChaobiao(List<总表抄表Entity> 总表抄表List, List<总表Entity> 总表List) {
		HashMap<String, String> result = new HashMap<>();

		getMeterIdList();

		Map<String, 总表Entity> tempMap = new HashMap<>();
		总表List.forEach(o -> {
			tempMap.put(o.get总表ID(), o);
		});

		if (Datatrans.meterIdList.size() > 0) {
			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
			Session session = sessionFactory.openSession();

			session.doWork(new Work() {
				@Override
				public void execute(Connection conn) throws SQLException {

					StringBuilder sql = new StringBuilder(
							"insert into t_community_meter_record(id,meterId,recordDate,isZero,oldId,recorder,recordNum,lastRecordId,state,additionalAmount,additionalInstructions) values(?,?,?,?,?,?,?,?,?,?,?)");

					Integer transCount = 0;
					PreparedStatement stmt = conn.prepareStatement(sql.toString());
					Integer recordId = 1;
					for (总表抄表Entity o : 总表抄表List) {
						stmt.setInt(1, recordId);
						stmt.setInt(2, Datatrans.meterIdList.get(o.get总表ID()));
						stmt.setDate(3, new Date(o.get本次读数日期().getTime()));
						stmt.setInt(4, o.get是否归零());
						stmt.setString(5, o.getId());
						stmt.setString(6, o.get抄表人());
						stmt.setBigDecimal(7, o.get本次读数());
						if (StringUtils.isNotEmpty(o.get上次读数ID())) {
							stmt.setInt(8, recordId - 1);
						} else {
							stmt.setNull(8, Types.INTEGER);
						}
						stmt.setInt(9, 1);

						/*
						 * 正式割接要修改记录时间
						 */
						stmt.setInt(10,
								null != tempMap.get(o.get总表ID()).get额外用量()
										&& DateUtil.formatShortFormat(o.get本次读数日期()).equals("2022-09-30")
												? tempMap.get(o.get总表ID()).get额外用量().intValue()
												: 0);
						stmt.setString(11,
								null != tempMap.get(o.get总表ID()).get额外说明()
										&& DateUtil.formatShortFormat(o.get本次读数日期()).equals("2022-09-30")
												? tempMap.get(o.get总表ID()).get额外说明()
												: "");
						stmt.addBatch();
						recordId++;
						transCount++;
						if (transCount % 400 == 0) {
							stmt.executeBatch();
							conn.commit();
							log.info("还剩余" + (总表抄表List.size() - transCount) + "条数据未割接！");
						}
					}
					stmt.executeBatch();
					conn.commit();
					result.put("割接结果", "共有总表抄表记录" + 总表抄表List.size() + "条，本次割接" + transCount + "个");
				}
			});
			session.close();
		} else {
			log.info("meterIdList不能为空！！！");
			result.put("割接失败", "meterIdList不能为空！！！");
		}
		return result;
	}

	/*
	 * 导入用户表
	 */

	protected HashMap<String, String> transYonghubiao(List<用户表Entity> 用户表List) {
		HashMap<String, String> result = new HashMap<>();
		getShuxingIdList();
		getMeterIdList();
		getEstateIdList();

		if (Datatrans.shuxingIdList.size() > 0 && Datatrans.meterIdList.size() > 0
				&& Datatrans.estateIdList.size() > 0) {
			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
			Session session = sessionFactory.openSession();

			session.doWork(new Work() {
				@Override
				public void execute(Connection conn) throws SQLException {

					StringBuilder sql = new StringBuilder(
							"insert into t_community_meter(allocationMethod,comment,expirationDates,isDisabled,initialData,installationsite,isCommon,level,meterCode,meterName,meterAttributesId,formulaId,parentMeterId,oldId,isApportioned) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

					PreparedStatement stmt = conn.prepareStatement(sql.toString());
					conn.setAutoCommit(false);
					Integer transCount = 0;
					for (用户表Entity o : 用户表List) {
						stmt.setNull(1, Types.INTEGER);
						stmt.setString(2, o.get备注());
						if (null != o.get表坏日期()) {
							stmt.setDate(3, new Date(o.get表坏日期().getTime()));
							stmt.setInt(4, 1);
						} else {
							stmt.setNull(3, Types.DATE);
							stmt.setInt(4, 0);
						}

						stmt.setBigDecimal(5, o.get底数());
						stmt.setString(6, o.get安表地点());
						stmt.setInt(7, 0);
						stmt.setInt(8, 1);
						stmt.setString(9, o.get安表地点());
						stmt.setString(10, "用户表");
						stmt.setInt(11, Datatrans.shuxingIdList.get(o.get属性ID()));
						stmt.setNull(12, Types.INTEGER);
						if (StringUtils.isNotEmpty(o.get总表ID()) && !o.get总表ID().equals("null")) {
							stmt.setInt(13, Datatrans.meterIdList.get(o.get总表ID()));
						} else {
							stmt.setNull(13, Types.INTEGER);
						}
						stmt.setString(14, o.get用户表ID());
						stmt.setInt(15, 0);
						stmt.addBatch();
						transCount++;
						if (transCount % 100 == 0) {
							stmt.executeBatch();
							conn.commit();
						}

					}
					stmt.executeBatch();
					conn.commit();

					refreshMeterIdList();

					StringBuilder sql2 = new StringBuilder(
							"insert into t_community_meter_property(meterId,propertyId) values(?,?)");
					PreparedStatement stmt2 = conn.prepareStatement(sql2.toString());
					Integer flag = 0;
					for (用户表Entity o : 用户表List) {
						stmt2.setInt(1, Datatrans.meterIdList.get(o.get用户表ID()));
						stmt2.setInt(2, Datatrans.estateIdList.get(o.get单元ID()));
						stmt2.addBatch();
						flag++;
						if (flag % 200 == 0) {
							stmt2.executeBatch();
							conn.commit();
						}
					}

					stmt2.executeBatch();
					conn.commit();
					result.put("割接结果", "共有用户表" + 用户表List.size() + "个，本次割接" + transCount + "个");

				}
			});
			session.close();
		} else {
			if (Datatrans.shuxingIdList.size() == 0) {
				log.info("shuxingIdList不能为空！！！");
				result.put("割接失败", "shuxingIdList不能为空！！！");
			} else if (Datatrans.meterIdList.size() == 0) {
				log.info("meterIdList不能为空！！！");
				result.put("割接失败", "meterIdList不能为空！！！");
			} else if (Datatrans.estateIdList.size() == 0) {
				log.info("estateIdList不能为空！！！");
				result.put("割接失败", "estateIdList不能为空！！！");
			} else {
				log.info("shuxingIdList、gongshiIdList、estateIdList不能为空！！！");
				result.put("割接失败", "shuxingIdList、gongshiIdList、estateIdList不能为空！！！");
			}
		}
		return result;
	}

	/*
	 * 导入用户表抄表
	 */
	protected HashMap<String, String> transYonghubiaoChaobiao(List<用户表抄表Entity> 用户表抄表List) {
		HashMap<String, String> result = new HashMap<>();
		getMeterIdList();
		if (Datatrans.meterIdList.size() > 0) {
			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
			Session session = sessionFactory.openSession();

			session.doWork(new Work() {
				@Override
				public void execute(Connection conn) throws SQLException {
					StringBuilder sql = new StringBuilder(
							"insert into t_community_meter_record(id,meterId,recordDate,isZero,oldId,recorder,recordNum,lastRecordId,state,additionalAmount,additionalInstructions) values(?,?,?,?,?,?,?,?,?,?,?)");

					Integer transCount = 0;

					Statement st = conn.createStatement();
					ResultSet rs = st.executeQuery("select max(id) as id from t_community_meter_record");
					Integer recordId = 1;

					while (rs.next()) {
						recordId = rs.getInt("id") + 1;
					}

					rs.close();
					st.close();

					PreparedStatement stmt = conn.prepareStatement(sql.toString());

					for (用户表抄表Entity o : 用户表抄表List) {
						stmt.setInt(1, recordId);
						stmt.setInt(2, Datatrans.meterIdList.get(o.get用户表ID()));
						stmt.setDate(3, new Date(o.get本次读数日期().getTime()));
						stmt.setInt(4, o.get是否归零());
						stmt.setString(5, o.getId());
						stmt.setString(6, o.get抄表人());
						stmt.setBigDecimal(7, o.get本次读数());
						if (StringUtils.isNotEmpty(o.get上次读数ID())) {
							stmt.setInt(8, recordId - 1);
						} else {
							stmt.setNull(8, Types.INTEGER);
						}
						stmt.setInt(9, 1);
						stmt.setInt(10, 0);
						stmt.setString(11, "");
						stmt.addBatch();
						recordId++;
						transCount++;
						if (transCount % 400 == 0) {
							stmt.executeBatch();
							conn.commit();
							log.info("还剩余" + (用户表抄表List.size() - transCount) + "条数据未割接！");
						}
					}
					stmt.executeBatch();
					conn.commit();
					result.put("割接结果", "共有用户表抄表记录" + 用户表抄表List.size() + "条，本次割接" + transCount + "个");
				}
			});
			session.close();
		} else {
			log.info("meterIdList不能为空！！！");
			result.put("割接失败", "meterIdList不能为空！！！");
		}
		return result;
	}

	// 收费设定导入
	protected HashMap<String, String> transShoufeiSheding(List<收费设定Entity> 收费设定List) {
		HashMap<String, String> result = new HashMap<>();

		HashMap<String, Integer> temp = new HashMap<>();
		temp.put("代收车位租金", 0);
		temp.put("出租车位管理服务费", 0);
		temp.put("别墅管理费", 0);
		temp.put("商铺管理费", 0);
		temp.put("洋房管理费", 0);
		temp.put("物业管理费", 0);
		temp.put("花园及停车位管理费", 0);
		temp.put("花园管理费", 0);
		temp.put("车位服务费", 0);
		temp.put("车位管理费", 0);
		temp.put("露天车位管理服务费", 0);
		temp.put("露天车位管理服务费2", 0);
		temp.put("露天车位管理服务费3", 0);
		temp.put("露天车位管理服务费4", 0);

		Map<String, String> temp1 = new HashMap<>();
		temp1.put("代收车位租金", "代收车位租金违约金");
		temp1.put("出租车位管理服务费", "出租车位管理服务费违约金");
		temp1.put("别墅管理费", "违约金");
		temp1.put("商铺管理费", "违约金");
		temp1.put("洋房管理费", "违约金");
		temp1.put("物业管理费", "违约金");
		temp1.put("花园及停车位管理费", "花园及停车位管理费违约金");
		temp1.put("花园管理费", "违约金");
		temp1.put("车位服务费", "车位服务费违约金");
		temp1.put("车位管理费", "车位管理费违约金");
		temp1.put("露天车位管理服务费", "露天车位管理服务费违约金");
		temp1.put("露天车位管理服务费2", "露天车位管理服务费2违约金");
		temp1.put("露天车位管理服务费3", "露天车位管理服务费3违约金");
		temp1.put("露天车位管理服务费4", "露天车位管理服务费4违约金");

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {

				StringBuilder sql = new StringBuilder(
						"insert into t_community_pay_items(chargeCategory,comment,priceUnit,endTime,itemsName,oldId,"
								+ "payDate,price,startTime,isReceivables,feeCalType,isBreach,breachRatio,breachName,state) "
								+ "values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

				PreparedStatement stmt = conn.prepareStatement(sql.toString());
				conn.setAutoCommit(false);
				Integer transCount = 0;
				for (收费设定Entity o : 收费设定List) {

					if (!o.get收费项目().equals("花园及停车位管理费违约金")) {
						if (o.get类别().equals("管理费")) {
							stmt.setInt(1, CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_MANAGEMENT);
						} else if (o.get类别().equals("车位费")) {
							stmt.setInt(1, CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_CAR);
						} else if (o.get类别().equals("商管部")) {
							stmt.setInt(1, CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_SHOP);
						} else if (o.get类别().equals("分摊费")) {
							stmt.setInt(1, CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_ALLOCATION);
						} else if (o.get类别().equals("违约金")) {
							stmt.setInt(1, CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_BREAK);
						} else if (o.get类别().equals("押金类")) {
							stmt.setInt(1, CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_DEPOSIT);
						} else if (o.get类别().equals("有偿服务类")) {
							stmt.setInt(1, CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_COST);
						} else {
							stmt.setInt(1, CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_OTHER);
						}

						stmt.setString(2, o.get备注());
						stmt.setString(3, o.get单位1());
						if (null != o.get截止时间()) {
							stmt.setDate(4, new Date(o.get截止时间().getTime()));
						} else {
							try {
								stmt.setDate(4, new Date(DateUtil.parseLongFormat("2099-01-01 00:00:00").getTime()));
							} catch (SQLException | ParseException e) {
								// TODO Auto-generated catch block
								e.printStackTrace();
							}
						}

						stmt.setString(5, o.get收费项目().replaceAll("违约金", "违约金收费"));
						stmt.setString(6, o.get项目ID());
						stmt.setInt(7, o.get交款日());
						stmt.setBigDecimal(8, o.get单价());
						if (null != o.get起始时间()) {
							stmt.setDate(9, new Date(o.get起始时间().getTime()));
						} else {
							try {
								stmt.setDate(9, new Date(DateUtil.parseLongFormat("1999-01-01 00:00:00").getTime()));
							} catch (SQLException | ParseException e) {
								// TODO Auto-generated catch block
								e.printStackTrace();
							}
						}

						stmt.setInt(10, o.get自动生成应收款());
						stmt.setInt(11, o.get单位().equals("meter_area_building")
								? CommunityContext.COMMUNITY_PAY_ITEMS_CAL_TYPE_CHARGE
								: o.get单位().equals("meter_area_add") ? CommunityContext.COMMUNITY_PAY_ITEMS_CAL_TYPE_ADD
										: o.get单位().equals("master")
												? CommunityContext.COMMUNITY_PAY_ITEMS_CAL_TYPE_ONCE
												: 0);
//					stmt.setString(12, o.get单位().equals("meter_area_building") ? "单价*收费面积/本月天数*计费天数"
//							: o.get单位().equals("meter_area_add") ? "单价*附加面积/本月天数*计费天数" : ");
						stmt.setInt(12, temp.containsKey(o.get收费项目()) ? 1 : 0);
						stmt.setBigDecimal(13,
								temp.containsKey(o.get收费项目()) ? new BigDecimal("0.003") : new BigDecimal("0"));
//					stmt.setString(14, temp1.containsKey(o.get收费项目()) ? temp1.get(o.get收费项目()) : ");
						stmt.setString(14, temp1.containsKey(o.get收费项目()) ? temp1.get(o.get收费项目()) : "");

						stmt.setInt(15, 1);

						stmt.addBatch();
						transCount++;
						if (transCount % 100 == 0) {
							stmt.executeBatch();
							conn.commit();
							log.info("还剩余" + (收费设定List.size() - transCount) + "条数据未割接！");
						}
					}
				}
				stmt.executeBatch();
				conn.commit();
				result.put("割接结果", "共有收费设定" + 收费设定List.size() + "个，本次割接" + transCount + "个");

			}
		});
		session.close();
		return result;
	}

	// 其他收费项目
	protected HashMap<String, String> transOtherPayItem(List<收费项目其他收费Entity> 其他收费项目List) {
		HashMap<String, String> result = new HashMap<>();

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {

				StringBuilder sql = new StringBuilder(
						"insert into t_community_pay_items(chargeCategory,comment,priceUnit,itemsName,payDate,price,"
								+ "isReceivables,isBreach,oldId,state) values(?,?,?,?,?,?,?,?,?,?)");

				PreparedStatement stmt = conn.prepareStatement(sql.toString());
				conn.setAutoCommit(false);

				for (收费项目其他收费Entity o : 其他收费项目List) {

					if (o.get类别().equals("管理费")) {
						stmt.setInt(1, CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_MANAGEMENT);
					} else if (o.get类别().equals("车位费")) {
						stmt.setInt(1, CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_CAR);
					} else if (o.get类别().equals("分摊费")) {
						stmt.setInt(1, CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_ALLOCATION);
					} else if (o.get类别().equals("违约金")) {
						stmt.setInt(1, CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_BREAK);
					} else if (o.get类别().equals("押金类")) {
						stmt.setInt(1, CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_DEPOSIT);
					} else if (o.get类别().equals("有偿服务类")) {
						stmt.setInt(1, CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_COST);
					} else {
						stmt.setInt(1, CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_OTHER);
					}

					stmt.setString(2, "");
					stmt.setString(3, o.get单位());
					stmt.setString(4, o.get收费项目());
					stmt.setInt(5, 0);
					stmt.setBigDecimal(6, o.get金额());
					stmt.setInt(7, 0);
					stmt.setInt(8, 0);
					stmt.setString(9, o.get收费项目());
					if (StringUtils.isNotEmpty(o.get管理处()) && o.get管理处().equals("glc")) {
						stmt.setInt(10, 1);
					} else {
						stmt.setInt(10, 0);
					}

					stmt.addBatch();

				}
				stmt.executeBatch();

				conn.commit();
			}
		});
		session.close();

		return result;
	}

	// 收费范围导入
	protected HashMap<String, String> transShoufeiFanwei(List<收费范围Entity> 收费范围List) {
		HashMap<String, String> result = new HashMap<>();

		getPayItemIdList();
		getEstateIdList();

		if (Datatrans.payItemIdList.size() > 0 && Datatrans.estateIdList.size() > 0) {
			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
			Session session = sessionFactory.openSession();

			session.doWork(new Work() {
				@Override
				public void execute(Connection conn) throws SQLException {

					StringBuilder sql = new StringBuilder(
							"insert into t_community_property_pay_items(payItemsId,propertyId) values(?,?)");

					PreparedStatement stmt = conn.prepareStatement(sql.toString());
					conn.setAutoCommit(false);
					Integer transCount = 0;
					for (收费范围Entity o : 收费范围List) {
						stmt.setInt(1, Datatrans.payItemIdList.get(o.getId().get项目ID()));
						stmt.setInt(2, Datatrans.estateIdList.get(o.getId().get单元ID()));
						stmt.addBatch();
						transCount++;
						if (transCount % 400 == 0) {
							stmt.executeBatch();
							conn.commit();
							log.info("还剩余" + (收费范围List.size() - transCount) + "条数据未割接！");
						}
					}

					stmt.executeBatch();
					conn.commit();
					result.put("割接结果", "共有收费范围" + 收费范围List.size() + "个，本次割接" + transCount + "个");

				}
			});
			session.close();
		} else {
			if (Datatrans.payItemIdList.size() == 0) {
				log.info("payItemIdList不能为空！！！");
				result.put("割接失败", "payItemIdList不能为空！！！");
			} else if (Datatrans.estateIdList.size() == 0) {
				log.info("estateIdList不能为空！！！");
				result.put("割接失败", "estateIdList不能为空！！！");
			} else {
				log.info("payItemIdList和estateIdList不能为空！！！");
				result.put("割接失败", "payItemIdList和estateIdList不能为空！！！");
			}
		}

		return result;
	}

	// 公用分摊范围导入
	protected HashMap<String, String> transGongyongFentanFanwei(List<公用分摊范围Entity> 公用分摊范围List) {
		HashMap<String, String> result = new HashMap<>();

		getMeterIdList();
		getEstateIdList();

		if (Datatrans.meterIdList.size() > 0 && Datatrans.estateIdList.size() > 0) {

			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
			Session session = sessionFactory.openSession();

			session.doWork(new Work() {
				@Override
				public void execute(Connection conn) throws SQLException {

					StringBuilder sql = new StringBuilder(
							"insert into t_community_meter_property(meterId,propertyId) values(?,?)");

					PreparedStatement stmt = conn.prepareStatement(sql.toString());
					conn.setAutoCommit(false);
					Integer transCount = 0;
					for (公用分摊范围Entity o : 公用分摊范围List) {
						stmt.setInt(1, Datatrans.meterIdList.get(o.getId().get总表ID()));
						stmt.setInt(2, Datatrans.estateIdList.get(o.getId().get单元ID()));
						stmt.addBatch();
						transCount++;
						if (transCount % 200 == 0) {
							stmt.executeBatch();
							conn.commit();
							stmt.clearBatch();
							log.info("还剩余" + (公用分摊范围List.size() - transCount) + "条数据未割接！");
						}
					}

					stmt.executeBatch();
					conn.commit();
					result.put("割接结果", "共有公用分摊范围" + 公用分摊范围List.size() + "个，本次割接" + transCount + "个");

				}
			});
			session.close();
			Datatrans.meterIdList.clear();
		} else {
			if (Datatrans.meterIdList.size() == 0) {
				log.info("meterIdList不能为空！！！");
				result.put("割接失败", "meterIdList不能为空！！！");
			} else if (Datatrans.estateIdList.size() == 0) {
				log.info("estateIdList不能为空！！！");
				result.put("割接失败", "estateIdList不能为空！！！");
			} else {
				log.info("meterIdList和estateIdList不能为空！！！");
				result.put("割接失败", "meterIdList和estateIdList不能为空！！！");
			}
		}
		return result;
	}

	// 应收款
	protected HashMap<String, String> transYingshoukuan(List<应收款Entity> 应收款List) {
		HashMap<String, String> result = new HashMap<>();

		getEstateIdList();
		getPayItemIdList();

		if (Datatrans.estateIdList.size() > 0 && Datatrans.payItemIdList.size() > 0) {

			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
			Session session = sessionFactory.openSession();

			session.doWork(new Work() {
				@Override
				public void execute(Connection conn) throws SQLException {

					StringBuilder sql = new StringBuilder(
							"insert into t_community_receivables(chargeCategory,chargeSource,comment,endTime,oldData,oldId,"
									+ "payItemsName,paymentPeriod,receivableAmount,receivableDate,"
									+ "receivedAmount,sourceNotes,startTime,estateId,payItemId,lockMark,receivablesNO) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

					PreparedStatement stmt = conn.prepareStatement(sql.toString());
					conn.setAutoCommit(false);
					Integer transCount = 0;
					for (应收款Entity o : 应收款List) {
						if (StringUtils.isNotEmpty(o.get项目类别())) {
							if (o.get项目类别().equals("公摊费") || o.get项目类别().equals("分摊费") || o.get收费项目().equals("车库公共电费")
									|| o.get收费项目().equals("空中花园公共电费") || o.get收费项目().equals("景观大道路灯电费")
									|| o.get收费项目().equals("公共电费分摊")) {
								stmt.setString(1, "分摊费");
							} else if (o.get项目类别().equals("违约金") || o.get项目类别().equals("滞纳金")
									|| o.get收费项目().equals("滞纳金")) {
								stmt.setString(1, "违约金");
							} else if (o.get收费项目().equals("车位管理费") || o.get收费项目().equals("车位管理服务费")
									|| o.get收费项目().equals("出租车位管理费") || o.get收费项目().equals("出租车位管理服务费")
									|| o.get收费项目().equals("代收车位租金")) {
								stmt.setString(1, "车位费");
							} else if (o.get项目类别().equals("其他") || o.get收费项目().equals("代收水费")
									|| o.get收费项目().equals("代收电费")) {
								stmt.setString(1, "其它类");
							} else if (o.get收费项目().equals("物业管理费")) {
								stmt.setString(1, "管理费");
							} else if (o.get收费项目().equals("损害财产赔偿费")) {
								stmt.setString(1, "有偿服务费");
							} else {
								stmt.setString(1, o.get项目类别());
							}
						} else {
							if (o.get收费项目().equals("公共电费分摊")) {
								stmt.setString(1, "分摊费");
							} else if (o.get收费项目().equals("违约金") || o.get收费项目().equals("滞纳金")
									|| o.get收费项目().equals("露天车位管理服务费滞纳金")) {
								stmt.setString(1, "违约金");
							} else if (o.get收费项目().equals("出租车位管理服务费") || o.get收费项目().equals("代收车位租金")) {
								stmt.setString(1, "车位费");
							} else {
								stmt.setString(1, "其它类");
							}
						}
						stmt.setString(2,
								StringUtils.isNotEmpty(o.get项目来源()) ? o.get项目来源().replaceAll("滞纳金", "违约金") : "");
						stmt.setString(3, StringUtils.isNotEmpty(o.get备注()) ? o.get备注().replaceAll("滞纳金", "违约金") : "");
						if (null != o.get截止日期()) {
							stmt.setDate(4, new Date(o.get截止日期().getTime()));
						} else {
							stmt.setNull(4, Types.DATE);
						}
						stmt.setString(5, "");
						stmt.setString(6, o.get应收款ID());
//						if (o.get收费项目().equals("IC卡工本费") || o.get收费项目().equals("IC工卡本费") || o.get收费项目().equals("IC工本费")
//								|| o.get收费项目().equals("过卡IC卡工本、服务费") || o.get收费项目().equals("过桥IC卡工本\\服务费")
//								|| o.get收费项目().equals("过桥IC卡工本、服务费") || o.get收费项目().equals("过桥IC卡工本服务费")
//								|| o.get收费项目().equals("过桥IC卡工本费") || o.get收费项目().equals("过桥IC工本、服务费")
//								|| o.get收费项目().equals("过桥卡IC卡工本、服务费")) {
//							stmt.setString(7, "IC卡工本费");
//						} else if (o.get收费项目().equals("损害绿化赔偿款") || o.get收费项目().equals("损害路灯、绿化赔偿款")) {
//							stmt.setString(7, "损坏绿化赔偿款");
//						} else if (o.get收费项目().equals("损害栏杆赔偿款")) {
//							stmt.setString(7, "损坏栏杆赔偿款");
//						} else if (o.get收费项目().equals("损害财产赔偿款") || o.get收费项目().equals("损害财产赔偿费")) {
//							stmt.setString(7, "损坏财产赔偿款");
//						} else if (o.get收费项目().equals("损害电缆赔偿款")) {
//							stmt.setString(7, "损坏电缆赔偿款");
//						} else if (o.get收费项目().equals("损害花基赔偿款")) {
//							stmt.setString(7, "损坏花基赔偿款");
//						} else if (o.get收费项目().equals("损害路灯赔偿款")) {
//							stmt.setString(7, "损坏路灯赔偿款");
//						} else if (o.get收费项目().equals("损害路面赔偿款")) {
//							stmt.setString(7, "损坏路面赔偿款");
//						} else if (o.get收费项目().equals("损害道闸赔偿款")) {
//							stmt.setString(7, "损坏道闸赔偿款");
//						} else if (o.get收费项目().equals("日常办公费") || o.get收费项目().equals("日常办公费用")) {
//							stmt.setString(7, "日常办公费");
//						} else if (o.get收费项目().equals("有偿服务清洁费") || o.get收费项目().equals("有偿清洁服务")) {
//							stmt.setString(7, "有偿清洁服务费");
//						} else if (o.get收费项目().equals("车位服务费") || o.get收费项目().equals("车位管理服务费")) {
//							stmt.setString(7, "车位管理费");
//						} else if (o.get收费项目().equals("有偿绿化服务")) {
//							stmt.setString(7, "有偿绿化服务费");
//						} else {
//							stmt.setString(7, o.get收费项目());
//						}

						stmt.setString(7, o.get收费项目().replaceAll("滞纳金", "违约金"));

						if (null != o.get款项属期()) {
							stmt.setDate(8, new Date(o.get款项属期().getTime()));
						} else {
							stmt.setNull(8, Types.DATE);
						}
						stmt.setBigDecimal(9, o.get应收金额());
						stmt.setDate(10, new Date(o.get应收日期().getTime()));
						stmt.setBigDecimal(11, o.get已收金额());
						stmt.setString(12, o.get来源备注());
						if (null != o.get起始日期()) {
							stmt.setDate(13, new Date(o.get起始日期().getTime()));
						} else {
							stmt.setNull(13, Types.DATE);
						}
						stmt.setInt(14, Datatrans.estateIdList.get(o.get单元ID()));

						if (StringUtils.isNotEmpty(o.get来源备注()) && o.get来源备注().length() >= 36
								&& Datatrans.payItemIdList.containsKey(o.get来源备注().substring(0, 36))) {
							stmt.setInt(15, Datatrans.payItemIdList.get(o.get来源备注().substring(0, 36)));
						} else {
							if (o.get收费项目().equals("装修施工证工本费") || o.get收费项目().equals("证件工本费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("证件工本费"));
							} else if (o.get收费项目().trim().equals("IC卡工本费") || o.get收费项目().trim().equals("IC工卡本费")
									|| o.get收费项目().trim().equals("IC工本费") || o.get收费项目().trim().equals("工本费")
									|| o.get收费项目().trim().equals("远程卡工本费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("工本费"));
							} else if (o.get收费项目().equals("业主过桥IC卡工本、服务费") || o.get收费项目().equals("过卡IC卡工本、服务费")
									|| o.get收费项目().equals("过桥IC卡工本\\服务费") || o.get收费项目().equals("过桥IC卡工本、服务费")
									|| o.get收费项目().equals("过桥IC卡工本服务费") || o.get收费项目().equals("过桥IC工本、服务费")
									|| o.get收费项目().equals("过桥卡IC卡工本、服务费") || o.get收费项目().equals("过桥IC卡工本费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("过桥IC卡工本费、服务费"));
							} else if (o.get收费项目().equals("临时停车收费") || o.get收费项目().equals("临时停车费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("临时停车费"));
							} else if (o.get收费项目().equals("代收代办房屋办证费") || o.get收费项目().equals("代收代缴房屋办证费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("代收代缴房屋办证费"));
							} else if (o.get收费项目().equals("代收三水大桥过桥费") || o.get收费项目().equals("代收过三水大桥费")
									|| o.get收费项目().equals("代收过三水大桥过桥费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("代收三水大桥过桥费"));
							} else if (o.get收费项目().equals("代缴社保费") || o.get收费项目().equals("代购社保款")
									|| o.get收费项目().equals("代购社保费") || o.get收费项目().equals("过桥IC卡工本、服务费")
									|| o.get收费项目().equals("社保费") || o.get收费项目().equals("收回代交社保费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("代缴社保费"));
							} else if (o.get收费项目().equals("餐费") || o.get收费项目().equals("伙食费")
									|| o.get收费项目().equals("员工餐费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("员工餐费"));
							} else if (o.get收费项目().equals("垃圾清运费") || o.get收费项目().equals("代收垃圾清运费")
									|| o.get收费项目().equals("装修垃圾清运费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("代收垃圾清运费"));
							} else if (o.get收费项目().equals("宿舍洗衣机洗衣费") || o.get收费项目().equals("宿舍洗衣机费用")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("宿舍洗衣机费用"));
							} else if (o.get收费项目().equals("日常办公费") || o.get收费项目().equals("日常办公费用")
									|| o.get收费项目().equals("复印费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("日常办公费"));
							} else if (o.get收费项目().equals("工作服折旧费") || o.get收费项目().equals("工服费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("工服费"));
							} else if (o.get收费项目().equals("代收工程维修款") || o.get收费项目().equals("工程维修")
									|| o.get收费项目().equals("有偿维修服务费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("工程维修"));
							} else if (o.get收费项目().equals("施工服务费") || o.get收费项目().equals("施工管理费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("施工服务费"));
							} else if (o.get收费项目().equals("有偿清洁服务") || o.get收费项目().equals("有偿清洁服务费")
									|| o.get收费项目().equals("有偿服务清洁费") || o.get收费项目().equals("开荒清洁费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("有偿清洁服务费"));
							} else if (o.get收费项目().equals("有偿服务费") || o.get收费项目().equals("综合有偿服务费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("有偿服务费"));
							} else if (o.get收费项目().equals("有偿绿化服务") || o.get收费项目().equals("有偿绿化服务费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("有偿绿化服务费"));
							} else if (o.get收费项目().equals("损害财产赔偿款") || o.get收费项目().equals("损害财产赔偿费")
									|| o.get收费项目().equals("损坏财产赔偿款")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("损害财产赔偿费"));
							} else if (o.get收费项目().equals("自行车使用款") || o.get收费项目().equals("自行车款")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("自行车使用款"));
							} else if (o.get收费项目().equals("装修管理服务费") || o.get收费项目().equals("装修管理费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("装修管理服务费"));
							} else if (o.get收费项目().equals("运费") || o.get收费项目().equals("运输费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("运输费"));
							} else if (o.get收费项目().equals("开锁服务费") || o.get收费项目().equals("车辆开锁费")
									|| o.get收费项目().equals("违规停放车辆")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("车辆开锁服务费"));
							} else if (o.get收费项目().equals("沙石费用") || o.get收费项目().equals("砂石等费用")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("砂石等费用"));
							} else if (o.get收费项目().equals("损坏绿化赔偿款") || o.get收费项目().equals("损害绿化赔偿款")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("损害绿化赔偿款"));
							} else if (o.get收费项目().equals("销售废品") || o.get收费项目().equals("废品款")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("废品款"));
							} else if (o.get收费项目().equals("施工保证金") || o.get收费项目().equals("装修保证金")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("装修保证金"));
							} else if (o.get收费项目().equals("收还借款") || o.get收费项目().equals("还借款")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("还借款"));
							} else if (o.get收费项目().equals("代收商铺租金") || o.get收费项目().equals("代收租金")
									|| o.get收费项目().equals("商铺租金")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("代收商铺租金"));
							} else if (o.get收费项目().equals("电费") || o.get收费项目().equals("宿舍电费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("合作单位"));
							} else if (o.get收费项目().equals("水费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("楼梯的水费"));
							} else if (o.get收费项目().equals("公摊电费")) {
								stmt.setInt(15, Datatrans.payItemNameList.get("楼梯的公摊电费"));
							} else if (o.get收费项目().indexOf("违约金") >= 0 || o.get收费项目().indexOf("滞纳金") >= 0) {
								stmt.setInt(15, Datatrans.payItemNameList.get("违约金收费"));
							} else if (Datatrans.payItemNameList.containsKey(o.get收费项目())) {
								stmt.setInt(15, Datatrans.payItemNameList.get(o.get收费项目()));
							} else {
								stmt.setNull(15, Types.INTEGER);
							}

						}
						if (o.get锁定标志().equals(1) || o.get收费项目().indexOf("滞纳金") >= 0) {
							stmt.setInt(16, 2);
						} else {
							stmt.setInt(16, 0);
						}
						stmt.setString(17,o.get应收款no()+"");
						stmt.addBatch();
						transCount++;
						if (transCount % 1000 == 0) {
							stmt.executeBatch();
							conn.commit();
							log.info("还剩余" + (应收款List.size() - transCount) + "笔应收款未割接！");
						}
					}
					stmt.executeBatch();
					conn.commit();
					result.put("割接结果", "共有应收款" + 应收款List.size() + "笔，本次割接" + transCount + "个");

				}
			});
			session.close();
		} else {
			if (Datatrans.payItemIdList.size() == 0) {
				log.info("payItemIdList不能为空！！！");
				result.put("割接失败", "payItemIdList不能为空！！！");
			} else if (Datatrans.estateIdList.size() == 0) {
				log.info("estateIdList不能为空！！！");
				result.put("割接失败", "estateIdList不能为空！！！");
			} else {
				log.info("payItemIdList和estateIdList不能为空！！！");
				result.put("割接失败", "payItemIdList和estateIdList不能为空！！！");
			}
		}
		return result;
	}

	// 应收款减加
	protected HashMap<String, String> transYingshoukuanJianjia(List<应收款减加Entity> 应收款减加List) {
		HashMap<String, String> result = new HashMap<>();

		getYingshoukuanIdList();
		getEstateIdList();

		if (Datatrans.yingshoukuanIdList.size() > 0 && Datatrans.estateIdList.size() > 0) {

			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
			Session session = sessionFactory.openSession();

			session.doWork(new Work() {
				@Override
				public void execute(Connection conn) throws SQLException {

					StringBuilder sql = new StringBuilder(
							"insert into t_community_receivables_changes(approver,changeAmount,changeDate,changeType,comment,oldData,"
									+ "oldId,payItemsName,receivableAmount,receivableDate,receivedAmount,"
									+ "recorder,receivablesId,estateId) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

					PreparedStatement stmt = conn.prepareStatement(sql.toString());
					conn.setAutoCommit(false);
					Integer transCount = 0;
					for (应收款减加Entity o : 应收款减加List) {
						if (Datatrans.yingshoukuanIdList.containsKey(o.get应收款ID())
								&& Datatrans.estateIdList.containsKey(o.get单元ID())) {
							stmt.setString(1, o.get批准人());
							stmt.setBigDecimal(2, o.get减加金额());
							stmt.setDate(3, new Date(o.get减加日期().getTime()));
							stmt.setInt(4, 0);
							stmt.setString(5, o.get备注());
							stmt.setString(6, o.get单元ID());
							stmt.setString(7, o.get应收款ID());
							stmt.setString(8, o.get收费项目().replaceAll("滞纳金", "违约金"));
							stmt.setBigDecimal(9, o.get应收金额());
							stmt.setDate(10, new Date(o.get应收日期().getTime()));
							stmt.setBigDecimal(11, o.get已收金额());
							stmt.setString(12, o.get减加人());
							stmt.setInt(13, Datatrans.yingshoukuanIdList.get(o.get应收款ID()));
							stmt.setInt(14, Datatrans.estateIdList.get(o.get单元ID()));
							stmt.addBatch();
							transCount++;
							if (transCount % 500 == 0) {
								stmt.executeBatch();
								conn.commit();
								log.info("还剩余" + (应收款减加List.size() - transCount) + "笔应收款减加记录未割接！");
							}
						} else {
							log.info("割接异常====>>>应收款Id(" + o.get应收款ID() + ")或者单元Id(" + o.get单元ID() + ")割接不成功！！！");
						}
					}
					stmt.executeBatch();
					conn.commit();
					result.put("割接结果", "共有应收款减加记录" + 应收款减加List.size() + "笔，本次割接" + transCount + "个");

				}
			});
			session.close();
		} else {
			if (Datatrans.yingshoukuanIdList.size() == 0) {
				log.info("yingshoukuanIdList不能为空！！！");
				result.put("割接失败", "yingshoukuanIdList不能为空！！！");
			} else if (Datatrans.estateIdList.size() == 0) {
				log.info("estateIdList不能为空！！！");
				result.put("割接失败", "estateIdList不能为空！！！");
			} else {
				log.info("yingshoukuanIdList和estateIdList不能为空！！！");
				result.put("割接失败", "yingshoukuanIdList和estateIdList不能为空！！！");
			}
		}
		return result;
	}

	// 应预收款收据
	protected HashMap<String, String> transShouju(List<应预收款收据Entity> 应预收款收据List) {
		HashMap<String, String> result = new HashMap<>();

		getEstateIdList();
		if (Datatrans.estateIdList.size() > 0) {

			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
			Session session = sessionFactory.openSession();

			session.doWork(new Work() {
				@Override
				public void execute(Connection conn) throws SQLException {

					StringBuilder sql = new StringBuilder(
							"insert into t_community_receipt(receiptCode,receiptDate,payerName,amount,paymentMethod,comment,"
									+ "agent,receiptType,paymentType,printNum,estateId,oldId,feeType,haveInvoice) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

					PreparedStatement stmt = conn.prepareStatement(sql.toString());
					conn.setAutoCommit(false);
					Integer transCount = 0;
					for (应预收款收据Entity o : 应预收款收据List) {
						if (Datatrans.estateIdList.containsKey(o.get单元ID())) {
							stmt.setString(1, o.get单据编号());
							stmt.setDate(2, new Date(o.get收款日期().getTime()));
							stmt.setString(3, o.get姓名());
							stmt.setBigDecimal(4, o.get金额());
							stmt.setString(5, o.get收款方式());
							stmt.setString(6, o.get备注());
							stmt.setString(7, o.get经办人());
							stmt.setString(8, o.get单据类别());
							stmt.setInt(9, 0);
							if (null != o.get是否打印()) {
								stmt.setInt(10, o.get是否打印());
							} else {
								stmt.setNull(10, Types.INTEGER);
							}
							stmt.setInt(11, Datatrans.estateIdList.get(o.get单元ID()));
							stmt.setString(12, o.get收据ID());
							stmt.setInt(13, 0);
							stmt.setInt(14, null != o.get单据类别() && o.get单据类别().indexOf("发票") >= 0 ? 1 : 0);
							stmt.addBatch();
							transCount++;
							if (transCount % 1000 == 0) {
								stmt.executeBatch();
								conn.commit();
								log.info("还剩余" + (应预收款收据List.size() - transCount) + "张收据未割接！");
							}
						} else {
							log.info("割接异常====>>>单元Id(" + o.get单元ID() + ")割接不成功！！！");
						}
					}
					stmt.executeBatch();
					conn.commit();
					result.put("割接结果", "共有收据" + 应预收款收据List.size() + "张，本次割接" + transCount + "个");

				}
			});
			session.close();
			Datatrans.estateIdList.clear();
		} else {
			log.info("estateIdList不能为空！！！");
			result.put("割接失败", "estateIdList不能为空！！！");
		}
		return result;
	}

	// 实收款
	protected HashMap<String, String> transShishoukuan(List<实收款Entity> 实收款List) {
		HashMap<String, String> result = new HashMap<>();

		getYingshoukuanIdList();
		getShoujuIdList();

		if (Datatrans.yingshoukuanIdList.size() > 0 && Datatrans.shoujuIdList.size() > 0) {
			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
			Session session = sessionFactory.openSession();

			session.doWork(new Work() {
				@Override
				public void execute(Connection conn) throws SQLException {

					StringBuilder sql = new StringBuilder(
							"insert into t_community_receipt_receivables(receivedAmount,currentAmount,receivablesId,receiptId) values(?,?,?,?)");

					PreparedStatement stmt = conn.prepareStatement(sql.toString());
					conn.setAutoCommit(false);
					Integer transCount = 0;
					for (实收款Entity o : 实收款List) {
						stmt.setBigDecimal(1, o.get已收金额());
						stmt.setBigDecimal(2, o.get本次收款());
						if (Datatrans.yingshoukuanIdList.containsKey(o.getId().get应收款ID())
								&& Datatrans.shoujuIdList.containsKey(o.getId().get收据ID())) {

							stmt.setInt(3, Datatrans.yingshoukuanIdList.get(o.getId().get应收款ID()));
							stmt.setInt(4, Datatrans.shoujuIdList.get(o.getId().get收据ID()));

						}
						if (!Datatrans.yingshoukuanIdList.containsKey(o.getId().get应收款ID())) {
							CommunityReceivablesEntity receivables = communityReceivablesDao.getUniqueByHql(
									"from CommunityReceivablesEntity a where a.oldId='" + o.getId().get应收款ID() + "'");
							stmt.setInt(3, receivables.getId());
						}
						if (!Datatrans.shoujuIdList.containsKey(o.getId().get收据ID())) {
							CommunityReceiptEntity receipt = communityReceiptDao.getUniqueByHql(
									"from CommunityReceiptEntity a where a.oldId='" + o.getId().get收据ID() + "'");
							stmt.setInt(4, receipt.getId());
						}

						stmt.addBatch();
						transCount++;
						if (transCount % 111 == 0) {
							stmt.executeBatch();
							conn.commit();
							stmt.clearBatch();
							log.info("还剩余" + (实收款List.size() - transCount) + "笔实收款未割接！");
						}

					}
					stmt.executeBatch();
					conn.commit();
					result.put("割接结果", "共有实收款" + 实收款List.size() + "笔，本次割接" + transCount + "个");

				}
			});
			session.close();
		} else {
			if (Datatrans.yingshoukuanIdList.size() == 0) {
				log.info("yingshoukuanIdList不能为空！！！");
				result.put("割接失败", "payItemIdList不能为空！！！");
			} else if (Datatrans.shoujuIdList.size() == 0) {
				log.info("shoujuIdList不能为空！！！");
				result.put("割接失败", "shoujuIdList不能为空！！！");
			} else {
				log.info("yingshoukuanIdList和shoujuIdList不能为空！！！");
				result.put("割接失败", "yingshoukuanIdList和shoujuIdList不能为空！！！");
			}
		}
		return result;
	}

	// 财务其他收费应收和收据
	protected HashMap<String, String> transOtherFee(List<财务其他收费Entity> 其他收费List) {
		HashMap<String, String> result = new HashMap<>();

		getEstateIdList();
		getPayItemIdList();

		List<CommunityPayItemsEntity> estateList = communityPayItemsDao.getListByHql("from CommunityPayItemsEntity a");
		estateList.forEach(o -> {
			CommunityCache.payItemsList.put(o.getId(), o);
		});

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {

				conn.setAutoCommit(false);
				// 应收款
				StringBuilder sql1 = new StringBuilder(
						"insert into t_community_receivables(chargeCategory,chargeSource,comment,endTime,oldId,"
								+ "payItemsName,paymentPeriod,receivableAmount,receivableDate,"
								+ "receivedAmount,startTime,estateId,payItemId) values(?,?,?,?,?,?,?,?,?,?,?,?,?)");
				PreparedStatement stmt1 = conn.prepareStatement(sql1.toString());
				// 收据
				StringBuilder sql2 = new StringBuilder(
						"insert into t_community_receipt(receiptCode,receiptDate,payerName,amount,paymentMethod,comment,"
								+ "agent,receiptType,paymentType,printNum,estateId,oldId,estateAddress,feeType,haveInvoice) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
				PreparedStatement stmt2 = conn.prepareStatement(sql2.toString());

				Integer transCount = 0;
				for (财务其他收费Entity o : 其他收费List) {
					if (Datatrans.estateIdList.containsKey(o.get单元ID())) {
						// 应收款

						stmt1.setString(1,
								Datatrans.payItemNameList.containsKey(o.get收费项目())
										? CommunityPayItemsEntity.getChargeCategoryStr(CommunityCache.payItemsList
												.get(Datatrans.payItemNameList.get(o.get收费项目())).getChargeCategory())
										: "");
						stmt1.setString(2, "其他收费");
						stmt1.setString(3, o.get备注());
						stmt1.setDate(4, new Date(o.get收款日期().getTime()));
						stmt1.setString(5, o.get收据ID());

						stmt1.setString(6, o.get收费项目());
						stmt1.setDate(7, new Date(o.get收款日期().getTime()));
						stmt1.setBigDecimal(8, o.get金额());
						stmt1.setDate(9, new Date(o.get收款日期().getTime()));
						stmt1.setBigDecimal(10, o.get金额());
						stmt1.setDate(11, new Date(o.get收款日期().getTime()));
						stmt1.setInt(12, Datatrans.estateIdList.get(o.get单元ID()));

						if (o.get收费项目().equals("装修施工证工本费") || o.get收费项目().equals("证件工本费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("证件工本费"));
						} else if (o.get收费项目().trim().equals("IC卡工本费") || o.get收费项目().trim().equals("IC工卡本费")
								|| o.get收费项目().trim().equals("IC工本费") || o.get收费项目().trim().equals("工本费")
								|| o.get收费项目().trim().equals("远程卡工本费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("工本费"));
						} else if (o.get收费项目().equals("业主过桥IC卡工本、服务费") || o.get收费项目().equals("过卡IC卡工本、服务费")
								|| o.get收费项目().equals("过桥IC卡工本\\服务费") || o.get收费项目().equals("过桥IC卡工本、服务费")
								|| o.get收费项目().equals("过桥IC卡工本服务费") || o.get收费项目().equals("过桥IC工本、服务费")
								|| o.get收费项目().equals("过桥卡IC卡工本、服务费") || o.get收费项目().equals("过桥IC卡工本费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("过桥IC卡工本费、服务费"));
						} else if (o.get收费项目().equals("临时停车收费") || o.get收费项目().equals("临时停车费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("临时停车费"));
						} else if (o.get收费项目().equals("代收代办房屋办证费") || o.get收费项目().equals("代收代缴房屋办证费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("代收代缴房屋办证费"));
						} else if (o.get收费项目().equals("代收三水大桥过桥费") || o.get收费项目().equals("代收过三水大桥费")
								|| o.get收费项目().equals("代收过三水大桥过桥费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("代收三水大桥过桥费"));
						} else if (o.get收费项目().equals("代缴社保费") || o.get收费项目().equals("代购社保款")
								|| o.get收费项目().equals("代购社保费") || o.get收费项目().equals("过桥IC卡工本、服务费")
								|| o.get收费项目().equals("社保费") || o.get收费项目().equals("收回代交社保费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("代缴社保费"));
						} else if (o.get收费项目().equals("餐费") || o.get收费项目().equals("伙食费")
								|| o.get收费项目().equals("员工餐费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("员工餐费"));
						} else if (o.get收费项目().equals("垃圾清运费") || o.get收费项目().equals("代收垃圾清运费")
								|| o.get收费项目().equals("装修垃圾清运费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("代收垃圾清运费"));
						} else if (o.get收费项目().equals("宿舍洗衣机洗衣费") || o.get收费项目().equals("宿舍洗衣机费用")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("宿舍洗衣机费用"));
						} else if (o.get收费项目().equals("日常办公费") || o.get收费项目().equals("日常办公费用")
								|| o.get收费项目().equals("复印费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("日常办公费"));
						} else if (o.get收费项目().equals("工作服折旧费") || o.get收费项目().equals("工服费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("工服费"));
						} else if (o.get收费项目().equals("代收工程维修款") || o.get收费项目().equals("工程维修")
								|| o.get收费项目().equals("有偿维修服务费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("工程维修"));
						} else if (o.get收费项目().equals("施工服务费") || o.get收费项目().equals("施工管理费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("施工服务费"));
						} else if (o.get收费项目().equals("有偿清洁服务") || o.get收费项目().equals("有偿清洁服务费")
								|| o.get收费项目().equals("有偿服务清洁费") || o.get收费项目().equals("开荒清洁费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("有偿清洁服务费"));
						} else if (o.get收费项目().equals("有偿服务费") || o.get收费项目().equals("综合有偿服务费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("有偿服务费"));
						} else if (o.get收费项目().equals("有偿绿化服务") || o.get收费项目().equals("有偿绿化服务费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("有偿绿化服务费"));
						} else if (o.get收费项目().equals("损害财产赔偿款") || o.get收费项目().equals("损害财产赔偿费")
								|| o.get收费项目().equals("损坏财产赔偿款")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("损害财产赔偿费"));
						} else if (o.get收费项目().equals("自行车使用款") || o.get收费项目().equals("自行车款")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("自行车使用款"));
						} else if (o.get收费项目().equals("装修管理服务费") || o.get收费项目().equals("装修管理费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("装修管理服务费"));
						} else if (o.get收费项目().equals("运费") || o.get收费项目().equals("运输费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("运输费"));
						} else if (o.get收费项目().equals("开锁服务费") || o.get收费项目().equals("车辆开锁费")
								|| o.get收费项目().equals("违规停放车辆")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("车辆开锁服务费"));
						} else if (o.get收费项目().equals("沙石费用") || o.get收费项目().equals("砂石等费用")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("砂石等费用"));
						} else if (o.get收费项目().equals("损坏绿化赔偿款") || o.get收费项目().equals("损害绿化赔偿款")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("损害绿化赔偿款"));
						} else if (o.get收费项目().equals("销售废品") || o.get收费项目().equals("废品款")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("废品款"));
						} else if (o.get收费项目().equals("施工保证金") || o.get收费项目().equals("装修保证金")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("装修保证金"));
						} else if (o.get收费项目().equals("收还借款") || o.get收费项目().equals("还借款")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("还借款"));
						} else if (o.get收费项目().equals("代收商铺租金") || o.get收费项目().equals("代收租金")
								|| o.get收费项目().equals("商铺租金")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("代收商铺租金"));
						} else if (o.get收费项目().equals("电费") || o.get收费项目().equals("宿舍电费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("合作单位"));
						} else if (o.get收费项目().equals("水费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("楼梯的水费"));
						} else if (o.get收费项目().equals("公摊电费")) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("楼梯的公摊电费"));
						} else if (o.get收费项目().indexOf("违约金") >= 0 || o.get收费项目().indexOf("滞纳金") >= 0) {
							stmt1.setInt(13, Datatrans.payItemNameList.get("违约金收费"));
						} else if (Datatrans.payItemNameList.containsKey(o.get收费项目())) {
							stmt1.setInt(13, Datatrans.payItemNameList.get(o.get收费项目()));
						} else {
							stmt1.setNull(13, Types.INTEGER);
						}

						stmt1.addBatch();
						transCount++;

						// 收据
						stmt2.setString(1, o.get单据编号());
						stmt2.setDate(2, new Date(o.get收款日期().getTime()));
						stmt2.setString(3, o.get姓名());
						stmt2.setBigDecimal(4, o.get金额());
						stmt2.setString(5, o.get收款方式());
						stmt2.setString(6, o.get备注());
						stmt2.setString(7, o.get经办人());
						stmt2.setString(8, o.get单据类别());
						stmt2.setInt(9, 0);
						stmt2.setInt(10, 0);
						stmt2.setInt(11, Datatrans.estateIdList.get(o.get单元ID()));
						stmt2.setString(12, o.get收据ID());
						stmt2.setString(13, o.get单元地址());
						stmt2.setInt(14, 1);
						stmt2.setInt(15, 0);
						stmt2.addBatch();

						if (transCount % 300 == 0) {
							stmt1.executeBatch();
							stmt2.executeBatch();
							conn.commit();
							log.info("其他收费第一阶段还剩余" + (其他收费List.size() - transCount) + "条数据未割接！");
						}

					}

				}
				stmt1.executeBatch();
				stmt2.executeBatch();
				conn.commit();

			}
		});
		Datatrans.yingshoukuanIdList.clear();
		Datatrans.shoujuIdList.clear();
		log.info("其它收费应收和收据割接完毕");
		session.close();
		return result;
	}

	// 财务其他收费实收退款
	protected HashMap<String, String> transOtherFeeDeposit(List<财务其他收费Entity> 其他收费List) {
		HashMap<String, String> result = new HashMap<>();

		getEstateIdList();
		getYingshoukuanIdList();
		getShoujuIdList();

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {

				conn.setAutoCommit(false);

				Integer transCount = 0;

				transCount = 0;
				// 实收
				StringBuilder sql3 = new StringBuilder(
						"insert into t_community_receipt_receivables(receivedAmount,currentAmount,receivablesId,receiptId) values(?,?,?,?)");
				PreparedStatement stmt3 = conn.prepareStatement(sql3.toString());
				// 退款
				StringBuilder sql4 = new StringBuilder(
						"insert into t_community_receivables_changes(approver,changeAmount,changeDate,changeType,comment,"
								+ "oldId,payItemsName,receivableAmount,receivableDate,receivedAmount,"
								+ "recorder,receivablesId,estateId,receiptId,refundMethodName) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
				PreparedStatement stmt4 = conn.prepareStatement(sql4.toString());
				for (财务其他收费Entity o : 其他收费List) {
					if (Datatrans.yingshoukuanIdList.containsKey(o.get收据ID())) {
						stmt3.setBigDecimal(1, new BigDecimal("0"));
						stmt3.setBigDecimal(2, o.get金额());
						stmt3.setInt(3, Datatrans.yingshoukuanIdList.get(o.get收据ID()));
						stmt3.setInt(4, Datatrans.shoujuIdList.get(o.get收据ID()));
						stmt3.addBatch();

						if (null != o.get退款日期()) {
							stmt4.setString(1, "");
							stmt4.setBigDecimal(2, o.get退款金额());
							stmt4.setDate(3, new Date(o.get退款日期().getTime()));
							stmt4.setInt(4, 2);
							stmt4.setString(5, o.get退款备注());
							stmt4.setString(6, o.get收据ID());
							stmt4.setString(7, o.get收费项目().replaceAll("滞纳金", "违约金"));
							stmt4.setBigDecimal(8, o.get金额());
							stmt4.setDate(9, new Date(o.get收款日期().getTime()));
							stmt4.setBigDecimal(10, o.get金额());
							stmt4.setString(11, o.get退款人());
							stmt4.setInt(12, Datatrans.yingshoukuanIdList.get(o.get收据ID()));
							stmt4.setInt(13, Datatrans.estateIdList.get(o.get单元ID()));
							stmt4.setInt(14, Datatrans.shoujuIdList.get(o.get收据ID()));
							stmt4.setString(15,o.get退款方式());
							stmt4.addBatch();
						}
						transCount++;
						if (transCount % 300 == 0) {
							stmt3.executeBatch();
							stmt4.executeBatch();
							conn.commit();
							log.info("其他收费第二阶段还剩余" + (其他收费List.size() - transCount) + "条数据未割接！");
						}
					}
				}
				stmt3.executeBatch();
				stmt4.executeBatch();
				conn.commit();
			}
		});
		log.info("其它收费割接完毕");
		session.close();
		return result;
	}

	/*
	 * 正式操作前需要调整分摊时间 allocationDate、allocationMonth的值要进行修改
	 */
	protected HashMap<String, String> transZongbiaoFentan(List<分摊分摊用量临时Entity> 总表用量分摊List) {
		HashMap<String, String> result = new HashMap<>();

		getMeterIdList();

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {

				StringBuilder sql = new StringBuilder(
						"insert into t_community_meter_allocation(allocationAmount,allocationDate,allocationMonth,allocationNum,"
								+ "unitPrice,allocationYear,meterId,oldId) values(?,?,?,?,?,?,?,?)");

				PreparedStatement stmt = conn.prepareStatement(sql.toString());
				conn.setAutoCommit(false);
				Integer transCount = 0;
				for (分摊分摊用量临时Entity o : 总表用量分摊List) {
					stmt.setBigDecimal(1, o.get分摊后金额());
					try {
						// 这个字段的值要修改为正式的分摊时间
						stmt.setDate(2, new Date(DateUtil.parseShortFormat("2022-09-30").getTime()));
					} catch (SQLException | ParseException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
					// 这个字段的值要修改成正式的分摊月份
					stmt.setInt(3, 9);
					stmt.setBigDecimal(4, o.get分摊后用量());
					if (o.get分摊后用量().intValue() != 0) {
						stmt.setBigDecimal(5, o.get分摊后金额().divide(o.get分摊后用量(), 4, BigDecimal.ROUND_HALF_UP));
					} else {
						CommunityMeterAttributesEntity attribute = communityMeterAttributesDao.getUniqueByHql(
								"select a from CommunityMeterAttributesEntity a inner join a.meterList b where b.id='"
										+ Datatrans.meterIdList.get(o.get总表ID()) + "'");
						stmt.setBigDecimal(5, attribute.getUnitPrice());
					}

					stmt.setInt(6, 2022);
					stmt.setInt(7, Datatrans.meterIdList.get(o.get总表ID()));
					stmt.setString(8, o.get总表ID());
					;
					stmt.addBatch();
					transCount++;
					if (transCount % 100 == 0) {
						stmt.executeBatch();
						conn.commit();
						log.info("还剩余" + (总表用量分摊List.size() - transCount) + "条数据未割接！");
					}
				}
				stmt.executeBatch();
				conn.commit();
				result.put("割接结果", "共有收费设定" + 总表用量分摊List.size() + "个，本次割接" + transCount + "个");

			}
		});
		session.close();
		return result;
	}

	// 单元总表用量分摊
	protected HashMap<String, String> transDanyuanZongbiaoFentan(List<分摊单元分摊用量临时Entity> 单元总表用量List) {
		HashMap<String, String> result = new HashMap<>();

		getEstateIdList();
		getZongbiaoFentanIdList();

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {

				StringBuilder sql1 = new StringBuilder(
						"insert into t_community_meter_allocation_item(allocationAmount,allocationNum,allocationId,estateid,oldData,oldId) values(?,?,?,?,?,?)");
				PreparedStatement stmt1 = conn.prepareStatement(sql1.toString());
				conn.setAutoCommit(false);
				Integer transCount = 0;
				for (分摊单元分摊用量临时Entity o : 单元总表用量List) {
					if (Datatrans.zongbiaoFentanIdList.containsKey(o.getId().get总表ID())) {
						stmt1.setBigDecimal(1, o.get分摊金额());
						stmt1.setBigDecimal(2, o.get分摊用量());
						stmt1.setInt(3, Datatrans.zongbiaoFentanIdList.get(o.getId().get总表ID()));
						stmt1.setInt(4, Datatrans.estateIdList.get(o.getId().get单元ID()));
						stmt1.setString(5, o.getId().get总表ID());
						stmt1.setString(6, o.getId().get单元ID());
						stmt1.addBatch();
						transCount++;
						if (transCount % 100 == 0) {
							stmt1.executeBatch();
							conn.commit();
							log.info("还剩余" + (单元总表用量List.size() - transCount) + "条数据未割接！");
						}
					}
				}
				stmt1.executeBatch();
				conn.commit();
				result.put("割接结果", "共有收费设定" + 单元总表用量List.size() + "个，本次割接" + transCount + "个");
			}
		});
		session.close();
		return result;
	}

	// 银行划账单元筛选
	protected HashMap<String, String> transYinhangHuazhangDanyuanShaixuan(List<Object[]> 划账List) {
		HashMap<String, String> result = new HashMap<>();

		getEstateIdList();
		getPayItemIdList();

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {

				StringBuilder sql = new StringBuilder(
						"insert into t_community_bankdeposit_estate_payitems(estateId,payItemsId,depositType) values(?,?,?)");
				PreparedStatement stmt = conn.prepareStatement(sql.toString());
				conn.setAutoCommit(false);
				Integer transCount = 0;
				for (Object[] o : 划账List) {
					try {
						Integer estateId = Datatrans.estateIdList.get(o[0].toString());
						Integer payItemsId = null != o[1] ? Datatrans.payItemIdList.get(o[1].toString())
								: Datatrans.payItemNameList.get(o[2].toString());
						Integer depositType = Integer.valueOf(o[3].toString());

						if (null != payItemsId) {
							stmt.setInt(1, estateId);
							stmt.setInt(2, payItemsId);
							stmt.setInt(3, depositType);
							stmt.addBatch();
							transCount++;
						} else {
							log.info(estateId + ":::" + o[2].toString() + "割接失败！！！");
						}
					} catch (Exception e) {
						e.printStackTrace();
						log.info(o[0].toString() + ":::" + o[2].toString() + "割接失败！！！");
					}
				}
				stmt.executeBatch();
				conn.commit();

				result.put("割接结果", "共割接银行划账单元筛选数据" + transCount + "个");
			}
		});
		session.close();
		return result;
	}

	// 离退户主
	protected HashMap<String, String> transHuzhu1(List<户主Entity> 户主List) {
		HashMap<String, String> result = new HashMap<>();

		getEstateIdList();

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {

				StringBuilder sql = new StringBuilder(
						"insert into t_account(oldId,userName,sex,phone,birthday,nation,nativePlace,policeStation,company,homeAddress,postalCode,idCard,idType,comment,contactPerson,emergencyContact,accountType,invoiceType,buyersName,buyersAddress,buyersBankAccount,paytaxNo,businessType,memberType,homePhone) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

				PreparedStatement stmt = conn.prepareStatement(sql.toString());
				conn.setAutoCommit(false);
				Integer transCount = 0;
				for (户主Entity o : 户主List) {
					stmt.setString(1, o.getId().get户主id());
					stmt.setString(2, o.get姓名());
					stmt.setInt(3, o.get性别().equals("男") ? 1 : 0);
					stmt.setString(4, getPhone(o.get电话()));
					if (null != o.get出生日期()) {
						stmt.setDate(5, new Date(o.get出生日期().getTime()));
					} else {
						stmt.setNull(5, Types.DATE);
					}
					stmt.setString(6, o.get民族());
					stmt.setString(7, o.get籍贯());
					stmt.setString(8, o.get户口所在地派出所());
					stmt.setString(9, o.get工作单位());
					stmt.setString(10, o.get联系地址());
					stmt.setString(11, o.get邮政编码());
					stmt.setString(12, o.get证件号码());
					stmt.setString(13, o.get证件类型());
					stmt.setString(14, o.get备注());
					stmt.setString(15, o.get紧急联系人姓名());
					stmt.setString(16, o.get紧急联系方式());
					stmt.setString(17, "C");
					stmt.setString(18, o.get开票类型());
					stmt.setString(19, o.get购方名称());
					stmt.setString(20, o.get购方地址电话());
					stmt.setString(21, o.get购方银行帐号());
					stmt.setString(22, o.get购方税号());
					stmt.setString(23, o.get购方企业类型());
					stmt.setInt(24, 0);
					stmt.setString(25, o.get电话());
					stmt.addBatch();
					transCount++;
				}
				stmt.executeBatch();
				conn.commit();

				getHuzhuIdList();

				StringBuilder sql1 = new StringBuilder(
						"insert into t_community_member_property(billingDate,isCurrentMember,isCurrentOwner,memberType,recordDate,terminationDate,memberId,propertyId) values(?,?,?,?,?,?,?,?)");
				stmt = conn.prepareStatement(sql1.toString());

				StringBuilder sql1_1 = new StringBuilder("update t_community_property set estateState=? where id=?");
				PreparedStatement stmt2 = conn.prepareStatement(sql1_1.toString());

				for (户主Entity o : 户主List) {
					if (null != o.get计费日期()) {
						stmt.setDate(1, new Date(o.get计费日期().getTime()));
					} else {
						stmt.setNull(1, Types.DATE);
					}
					stmt.setInt(2, 0);
					stmt.setInt(3, 0);
					stmt.setInt(4, 0);
					if (null != o.get入住日期()) {
						stmt.setDate(5, new Date(o.get入住日期().getTime()));
					} else {
						stmt.setNull(5, Types.DATE);
					}
					if (null != o.get离退日期()) {
						stmt.setDate(6, new Date(o.get离退日期().getTime()));
					} else {
						stmt.setNull(6, Types.DATE);
					}
					stmt.setInt(7, Datatrans.huzhuIdList.get(o.getId().get户主id()));
					stmt.setInt(8, Datatrans.estateIdList.get(o.getId().get单元id()));
					stmt.addBatch();

					stmt2.setInt(1, 4);
					stmt2.setInt(2, Datatrans.estateIdList.get(o.getId().get单元id()));
					stmt2.addBatch();
				}
				stmt.executeBatch();
				stmt2.executeBatch();

				conn.commit();

				StringBuilder sql2 = new StringBuilder(
						"insert into t_community_payment_account(state,bankAccount,bankName,accountName,memberId) values(?,?,?,?,?)");
				stmt = conn.prepareStatement(sql2.toString());
				for (户主Entity o : 户主List) {
					if (StringUtils.isNotEmpty(o.get银行帐户())) {
						stmt.setInt(1, 1);
						stmt.setString(2, o.get银行帐户());
						stmt.setString(3, o.get划帐银行());
						stmt.setString(4, o.get帐号名());
						stmt.setInt(5, Datatrans.huzhuIdList.get(o.getId().get户主id()));
						stmt.addBatch();
					}
				}
				stmt.executeBatch();
				conn.commit();

				result.put("割接结果", "共有离退户主" + 户主List.size() + "个，本次割接" + transCount + "个");

			}
		});
		session.close();
		return result;
	}

	// 户主
	protected HashMap<String, String> transHuzhu2(List<户主Entity> 户主List) {
		HashMap<String, String> result = new HashMap<>();

		getEstateIdList();

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@SuppressWarnings("unchecked")
			@Override
			public void execute(Connection conn) throws SQLException {

				StringBuilder sql = new StringBuilder(
						"insert into t_account(oldId,userName,sex,phone,birthday,nation,nativePlace,"
						+ "policeStation,company,homeAddress,postalCode,idCard,idType,comment,"
						+ "contactPerson,emergencyContact,accountType,invoiceType,memberType,homePhone) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
				int temp = 0;
				PreparedStatement stmt = conn.prepareStatement(sql.toString());
				conn.setAutoCommit(false);
				Integer transCount = 0;
				for (户主Entity o : 户主List) {
					if (temp == 0 || !o.getId().get户主id().equals("042A5E25-F1AC-440B-98EA-0E4D45CC9923")) {
						stmt.setString(1, o.getId().get户主id());
						stmt.setString(2, o.get姓名());
						stmt.setInt(3, o.get性别().equals("男") ? 1 : 0);
						stmt.setString(4, getPhone(o.get电话()));
						if (null != o.get出生日期()) {
							stmt.setDate(5, new Date(o.get出生日期().getTime()));
						} else {
							stmt.setNull(5, Types.DATE);
						}
						stmt.setString(6, o.get民族());
						stmt.setString(7, o.get籍贯());
						stmt.setString(8, o.get户口所在地派出所());
						stmt.setString(9, o.get工作单位());
						stmt.setString(10, o.get联系地址());
						stmt.setString(11, o.get邮政编码());
						stmt.setString(12, o.get证件号码());
						stmt.setString(13, o.get证件类型());
						stmt.setString(14, o.get备注());
						stmt.setString(15, o.get紧急联系人姓名());
						stmt.setString(16, o.get紧急联系方式());
						stmt.setString(17, "C");
						stmt.setString(18, o.get开票类型());
						stmt.setInt(19, 0);
						stmt.setString(20, o.get电话());
						stmt.addBatch();
						transCount++;
						if (o.getId().get户主id().equals("042A5E25-F1AC-440B-98EA-0E4D45CC9923")) {
							temp++;
						}
					}
				}
				stmt.executeBatch();
				conn.commit();

				StringBuilder sql4 = new StringBuilder(
						"UPDATE t_account SET username = REPLACE(REPLACE(REPLACE(username,CHAR(10),''),CHAR(13),''),CHAR(9),'');");
				PreparedStatement stmt4 = conn.prepareStatement(sql4.toString());
				stmt4.addBatch();
				stmt4.executeBatch();

				conn.commit();

				getHuzhuIdList();

				StringBuilder sql1 = new StringBuilder(
						"insert into t_community_member_property(billingDate,isCurrentMember,isCurrentOwner,memberType,recordDate,terminationDate,memberId,propertyId) values(?,?,?,?,?,?,?,?)");
				stmt = conn.prepareStatement(sql1.toString());

				StringBuilder sql1_1 = new StringBuilder("update t_community_property set estateState=? where id=?");
				PreparedStatement stmt2 = conn.prepareStatement(sql1_1.toString());

				for (户主Entity o : 户主List) {
					if (null != o.get计费日期()) {
						stmt.setDate(1, new Date(o.get计费日期().getTime()));
					} else {
						stmt.setNull(1, Types.DATE);
					}
					stmt.setInt(2, 1);
					stmt.setInt(3, 1);
					stmt.setInt(4, 0);
					if (null != o.get入住日期()) {
						stmt.setDate(5, new Date(o.get入住日期().getTime()));
					} else {
						stmt.setNull(5, Types.DATE);
					}
					if (null != o.get离退日期()) {
						stmt.setDate(6, new Date(o.get离退日期().getTime()));
					} else {
						stmt.setNull(6, Types.DATE);
					}

					stmt.setInt(7, Datatrans.huzhuIdList.get(o.getId().get户主id()));
					stmt.setInt(8, Datatrans.estateIdList.get(o.getId().get单元id()));
					stmt.addBatch();

					String state = o.get状态();
					stmt2.setInt(1, state.equals("出租") ? 3
							: state.equals("装修中") ? 2 : state.equals("未收楼") ? 0 : state.equals("自住") ? 5 : 1);
					stmt2.setInt(2, Datatrans.estateIdList.get(o.getId().get单元id()));
					stmt2.addBatch();
				}
				stmt.executeBatch();
				stmt2.executeBatch();
				conn.commit();
				temp = 0;
				StringBuilder sql2 = new StringBuilder(
						"insert into t_community_payment_account(state,bankAccount,bankName,accountName,memberId) values(?,?,?,?,?)");
				stmt = conn.prepareStatement(sql2.toString());
				for (户主Entity o : 户主List) {
					if ((temp == 0 || !o.getId().get户主id().equals("042A5E25-F1AC-440B-98EA-0E4D45CC9923"))
							&& StringUtils.isNotEmpty(o.get银行帐户())) {
						stmt.setInt(1, 1);
						stmt.setString(2, o.get银行帐户());
						stmt.setString(3, o.get划帐银行());
						stmt.setString(4, o.get帐号名());
						stmt.setInt(5, Datatrans.huzhuIdList.get(o.getId().get户主id()));
						stmt.addBatch();
						if (o.getId().get户主id().equals("042A5E25-F1AC-440B-98EA-0E4D45CC9923")) {
							temp++;
						}
					}
				}
				stmt.executeBatch();
				conn.commit();

				List<Object[]> paymentList = communityPropertyDao.createSQLQuery(
						"SELECT b.id,a.propertyid FROM t_community_member_property a,t_community_payment_account b WHERE b.state=1 AND a.memberid =b.memberid")
						.list();

				StringBuilder sql3 = new StringBuilder("update t_community_property set paymentAccountId=? where id=?");
				stmt = conn.prepareStatement(sql3.toString());
				for (Object[] o : paymentList) {
					stmt.setInt(1, Integer.parseInt(o[0].toString()));
					stmt.setInt(2, Integer.parseInt(o[1].toString()));
					stmt.addBatch();
				}
				stmt.executeBatch();
				conn.commit();
				result.put("割接结果", "共有离退户主" + 户主List.size() + "个，本次割接" + transCount + "个");

			}
		});
		session.close();
		return result;

	}

	// 住户成员
	protected HashMap<String, String> transZhuhuchengyuan(List<住户成员Entity> 住户成员List) {
		HashMap<String, String> result = new HashMap<>();

//			getEstateIdList();
//			getZongbiaoFentanIdList();
//
//			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
//			Session session = sessionFactory.openSession();
//
//			session.doWork(new Work() {
//				@Override
//				public void execute(Connection conn) throws SQLException {
//
//					StringBuilder sql = new StringBuilder(
//							"insert into t_community_meter_allocation_item(allocationAmount,allocationNum,allocationId,estateid,oldData,oldId) values(?,?,?,?,?,?)");
//
//					PreparedStatement stmt = conn.prepareStatement(sql.toString());
//					conn.setAutoCommit(false);
//					Integer transCount = 0;
//					for (分摊单元分摊用量临时Entity o : 单元总表用量List) {
//						if (o.get分摊用量().intValue() != 0 && o.get分摊金额().intValue() != 0) {
//							stmt.setBigDecimal(1, o.get分摊金额());
//							stmt.setBigDecimal(2, o.get分摊用量());
//							stmt.setInt(3, Datatrans.zongbiaoFentanIdList.get(o.getId().get总表Id()));
//							stmt.setInt(4, Datatrans.estateIdList.get(o.getId().get单元Id()));
//							stmt.setString(5, o.getId().get总表Id());
//							stmt.setString(6, o.getId().get单元Id());
//							stmt.addBatch();
//							transCount++;
//							if (transCount % 100 == 0) {
//								stmt.executeBatch();
//								conn.commit();
//								log.info("还剩余" + (单元总表用量List.size() - transCount) + "条数据未割接！");
//							}
//						}
//					}
//					stmt.executeBatch();
//					conn.commit();
//					result.put("割接结果", "共有收费设定" + 单元总表用量List.size() + "个，本次割接" + transCount + "个");
//
//				}
//			});
//			session.close();
		return result;
	}

	// 车位
	protected HashMap<String, String> transChewei(List<Object[]> 车位List) {
		HashMap<String, String> result = new HashMap<>();

		getEstateIdList();
		getHuzhuIdList();

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {

				StringBuilder sql2 = new StringBuilder(
						"insert into t_community_member_property(billingDate,isCurrentMember,isCurrentOwner,memberType,"
								+ "terminationDate,endDate,comment,propertyId,memberid,parentPropertyId,carInfo) "
								+ "values(?,?,?,?,?,?,?,?,?,?,?)");
				PreparedStatement stmt2 = conn.prepareStatement(sql2.toString());

				conn.setAutoCommit(false);
				Integer transCount = 0;
				for (Object[] o : 车位List) {

					stmt2.setDate(1,
							new Date(Date.from(LocalDateTime
									.parse(o[7].toString().substring(0, o[7].toString().length() - 2),
											DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
									.atZone(ZoneId.systemDefault()).toInstant()).getTime()));
					stmt2.setInt(2, null != o[9] ? 0 : 1);
					stmt2.setInt(3, null != o[9] ? 0 : 1);
					if (null != o[10]) {
						Date endDate = new Date(Date.from(LocalDateTime
								.parse(o[10].toString().substring(0, o[10].toString().length() - 2),
										DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
								.atZone(ZoneId.systemDefault()).toInstant()).getTime());
						Date tempDate = new Date(Date.from(LocalDateTime
								.parse("2030-01-01 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
								.atZone(ZoneId.systemDefault()).toInstant()).getTime());

						stmt2.setInt(4, null != o[12] && (o[12].toString().equals("出售")
								|| (o[12].toString().equals("租用") && endDate.after(tempDate))) ? 0 : 2);
					} else {
						stmt2.setInt(4, null != o[12] && o[12].toString().equals("出售") ? 0 : 2);
					}
					if (null != o[9]) {
						stmt2.setDate(5,
								new Date(Date.from(LocalDateTime
										.parse(o[9].toString().substring(0, o[9].toString().length() - 2),
												DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
										.atZone(ZoneId.systemDefault()).toInstant()).getTime()));
					} else {
						stmt2.setNull(5, Types.DATE);
					}
					if (null != o[10]) {
						stmt2.setDate(6,
								new Date(Date.from(LocalDateTime
										.parse(o[10].toString().substring(0, o[10].toString().length() - 2),
												DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
										.atZone(ZoneId.systemDefault()).toInstant()).getTime()));
					} else {
						stmt2.setNull(6, Types.DATE);
					}

					stmt2.setString(7, null != o[11] ? o[11].toString() : "");
					stmt2.setInt(8, Datatrans.estateIdList.get(o[0].toString()));
					stmt2.setInt(9, Datatrans.huzhuIdList.get(o[16].toString()));
					stmt2.setInt(10, Datatrans.estateIdList.get(o[13].toString()));
					Map<String, String> carInfo = new HashMap<>();
					if (null != o[10]) {
						Date endDate = new Date(Date.from(LocalDateTime
								.parse(o[10].toString().substring(0, o[10].toString().length() - 2),
										DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
								.atZone(ZoneId.systemDefault()).toInstant()).getTime());
						Date tempDate = new Date(Date.from(LocalDateTime
								.parse("2030-01-01 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
								.atZone(ZoneId.systemDefault()).toInstant()).getTime());
						carInfo.put("parkingType", null != o[12]
								? (o[12].toString().equals("租用") && endDate.after(tempDate) ? "出售" : o[12].toString())
								: "租用");
					} else {
						carInfo.put("parkingType", null != o[12] ? o[12].toString() : "");
					}
					carInfo.put("licensePlateNo", null != o[15] ? o[15].toString() : "");
					carInfo.put("characteristics", null != o[5] ? o[5].toString() : "");
					carInfo.put("carOwner", null != o[4] ? o[4].toString() : "");
					carInfo.put("agent", null != o[14] ? o[14].toString() : "");
					try {
						stmt2.setString(11, mapper.writeValueAsString(carInfo));
					} catch (JsonProcessingException | SQLException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
						stmt2.setString(11, "");
					}
					stmt2.addBatch();

					transCount++;
				}
				stmt2.executeBatch();

				conn.commit();
				result.put("割接结果", "共有有效车位" + 车位List.size() + "个，本次割接" + transCount + "个");

			}
		});
		session.close();
		return result;
	}

	// 车位数据
	protected HashMap<String, String> transCheweiData(Map<Integer, String> cheweiData) {
		HashMap<String, String> result = new HashMap<>();

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {

				StringBuilder sql = new StringBuilder("update t_community_property set reservedField=? where id=?");
				PreparedStatement stmt = conn.prepareStatement(sql.toString());
				conn.setAutoCommit(false);
				Integer transCount = 0;

				for (Integer o : cheweiData.keySet()) {
					stmt.setString(1, cheweiData.get(o));
					stmt.setInt(2, o);
					stmt.addBatch();
					transCount++;
				}

				stmt.executeBatch();

				conn.commit();
				result.put("车位数据处理结果", "共处理车位" + transCount + "个");
			}
		});
		session.close();
		return result;
	}

	// 报修数据导入
	protected HashMap<String, String> transRepair(List<住户管理报修登记Entity> 报修List) {
		HashMap<String, String> result = new HashMap<>();

		getEstateIdList();
		getRepairIdList();

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {

				StringBuilder sql = new StringBuilder(
						"insert into t_community_property_service(oldId,propertyId,eventsCode,serviceType,"
								+ "reportTime,client,agent,phone,address,reportContent,serviceEvaluation,handler,"
								+ "dispatchingTime,completionTime,materialCost,laborCost,receiver,serviceState,"
								+ "isGeneratedBills,centent,competentDepartment,serviceContent,feedback,visitCommissione) "
								+ "values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

				StringBuilder sql1 = new StringBuilder("update t_community_receivables set chargeSource=? where id=?");

				PreparedStatement stmt = conn.prepareStatement(sql.toString());
				PreparedStatement stmt1 = conn.prepareStatement(sql1.toString());
				conn.setAutoCommit(false);
				Integer transCount = 0;
				for (住户管理报修登记Entity o : 报修List) {
					if (Datatrans.estateIdList.containsKey(o.get单元ID())
							&& (Datatrans.repairIdList.containsKey(o.get报修ID())
									|| Datatrans.repairIdList.containsKey(o.get维修单号()))) {
						stmt.setString(1, o.get报修ID());
						stmt.setInt(2, Datatrans.estateIdList.get(o.get单元ID()));
						stmt.setString(3, o.get维修单号());
						stmt.setString(4, o.get报修类别());
						stmt.setDate(5, new Date(o.get报修日期().getTime()));
						stmt.setString(6, o.get申请人());
						stmt.setString(7, o.get联系人());
						stmt.setString(8, o.get联系电话());
						stmt.setString(9, o.get维修地点());
						stmt.setString(10, o.get报修内容());
						stmt.setString(11, o.get满意度());
						stmt.setString(12, o.get被派人());
						if (null != o.get派工时间()) {
							stmt.setDate(13, new Date(o.get派工时间().getTime()));
						} else {
							stmt.setNull(13, Types.DATE);
						}

						if (null != o.get完成时间()) {
							stmt.setDate(14, new Date(o.get完成时间().getTime()));
						} else {
							stmt.setNull(14, Types.DATE);
						}

						stmt.setString(15, o.get材料费用().toString());
						stmt.setString(16, o.get人工费用().toString());
						stmt.setString(17, o.get验收人());
						stmt.setString(18, "完成");
						stmt.setInt(19, 1);
						stmt.setString(20, o.get备注());
						stmt.setString(21, o.get工程主管());
						stmt.setString(22, o.get维修内容());
						stmt.setString(23, o.get验收意见());
						stmt.setString(24, o.get验收人());
						stmt.addBatch();

						stmt1.setString(1, "报修单:" + o.get维修单号());
						stmt1.setInt(2, Datatrans.repairIdList.get(o.get报修ID()));
						stmt1.addBatch();

						transCount++;
						if (transCount % 400 == 0) {
							stmt.executeBatch();
							stmt1.executeBatch();
							conn.commit();
//							log.info("还剩余" + (报修List.size() - transCount) + "条数据未割接！");
						}
					} else {
						log.info("单元id:" + o.get单元ID() + ":::报修id:" + o.get报修ID() + "割接失败");
					}

				}

				stmt.executeBatch();
				stmt1.executeBatch();
				conn.commit();
				result.put("割接结果", "共有报修记录" + 报修List.size() + "个，本次割接" + transCount + "个");

			}
		});
		session.close();

		return result;
	}

	/*
	 * 抽取字符串中的电话号码
	 */
	public String getPhone(String phone) {
		String retPhone = "";
		Pattern pattern = Pattern.compile("(1[3-9][0-9])\\d{8}");
		if (StringUtils.isNotEmpty(phone)) {
			Matcher matcher = pattern.matcher(phone);
			while (matcher.find()) {
				retPhone = matcher.group();
				break;
			}
		}
		return retPhone;
	}

	@SuppressWarnings("unchecked")
	protected void getBuildingIdList() {
		if (Datatrans.buildingIdList.keySet().size() == 0) {
			List<Object[]> buildingList = communityBuildingDao
					.createQuery("select a.id,a.oldId from CommunityBuildingEntity a").list();
			buildingList.forEach(o -> {
				Datatrans.buildingIdList.put(o[1].toString(), Integer.valueOf(o[0].toString()));
			});
		}
	}

	@SuppressWarnings("unchecked")
	protected void getShuxingIdList() {
		if (Datatrans.shuxingIdList.keySet().size() == 0) {
			List<Object[]> attributeList = communityMeterAttributesDao
					.createQuery("select a.id,a.oldId from CommunityMeterAttributesEntity a").list();
			attributeList.forEach(o -> {
				Datatrans.shuxingIdList.put(o[1].toString(), Integer.valueOf(o[0].toString()));
			});
		}
	}

	@SuppressWarnings("unchecked")
	protected void getGongshiIdList() {
		if (Datatrans.gongshiIdList.keySet().size() == 0) {
			List<Object[]> formulaList = communityMeterFormulaDao
					.createQuery("select a.id,a.formulaName from CommunityMeterFormulaEntity a").list();
			formulaList.forEach(o -> {
				Datatrans.gongshiIdList.put(o[1].toString(), Integer.valueOf(o[0].toString()));
			});
		}
	}

	@SuppressWarnings("unchecked")
	protected void getMeterIdList() {
		if (Datatrans.meterIdList.keySet().size() == 0) {
			List<Object[]> meterList = communityMeterDao.createQuery("select a.id,a.oldId from CommunityMeterEntity a")
					.list();
			meterList.forEach(o -> {
				Datatrans.meterIdList.put(o[1].toString(), Integer.valueOf(o[0].toString()));
			});
		}
	}

	@SuppressWarnings("unchecked")
	private void refreshMeterIdList() {
		Datatrans.meterIdList.clear();
		List<Object[]> meterList = communityMeterDao.createQuery("select a.id,a.oldId from CommunityMeterEntity a")
				.list();
		meterList.forEach(o -> {
			Datatrans.meterIdList.put(o[1].toString(), Integer.valueOf(o[0].toString()));
		});
	}

	@SuppressWarnings("unchecked")
	protected void getEstateIdList() {
		if (Datatrans.estateIdList.keySet().size() == 0) {
			List<Object[]> estateList = communityEstateDao
					.createQuery("select a.id,a.oldId from CommunityEstateEntity a").list();
			estateList.forEach(o -> {
				Datatrans.estateIdList.put(o[1].toString(), Integer.valueOf(o[0].toString()));
			});
		}
	}

	@SuppressWarnings("unchecked")
	protected void getPayItemIdList() {
		if (Datatrans.payItemIdList.keySet().size() == 0) {
			List<Object[]> payItemList = communityPayItemsDao
					.createQuery(
							"select a.id,a.oldId,a.itemsName from CommunityPayItemsEntity a where a.oldId is not null")
					.list();
			payItemList.forEach(o -> {
				Datatrans.payItemIdList.put(o[1].toString(), Integer.valueOf(o[0].toString()));
				Datatrans.payItemNameList.put(o[2].toString(), Integer.valueOf(o[0].toString()));
			});
		}
	}

	@SuppressWarnings("unchecked")
	protected void getYingshoukuanIdList() {
		if (Datatrans.yingshoukuanIdList.keySet().size() == 0) {
			List<Object[]> yingshoukuanList = communityReceivablesDao
					.createQuery("select a.id,a.oldId from CommunityReceivablesEntity a").list();
			yingshoukuanList.forEach(o -> {
				Datatrans.yingshoukuanIdList.put(o[1].toString(), Integer.valueOf(o[0].toString()));
			});
		}
	}

	@SuppressWarnings("unchecked")
	protected void getShoujuIdList() {
		if (Datatrans.shoujuIdList.keySet().size() == 0) {
			List<Object[]> shoujuIdList = communityReceiptDao
					.createQuery("select a.id,a.oldId from CommunityReceiptEntity a").list();
			shoujuIdList.forEach(o -> {
				Datatrans.shoujuIdList.put(o[1].toString(), Integer.valueOf(o[0].toString()));
			});
		}
	}

	@SuppressWarnings("unchecked")
	protected void getZongbiaoFentanIdList() {
		if (Datatrans.zongbiaoFentanIdList.keySet().size() == 0) {
			List<Object[]> zongbiaoFentanIdList = communityMeterAllocationDao
					.createQuery("select a.id,a.oldId from CommunityMeterAllocationEntity a").list();
			zongbiaoFentanIdList.forEach(o -> {
				Datatrans.zongbiaoFentanIdList.put(o[1].toString(), Integer.valueOf(o[0].toString()));
			});
		}
	}

	@SuppressWarnings("unchecked")
	protected void getHuzhuIdList() {
		Datatrans.huzhuIdList.clear();
		List<Object[]> huzhuIdList = communityMemberDao.createQuery("select a.id,a.oldId from CommunityMemberEntity a")
				.list();
		huzhuIdList.forEach(o -> {
			Datatrans.huzhuIdList.put(o[1].toString(), Integer.valueOf(o[0].toString()));
		});
	}

	@SuppressWarnings("unchecked")
	protected void getRepairIdList() {
		if (Datatrans.repairIdList.keySet().size() == 0) {
			List<Object[]> repairIdList = communityReceiptDao.createQuery(
					"select a.id,a.chargeSource from CommunityReceivablesEntity a where a.chargeCategory='有偿服务费' and a.chargeSource like '报修单%'")
					.list();
			repairIdList.forEach(o -> {
				String repairId = o[1].toString().substring(4);
				Datatrans.repairIdList.put(repairId, Integer.valueOf(o[0].toString()));
			});
		}
	}

	protected List<收费项目其他收费Entity> get其它收费项目List() {
		List<收费项目其他收费Entity> 其他收费项目List = new ArrayList<>();
		收费项目其他收费Entity e1 = new 收费项目其他收费Entity("证件工本费", new BigDecimal("0.00"), "", "其它类", "glc");
		其他收费项目List.add(e1);
		收费项目其他收费Entity e2 = new 收费项目其他收费Entity("工本费", new BigDecimal("0.00"), "元", "有偿服务类", "glc");
		其他收费项目List.add(e2);
		收费项目其他收费Entity e3 = new 收费项目其他收费Entity("过桥IC卡工本费、服务费", new BigDecimal("0.00"), "", "有偿服务类", "");
		其他收费项目List.add(e3);
		收费项目其他收费Entity e4 = new 收费项目其他收费Entity("临时停车费", new BigDecimal("50.00"), "元", "其它类", "");
		其他收费项目List.add(e4);
		收费项目其他收费Entity e5 = new 收费项目其他收费Entity("代收代缴房屋办证费", new BigDecimal("0.00"), "", "其它类", "glc");
		其他收费项目List.add(e5);
		收费项目其他收费Entity e6 = new 收费项目其他收费Entity("代收三水大桥过桥费", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e6);
		收费项目其他收费Entity e7 = new 收费项目其他收费Entity("代缴社保费", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e7);
		收费项目其他收费Entity e8 = new 收费项目其他收费Entity("员工餐费", new BigDecimal("0.00"), "", "其它类", "glc");
		其他收费项目List.add(e8);
		收费项目其他收费Entity e9 = new 收费项目其他收费Entity("代收垃圾清运费", new BigDecimal("0.00"), "", "有偿服务类", "glc");
		其他收费项目List.add(e9);
		收费项目其他收费Entity e10 = new 收费项目其他收费Entity("宿舍洗衣机费用", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e10);
		收费项目其他收费Entity e11 = new 收费项目其他收费Entity("日常办公费", new BigDecimal("0.00"), "元", "有偿服务类", "glc");
		其他收费项目List.add(e11);
		收费项目其他收费Entity e12 = new 收费项目其他收费Entity("工服费", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e12);
		收费项目其他收费Entity e13 = new 收费项目其他收费Entity("工程维修", new BigDecimal("0.00"), "", "有偿服务类", "glc");
		其他收费项目List.add(e13);
		收费项目其他收费Entity e14 = new 收费项目其他收费Entity("施工服务费", new BigDecimal("0.00"), "", "有偿服务类", "glc");
		其他收费项目List.add(e14);
		收费项目其他收费Entity e15 = new 收费项目其他收费Entity("有偿清洁服务费", new BigDecimal("0.00"), "", "有偿服务类", "glc");
		其他收费项目List.add(e15);
		收费项目其他收费Entity e16 = new 收费项目其他收费Entity("有偿服务费", new BigDecimal("0.00"), "", "有偿服务类", "glc");
		其他收费项目List.add(e16);
		收费项目其他收费Entity e17 = new 收费项目其他收费Entity("有偿绿化服务费", new BigDecimal("0.00"), "", "有偿服务类", "glc");
		其他收费项目List.add(e17);
		收费项目其他收费Entity e18 = new 收费项目其他收费Entity("损害财产赔偿费", new BigDecimal("0.00"), "", "有偿服务类", "glc");
		其他收费项目List.add(e18);
		收费项目其他收费Entity e19 = new 收费项目其他收费Entity("自行车使用款", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e19);
		收费项目其他收费Entity e20 = new 收费项目其他收费Entity("装修管理服务费", new BigDecimal("0.00"), "", "有偿服务类", "glc");
		其他收费项目List.add(e20);
		收费项目其他收费Entity e21 = new 收费项目其他收费Entity("运输费", new BigDecimal("0.00"), "元", "有偿服务类", "glc");
		其他收费项目List.add(e21);
		收费项目其他收费Entity e22 = new 收费项目其他收费Entity("车辆开锁服务费", new BigDecimal("0.00"), "元", "有偿服务类", "glc");
		其他收费项目List.add(e22);
		收费项目其他收费Entity e23 = new 收费项目其他收费Entity("砂石等费用", new BigDecimal("0.00"), "元", "有偿服务类", "glc");
		其他收费项目List.add(e23);
		收费项目其他收费Entity e24 = new 收费项目其他收费Entity("损害绿化赔偿款", new BigDecimal("0.00"), "", "有偿服务类", "glc");
		其他收费项目List.add(e24);
		收费项目其他收费Entity e25 = new 收费项目其他收费Entity("废品款", new BigDecimal("0.00"), "元", "其它类", "glc");
		其他收费项目List.add(e25);
		收费项目其他收费Entity e26 = new 收费项目其他收费Entity("装修保证金", new BigDecimal("0.00"), "元", "押金类", "glc");
		其他收费项目List.add(e26);
		收费项目其他收费Entity e27 = new 收费项目其他收费Entity("还借款", new BigDecimal("0.00"), "", "其它类", "glc");
		其他收费项目List.add(e27);
		收费项目其他收费Entity e28 = new 收费项目其他收费Entity("代收商铺租金", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e28);
		收费项目其他收费Entity e29 = new 收费项目其他收费Entity("合作单位", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e29);
		收费项目其他收费Entity e30 = new 收费项目其他收费Entity("楼梯的水费", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e30);
		收费项目其他收费Entity e31 = new 收费项目其他收费Entity("楼梯的公摊电费", new BigDecimal("0.00"), "元", "其它类", "");
		其他收费项目List.add(e31);
		收费项目其他收费Entity e32 = new 收费项目其他收费Entity("钓鱼管理费", new BigDecimal("0.00"), "", "有偿服务类", "glc");
		其他收费项目List.add(e32);
		收费项目其他收费Entity e33 = new 收费项目其他收费Entity("场地费", new BigDecimal("0.00"), "元", "其它类", "glc");
		其他收费项目List.add(e33);
		收费项目其他收费Entity e34 = new 收费项目其他收费Entity("穿梭车车费", new BigDecimal("0.00"), "", "其它类", "glc");
		其他收费项目List.add(e34);
		收费项目其他收费Entity e35 = new 收费项目其他收费Entity("地下车库停车费", new BigDecimal("0.00"), "", "其它类", "glc");
		其他收费项目List.add(e35);
		收费项目其他收费Entity e36 = new 收费项目其他收费Entity("儿童公园及精灵屋门票款", new BigDecimal("0.00"), "", "其它类", "glc");
		其他收费项目List.add(e36);
		收费项目其他收费Entity e37 = new 收费项目其他收费Entity("灭火器租金", new BigDecimal("0.00"), "", "其它类", "glc");
		其他收费项目List.add(e37);
		收费项目其他收费Entity e38 = new 收费项目其他收费Entity("商业街临时停车费", new BigDecimal("0.00"), "", "其它类", "glc");
		其他收费项目List.add(e38);
		收费项目其他收费Entity e39 = new 收费项目其他收费Entity("社区活动收入", new BigDecimal("0.00"), "", "其它类", "glc");
		其他收费项目List.add(e39);
		收费项目其他收费Entity e40 = new 收费项目其他收费Entity("诉讼等相关费用", new BigDecimal("0.00"), "", "其它类", "glc");
		其他收费项目List.add(e40);
		收费项目其他收费Entity e41 = new 收费项目其他收费Entity("退预收款", new BigDecimal("0.00"), "", "其它类", "glc");
		其他收费项目List.add(e41);
		收费项目其他收费Entity e42 = new 收费项目其他收费Entity("往来款", new BigDecimal("0.00"), "", "其它类", "glc");
		其他收费项目List.add(e42);
		收费项目其他收费Entity e43 = new 收费项目其他收费Entity("小卖部食品", new BigDecimal("0.00"), "", "其它类", "glc");
		其他收费项目List.add(e43);
		收费项目其他收费Entity e44 = new 收费项目其他收费Entity("小区临时停车收费", new BigDecimal("0.00"), "", "其它类", "glc");
		其他收费项目List.add(e44);
		收费项目其他收费Entity e45 = new 收费项目其他收费Entity("游泳池门票", new BigDecimal("0.00"), "", "其它类", "glc");
		其他收费项目List.add(e45);
		收费项目其他收费Entity e46 = new 收费项目其他收费Entity("公共设施保证金", new BigDecimal("0.00"), "", "押金类", "glc");
		其他收费项目List.add(e46);
		收费项目其他收费Entity e47 = new 收费项目其他收费Entity("灭火器押金", new BigDecimal("0.00"), "", "押金类", "glc");
		其他收费项目List.add(e47);
		收费项目其他收费Entity e48 = new 收费项目其他收费Entity("重型机械进场押金", new BigDecimal("0.00"), "", "押金类", "glc");
		其他收费项目List.add(e48);
		收费项目其他收费Entity e49 = new 收费项目其他收费Entity("租金保证金", new BigDecimal("0.00"), "", "押金类", "glc");
		其他收费项目List.add(e49);
		收费项目其他收费Entity e50 = new 收费项目其他收费Entity("IC卡押金", new BigDecimal("0.00"), "", "押金类", "glc");
		其他收费项目List.add(e50);
		收费项目其他收费Entity e51 = new 收费项目其他收费Entity("二十区公共电费分摊", new BigDecimal("0.00"), "", "分摊费", "");
		其他收费项目List.add(e51);
		收费项目其他收费Entity e52 = new 收费项目其他收费Entity("二十区车库顶板公共电费", new BigDecimal("0.00"), "", "分摊费", "");
		其他收费项目List.add(e52);
		收费项目其他收费Entity e53 = new 收费项目其他收费Entity("车库公共水费分摊", new BigDecimal("0.00"), "", "分摊费", "");
		其他收费项目List.add(e53);
		收费项目其他收费Entity e54 = new 收费项目其他收费Entity("车库顶板公共电费", new BigDecimal("0.00"), "", "分摊费", "");
		其他收费项目List.add(e54);
		收费项目其他收费Entity e55 = new 收费项目其他收费Entity("出租车位管理服务费1", new BigDecimal("0.00"), "", "车位费", "");
		其他收费项目List.add(e55);
		收费项目其他收费Entity e56 = new 收费项目其他收费Entity("室内报警管理费", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e56);
		收费项目其他收费Entity e57 = new 收费项目其他收费Entity("室内报警系统安装费", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e57);
		收费项目其他收费Entity e58 = new 收费项目其他收费Entity("临时施工押金", new BigDecimal("0.00"), "", "押金类", "");
		其他收费项目List.add(e58);
		收费项目其他收费Entity e59 = new 收费项目其他收费Entity("人员出入证押金", new BigDecimal("0.00"), "", "押金类", "");
		其他收费项目List.add(e59);
		收费项目其他收费Entity e60 = new 收费项目其他收费Entity("代办手续费", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e60);
		收费项目其他收费Entity e61 = new 收费项目其他收费Entity("代收代缴房屋契税", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e61);
		收费项目其他收费Entity e62 = new 收费项目其他收费Entity("损耗电费分摊", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e62);
		收费项目其他收费Entity e63 = new 收费项目其他收费Entity("食堂分摊费用", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e63);
		收费项目其他收费Entity e64 = new 收费项目其他收费Entity("预收物管费", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e64);
		收费项目其他收费Entity e65 = new 收费项目其他收费Entity("钟点工", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e65);
		收费项目其他收费Entity e66 = new 收费项目其他收费Entity("钓鱼协会会费", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e66);
		收费项目其他收费Entity e67 = new 收费项目其他收费Entity("退煤气瓶押金", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e67);
		收费项目其他收费Entity e68 = new 收费项目其他收费Entity("退回移动刷卡押金", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e68);
		收费项目其他收费Entity e69 = new 收费项目其他收费Entity("违规装修整改保证金", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e69);
		收费项目其他收费Entity e70 = new 收费项目其他收费Entity("车辆出入证押金", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e70);
		收费项目其他收费Entity e71 = new 收费项目其他收费Entity("车位管理服务费", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e71);
		收费项目其他收费Entity e72 = new 收费项目其他收费Entity("西樵镇村级垃圾清运项目投标保证金", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e72);
		收费项目其他收费Entity e73 = new 收费项目其他收费Entity("装修许可证押金", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e73);
		收费项目其他收费Entity e74 = new 收费项目其他收费Entity("装修押金", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e74);
		收费项目其他收费Entity e75 = new 收费项目其他收费Entity("蓝牙卡押金", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e75);
		收费项目其他收费Entity e76 = new 收费项目其他收费Entity("自行车押金", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e76);
		收费项目其他收费Entity e77 = new 收费项目其他收费Entity("粤建通卡费用", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e77);
		收费项目其他收费Entity e78 = new 收费项目其他收费Entity("管理费押金", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e78);
		收费项目其他收费Entity e79 = new 收费项目其他收费Entity("留宿管理押金", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e79);
		收费项目其他收费Entity e80 = new 收费项目其他收费Entity("水电押金", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e80);
		收费项目其他收费Entity e81 = new 收费项目其他收费Entity("服务费", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e81);
		收费项目其他收费Entity e82 = new 收费项目其他收费Entity("施工证押金", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e82);
		收费项目其他收费Entity e83 = new 收费项目其他收费Entity("收银找零备用金", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e83);
		收费项目其他收费Entity e84 = new 收费项目其他收费Entity("搬运押金", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e84);
		收费项目其他收费Entity e85 = new 收费项目其他收费Entity("损害道闸赔偿款", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e85);
		收费项目其他收费Entity e86 = new 收费项目其他收费Entity("损害路面赔偿款", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e86);
		收费项目其他收费Entity e87 = new 收费项目其他收费Entity("损害路灯赔偿款", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e87);
		收费项目其他收费Entity e88 = new 收费项目其他收费Entity("损害路灯、绿化赔偿款", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e88);
		收费项目其他收费Entity e89 = new 收费项目其他收费Entity("损害财产维修押金", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e89);
		收费项目其他收费Entity e90 = new 收费项目其他收费Entity("损害花基赔偿款", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e90);
		收费项目其他收费Entity e91 = new 收费项目其他收费Entity("损害电缆赔偿款", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e91);
		收费项目其他收费Entity e92 = new 收费项目其他收费Entity("损害栏杆赔偿款", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e92);
		收费项目其他收费Entity e93 = new 收费项目其他收费Entity("报废摩托车款", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e93);
		收费项目其他收费Entity e94 = new 收费项目其他收费Entity("平板电脑押金", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e94);
		收费项目其他收费Entity e95 = new 收费项目其他收费Entity("安防管理费", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e95);
		收费项目其他收费Entity e96 = new 收费项目其他收费Entity("外墙排水管安装费", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e96);
		收费项目其他收费Entity e97 = new 收费项目其他收费Entity("出租车位管理费", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e97);
		收费项目其他收费Entity e98 = new 收费项目其他收费Entity("其他", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e98);
		收费项目其他收费Entity e99 = new 收费项目其他收费Entity("代收车位租金1", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e99);
		收费项目其他收费Entity e100 = new 收费项目其他收费Entity("代收补偿金", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e100);
		收费项目其他收费Entity e101 = new 收费项目其他收费Entity("代收收缴房屋办证费", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e101);
		收费项目其他收费Entity e102 = new 收费项目其他收费Entity("代收商铺管理费", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e102);
		收费项目其他收费Entity e103 = new 收费项目其他收费Entity("代收商铺公共电费分摊", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e103);
		收费项目其他收费Entity e104 = new 收费项目其他收费Entity("代收售房定金刷卡手续费", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e104);
		收费项目其他收费Entity e105 = new 收费项目其他收费Entity("代收公共电费分摊", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e105);
		收费项目其他收费Entity e106 = new 收费项目其他收费Entity("交回3月多发工资", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e106);
		收费项目其他收费Entity e107 = new 收费项目其他收费Entity("个人社保费", new BigDecimal("0.00"), "", "其它类", "");
		其他收费项目List.add(e107);

//		收费项目其他收费Entity e58 = new 收费项目其他收费Entity("垃圾清运费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e58);
//		收费项目其他收费Entity e59 = new 收费项目其他收费Entity("代收售房定金刷卡手续费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e59);
//		收费项目其他收费Entity e60 = new 收费项目其他收费Entity("小卖部食品", new BigDecimal("0.00"), ", "其它类", "glc");
//		其他收费项目List.add(e60);
//		收费项目其他收费Entity e61 = new 收费项目其他收费Entity("代收收缴房屋办证费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e61);
//		收费项目其他收费Entity e62 = new 收费项目其他收费Entity("平板电脑押金", new BigDecimal("0.00"), ", "押金类", ");
//		其他收费项目List.add(e62);
//		收费项目其他收费Entity e63 = new 收费项目其他收费Entity("个人社保费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e63);
//		收费项目其他收费Entity e64 = new 收费项目其他收费Entity("食堂分摊费用", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e64);
//		收费项目其他收费Entity e65 = new 收费项目其他收费Entity("管理费押金", new BigDecimal("0.00"), ", "押金类", ");
//		其他收费项目List.add(e65);
//		收费项目其他收费Entity e66 = new 收费项目其他收费Entity("粤建通卡费用", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e66);
//		收费项目其他收费Entity e67 = new 收费项目其他收费Entity("代收商铺管理费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e67);
//		收费项目其他收费Entity e68 = new 收费项目其他收费Entity("代收公共电费分摊", new BigDecimal("0.00"), ", "分摊费", ");
//		其他收费项目List.add(e68);
//		收费项目其他收费Entity e69 = new 收费项目其他收费Entity("代收过三水大桥过桥费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e69);
//		收费项目其他收费Entity e70 = new 收费项目其他收费Entity("伙食费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e70);
//		收费项目其他收费Entity e71 = new 收费项目其他收费Entity("报废摩托车款", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e71);
//		收费项目其他收费Entity e72 = new 收费项目其他收费Entity("西樵镇村级垃圾清运项目投标保证金", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e72);
//		收费项目其他收费Entity e73 = new 收费项目其他收费Entity("宿舍电费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e73);
//		收费项目其他收费Entity e74 = new 收费项目其他收费Entity("代收代办房屋办证费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e74);
//		收费项目其他收费Entity e75 = new 收费项目其他收费Entity("宿舍洗衣机洗衣费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e75);
//		收费项目其他收费Entity e76 = new 收费项目其他收费Entity("工服费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e76);
//		收费项目其他收费Entity e77 = new 收费项目其他收费Entity("复印费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e77);
//		收费项目其他收费Entity e78 = new 收费项目其他收费Entity("退煤气瓶押金", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e78);
//		收费项目其他收费Entity e79 = new 收费项目其他收费Entity("损害栏杆赔偿款", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e79);
//		收费项目其他收费Entity e80 = new 收费项目其他收费Entity("代收三水大桥过桥费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e80);
//		收费项目其他收费Entity e81 = new 收费项目其他收费Entity("销售废品", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e81);
//		收费项目其他收费Entity e82 = new 收费项目其他收费Entity("违规停放车辆", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e82);
//		收费项目其他收费Entity e83 = new 收费项目其他收费Entity("预收物管费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e83);
//		收费项目其他收费Entity e84 = new 收费项目其他收费Entity("蓝牙卡押金", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e84);
//		收费项目其他收费Entity e85 = new 收费项目其他收费Entity("临时停车费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e85);
//		收费项目其他收费Entity e86 = new 收费项目其他收费Entity("工作服折旧费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e86);
//		收费项目其他收费Entity e87 = new 收费项目其他收费Entity("代购社保款", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e87);
//		收费项目其他收费Entity e88 = new 收费项目其他收费Entity("退回移动刷卡押金", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e88);
//		收费项目其他收费Entity e89 = new 收费项目其他收费Entity("服务费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e89);
//		收费项目其他收费Entity e90 = new 收费项目其他收费Entity("收回代交社保费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e90);
//		收费项目其他收费Entity e91 = new 收费项目其他收费Entity("留宿管理押金", new BigDecimal("0.00"), ", "押金类", ");
//		其他收费项目List.add(e91);
//		收费项目其他收费Entity e92 = new 收费项目其他收费Entity("交回3月多发工资", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e92);
//		收费项目其他收费Entity e93 = new 收费项目其他收费Entity("外墙排水管安装费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e93);
//		收费项目其他收费Entity e94 = new 收费项目其他收费Entity("开荒清洁费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e94);
//		收费项目其他收费Entity e95 = new 收费项目其他收费Entity("自行车使用款", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e95);
//		收费项目其他收费Entity e96 = new 收费项目其他收费Entity("代收商铺公共电费分摊", new BigDecimal("0.00"), ", "分摊费", ");
//		其他收费项目List.add(e96);
//		收费项目其他收费Entity e97 = new 收费项目其他收费Entity("代收补偿金", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e97);
//		收费项目其他收费Entity e98 = new 收费项目其他收费Entity("施工保证金", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e98);
//		收费项目其他收费Entity e99 = new 收费项目其他收费Entity("收还借款", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e99);
//		收费项目其他收费Entity e100 = new 收费项目其他收费Entity("代收商铺租金滞纳金", new BigDecimal("0.00"), ", "违约金", ");
//		其他收费项目List.add(e100);
//		收费项目其他收费Entity e101 = new 收费项目其他收费Entity("商铺租金", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e101);
//		收费项目其他收费Entity e102 = new 收费项目其他收费Entity("钟点工", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e102);

//		收费项目其他收费Entity e108 = new 收费项目其他收费Entity("代办手续费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e108);
//		收费项目其他收费Entity e109 = new 收费项目其他收费Entity("代收车位租金1", new BigDecimal("0.00"), ", "车位费", ");
//		其他收费项目List.add(e109);
//		收费项目其他收费Entity e110 = new 收费项目其他收费Entity("代收车位租金滞纳金", new BigDecimal("0.00"), ", "违约金", ");
//		其他收费项目List.add(e110);
//		收费项目其他收费Entity e111 = new 收费项目其他收费Entity("代收车位租金违约金", new BigDecimal("0.00"), ", "违约金", ");
//		其他收费项目List.add(e111);
//		收费项目其他收费Entity e112 = new 收费项目其他收费Entity("出租车位管理服务费1", new BigDecimal("0.00"), ", "车位费", ");
//		其他收费项目List.add(e112);
//		收费项目其他收费Entity e113 = new 收费项目其他收费Entity("出租车位管理服务费滞纳金", new BigDecimal("0.00"), ", "违约金", ");
//		其他收费项目List.add(e113);
//		收费项目其他收费Entity e114 = new 收费项目其他收费Entity("出租车位管理服务费违约金", new BigDecimal("0.00"), ", "违约金", ");
//		其他收费项目List.add(e114);
//		收费项目其他收费Entity e115 = new 收费项目其他收费Entity("出租车位管理费", new BigDecimal("0.00"), ", "车位费", ");
//		其他收费项目List.add(e115);
//		收费项目其他收费Entity e116 = new 收费项目其他收费Entity("安防管理费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e116);
//		收费项目其他收费Entity e117 = new 收费项目其他收费Entity("室内报警管理费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e117);
//		收费项目其他收费Entity e118 = new 收费项目其他收费Entity("室内报警系统安装费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e118);
//		收费项目其他收费Entity e119 = new 收费项目其他收费Entity("工本费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e119);
//		收费项目其他收费Entity e120 = new 收费项目其他收费Entity("工程维修", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e120);
//		收费项目其他收费Entity e121 = new 收费项目其他收费Entity("开锁服务费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e121);
//		收费项目其他收费Entity e122 = new 收费项目其他收费Entity("损坏绿化赔偿款", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e122);
//		收费项目其他收费Entity e123 = new 收费项目其他收费Entity("损坏财产赔偿款", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e123);
//		收费项目其他收费Entity e124 = new 收费项目其他收费Entity("损害电缆赔偿款", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e124);
//		收费项目其他收费Entity e125 = new 收费项目其他收费Entity("损害绿化赔偿款", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e125);
//		收费项目其他收费Entity e126 = new 收费项目其他收费Entity("损害花基赔偿款", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e126);
//		收费项目其他收费Entity e127 = new 收费项目其他收费Entity("损害财产赔偿费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e127);
//		收费项目其他收费Entity e128 = new 收费项目其他收费Entity("损害路灯、绿化赔偿款", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e128);
//		收费项目其他收费Entity e129 = new 收费项目其他收费Entity("损害路灯赔偿款", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e129);
//		收费项目其他收费Entity e130 = new 收费项目其他收费Entity("损害路面赔偿款", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e130);
//		收费项目其他收费Entity e131 = new 收费项目其他收费Entity("损害道闸赔偿款", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e131);
//		收费项目其他收费Entity e132 = new 收费项目其他收费Entity("损耗电费分摊", new BigDecimal("0.00"), ", "分摊费", ");
//		其他收费项目List.add(e132);
//		收费项目其他收费Entity e133 = new 收费项目其他收费Entity("施工服务费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e133);
//		收费项目其他收费Entity e134 = new 收费项目其他收费Entity("施工管理费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e134);
//		收费项目其他收费Entity e135 = new 收费项目其他收费Entity("日常办公费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e135);
//		收费项目其他收费Entity e136 = new 收费项目其他收费Entity("日常办公费用", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e136);
//		收费项目其他收费Entity e137 = new 收费项目其他收费Entity("月租露天停车费", new BigDecimal("0.00"), ", "车位费", ");
//		其他收费项目List.add(e137);
//		收费项目其他收费Entity e138 = new 收费项目其他收费Entity("有偿服务清洁费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e138);
//		收费项目其他收费Entity e139 = new 收费项目其他收费Entity("有偿服务费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e139);
//		收费项目其他收费Entity e140 = new 收费项目其他收费Entity("有偿清洁服务", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e140);
//		收费项目其他收费Entity e141 = new 收费项目其他收费Entity("有偿清洁服务费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e141);
//		收费项目其他收费Entity e142 = new 收费项目其他收费Entity("有偿维修服务费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e142);
//		收费项目其他收费Entity e143 = new 收费项目其他收费Entity("有偿绿化服务", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e143);
//		收费项目其他收费Entity e144 = new 收费项目其他收费Entity("有偿绿化服务费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e144);
//		收费项目其他收费Entity e145 = new 收费项目其他收费Entity("滞纳金", new BigDecimal("0.00"), ", "违约金", ");
//		其他收费项目List.add(e145);
//		收费项目其他收费Entity e146 = new 收费项目其他收费Entity("砂石等费用", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e146);
//		收费项目其他收费Entity e147 = new 收费项目其他收费Entity("综合有偿服务费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e147);
//		收费项目其他收费Entity e148 = new 收费项目其他收费Entity("花园及停车位管理费滞纳金", new BigDecimal("0.00"), ", "违约金", ");
//		其他收费项目List.add(e148);
//		收费项目其他收费Entity e149 = new 收费项目其他收费Entity("装修垃圾清运费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e149);
//		收费项目其他收费Entity e150 = new 收费项目其他收费Entity("装修管理服务费", new BigDecimal("0.00"), ", "其它类", "glc");
//		其他收费项目List.add(e150);
//		收费项目其他收费Entity e151 = new 收费项目其他收费Entity("装修违约金", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e151);
//		收费项目其他收费Entity e152 = new 收费项目其他收费Entity("车位服务费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e152);
//		收费项目其他收费Entity e153 = new 收费项目其他收费Entity("车位管理服务费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e153);
//		收费项目其他收费Entity e154 = new 收费项目其他收费Entity("车位管理费滞纳金", new BigDecimal("0.00"), ", "违约金", ");
//		其他收费项目List.add(e154);
//		收费项目其他收费Entity e155 = new 收费项目其他收费Entity("车位管理费违约金", new BigDecimal("0.00"), ", "违约金", ");
//		其他收费项目List.add(e155);

//		收费项目其他收费Entity e158 = new 收费项目其他收费Entity("车辆开锁费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e158);
//		收费项目其他收费Entity e159 = new 收费项目其他收费Entity("过卡IC卡工本、服务费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e159);
//		收费项目其他收费Entity e160 = new 收费项目其他收费Entity("过桥IC卡工本\\服务费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e160);
//		收费项目其他收费Entity e161 = new 收费项目其他收费Entity("过桥IC卡工本服务费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e161);
//		收费项目其他收费Entity e162 = new 收费项目其他收费Entity("过桥IC工本、服务费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e162);
//		收费项目其他收费Entity e163 = new 收费项目其他收费Entity("过桥卡IC卡工本、服务费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e163);
//		收费项目其他收费Entity e164 = new 收费项目其他收费Entity("运输费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e164);
//		收费项目其他收费Entity e165 = new 收费项目其他收费Entity("远程卡工本费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e165);
//		收费项目其他收费Entity e166 = new 收费项目其他收费Entity("钓鱼管理费", new BigDecimal("0.00"), ", "其它类", ");
//		其他收费项目List.add(e166);
//		收费项目其他收费Entity e167 = new 收费项目其他收费Entity("露天车位管理服务费2滞纳金", new BigDecimal("0.00"), ", "违约金", ");
//		其他收费项目List.add(e167);
//		收费项目其他收费Entity e168 = new 收费项目其他收费Entity("露天车位管理服务费2违约金", new BigDecimal("0.00"), ", "违约金", ");
//		其他收费项目List.add(e168);
//		收费项目其他收费Entity e169 = new 收费项目其他收费Entity("露天车位管理服务费滞纳金", new BigDecimal("0.00"), ", "违约金", ");
//		其他收费项目List.add(e169);
//		收费项目其他收费Entity e170 = new 收费项目其他收费Entity("露天车位管理服务费违约金", new BigDecimal("0.00"), ", "违约金", ");
//		其他收费项目List.add(e170);
//		收费项目其他收费Entity e171 = new 收费项目其他收费Entity("社区活动收入", new BigDecimal("0.00"), ", "其它类", "glc");
//		其他收费项目List.add(e171);
		return 其他收费项目List;
	}

	protected static String getMeterCode(String meterName) {

		String meterCode = "";
		Map<String, String> meter = new HashMap<String, String>();
		meter.put("新三区清洁用水A", "212220513116");
		meter.put("新十六区人防负一层清洁用水表（52座）", "014200155015");
		meter.put("新十六区人防负一、二层清洁用水表（45座）", "014200155012");
		meter.put("新十六区人防负一层清洁用水表（42座）", "014200155017");
		meter.put("新十六区人防负一层清洁用水表（20座）", "014200155006");
		meter.put("新十六区产权负一层清洁用水表（7座）", "014200155014");
		meter.put("新十六区人防负一层清洁用水表（35座）", "014191294919");
		meter.put("新三区清洁用水1", "013170105658");
		meter.put("新三区清洁用水2", "013170105687");
		meter.put("新三区清洁用水4", "013170105503");
		meter.put("新三区清洁用水5", "013170105544");
		meter.put("新三区清洁用水6", "013170614705");
		meter.put("新三区清洁用水7", "013170614861");
		meter.put("新三区清洁用水8", "013170614637");
		meter.put("新三区清洁用水9", "013170614945");
		meter.put("新三区清洁用水10", "013170614928");
		meter.put("新三区清洁用水11", "013170614837");
		meter.put("新三区清洁用水13", "013170614877");
		meter.put("新十八区负一层清洁用水", "034170900477");
		meter.put("新十八区41B单元32层清洁用水表", "032171157734");
		meter.put("新十八区41B单元26层清洁用水表", "032171157273");
		meter.put("新十八区41B单元16层清洁用水表", "032171157571");
		meter.put("新十八区41B单元11层清洁用水表", "032171157800");
		meter.put("新十八区41B单元6层清洁用水表", "032171159207");
		meter.put("新十八区41B单元1层清洁用水表", "032171055643");
		meter.put("新十八区41A单元32层清洁用水表", "032171054902");
		meter.put("新十八区41A单元26层清洁用水表", "032171055766");
		meter.put("新十八区41A单元16层清洁用水表", "032171158463");
		meter.put("新十八区41A单元11层清洁用水表", "032171054889");
		meter.put("新十八区41A单元6层清洁用水表", "032171158761");
		meter.put("新十八区41A单元1层清洁用水表", "032171156430");
		meter.put("新十八区42B单元32层清洁用水表", "032171055600");
		meter.put("新十八区42B单元26层清洁用水表", "032171158407");
		meter.put("新十八区42B单元16层清洁用水表", "032171056364");
		meter.put("新十八区42B单元11层清洁用水表", "032171055543");
		meter.put("新十八区42B单元6层清洁用水表", "032171158343");
		meter.put("新十八区42B单元1层清洁用水表", "032171053729");
		meter.put("新十八区42A单元32层清洁用水表", "032171158819");
		meter.put("新十八区42A单元26层清洁用水表", "032170942665");
		meter.put("新十八区42A单元16层清洁用水表", "032170944350");
		meter.put("新十八区42A单元11层清洁用水表", "032171157123");
		meter.put("新十八区42A单元6层清洁用水表", "032171157693");
		meter.put("新十八区42A单元1层清洁用水表", "032171055533");
		meter.put("新十八区43B单元32层清洁用水表", "032171156432");
		meter.put("新十八区43B单元26层清洁用水表", "032171055679");
		meter.put("新十八区43B单元16层清洁用水表", "032171157965");
		meter.put("新十八区43B单元11层清洁用水表", "032171157883");
		meter.put("新十八区43B单元6层清洁用水表", "032171157425");
		meter.put("新十八区43B单元1层清洁用水表", "032171157850");
		meter.put("新十八区43A单元32层清洁用水表", "032171158734");
		meter.put("新十八区43A单元26层清洁用水表", "032171055778");
		meter.put("新十八区43A单元16层清洁用水表", "032171158057");
		meter.put("新十八区43A单元11层清洁用水表", "032171157797");
		meter.put("新十八区43A单元6层清洁用水表", "032171158755");
		meter.put("新十八区43A单元1层清洁用水表", "032171053813");
		meter.put("新十八区45B单元32层清洁用水表", "032171158795");
		meter.put("新十八区45B单元26层清洁用水表", "032171056404");
		meter.put("新十八区45B单元16层清洁用水表", "032171158072");
		meter.put("新十八区45B单元11层清洁用水表", "032171051190");
		meter.put("新十八区45B单元6层清洁用水表", "032171158563");
		meter.put("新十八区45B单元1层清洁用水表", "032171055125");
		meter.put("新十八区45A单元32层清洁用水表", "032171157522");
		meter.put("新十八区45A单元26层清洁用水表", "032171158285");
		meter.put("新十八区45A单元16层清洁用水表", "032171054548");
		meter.put("新十八区45A单元11层清洁用水表", "032171054748");
		meter.put("新十八区45A单元6层清洁用水表", "032171054306");
		meter.put("新十八区45A单元1层清洁用水表", "032171055368");
		meter.put("新十八区46B单元32层清洁用水表", "032171157757");
		meter.put("新十八区46B单元26层清洁用水表", "032171054890");
		meter.put("新十八区46B单元16层清洁用水表", "032171157550");
		meter.put("新十八区46B单元11层清洁用水表", "032171055602");
		meter.put("新十八区46B单元6层清洁用水表", "032170947996");
		meter.put("新十八区46B单元1层清洁用水表", "032171053618");
		meter.put("新十八区46A单元32层清洁用水表", "032171053515");
		meter.put("新十八区46A单元26层清洁用水表", "032171056338");
		meter.put("新十八区46A单元16层清洁用水表", "032171054620");
		meter.put("新十八区46A单元11层清洁用水表", "032171054692");
		meter.put("新十八区46A单元6层清洁用水表", "032171053020");
		meter.put("新十八区46A单元1层清洁用水表", "032171055394");
		meter.put("新十八区47B单元32层清洁用水表", "032171054716");
		meter.put("新十八区47B单元26层清洁用水表", "032171053559");
		meter.put("新十八区47B单元16层清洁用水表", "032171156698");
		meter.put("新十八区47B单元11层清洁用水表", "032171053661");
		meter.put("新十八区47B单元6层清洁用水表", "032171054458");
		meter.put("新十八区47B单元1层清洁用水表", "032171055392");
		meter.put("新十八区47A单元32层清洁用水表", "032171052311");
		meter.put("新十八区47A单元26层清洁用水表", "032171056293");
		meter.put("新十八区47A单元16层清洁用水表", "032171054380");
		meter.put("新十八区47A单元11层清洁用水表", "032171052708");
		meter.put("新十八区47A单元6层清洁用水表", "032171054989");
		meter.put("新十八区47A单元1层清洁用水表", "032171054121");
		meter.put("新十八区48B单元32层清洁用水表", "032171158818");
		meter.put("新十八区48B单元26层清洁用水表", "032171054794");
		meter.put("新十八区48B单元16层清洁用水表", "032171052698");
		meter.put("新十八区48B单元11层清洁用水表", "032171055980");
		meter.put("新十八区48B单元6层清洁用水表", "032171158166");
		meter.put("新十八区48B单元1层清洁用水表", "032171054912");
		meter.put("新十八区48A单元32层清洁用水表", "032171055424");
		meter.put("新十八区48A单元26层清洁用水表", "032170948432");
		meter.put("新十八区48A单元16层清洁用水表", "032171156760");
		meter.put("新十八区48A单元11层清洁用水表", "032171053013");
		meter.put("新十八区48A单元6层清洁用水表", "032171054982");
		meter.put("新十八区48A单元1层清洁用水表", "032171055101");
		meter.put("新十六区8座32层清洁用水表", "012200473881");
		meter.put("新十六区8座28层清洁用水表", "012200575104");
		meter.put("新十六区8座22层清洁用水表", "012200571432");
		meter.put("新十六区8座17层清洁用水表", "012200572297");
		meter.put("新十六区8座12层清洁用水表", "012200574940");
		meter.put("新十六区8座7层清洁用水表", "012200573935");
		meter.put("新十六区8座2层清洁用水表", "012200575131");
		meter.put("新十六区7座32层清洁用水表", "012200575128");
		meter.put("新十六区7座28层清洁用水表", "012200475201");
		meter.put("新十六区7座22层清洁用水表", "012200575099");
		meter.put("新十六区7座17层清洁用水表", "012200575216");
		meter.put("新十六区7座12层清洁用水表", "012200574958");
		meter.put("新十六区7座7层清洁用水表", "012200575550");
		meter.put("新十六区7座2层清洁用水表", "012200572652");
		meter.put("新十六区6座32层清洁用水表", "012200574419");
		meter.put("新十六区6座27层清洁用水表", "012200571433");
		meter.put("新十六区6座22层清洁用水表", "012200470624");
		meter.put("新十六区6座17层清洁用水表", "012200573754");
		meter.put("新十六区6座12层清洁用水表", "012200571964");
		meter.put("新十六区6座7层清洁用水表", "012200575628");
		meter.put("新十六区6座2层清洁用水表", "012200575521");
		meter.put("新十六区5座32层清洁用水表", "012200574623");
		meter.put("新十六区5座27层清洁用水表", "012200571238");
		meter.put("新十六区5座22层清洁用水表", "012200573906");
		meter.put("新十六区5座17层清洁用水表", "012200571568");
		meter.put("新十六区5座12层清洁用水表", "012200572636");
		meter.put("新十六区5座7层清洁用水表", "012200575330");
		meter.put("新十六区5座2层清洁用水表", "012200571488");
		meter.put("新十六区3座32层清洁用水表", "012200150297");
		meter.put("新十六区3座28层清洁用水表", "012200571010");
		meter.put("新十六区3座22层清洁用水表", "012200575986");
		meter.put("新十六区3座17层清洁用水表", "012200571376");
		meter.put("新十六区3座12层清洁用水表", "012200575805");
		meter.put("新十六区3座7层清洁用水表", "012200571183");
		meter.put("新十六区3座2层清洁用水表", "012200574298");
		meter.put("新十六区2座32层清洁用水表", "012200571084");
		meter.put("新十六区2座27层清洁用水表", "012200470655");
		meter.put("新十六区2座22层清洁用水表", "012200574645");
		meter.put("新十六区2座17层清洁用水表", "012200574337");
		meter.put("新十六区2座12层清洁用水表", "012200574340");
		meter.put("新十六区2座7层清洁用水表", "012200575208");
		meter.put("新十六区2座2层清洁用水表", "012200571511");
		meter.put("新十六区11座27层清洁用水表", "012200151687");
		meter.put("新十六区11座22层清洁用水表", "012200572963");
		meter.put("新十六区11座17层清洁用水表", "012200474532");
		meter.put("新十六区11座12层清洁用水表", "012200574981");
		meter.put("新十六区11座7层清洁用水表", "012200572146");
		meter.put("新十六区11座2层清洁用水表", "012200574799");
		meter.put("新十六区10座32层清洁用水表", "012200475077");
		meter.put("新十六区10座28层清洁用水表", "012200474360");
		meter.put("新十六区10座22层清洁用水表", "012200150054");
		meter.put("新十六区10座17层清洁用水表", "012200358585");
		meter.put("新十六区10座12层清洁用水表", "012200358587");
		meter.put("新十六区10座7层清洁用水表", "012200575148");
		meter.put("新十六区10座2层清洁用水表", "012200475586");
		meter.put("新十六区9座32层清洁用水表", "012200575439");
		meter.put("新十六区9座28层清洁用水表", "012200575535");
		meter.put("新十六区9座22层清洁用水表", "012200571695");
		meter.put("新十六区9座17层清洁用水表", "012200152753");
		meter.put("新十六区9座12层清洁用水表", "012200150880");
		meter.put("新十六区9座7层清洁用水表", "012200474008");
		meter.put("新十六区9座2层清洁用水表", "012200473511");
		meter.put("新十六区17座27层清洁用水表", "012200573467");
		meter.put("新十六区17座22层清洁用水表", "012200575569");
		meter.put("新十六区17座17层清洁用水表", "012200573670");
		meter.put("新十六区17座12层清洁用水表", "012200574473");
		meter.put("新十六区17座7层清洁用水表", "012200573978");
		meter.put("新十六区17座2层清洁用水表", "012200574137");
		meter.put("新十六区18座27层清洁用水表", "012200150572");
		meter.put("新十六区18座22层清洁用水表", "012200152616");
		meter.put("新十六区18座17层清洁用水表", "012200151660");
		meter.put("新十六区18座12层清洁用水表", "012200151648");
		meter.put("新十六区18座7层清洁用水表", "012200152347");
		meter.put("新十六区18座2层清洁用水表", "012200150355");
		meter.put("新十六区20座27层清洁用水表", "012200475920");
		meter.put("新十六区20座22层清洁用水表", "012200474288");
		meter.put("新十六区20座17层清洁用水表", "012200474233");
		meter.put("新十六区20座12层清洁用水表", "012200473184");
		meter.put("新十六区20座7层清洁用水表", "012200359821");
		meter.put("新十六区20座2层清洁用水表", "012200152778");
		meter.put("新十六区21座27层清洁用水表", "012200152633");
		meter.put("新十六区21座22层清洁用水表", "012200150359");
		meter.put("新十六区21座17层清洁用水表", "012200357885");
		meter.put("新十六区21座12层清洁用水表", "012200474434");
		meter.put("新十六区21座7层清洁用水表", "012200150550");
		meter.put("新十六区21座2层清洁用水表", "012200152721");
		meter.put("新十六区22座32层清洁用水表", "012200572851");
		meter.put("新十六区22座27层清洁用水表", "012200572801");
		meter.put("新十六区22座22层清洁用水表", "012200575656");
		meter.put("新十六区22座17层清洁用水表", "012200151682");
		meter.put("新十六区22座12层清洁用水表", "012200575582");
		meter.put("新十六区22座7层清洁用水表", "012200575553");
		meter.put("新十六区22座2层清洁用水表", "012200575508");
		meter.put("新十六区23座32层清洁用水表", "012200152334");
		meter.put("新十六区23座27层清洁用水表", "012200151348");
		meter.put("新十六区23座22层清洁用水表", "012200151694");
		meter.put("新十六区23座17层清洁用水表", "012200573220");
		meter.put("新十六区23座12层清洁用水表", "012200152106");
		meter.put("新十六区23座7层清洁用水表", "012200574885");
		meter.put("新十六区23座2层清洁用水表", "012200575738");
		meter.put("新十六区25座32层清洁用水表", "012200571850");
		meter.put("新十六区25座27层清洁用水表", "012200572827");
		meter.put("新十六区25座22层清洁用水表", "012200575334");
		meter.put("新十六区25座17层清洁用水表", "012200573627");
		meter.put("新十六区25座12层清洁用水表", "012200572016");
		meter.put("新十六区25座7层清洁用水表", "012200572350");
		meter.put("新十六区25座2层清洁用水表", "012200571351");
		meter.put("新十六区26座32层清洁用水表", "012200572879");
		meter.put("新十六区26座27层清洁用水表", "012200572344");
		meter.put("新十六区26座22层清洁用水表", "012200573872");
		meter.put("新十六区26座17层清洁用水表", "012200358444");
		meter.put("新十六区26座12层清洁用水表", "012200572427");
		meter.put("新十六区26座7层清洁用水表", "012200572884");
		meter.put("新十六区26座2层清洁用水表", "012200572014");
		meter.put("新十六区27座32层清洁用水表", "012200574459");
		meter.put("新十六区27座27层清洁用水表", "012200572935");
		meter.put("新十六区27座22层清洁用水表", "012200575983");
		meter.put("新十六区27座17层清洁用水表", "012200575692");
		meter.put("新十六区27座12层清洁用水表", "012200572625");
		meter.put("新十六区27座7层清洁用水表", "012200573340");
		meter.put("新十六区27座2层清洁用水表", "012200575663");
		meter.put("新十六区28座2层清洁用水表", "012200575964");
		meter.put("新十六区28座7层清洁用水表", "012200571524");
		meter.put("新十六区28座12层清洁用水表", "012200572218");
		meter.put("新十六区28座17层清洁用水表", "012200575940");
		meter.put("新十六区28座22层清洁用水表", "012200575212");
		meter.put("新十六区28座27层清洁用水表", "012200573090");
		meter.put("新十六区28座32层清洁用水表", "012200574054");
		meter.put("新十六区33座2层清洁用水表", "012200572670");
		meter.put("新十六区33座7层清洁用水表", "012200473051");
		meter.put("新十六区33座12层清洁用水表", "012200575635");
		meter.put("新十六区33座17层清洁用水表", "012200574008");
		meter.put("新十六区33座22层清洁用水表", "012200572531");
		meter.put("新十六区33座27层清洁用水表", "012200680292");
		meter.put("新十六区33座32层清洁用水表", "012200679393");
		meter.put("新十六区29座2层清洁用水表", "012200575617");
		meter.put("新十六区29座7层清洁用水表", "012200470619");
		meter.put("新十六区29座12层清洁用水表", "012200574049");
		meter.put("新十六区29座17层清洁用水表", "012200575834");
		meter.put("新十六区29座22层清洁用水表", "012200573487");
		meter.put("新十六区29座27层清洁用水表", "012200470894");
		meter.put("新十六区29座32层清洁用水表", "012200575250");
		meter.put("新十六区30座2层清洁用水表", "012200572596");
		meter.put("新十六区30座7层清洁用水表", "012200573616");
		meter.put("新十六区30座12层清洁用水表", "012200470657");
		meter.put("新十六区30座17层清洁用水表", "012200575619");
		meter.put("新十六区30座22层清洁用水表", "012200575309");
		meter.put("新十六区30座27层清洁用水表", "012200470900");
		meter.put("新十六区30座32层清洁用水表", "012200573036");
		meter.put("新十六区31座7层清洁用水表", "012200571428");
		meter.put("新十六区31座12层清洁用水表", "012200679123");
		meter.put("新十六区31座17层清洁用水表", "012200572667");
		meter.put("新十六区31座22层清洁用水表", "012200572509");
		meter.put("新十六区31座27层清洁用水表", "012200572578");
		meter.put("新十六区31座32层清洁用水表", "012200575274");
		meter.put("新十六区32座2层清洁用水表", "012200475565");
		meter.put("新十六区32座7层清洁用水表", "012200473059");
		meter.put("新十六区32座12层清洁用水表", "012200575817");
		meter.put("新十六区32座17层清洁用水表", "012200572700");
		meter.put("新十六区32座22层清洁用水表", "012200574970");
		meter.put("新十六区32座27层清洁用水表", "012200571980");
		meter.put("新十六区32座32层清洁用水表", "012200470961");
		meter.put("新十六区35座2层清洁用水表", "012200572051");
		meter.put("新十六区35座7层清洁用水表", "012200152143");
		meter.put("新十六区35座12层清洁用水表", "012200571821");
		meter.put("新十六区35座17层清洁用水表", "012200574489");
		meter.put("新十六区35座22层清洁用水表", "012200572400");
		meter.put("新十六区35座28层清洁用水表", "012200574101");
		meter.put("新十六区35座32层清洁用水表", "012200571862");
		meter.put("新十六区36座2层清洁用水表", "012200571888");
		meter.put("新十六区36座7层清洁用水表", "012200150890");
		meter.put("新十六区36座12层清洁用水表", "012200573375");
		meter.put("新十六区36座17层清洁用水表", "012200575477");
		meter.put("新十六区36座22层清洁用水表", "012200572236");
		meter.put("新十六区36座28层清洁用水表", "012200572702");
		meter.put("新十六区36座32层清洁用水表", "012200574562");
		meter.put("新十六区37座2层清洁用水表", "012200152776");
		meter.put("新十六区37座7层清洁用水表", "012200572829");
		meter.put("新十六区37座12层清洁用水表", "012200573632");
		meter.put("新十六区37座17层清洁用水表", "012200572891");
		meter.put("新十六区37座22层清洁用水表", "012200571620");
		meter.put("新十六区37座28层清洁用水表", "012200571303");
		meter.put("新十六区37座32层清洁用水表", "012200473839");
		meter.put("新十六区38座32层清洁用水表", "012200575446");
		meter.put("新十六区38座22层清洁用水表", "012200572990");
		meter.put("新十六区38座17层清洁用水表", "012200575038");
		meter.put("新十六区38座12层清洁用水表", "012200571765");
		meter.put("新十六区38座7层清洁用水表", "012200573944");
		meter.put("新十六区38座2层清洁用水表", "012200571729");
		meter.put("新十六区39座32层清洁用水表", "012200573430");
		meter.put("新十六区39座28层清洁用水表", "012200574526");
		meter.put("新十六区39座22层清洁用水表", "012200571602");
		meter.put("新十六区39座17层清洁用水表", "012200359823");
		meter.put("新十六区39座12层清洁用水表", "012200571675");
		meter.put("新十六区39座7层清洁用水表", "012200361441");
		meter.put("新十六区39座2层清洁用水表", "012200151432");
		meter.put("新十六区40座32层清洁用水表", "012200573434");
		meter.put("新十六区40座28层清洁用水表", "012200150349");
		meter.put("新十六区40座22层清洁用水表", "012200573354");
		meter.put("新十六区40座17层清洁用水表", "012200152110");
		meter.put("新十六区40座12层清洁用水表", "012200575449");
		meter.put("新十六区40座7层清洁用水表", "012200572864");
		meter.put("新十六区40座2层清洁用水表", "012200573405");
		meter.put("新十六区41座27层清洁用水表", "012200474165");
		meter.put("新十六区41座22层清洁用水表", "012200473462");
		meter.put("新十六区41座17层清洁用水表", "012200474381");
		meter.put("新十六区41座12层清洁用水表", "012200475514");
		meter.put("新十六区41座7层清洁用水表", "012200474259");
		meter.put("新十六区41座2层清洁用水表", "012200473339");
		meter.put("新十六区42座26层清洁用水表", "012200475373");
		meter.put("新十六区42座22层清洁用水表", "012200475073");
		meter.put("新十六区42座17层清洁用水表", "012200475750");
		meter.put("新十六区42座12层清洁用水表", "012200474004");
		meter.put("新十六区42座7层清洁用水表", "012200474420");
		meter.put("新十六区42座2层清洁用水表", "012200360814");
		meter.put("新十六区48座32层清洁用水表", "012200572986");
		meter.put("新十六区48座27层清洁用水表", "012200575930");
		meter.put("新十六区48座22层清洁用水表", "012200475188");
		meter.put("新十六区48座17层清洁用水表", "012200474036");
		meter.put("新十六区48座12层清洁用水表", "012200475347");
		meter.put("新十六区48座7层清洁用水表", "012200475300");
		meter.put("新十六区48座2层清洁用水表", "012200473613");
		meter.put("新十六区47座32层清洁用水表", "012200572039");
		meter.put("新十六区47座27层清洁用水表", "012200475978");
		meter.put("新十六区47座22层清洁用水表", "012200150375");
		meter.put("新十六区47座17层清洁用水表", "012200150345");
		meter.put("新十六区47座12层清洁用水表", "012200152720");
		meter.put("新十六区47座7层清洁用水表", "012200151749");
		meter.put("新十六区47座2层清洁用水表", "012200152781");
		meter.put("新十六区46座32层清洁用水表", "012200474423");
		meter.put("新十六区46座27层清洁用水表", "012200474691");
		meter.put("新十六区46座22层清洁用水表", "012200571012");
		meter.put("新十六区46座17层清洁用水表", "012200475274");
		meter.put("新十六区46座12层清洁用水表", "012200571020");
		meter.put("新十六区46座7层清洁用水表", "012200575009");
		meter.put("新十六区46座2层清洁用水表", "012200571350");
		meter.put("新十六区45座32层清洁用水表", "012200475096");
		meter.put("新十六区45座27层清洁用水表", "012200474166");
		meter.put("新十六区45座22层清洁用水表", "012200474343");
		meter.put("新十六区45座12层清洁用水表", "012200474032");
		meter.put("新十六区45座7层清洁用水表", "012200474357");
		meter.put("新十六区45座2层清洁用水表", "012200473764");
		meter.put("新十六区43座32层清洁用水表", "012200474813");
		meter.put("新十六区43座27层清洁用水表", "012200475391");
		meter.put("新十六区43座22层清洁用水表", "012200475367");
		meter.put("新十六区43座17层清洁用水表", "012200473527");
		meter.put("新十六区43座12层清洁用水表", "012200475185");
		meter.put("新十六区43座7层清洁用水表", "012200475532");
		meter.put("新十六区43座2层清洁用水表", "012200474995");
		meter.put("新十六区52座32层清洁用水表", "012200679547");
		meter.put("新十六区52座28层清洁用水表", "012200679436");
		meter.put("新十六区52座22层清洁用水表", "012200470913");
		meter.put("新十六区52座17层清洁用水表", "012200678623");
		meter.put("新十六区52座12层清洁用水表", "012200572590");
		meter.put("新十六区52座7层清洁用水表", "012200575837");
		meter.put("新十六区52座2层清洁用水表", "012200572669");
		meter.put("新十六区51座32层清洁用水表", "012200678656");
		meter.put("新十六区51座28层清洁用水表", "012200679124");
		meter.put("新十六区51座22层清洁用水表", "012200573054");
		meter.put("新十六区51座17层清洁用水表", "012200572126");
		meter.put("新十六区51座12层清洁用水表", "012200573588");
		meter.put("新十六区51座7层清洁用水表", "012200574837");
		meter.put("新十六区51座2层清洁用水表", "012200575331");
		meter.put("新十六区50座32层清洁用水表", "012200571139");
		meter.put("新十六区50座28层清洁用水表", "012200571140");
		meter.put("新十六区50座22层清洁用水表", "012200571187");
		meter.put("新十六区50座17层清洁用水表", "012200571188");
		meter.put("新十六区50座12层清洁用水表", "012200574354");
		meter.put("新十六区50座7层清洁用水表", "012200474286");
		meter.put("新十六区50座2层清洁用水表", "012200574353");
		meter.put("新十六区49座32层清洁用水表", "012200358671");
		meter.put("新十六区49座28层清洁用水表", "012200474310");
		meter.put("新十六区49座22层清洁用水表", "012200473190");
		meter.put("新十六区49座17层清洁用水表", "012200475984");
		meter.put("新十六区49座12层清洁用水表", "012200473840");
		meter.put("新十六区49座7层清洁用水表", "012200150635");
		meter.put("新十六区49座2层清洁用水表", "012200473118");
		meter.put("新二十区1座28层清洁用水表", "130729967");
		meter.put("新二十区1座22层清洁用水表", "130651646");
		meter.put("新二十区1座18层清洁用水表", "130652818");
		meter.put("新二十区1座12层清洁用水表", "130652323");
		meter.put("新二十区1座8层清洁用水表", "130511126");
		meter.put("新二十区1座2层清洁用水表", "130730260");
		meter.put("新二十区1座负一层车库清洁用水表（1）", "130922507");
		meter.put("新二十区1座负一层车库清洁用水表（2）", "130922481");
		meter.put("新二十区2座28层清洁用水表", "130730478");
		meter.put("新二十区2座22层清洁用水表", "130730512");
		meter.put("新二十区2座18层清洁用水表", "130652317");
		meter.put("新二十区2座12层清洁用水表", "130511014");
		meter.put("新二十区2座8层清洁用水表", "130503807");
		meter.put("新二十区2座2层清洁用水表", "130503974");
		meter.put("新二十区3座32层清洁用水表", "130533520");
		meter.put("新二十区3座28层清洁用水表", "130512896");
		meter.put("新二十区3座22层清洁用水表", "130533217");
		meter.put("新二十区3座18层清洁用水表", "130510769");
		meter.put("新二十区3座12层清洁用水表", "130532817");
		meter.put("新二十区3座8层清洁用水表", "130511197");
		meter.put("新二十区3座2层清洁用水表", "130533517");
		meter.put("新二十区5座32层清洁用水表", "130533129");
		meter.put("新二十区5座28层清洁用水表", "130533354");
		meter.put("新二十区5座22层清洁用水表", "130533146");
		meter.put("新二十区5座18层清洁用水表", "130533090");
		meter.put("新二十区5座12层清洁用水表", "130511101");
		meter.put("新二十区5座8层清洁用水表", "130533145");
		meter.put("新二十区5座2层清洁用水表", "130510815");
		meter.put("新二十区6座负一层清洁用水表（1）", "130922472");
		meter.put("新二十区6座负一层清洁用水表（2）", "130925509");
		meter.put("新二十区5座负一层清洁用水表(1)", "130922639");
		meter.put("新二十区6座32层清洁用水表", "130533079");
		meter.put("新二十区6座28层清洁用水表", "130512975");
		meter.put("新二十区6座22层清洁用水表", "130536780");
		meter.put("新二十区6座18层清洁用水表", "130538930");
		meter.put("新二十区6座12层清洁用水表", "130533246");
		meter.put("新二十区6座8层清洁用水表", "130533040");
		meter.put("新二十区6座2层清洁用水表", "130533035");
		meter.put("新二十区7座32层清洁用水表", "130538382");
		meter.put("新二十区7座28层清洁用水表", "130827641");
		meter.put("新二十区7座22层清洁用水表", "130651944");
		meter.put("新二十区7座18层清洁用水表", "130651520");
		meter.put("新二十区7座12层清洁用水表", "130730222");
		meter.put("新二十区7座8层清洁用水表", "130826974");
		meter.put("新二十区7座2层清洁用水表", "130651186");
		meter.put("新二十区8座负一层清洁用水表（1）", "130925466");
		meter.put("新二十区8座32层清洁用水表", "130533446");
		meter.put("新二十区8座28层清洁用水表", "130511037");
		meter.put("新二十区8座22层清洁用水表", "130619607");
		meter.put("新二十区8座18层清洁用水表", "130825708");
		meter.put("新二十区8座12层清洁用水表", "130535723");
		meter.put("新二十区8座8层清洁用水表", "130548094");
		meter.put("新二十区8座2层清洁用水表", "130925485");
		meter.put("新二十区9座32层清洁用水表", "130537937");
		meter.put("新二十区9座28层清洁用水表", "130619623");
		meter.put("新二十区9座22层清洁用水表", "130512967");
		meter.put("新二十区9座18层清洁用水表", "130652328");
		meter.put("新二十区9座12层清洁用水表", "130504234");
		meter.put("新二十区9座8层清洁用水表", "130652870");
		meter.put("新二十区9座2层清洁用水表", "130652172");
		meter.put("新二十区10座32层清洁用水表", "130512907");
		meter.put("新二十区10座28层清洁用水表", "130532978");
		meter.put("新二十区10座22层清洁用水表", "130652688");
		meter.put("新二十区10座18层清洁用水表", "130533310");
		meter.put("新二十区10座12层清洁用水表", "130533769");
		meter.put("新二十区10座8层清洁用水表", "130535370");
		meter.put("新二十区10座2层清洁用水表", "130512856");
		meter.put("新二十区11座负一层清洁用水表", "130922443");
		meter.put("新二十区11座32层清洁用水表", "130515889");
		meter.put("新二十区11座28层清洁用水表", "130652338");
		meter.put("新二十区11座22层清洁用水表", "130729978");
		meter.put("新二十区11座18层清洁用水表", "130730501");
		meter.put("新二十区11座12层清洁用水表", "130533281");
		meter.put("新二十区11座8层清洁用水表", "130508703");
		meter.put("新二十区11座2层清洁用水表", "130535662");
		meter.put("新二十区7座公厕用水", "130615783");
		meter.put("新十八区负二层清洁用水-产权", "034170900430-1");
		meter.put("新十八区负二层清洁用水-人防", "034170900430-2");
		meter.put("1区与3区园林路灯电表（2015-30210313）", "30210313");
		meter.put("1综合楼监控室用电表", "2015032414002602");
		meter.put("1综合楼监控室用电表1", "2009047741");
		meter.put("1综合楼监控室用电表2（物业监控室监控）", "120666010");
		meter.put("2区01座（1、2）梯灯电表(01456858)", "EAD01456858");
		meter.put("2区02座（1、2）梯灯电表A（2107054138）", "03591DY00000002107054138");
		meter.put("2区02座（3、4）梯灯电表(04157618)", "EAD01457618");
		meter.put("2区03座（1、2）梯灯电表(01457768)", "EAD01457768");
		meter.put("2区03座（3、4）梯灯电表(01457044)", "EAD01457044");
		meter.put("2区05座（1、2）梯灯电表(01450093)", "EAD01450093");
		meter.put("2区05座（3、4）梯灯电表(01450098)", "EAD01450098");
		meter.put("2区06座（1、2）电梯电表(20090563798)", "20090563798");
		meter.put("2区07座（1、2）电梯电表(20090563794)", "20090563794");
		meter.put("2区07座（3、4）电梯电表(20090563717)", "20090563717");
		meter.put("2区08座（1、2）电梯电表(2004305008980)", "2004305008980");
		meter.put("2区08座（3、4）电梯电表(20090563735)", "20090563735");
		meter.put("2区09座（1、2）梯灯电表(01454360)", "EAD01454360");
		meter.put("2区09座（3、4）梯灯电表(01454557)", "EAD01454557");
		meter.put("2区10座（1、2）电梯电表(20090563724)", "20090563724");
		meter.put("2区10座（3、4）电梯电表(2009046618)", "2009046618");
		meter.put("2区11座（1、2）电梯电表(20090563732)", "20090563732");
		meter.put("2区12座（1、2）电梯电表(20090563712)", "20090563712");
		meter.put("2区12座（3、4）电梯电表(20090563737)", "20090563737");
		meter.put("2区13座（1、2）电梯电表(20090563716)", "20090563716");
		meter.put("2区15座（1、2）梯灯电表(01457345)", "EAD01457345");
		meter.put("2区15座（3、4）梯灯电表(01457349)", "EAD01457349");
		meter.put("2区16座（1、2）梯灯电表(01452005)", "EAD01452005");
		meter.put("2区16座（3、4）梯灯电表(01452002)", "EAD01452002");
		meter.put("2区17座（1、2）梯灯电表（1603642424）", "03061DY00000001603642424");
		meter.put("2区17座（3、4）梯灯电表(01453756)", "EAD01453756");
		meter.put("2区18座（1、2）梯灯电表(01456242) 2", "EAD01456242");
		meter.put("2区19座（1、2）梯灯电表(01457799)", "EAD01457799");
		meter.put("2区19座（3、4）梯灯电表(01457803)", "EAD01457803");
		meter.put("2区车库电表（2009047776）", "2009047776");
		meter.put("2区二次供水电表(1604074367)2", "03061SF00000001604074367");
		meter.put("2区后山路灯（2009047327）", "2009047327");
		meter.put("2区景观大道路灯（20090563821）", "20090563821");
		meter.put("2区右侧路灯（2009047310）", "2009047310");
		meter.put("2区智能化机房", "20090563721");
		meter.put("3区车库电表（2009047086）", "47086");
		meter.put("3区节点房智能化电表", "40000003");
		meter.put("4区路灯电表（2010073687）", "2010073687");
		meter.put("4区智能化电表", "01053209");
		meter.put("7区6号路灯电表（2012-88003137）", "2012-88003137");
		meter.put("7区路灯电表（563738）", "20090563738");
		meter.put("7区智能化机房(2014305008988)", "2014305008988");
		meter.put("9区路灯电表", "131153016");
		meter.put("9区智能化电表", "10112455");
		meter.put("凤凰台园林照明用电", "131153027");
		meter.put("凤凰台智能化机房用电", "131153041");
		meter.put("湖边广场表2", "2008-019987");
		meter.put("明湖竹林路灯照明电表", "1208337593");
		meter.put("南大门岗亭外墙及车道照明用电表", "2012311030242");
		meter.put("商铺公共电表", "2008308032899");
		meter.put("十八区二期#3电房配电房及加压风机", "201707595");
		meter.put("十八区二期#4电房配电房及加压风机", "2021011570005109");
		meter.put("十八区二期#5电房配电房及加压风机", "201707625");
		meter.put("十八区二期#6电房配电房及加压风机", "201707551");
		meter.put("十八区二期#7电房配电房及加压风机", "201707557");
		meter.put("十八区二期#8电房配电房及加压风机", "201707589");
		meter.put("十八区二期#9电房配电房及加压风机", "201707599");
		meter.put("十八区二期41座A公共+应急照明灯电表", "03001SF00000211700154171");
		meter.put("十八区二期41座A航空障碍灯电表", "03001SF00000211700154170");
		meter.put("十八区二期41座A客梯电表", "03001SF00000211700145993");
		meter.put("十八区二期41座A消防电梯电表", "03001SF00000211700154168");
		meter.put("十八区二期41座B公共+应急照明灯电表", "03001SF00000211700145992");
		meter.put("十八区二期41座B航空障碍灯电表", "03001SF00000211700145991");
		meter.put("十八区二期41座B客梯电表", "03001SF00000211700145988");
		meter.put("十八区二期41座B消防电梯电表", "03001SF00000211700145989");
		meter.put("十八区二期42座A公共+应急照明灯电表", "03001SF00000211700146703");
		meter.put("十八区二期42座A航空障碍灯电表", "03001SF00000211700146702");
		meter.put("十八区二期42座A客梯电表", "03001SF00000211700146404");
		meter.put("十八区二期42座A消防电梯电表", "03001SF00000211700146403");
		meter.put("十八区二期42座B公共+应急照明灯电表", "03001SF00000211700146405");
		meter.put("十八区二期42座B航空障碍灯电表", "03001SF00000211700146406");
		meter.put("十八区二期42座B客梯电表", "03001SF00000211700154172");
		meter.put("十八区二期42座B消防电梯电表", "03001SF00000211700154173");
		meter.put("十八区二期43座A公共+应急照明灯电表", "03001SF00000211700149461");
		meter.put("十八区二期43座A航空障碍灯电表", "03061SF00000001603980282");
		meter.put("十八区二期43座A客梯电表", "03061SF00000001603978075");
		meter.put("十八区二期43座A消防电梯电表", "03001SF00000211700149458");
		meter.put("十八区二期43座B公共+应急照明灯电表", "03001SF00000211700149459");
		meter.put("十八区二期43座B航空障碍灯电表", "03001SF00000211700146707");
		meter.put("十八区二期43座B客梯电表", "03001SF00000211700146704");
		meter.put("十八区二期43座B消防电梯电表", "03001SF00000211700146705");
		meter.put("十八区二期45座A公共+应急照明灯电表", "03001SF00000211700140674");
		meter.put("十八区二期45座A航空障碍灯电表", "03001SF00000211700140675");
		meter.put("十八区二期45座A客梯电表", "03001SF00000211700161902");
		meter.put("十八区二期45座A消防电梯电表", "03001SF00000211700161903");
		meter.put("十八区二期45座B公共+应急照明灯电表", "03001SF00000211700161906");
		meter.put("十八区二期45座B航空障碍灯电表", "03001SF00000211700161905");
		meter.put("十八区二期45座B客梯电表", "03001SF00000211700161907");
		meter.put("十八区二期45座B消防电梯电表", "03001SF00000211700140677");
		meter.put("十八区二期46座A公共+应急照明灯电表", "03001SF00000211700148296");
		meter.put("十八区二期46座A航空障碍灯电表", "03001SF00000211700148295");
		meter.put("十八区二期46座A客梯电表", "03001SF00000211700148292");
		meter.put("十八区二期46座A消防电梯电表", "03001SF00000211700148293");
		meter.put("十八区二期46座B公共+应急照明灯电表", "03001SF00000211700161897");
		meter.put("十八区二期46座B航空障碍灯电表", "03001SF00000211700161896");
		meter.put("十八区二期46座B客梯电表", "03001SF00000211700148297");
		meter.put("十八区二期46座B消防电梯电表", "03001SF00000211700140672");
		meter.put("十八区二期47座A公共+应急照明灯电表", "03001SF00000211700149377");
		meter.put("十八区二期47座A航空障碍灯电表", "03001SF00000211700149376");
		meter.put("十八区二期47座A客梯电表", "03001SF00000211700149373");
		meter.put("十八区二期47座A消防电梯电表", "03001SF00000211700149374");
		meter.put("十八区二期47座B公共+应急照明灯电表", "03001SF00000211700149372");
		meter.put("十八区二期47座B航空障碍灯电表", "03001SF00000211700161901");
		meter.put("十八区二期47座B客梯电表", "03001SF00000211700161898");
		meter.put("十八区二期47座B消防电梯电表", "03001SF00000211700161899");
		meter.put("十八区二期48座A公共+应急照明灯电表", "03061SF00000001604045430");
		meter.put("十八区二期48座A航空障碍灯电表", "03061SF00000001604033068");
		meter.put("十八区二期48座A客梯电表", "03001SF00000211700154827");
		meter.put("十八区二期48座A消防电梯电表", "03061SF00000001603979196");
		meter.put("十八区二期48座B公共+应急照明灯电表", "03001SF00000211700154826");
		meter.put("十八区二期48座B航空障碍灯电表", "03001SF00000211700154825");
		meter.put("十八区二期48座B客梯电表", "03001SF00000211700154822");
		meter.put("十八区二期48座B消防电梯电表", "03001SF00000211700154823");
		meter.put("十八区二期地下二层防火分区1、3、6消防电表箱", "201707505");
		meter.put("十八区二期地下二层防火分区2、4消防电表箱", "201707497");
		meter.put("十八区二期地下二层防火分区5、8消防电表箱", "201707521");
		meter.put("十八区二期地下二层防火分区7、9消防电表箱", "201707509");
		meter.put("十八区二期地下二层普通照明电表1", "201707523");
		meter.put("十八区二期地下二层普通照明电表2", "201707507");
		meter.put("十八区二期地下二层潜水泵电表", "201707517");
		meter.put("十八区二期地下二层应急照明1", "201707529");
		meter.put("十八区二期地下二层应急照明2", "201707575");
		meter.put("十八区二期地下一层防火分区1、2、9消防电表箱", "201708161");
		meter.put("十八区二期地下一层防火分区3、8、10消防电表箱", "201707489");
		meter.put("十八区二期地下一层防火分区4、5、11消防电表箱", "201707515");
		meter.put("十八区二期地下一层防火分区6、7、13消防电表箱", "201707493");
		meter.put("十八区二期地下一层普通照明电表", "201707503");
		meter.put("十八区二期地下一层应急照明", "201707525");
		meter.put("十八区二期高层路灯用电电表", "180106005225");
		meter.put("十八区二期弱电间", "201707555");
		meter.put("十八区二期生活水泵电表", "201707527");
		meter.put("十八区二期消防控制器", "201707533");
		meter.put("十八区二期智能机房（2）", "2019011650044954");
		meter.put("十六区10座公共+应急照明用电", "03001SF00012512000007416");
		meter.put("十六区10座客梯用电", "03001SF00012512000007102");
		meter.put("十六区10座屋面层照明用电", "03001SF00012512000007101");
		meter.put("十六区10座消防梯用电", "03001SF00012512000007415");
		meter.put("十六区11座、17座空中花园照明", "2006385997");
		meter.put("十六区11座公共+应急照明用电", "03001SF00012512000001932");
		meter.put("十六区11座客梯用电", "03001SF00012512000007386");
		meter.put("十六区11座屋面层照明用电", "03001SF00012512000007385");
		meter.put("十六区11座消防梯用电", "03001SF00012512000001931");
		meter.put("十六区17座公共+应急照明用电", "03001SF00012512000001922");
		meter.put("十六区17座客梯用电", "03001SF00012512000001930");
		meter.put("十六区17座屋面层照明用电", "03001SF00012512000001929");
		meter.put("十六区17座消防梯用电", "03001SF00012512000001921");
		meter.put("十六区18座公共+应急照明用电", "03001SF00012512000001334");
		meter.put("十六区18座客梯用电", "03001SF00012512000002086");
		meter.put("十六区18座屋面层照明用电", "03001SF00012512000002085");
		meter.put("十六区18座消防梯用电", "03001SF00012512000001333");
		meter.put("十六区20座公共+应急照明用电", "03001SF00012512000004295");
		meter.put("十六区20座客梯用电", "03001SF00012512000004292");
		meter.put("十六区20座屋面层照明用电", "03001SF00012512000004296");
		meter.put("十六区20座消防梯用电", "03001SF00012512000004291");
		meter.put("十六区21座公共+应急照明用电", "03001SF00012512000004264");
		meter.put("十六区21座客梯用电", "03001SF00012512000004257");
		meter.put("十六区21座屋面层照明用电", "03001SF00012512000004263");
		meter.put("十六区21座消防梯用电", "03001SF00012512000004258");
		meter.put("十六区22座公共+应急照明用电", "03001SF00011222000049352");
		meter.put("十六区22座客梯用电", "03001SF00011222000049353");
		meter.put("十六区22座屋面层照明用电", "03001SF00011222000048049");
		meter.put("十六区22座消防梯用电", "03001SF00011222000048164");
		meter.put("十六区23座公共+应急照明用电", "03001SF00012512000002883");
		meter.put("十六区23座客梯用电", "03001SF00012512000003823");
		meter.put("十六区23座屋面层照明用电", "03001SF00012512000002884");
		meter.put("十六区23座消防梯用电", "03001SF00012512000003824");
		meter.put("十六区25座公共+应急照明用电", "03001SF00011222000047889");
		meter.put("十六区25座客梯用电", "03001SF00011222000048037");
		meter.put("十六区25座屋面层照明用电", "03001SF00011222000047888");
		meter.put("十六区25座消防梯用电", "03001SF00011222000048036");
		meter.put("十六区26座公共+应急照明用电", "03001SF00011222000048040");
		meter.put("十六区26座客梯用电", "03001SF00011222000049095");
		meter.put("十六区26座首层消防控制室", "03001SF00011222000049523");
		meter.put("十六区26座屋面层照明用电", "03001SF00011222000048041");
		meter.put("十六区26座消防梯用电", "03001SF00011222000049094");
		meter.put("十六区27座公共+应急照明用电", "03001SF00011222000049653");
		meter.put("十六区27座客梯用电", "03001SF00011222000049418");
		meter.put("十六区27座屋面层照明用电", "03001SF00011222000049419");
		meter.put("十六区27座消防梯用电", "03001SF00011222000049652");
		meter.put("十六区28座公共+应急照明用电", "03001SF00011222000050404");
		meter.put("十六区28座客梯用电", "03001SF00011222000048590");
		meter.put("十六区28座屋面层照明用电", "03001SF00011222000048591");
		meter.put("十六区28座消防梯用电", "03001SF00011222000050406");
		meter.put("十六区29座公共+应急照明用电", "03001SF00012512000001340");
		meter.put("十六区29座客梯用电", "03001SF00012512000001347");
		meter.put("十六区29座生活水泵房1（29座低、中区）", "03001SF00000221800017488");
		meter.put("十六区29座生活水泵房2（29座高区）", "03001SF00000221800017487");
		meter.put("十六区29座屋面层照明用电", "03001SF00012512000001339");
		meter.put("十六区29座消防水泵房用电", "03001SF00000221800017976");
		meter.put("十六区29座消防梯用电", "03001SF00012512000001348");
		meter.put("十六区2座公共+应急照明用电", "03001SF00012512000001275");
		meter.put("十六区2座客梯用电", "03001SF00012512000001666");
		meter.put("十六区2座屋面层照明用电", "03001SF00012512000001276");
		meter.put("十六区2座消防梯用电", "03001SF00012512000001665");
		meter.put("十六区30座公共+应急照明用电", "03001SF00012512000001663");
		meter.put("十六区30座客梯用电", "03001SF00012512000001668");
		meter.put("十六区30座屋面层照明用电", "03001SF00012512000001664");
		meter.put("十六区30座消防梯用电", "03001SF00012512000001667");
		meter.put("十六区31座公共+应急照明用电", "03001SF00012512000004293");
		meter.put("十六区31座客梯用电", "03001SF00012512000004290");
		meter.put("十六区31座屋面层照明用电", "03001SF00012512000004294");
		meter.put("十六区31座消防梯用电", "03001SF00012512000004289");
		meter.put("十六区32座公共+应急照明用电", "03001SF00011212000045539");
		meter.put("十六区32座客梯用电", "03001SF00011212000045540");
		meter.put("十六区32座屋面层照明用电", "03001SF00011212000046538");
		meter.put("十六区32座消防梯用电", "03001SF00011212000046540");
		meter.put("十六区33座公共+应急照明用电", "03001SF00011212000048257");
		meter.put("十六区33座客梯用电", "03001SF00011212000048260");
		meter.put("十六区33座屋面层照明用电", "03001SF00011212000047452");
		meter.put("十六区33座消防梯用电", "03001SF00011212000047451");
		meter.put("十六区35座公共+应急照明用电", "03001SF00011222000048017");
		meter.put("十六区35座客梯用电", "03001SF00011222000049093");
		meter.put("十六区35座屋面层照明用电", "03001SF00011222000048016");
		meter.put("十六区35座消防梯用电", "03001SF00011222000049092");
		meter.put("十六区36座公共+应急照明用电", "03001SF00011222000048050");
		meter.put("十六区36座客梯用电", "03001SF00011222000049416");
		meter.put("十六区36座屋面层照明用电", "03001SF00011222000048051");
		meter.put("十六区36座消防梯用电", "03001SF00011222000049417");
		meter.put("十六区37座公共+应急照明用电", "03001SF00011222000048035");
		meter.put("十六区37座客梯用电", "03001SF00011222000048332");
		meter.put("十六区37座屋面层照明用电", "03001SF00011222000048032");
		meter.put("十六区37座消防梯用电", "03001SF00011222000048333");
		meter.put("十六区38座公共+应急照明用电", "03001SF00011222000050468");
		meter.put("十六区38座客梯用电", "03001SF00011222000048033");
		meter.put("十六区38座屋面层照明用电", "03001SF00011222000050471");
		meter.put("十六区38座消防梯用电", "03001SF00011222000048034");
		meter.put("十六区39座公共+应急照明用电", "03001SF00011222000050931");
		meter.put("十六区39座客梯用电", "03001SF00011222000050930");
		meter.put("十六区39座屋面层照明用电", "03001SF00011222000048165");
		meter.put("十六区39座消防梯用电", "03001SF00011222000048167");
		meter.put("十六区3座公共+应急照明用电", "03001SF00012512000001273");
		meter.put("十六区3座客梯用电", "03001SF00012512000001672");
		meter.put("十六区3座屋面层照明用电", "03001SF00012512000001274");
		meter.put("十六区3座消防梯用电", "03001SF00012512000001671");
		meter.put("十六区40座公共+应急照明用电", "03001SF00011222000048018");
		meter.put("十六区40座客梯用电", "03001SF00011222000048019");
		meter.put("十六区40座生活水泵房用电1（40座低、中区）", "03001SF00000221800018730");
		meter.put("十六区40座生活水泵房用电2（40座高区）", "03001SF00000221800017635");
		meter.put("十六区40座屋面层照明用电", "03001SF00011222000048166");
		meter.put("十六区40座消防水泵房用电", "03001SF00000221800019213");
		meter.put("十六区40座消防梯用电", "03001SF00011222000048666");
		meter.put("十六区41座公共+应急照明用电", "03001SF00012512000002145");
		meter.put("十六区41座客梯用电", "03001SF00012512000002152");
		meter.put("十六区41座屋面层照明用电", "03001SF00012512000002146");
		meter.put("十六区41座消防梯用电", "03001SF00012512000002151");
		meter.put("十六区42座公共+应急照明用电", "03001SF00012512000002149");
		meter.put("十六区42座客梯用电", "03001SF00012512000002156");
		meter.put("十六区42座屋面层照明用电", "03001SF00012512000002150");
		meter.put("十六区42座消防梯用电", "03001SF00012512000002155");
		meter.put("十六区43座公共+应急照明用电", "03001SF00011212000044836");
		meter.put("十六区43座客梯用电", "03001SF00011212000044835");
		meter.put("十六区43座屋面层照明用电", "03001SF00011212000045167");
		meter.put("十六区43座消防梯用电", "03001SF00011212000044828");
		meter.put("十六区45座公共+应急照明用电", "03001SF00011212000014764");
		meter.put("十六区45座客梯用电", "03001SF00011212000014767");
		meter.put("十六区45座屋面层照明用电", "03001SF00011212000014765");
		meter.put("十六区45座消防梯用电", "03001SF00011212000014766");
		meter.put("十六区46座公共+应急照明用电", "03001SF00011212000044832");
		meter.put("十六区46座客梯用电", "03001SF00011212000044831");
		meter.put("十六区46座屋面层照明用电", "03001SF00011212000044830");
		meter.put("十六区46座消防梯用电", "03001SF00011212000045168");
		meter.put("十六区47座公共+应急照明用电", "03001SF00011212000047851");
		meter.put("十六区47座客梯用电", "03001SF00011212000044826");
		meter.put("十六区47座屋面层照明用电", "03001SF00011212000047330");
		meter.put("十六区47座消防梯用电", "03001SF00011212000044825");
		meter.put("十六区48座公共+应急照明用电", "03001SF00011212000044833");
		meter.put("十六区48座客梯用电", "03001SF00011212000046854");
		meter.put("十六区48座屋面层照明用电", "03001SF00011212000044834");
		meter.put("十六区48座消防梯用电", "03001SF00011212000046853");
		meter.put("十六区49座公共+应急照明用电", "03001SF00011212000045773");
		meter.put("十六区49座客梯用电", "03001SF00011212000047318");
		meter.put("十六区49座屋面层照明用电", "03001SF00011212000045774");
		meter.put("十六区49座消防梯用电", "03001SF00011212000047329");
		meter.put("十六区50座公共+应急照明用电", "03001SF00011212000047317");
		meter.put("十六区50座客梯用电", "03001SF00011212000047852");
		meter.put("十六区50座屋面层照明用电", "03001SF00011212000047326");
		meter.put("十六区50座消防梯用电", "03001SF00011212000048361");
		meter.put("十六区51座公共+应急照明用电", "03001SF00011212000045537");
		meter.put("十六区51座客梯用电", "03001SF00011212000045541");
		meter.put("十六区51座屋面层照明用电", "03001SF00011212000045538");
		meter.put("十六区51座消防梯用电", "03001SF00011212000045542");
		meter.put("十六区52座公共+应急照明用电", "03001SF00011212000045548");
		meter.put("十六区52座客梯用电", "03001SF00011212000045544");
		meter.put("十六区52座屋面层照明用电", "03001SF00011212000045547");
		meter.put("十六区52座消防梯用电", "03001SF00011212000045543");
		meter.put("十六区5座公共+应急照明用电", "03001SF00012512000001852");
		meter.put("十六区5座客梯用电", "03001SF00012512000001351");
		meter.put("十六区5座屋面层照明用电", "03001SF00012512000001851");
		meter.put("十六区5座消防梯用电", "03001SF00012512000001352");
		meter.put("十六区6座公共+应急照明用电", "03001SF00012512000003370");
		meter.put("十六区6座客梯用电", "03001SF00012512000001337");
		meter.put("十六区6座屋面层照明用电", "03001SF00012512000003369");
		meter.put("十六区6座消防梯用电", "03001SF00012512000001338");
		meter.put("十六区7座公共+应急照明用电", "03001SF00012512000004254");
		meter.put("十六区7座客梯用电", "03001SF00012512000001349");
		meter.put("十六区7座屋面层照明用电", "03001SF00012512000007333");
		meter.put("十六区7座消防梯用电", "03001SF00012512000001350");
		meter.put("十六区8座公共+应急照明用电", "03001SF00012512000001346");
		meter.put("十六区8座客梯用电", "03001SF00012512000008050");
		meter.put("十六区8座屋面层照明用电", "03001SF00012512000008049");
		meter.put("十六区8座消防梯用电", "03001SF00012512000001345");
		meter.put("十六区9座公共+应急照明用电", "03001SF00012512000007104");
		meter.put("十六区9座客梯用电", "03001SF00012512000002084");
		meter.put("十六区9座屋面层照明用电", "03001SF00012512000002083");
		meter.put("十六区9座消防梯用电", "03001SF00012512000007103");
		meter.put("十六区产权负一层3-5、7-9防火分区车库排水泵用电", "03001SF00000221800017675");
		meter.put("十六区产权负一层3-5、8-9防火分区车库公共照明", "03001SF00000221800017489");
		meter.put("十六区产权负一层3-5、8、9防火分区车库应急照明", "03001DY00012442000029874");
		meter.put("十六区产权负一层3防火分区动力用电", "03001SF00000221800017674");
		meter.put("十六区产权负一层4防火分区动力用电", "03001SF00000221800017676");
		meter.put("十六区产权负一层5防火分区动力用电", "03001SF00000221800017486");
		meter.put("十六区产权负一层7防火分区动力用电", "03001SF00000221800018687");
		meter.put("十六区产权负一层8防火分区动力用电", "03001SF00000221800017977");
		meter.put("十六区产权负一层人防7分区普通照明用电", "2020012140027614");
		meter.put("十六区产权负一层人防7分区应急照明用电", "2020012140027615");
		meter.put("十六区负二层25-29防火分区车库公共照明", "03001SF00000221800012130");
		meter.put("十六区负二层25-29防火分区车库排水泵用电", "03001SF00000221800017449");
		meter.put("十六区负二层25-29防火分区车库应急照明", "03001DY00012442000041092");
		meter.put("十六区负二层25防火分区动力用电", "03001SF00000221800019228");
		meter.put("十六区负二层26防火分区动力用电", "03001SF00000221800018073");
		meter.put("十六区负二层27防火分区动力用电", "03001SF00000221800017446");
		meter.put("十六区负二层28防火分区动力用电", "03001SF00000221800017447");
		meter.put("十六区负二层29防火分区动力用电", "03001SF00000221800017448");
		meter.put("十六区负二层30-35防火分区车库公共照明用电", "03001SF00000221800012009");
		meter.put("十六区负二层30-35防火分区车库排水泵用电", "03001SF00000221800019184");
		meter.put("十六区负二层30-35防火分区车库应急照明用电", "03001SF00011222000048667");
		meter.put("十六区负二层30防火分区动力用电", "03001SF00000221800012065");
		meter.put("十六区负二层31防火分区动力用电", "03001SF00000221800012131");
		meter.put("十六区负二层32防火分区动力用电", "03001SF00000221800019185");
		meter.put("十六区负二层33防火分区动力用电", "03001SF00000221800019177");
		meter.put("十六区负二层34防火分区动力用电", "03001SF00000221800017647");
		meter.put("十六区负二层35防火分区动力用电", "03001SF00000221800017624");
		meter.put("十六区负二层车库人防电站（43座）", "03001SF00000221800019182");
		meter.put("十六区负一层10防火分区动力用电", "03001SF00000221800018127");
		meter.put("十六区负一层11防火分区动力用电", "03001SF00000221800012059");
		meter.put("十六区负一层12防火分区动力用电", "03001SF00000221800017619");
		meter.put("十六区负一层13防火分区动力用电", "03001SF00000221800017620");
		meter.put("十六区负一层14防火分区动力用电", "03001SF00000221800017974");
		meter.put("十六区负一层15防火分区动力用电", "03001SF00000221800011993");
		meter.put("十六区负一层16防火分区动力用电", "03001SF00000221800017636");
		meter.put("十六区负一层17防火分区动力用电", "03001SF00000221800017649");
		meter.put("十六区负一层18防火分区动力用电", "03001SF00000221800017618");
		meter.put("十六区负一层19防火分区动力用电", "03001SF00011212000046537");
		meter.put("十六区负一层1防火分区动力用电", "03001SF00000221800019229");
		meter.put("十六区负一层20防火分区动力用电", "03001SF00000221800017648");
		meter.put("十六区负一层21防火分区动力用电", "03001SF00000221800017646");
		meter.put("十六区负一层22防火分区动力用电", "03001SF00000221800017625");
		meter.put("十六区负一层23防火分区动力用电", "03001SF00000221800018176");
		meter.put("十六区负一层24防火分区动力用电", "03001SF00000221800018240");
		meter.put("十六区负一层2防火分区动力用电", "03001SF00000221800017975");
		meter.put("十六区负一层6防火分区动力用电", "03001SF00000221800012007");
		meter.put("十六区负一层6防火分区弱电用电", "03001SF00011222000049522");
		meter.put("十六区负一层7防火分区弱电机房", "03001SF00011222000048099");
		meter.put("十六区负一层9防火分区动力用电", "03001SF00000221800019226");
		meter.put("十六区空中花园园林景观1用电", "03001SF00000221800017677");
		meter.put("十六区空中花园园林景观2用电", "03001SF00000221800019227");
		meter.put("十六区空中花园园林景观3用电", "03001SF00000221800017622");
		meter.put("十六区人防负一层 1、2、6、7、10-12防火分区车库公共", "03001SF00000221800018126");
		meter.put("十六区人防负一层1、2、6、7、10-12防火分区车库应急", "03001SF00011222000048048");
		meter.put("十六区人防负一层12、13、18、23防火分区车库排水泵", "03001SF00000221800018177");
		meter.put("十六区人防负一层13、18、23、24防火分区车库公共", "03001SF00000221800017623");
		meter.put("十六区人防负一层13、18、23、24防火分区车库应急", "03001SF00011232000012488");
		meter.put("十六区人防负一层14、15、19防火分区车库公共照明", "03001SF00011212000046539");
		meter.put("十六区人防负一层14、15、19防火分区车库排水泵用电", "03001SF00000221800018534");
		meter.put("十六区人防负一层14、15、19防火分区车库应急照明", "03001DY00012472000002340");
		meter.put("十六区人防负一层16、17、20-22防火分区车库公共", "03001SF00000221800017637");
		meter.put("十六区人防负一层16、17、20-22防火分区车库应急", "03001SF00011212000044827");
		meter.put("十六区人防负一层9分区普通照明", "2020012140027609");
		meter.put("十六区人防负一层9分区应急照明", "2020012140027613");
		meter.put("十六区人防负一层车库电站（38座）", "03001SF00000221700355714");
		meter.put("十六区人防负一层车库电站（52座）", "03001SF00000221800018238");
		meter.put("十六区人防负一层防火分区9排水泵用电", "190821032211");
		meter.put("十六区西大门高杆灯电表", "2013-86186151");
		meter.put("小湖湾1#箱（照明及水景）电表", "130629628");
		meter.put("小湖湾2#箱（照明及水景、单车棚）电表", "1208337590");
		meter.put("欣园01座电梯电表A", "03001SF00000231700245628");
		meter.put("欣园01座公共照明A", "03001SF00000231700243880");
		meter.put("欣园02座电梯电表A", "03001SF00000231700246207");
		meter.put("欣园02座公共照明A", "03001SF00000231700244260");
		meter.put("欣园03座电梯电表A", "03001SF00000231700245658");
		meter.put("欣园03座公共照明A", "03001SF00000231700249027");
		meter.put("欣园05座电梯电表A", "03001SF00000231700249026");
		meter.put("欣园05座公共照明A", "03001SF00000231700244261");
		meter.put("欣园06座电梯电表A", "03001SF00012522000105859");
		meter.put("欣园06座公共照明2", "2014310031833");
		meter.put("欣园07座公共照明", "EED00190119");
		meter.put("欣园07座客电梯电表", "EED00189858");
		meter.put("欣园07座消防电梯电表", "EED00210080");
		meter.put("欣园08座电梯电表A", "03001SF00000231700246068");
		meter.put("欣园08座公共照明A", "03001SF00000231700246020");
		meter.put("欣园09座电梯电表", "EED00190117");
		meter.put("欣园09座公共照明", "EED00189957");
		meter.put("欣园10座电梯电表", "EED00187535");
		meter.put("欣园10座公共照明", "EED00187516");
		meter.put("欣园10座航空障碍灯", "EED00208370");
		meter.put("欣园10座混流通风机", "EED00208369");
		meter.put("欣园11座电梯电表A", "03591SF00000002100781713");
		meter.put("欣园11座公共照明", "EED00188675");
		meter.put("欣园11座航空障碍灯", "EED00208368");
		meter.put("欣园11座混流通风机", "EED00208371");
		meter.put("欣园1座航空障碍灯A", "03001SF00012212100023397");
		meter.put("欣园1座混流通风机A", "03001SF00012212100023398");
		meter.put("欣园2座航空障碍灯A", "03001SF00012212100023428");
		meter.put("欣园2座混流通风机A", "03001SF00012212100023610");
		meter.put("欣园3座航空障碍灯A", "03001SF00012212100023444");
		meter.put("欣园3座混流通风机A", "03001SF00012212100023399");
		meter.put("欣园5座航空障碍灯", "EED00213616");
		meter.put("欣园5座混流通风机", "EED00213617");
		meter.put("欣园6座航空障碍灯A", "03001SF00012212100026550");
		meter.put("欣园6座混流通风机A", "03001SF00012212100023609");
		meter.put("欣园7座航空障碍灯", "EED00210083");
		meter.put("欣园7座混流通风机", "EED00210079");
		meter.put("欣园8座航空障碍灯", "EED00213612");
		meter.put("欣园8座混流通风机", "EED00213615");
		meter.put("欣园9座航空障碍灯", "EED00207189");
		meter.put("欣园9座混流通风机", "EED00207188");
		meter.put("欣园车库顶板公共照明1电表 2", "160611351484");
		meter.put("欣园车库顶板公共照明2电表", "2013-117114");
		meter.put("欣园二次供水电表", "EED00189179");
		meter.put("欣园负二层车库照明", "EED00189274");
		meter.put("欣园负一层车库照明", "EED00188730");
		meter.put("欣园弱电节点机房", "EED20090921769");
		meter.put("欣园外景观路及湖滨公园照明电表", "12218048293");
		meter.put("新三区清洁用水（2022.8月换新表）", "090443082");

		if (meter.containsKey(meterName)) {
			meterCode = meter.get(meterName);
		}

		return meterCode;
	}

}
