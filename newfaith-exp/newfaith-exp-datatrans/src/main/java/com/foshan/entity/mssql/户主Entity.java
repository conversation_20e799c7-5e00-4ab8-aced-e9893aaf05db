package com.foshan.entity.mssql;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;

import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
@Table(name = "户主")
public class 户主Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3971605028190689823L;
	@EmbeddedId
	@AttributeOverrides({ @AttributeOverride(name = "户主ID", column = @Column),
			@AttributeOverride(name = "单元ID", column = @Column) })
	private 户主Id id;
	/** @pdOid 2170cd85-746e-4d48-8b3f-94b5ec47900c */
	public String 姓名;
	/** @pdOid abd74061-0503-4c7b-b6cc-3d2658e5f036 */
	public String 性别;
	/** @pdOid 575d4309-3129-4a49-bf5b-43f5ba76a797 */
	public Date 出生日期;
	/** @pdOid 5ca296e4-a9ad-4b87-9866-67bdb9b0fb58 */
	public String 民族;
	/** @pdOid cb839eb2-ccba-4882-8e4d-11e436e9704e */
	public String 籍贯;
	/** @pdOid 0e59aace-25c2-473f-8d87-366dd4057ea6 */
	public String 户口所在地派出所;
	/** @pdOid b8e9284b-b209-46a8-91c5-d5d64544f852 */
	public String 银行帐户;
	/** @pdOid f57019e3-f80d-489f-871f-2f315ef0c1fd */
	public String 工作单位;
	/** @pdOid abb63066-a95a-4528-a073-d23df2d64f07 */
	public String 职务;
	/** @pdOid 5b329185-846e-4093-a4a3-df6dda7607a2 */
	public String 联系地址;
	/** @pdOid bcfccaaa-89ea-468e-8970-76e6c21a4ca6 */
	public String 邮政编码;
	/** @pdOid c19aadff-7279-4ef7-8754-ff64ff0a5cea */
	public String 电话;
	/** @pdOid 954df81c-e862-47c5-896e-bc7afa909da3 */
	public String 证件号码;
	/** @pdOid 4e899c40-159f-40de-91ec-01698356e7ba */
	public String 证件类型;
	/** @pdOid a50911c2-cb83-4d38-9b3a-83e813d39b90 */
	public String 国籍;
	/** @pdOid 92192d5b-ca59-4dbc-8dc2-2cf79afcce84 */
	public Date 入住日期;
	/** @pdOid 9125e0a8-2b1c-4e1a-bbb5-e6357c4b16b5 */
	public String 备注;
	/** @pdOid 3c062e2a-6c39-4c18-82b2-dee7537e5d50 */
	public String 状态;
	/** @pdOid 721227ed-1dba-4c93-b210-215fe248c015 */
	public Date 离退日期;
	/** @pdOid 3ec11c6c-d85a-4cac-b248-d1e3c3c69f50 */
	public String 紧急联系人姓名;
	/** @pdOid 5b2275bc-ba38-403f-ae6d-a6dd7fab95a6 */
	public String 紧急联系方式;
	/** @pdOid 6edd0cce-2110-4037-9717-a43b3f0e8f03 */
	public String 商铺编号;
	/** @pdOid 050573d3-601f-4db9-8e1f-53594a50b853 */
	public String 商铺名称;
	/** @pdOid 87f3996f-9e7a-49d2-919e-4b47de935504 */
	public String 经营项目;
	/** @pdOid 8cbe7413-8912-4d6c-ae30-092c91d4e6ef */
	public Date 起租日期;
	/** @pdOid 99faa3ec-2b61-4db8-a18e-5ef69b75a04e */
	public BigDecimal 租赁期限;
	/** @pdOid a7dc0608-7e9f-4c09-b530-cd62a02eff7e */
	public String 免租期;
	/** @pdOid 63f046a7-0368-45d8-8a86-9662e30f356f */
	public String 划帐银行;
	/** @pdOid 72119d93-ac22-435e-b4dc-99165b776cef */
	public String 单位名称;
	/** @pdOid 1f1c2be6-0ed0-4ba3-b50d-72e2fee827d4 */
	public String 企业法人;
	/** @pdOid 9a421071-30f6-43b1-929c-e79d8fe3fd80 */
	public String 企业性质;
	/** @pdOid 84dcf5ed-f69b-4ad9-af04-41c7112484ef */
	public String 客户编号;
	/** @pdOid 1a208109-b1db-4600-8074-151b55d0d9ac */
	public Date 计费日期;
	/** @pdOid f8ce4aaa-8516-4df0-92d4-70fe83e15772 */
	public Date 终止日期;
	/** @pdOid 2eb277ec-8d2a-409f-b3ae-5ba896f5a514 */
	public String 帐号名;
	/** @pdOid c24476ae-ee88-4e29-8078-3e67fa2b2085 */
	public String 学历;
	/** @pdOid ec06c3e4-db9b-411a-a151-5f4eff3204eb */
	public BigDecimal 滞纳金保证金;
	/** @pdOid f31fc6b0-fbd1-48c1-91af-0f1298184ac3 */
	public String 户主类型;
	/** @pdOid c992f5d5-afdc-4147-b6cb-6f9aca606ec0 */
	public String 合同号;
	/** @pdOid bae2293b-e6ca-42c0-a614-d5705ec6bee9 */
	public String 简称;
	/** @pdOid 1ab1c5a5-b0ab-4896-afba-735f4a17bca6 */
	public Integer 交款日;
	/** @pdOid b2cd502f-4f78-4fea-b683-cf0fcb9d8efb */
	public String 续租权;
	/** @pdOid 09e2b1a2-cc29-495a-9db9-82982f4ab378 */
	public String eMail;
	/** @pdOid e68e0ce6-235e-4174-99cb-56313979d0a5 */
	public Date 合同签订日期;
	/** @pdOid 9c7f5af9-5780-4f42-aa98-2510f6419d3c */
	public String 手机;
	/** @pdOid f66f4a4b-5502-43e0-84e9-1deaeb4d36ea */
	public String 银行行号;
	/** @pdOid 12bda0f3-65b0-43f8-9c87-8c43bd72f385 */
	public String 银行协议号;
	/** @pdOid facd66d8-f663-4881-bfd8-641068ede298 */
	public String 微信手机号;
	/** @pdOid a264e2f9-3336-45d9-a258-ef49ebdd64e8 */
	public String 微信号;
	/** @pdOid 4da6b246-bfb6-45f6-ba6e-06074a8f009b */
	public String 开票类型;
	/** @pdOid 545f05b9-abc8-48d7-a076-ca4cfbdbdc84 */
	public String 购方名称;
	/** @pdOid 9b450404-b20b-483f-82b4-0d6e51e39bd3 */
	public String 购方地址电话;
	/** @pdOid 7b5dbdd2-076b-4b8b-a381-fc612e055c49 */
	public String 购方银行帐号;
	/** @pdOid c151e0ba-b7af-4149-abcb-9991a8ee1286 */
	public String 购方税号;
	/** @pdOid ba91465b-1733-4190-95c5-dcf0f6271bd3 */
	public String 购方企业类型;
	/** @pdOid 8a71f134-6153-486e-a59f-bf4e8e73648b */
	public String 招商跟进人;
	/** @pdOid 2aec0795-a4cb-4b95-b9d9-dec9dd18d036 */
	public String 客服跟进人;
	/** @pdOid 71d9c543-8a76-4af9-a72c-45f2570f0e87 */
	public String 合同地址;
	/** @pdOid 5c571709-9dad-4bc9-8542-99a9933f1d48 */
	public String 客户属性;
}