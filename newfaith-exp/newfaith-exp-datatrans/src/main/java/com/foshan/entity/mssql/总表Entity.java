package com.foshan.entity.mssql;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import com.foshan.entity.IEntityBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "总表")
public class 总表Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8698408883827275536L;
	@Id
	public String 总表ID;
	/** @pdOid c84c38eb-7180-4cb6-b507-9686bd868802 */
	public String 编号;
	/** @pdOid 5c68c5e4-7197-4228-87b0-fe794f2a7618 */
	public Integer 分摊ID;
	/** @pdOid 078ae60e-5a94-48a3-991a-aff681247146 */
	public Integer 层次;
	/** @pdOid d8eb03cb-1b93-405d-b7ab-38602183fa94 */
	public BigDecimal 底数;
	/** @pdOid 8208bc89-beee-4cdd-a3a7-20c4edd1c077 */
	public String 安表地点;
	/** @pdOid 24fc8a1e-93bf-4325-8065-f6e6d33d2d76 */
	public Integer 公用 = 0;
	/** @pdOid b744347a-de2c-4881-a786-1abd6ec9b0de */
	public String 备注;
	/** @pdOid cc85d098-2f1a-4566-a472-ddee577c970f */
	public Integer 使用固定损耗率 = 0;
	/** @pdOid 95cc6a79-a4ea-4bfb-8291-69eebc1e8fb9 */
	public BigDecimal 损耗率;
	/** @pdOid 034e9c47-010c-486b-b8c8-92e7a694580d */
	public Integer 参与损耗分摊 = 1;
	/** @pdOid 0166f7b5-e0ba-4dd8-988e-9e416fcef442 */
	public Date 表坏日期;
	/** @pdOid a38df62f-7837-487d-b97e-0699555236b9 */
	public String 分摊费用名称;
	/** @pdOid a6cb9338-5a1f-4dfc-a07b-a8333d1d79b5 */
	public BigDecimal 额外用量;
	/** @pdOid 8107f7d8-d6fc-41de-91e5-e0a1850e1a90 */
	public String 额外说明;
	/** @pdOid ee602e90-2bb6-452e-9586-d2fd18c4d967 */
	public Integer 是否面积比例;
	/** @pdOid 4c2219f1-5716-442a-bd5d-d367b4f70a65 */
	public String 管理处;
	/** @pdOid a064746f-2f37-4909-b247-bfb94c15929c */
	public String 费用项目类别;
	/** @pdOid 1ea59f3b-c11f-42ac-9abf-afbff5347b22 */
	public String 公式名;
	private String 属性ID;
	private String 上级表ID;
}