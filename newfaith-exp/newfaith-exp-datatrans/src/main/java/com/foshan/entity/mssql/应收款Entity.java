package com.foshan.entity.mssql;

import java.math.BigDecimal;

import java.util.Date;


import javax.persistence.Entity;

import javax.persistence.Id;

import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "应收款")
public class 应收款Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = -9117211613071645684L;
	@Id
	private String 应收款ID;
	private String 收费项目;
	private Date 应收日期;
	private BigDecimal 应收金额;
	private BigDecimal 已收金额;
	private String 备注;
	private String 项目类别;
	private String 项目来源;
	private String 来源备注;
	private Integer  锁定标志 = 0;
	private Integer  银行划帐ID;
	private Date 起始日期;
	private Date 截止日期;
	private String 住户成员ID;
	private String 成员姓名;
	private Integer  是否审核;
	private Date 款项属期;
	private Integer  退款类型;
	private String 应收款类型;
	private String 户主ID;
	private Integer  应收款no;
	private String 单元ID;




}