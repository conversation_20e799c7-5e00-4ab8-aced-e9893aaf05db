package com.foshan.entity.mssql;

import java.math.BigDecimal;

import java.util.Date;

import javax.persistence.Entity;

import javax.persistence.Id;

import javax.persistence.Table;


import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "用户表_抄表")
public class 用户表抄表Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2515602215733806626L;
	@Id
	public String id;
	/** @pdOid 4889a650-2f09-422f-af84-e8c266b153cc */
	public BigDecimal 本次读数;
	/** @pdOid a64aa571-aaf0-48e7-872a-be091ceecea2 */
	public Date 本次读数日期;
	/** @pdOid 55fe7ca1-3761-434b-bfad-5280c834dcb3 */
	public String 抄表人;
	/** @pdOid 57437054-37bb-4dec-8231-66bb640edfdb */
	public Integer 是否归零;
	/** @pdOid 02060e5a-5da1-4cfa-8a86-1bafe24b9950 */
	public Integer 是否审核;
	private String 用户表ID;
	private String 上次读数ID;


}