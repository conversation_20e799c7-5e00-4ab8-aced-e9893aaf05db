package com.foshan.entity.mssql;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Id;

import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "财务_其他收费")
public class 财务其他收费Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 9187509425933167289L;
	@Id
	public String 收据ID;
	/** @pdOid d5a3ca6b-b399-4781-90e7-b17fef072991 */
	public String 单据编号;
	/** @pdOid c3974681-bd8f-47b3-8884-d3d60b7e100a */
	public Date 收款日期;
	/** @pdOid 5a79c4d7-d1f9-40fe-94c5-313d82f0f0f2 */
	public String 姓名;
	/** @pdOid 39df27e7-44e7-41ce-ad00-17fad4df8107 */
	public String 收费项目;
	/** @pdOid 46b5243f-f49c-44af-b825-5015684e56e0 */
	public BigDecimal 金额;
	/** @pdOid 45acad67-316f-4526-ad27-4e138a3ce1c6 */
	public String 收款方式;
	/** @pdOid bc1a1698-d154-4429-a1fc-f7751b16c7d6 */
	public String 支票号码;
	/** @pdOid 7104a7b7-e2fc-42ed-ae19-131d4fcb48cd */
	public String 经办人;
	/** @pdOid 33b4d884-4a3b-4e87-ba93-a2c2c1f75a63 */
	public String 出纳;
	/** @pdOid b9c934f2-270b-401b-a47f-84778a7a7a04 */
	public String 主管;
	/** @pdOid c5f7a4ed-276a-4673-b989-a71244260aed */
	public String 收费类别;
	/** @pdOid 0ad5d1f6-8233-4482-b3cc-b5ed4f5fa99e */
	public String 来源;
	/** @pdOid 6a39d50c-3dec-4a20-8dbe-72d547d964e6 */
	public String 备注;
	/** @pdOid 751dd693-5e51-4ed3-8bf1-84b04c86fe38 */
	public String 附加备注;
	/** @pdOid 203364d5-5d93-45df-aac2-e34629a2ea76 */
	public Integer 使用自动流水号;
	/** @pdOid 82b48656-438e-4a67-ba12-6a451c35f80d */
	public BigDecimal 退款金额;
	/** @pdOid 72343566-e74d-4570-8a87-c8838f15b9f0 */
	public String 退款人;
	/** @pdOid 850f5b90-9e2b-41e1-bf9b-a9eea20f8938 */
	public String 来源备注;
	/** @pdOid bf727d3c-eb0f-463e-a142-92e116060e97 */
	public Integer 是否审核;
	/** @pdOid d6328dd9-cbcc-4127-a00b-a1132a68dc61 */
	public Date 退款日期;
	/** @pdOid c4be0681-20ee-4005-8a7e-d8bb28abdb01 */
	public BigDecimal 数量;
	/** @pdOid dc324c82-9b52-41e2-a915-c2809c397658 */
	public Date 起始日期;
	/** @pdOid e8a46a35-c27b-4aaa-a5d2-7dbd01a3567a */
	public Date 截止日期;
	/** @pdOid 2b31bcf9-93d2-4168-8e85-829c17dfc5d8 */
	public Integer 是否提交;
	/** @pdOid 124bd726-d102-4a03-8c8b-e1377adbb51a */
	public String 单元地址;
	/** @pdOid bb1d1c78-315b-4980-a827-d50110297aca */
	public String 相同单据号;
	/** @pdOid ef0a1527-0f23-4861-a266-080f804d60b9 */
	public String 退款方式;
	/** @pdOid 1a09956c-b2ca-458d-8bc2-ac34644c3859 */
	public Integer 是否审核_退款;
	/** @pdOid 1b16db91-0e17-46c8-a55b-c7e2f7dbf195 */
	public String 单据类别;
	/** @pdOid 9f513735-621e-4f4d-8a51-eccc874e5cc5 */
	public String 退款单据编号;
	/** @pdOid 42961b89-0fb0-461f-a54e-8cc74c01634f */
	public String 退款单据类别;
	/** @pdOid c92daf1b-2289-4cf5-a9bc-c9c23fefd1b0 */
	public String 退款备注;
	/** @pdOid c26ffb45-3274-4c73-8fcb-e1d46665f0ca */
	public Integer 财务帐期;
	/** @pdOid 7251b0eb-819f-4ae8-ab32-ac4599121e15 */
	public Integer 退款财务帐期;
	private String 单元ID;

}