package com.foshan.entity.mssql;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Entity;

import javax.persistence.Id;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "楼阁")
public class 楼阁Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2379008338801741795L;
	@Id
	public String 楼阁ID;
	/** @pdOid 0e64248b-ce8a-4bd4-a348-974b040e5554 */
	public String 楼阁名称;
	/** @pdOid acfc5e24-c2af-4d58-a2fc-c9f4a7824f6c */
	public String 功能;
	/** @pdOid 4782c6aa-2b75-45a3-90fd-2505c6d5606f */
	public Integer 层数;
	/** @pdOid a08cbd43-8fdb-434d-bccc-ab84aef0fad8 */
	public BigDecimal 实用面积;
	/** @pdOid 69d74b99-05d0-4a29-a135-************ */
	public BigDecimal 建筑面积;
	/** @pdOid fd6baac9-6e9a-4a32-a088-bc571a8b016a */
	public String 建筑许可证号;
	/** @pdOid 2ebd40e4-603d-4bba-a1a0-6d86d586a951 */
	public String 预售许可证号;
	/** @pdOid b1454fcd-e99d-4c22-b562-defbb09ed32d */
	public Date 竣工日期;
	/** @pdOid 0aeb5b33-e6fb-4a88-b34a-7888feb807b1 */
	public Date 封顶日期;
	/** @pdOid bdca3191-e9f2-42ea-a14a-6c53d3f258a9 */
	public String 装修标准;
	/** @pdOid 996f2da9-0463-4b38-8899-f787668697f3 */
	public String 备注;
	/** @pdOid e58143f4-138c-4a21-a326-5735d57b1dab */
	public String 结构类别;
	/** @pdOid a5886122-dbe4-4016-b637-29e8a3b2fcd9 */
	public String 完损等级;
	/** @pdOid 4fc0b3f9-d3ba-44c5-8207-e8367b616c56 */
	public String 楼阁地址;
	/** @pdOid 8e95fdf8-fdb3-4c65-824c-4bff04e03e46 */
	public Integer 楼阁编号 = 0;
	/** @pdOid 8aec92d1-d50d-4778-8b20-c8baacc3431f */
	public String 显示模式;
	private String 楼盘ID;

}