package com.foshan.entity.mssql;



import javax.persistence.Entity;

import javax.persistence.Id;

import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "楼盘")
public class 楼盘Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8235974212999523677L;
	@Id
	public String 楼盘ID;
	/** @pdOid 2d3b0b59-067f-406a-a4b1-a5a4f2991bb9 */
	public String 楼盘名称;
	/** @pdOid 6eea2709-76a6-4c43-8233-44ea813991bb */
	public String 街区;
	/** @pdOid c736323f-a9f9-4549-9c30-7a899f2b4a82 */
	public String 地址;
	/** @pdOid e481b963-6bdf-46b4-8d0e-230a18d9db41 */
	public String 地号;
	/** @pdOid 03f9ba32-7b1d-4d8c-9a2e-c393063ee786 */
	public String 隶属;
	/** @pdOid f9c4251d-6a84-468b-a716-f35fffdc631f */
	public String 备注;
	/** @pdOid 188dd1a9-c019-402d-8ae9-261ad2b4bbfd */
	public Integer 楼盘编号 = 0;
	/** @pdOid f2b08d20-4b72-47c1-a8af-a6f98a9a4488 */
	public String 开票方识别号;
	/** @pdOid e5781bf8-bc40-4680-bdbc-dfd3134d5699 */
	public String 开票方电子档案号;
	/** @pdOid 2a4fa959-a660-49f1-a59d-fb7297605cf3 */
	public String 开票方名称;
	/** @pdOid 2f54afa9-fe1b-4ed0-a08e-add02726cbce */
	public String 税务机构代码;
	/** @pdOid 8ff20735-b0ca-4193-9f96-17c9f3666a1c */
	public String 主要开票项目;
	/** @pdOid edb4ba05-f845-45d5-b923-4bc4c511a850 */
	public String 销货方识别号;
	/** @pdOid c913281e-a4aa-4b7b-8532-f471e1bc3102 */
	public String 销货方名称;
	/** @pdOid fa11afe9-009c-46ff-b1ef-840cdd83c0df */
	public String 销货方地址;
	/** @pdOid fccbf3b3-a895-43af-adc6-86433b9a344a */
	public String 销货方电话;
	/** @pdOid 1328b390-3a21-4e1b-bcd6-465ecc69cc8b */
	public String 销货方银行帐号;
	/** @pdOid 3b06a1c7-5a46-416e-b194-0a3d89586780 */
	public String 行业代码;
	/** @pdOid 87169ee0-f039-4ba0-b90a-3391dfa67971 */
	public String 行业名称;

}