package com.foshan.entity.mssql;

import java.math.BigDecimal;

import javax.persistence.Entity;

import javax.persistence.Id;

import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "表属性")
public class 表属性Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3603415168622550300L;
	@Id
	public String 属性ID;
	/** @pdOid 4c52c8cf-dc30-4013-b9df-3a3b606d657d */
	public String 编号;
	/** @pdOid 9e1dd313-7f8d-4f2a-8ae3-52003bf2387b */
	public BigDecimal 倍率;
	/** @pdOid 20c3080a-cfe1-4129-9755-89fe4cf9d33e */
	public Integer 量程;
	/** @pdOid ef1224f4-2160-4ab2-aa88-1d9449a30d12 */
	public String 类别;
	/** @pdOid 24fca34a-b4e2-404c-a4e7-5809cd079d3d */
	public BigDecimal 最低用量;
	/** @pdOid bb208938-d316-4e0a-9622-308e2dcb88b9 */
	public BigDecimal 单价;
	/** @pdOid 23025f3e-ad64-4a88-8885-39c494ae2cc5 */
	public BigDecimal 限额;
	/** @pdOid 33828f4a-c28c-4072-b7ef-83eea7198991 */
	public BigDecimal 超额单价;
	/** @pdOid f9dc8876-1eb4-4f1e-ad55-763b41f50ea2 */
	public String 单位;
	/** @pdOid dd58baa7-7e37-4032-9fd1-5f04b976265c */
	public String 备注;
	/** @pdOid 4a5191bf-633d-48e7-9c91-fe7fcd115987 */
	public Integer  另类单价;
	/** @pdOid 42bfc26c-a744-40a4-87a6-39d610b701c8 */
	public BigDecimal 限额单价;
	/** @pdOid 51bdfddb-f572-4c12-a6dc-6f1a9517edaf */
	public Integer  是否水池;
	/** @pdOid 3bfa54be-7e15-4a3a-973d-b2892da6aa12 */
	public BigDecimal 征收税率;
	/** @pdOid 134d8141-5501-46cb-8ddb-72c8500d32aa */
	public Integer  是否分段单价;
	/** @pdOid 877e635c-ddfd-48da-876e-79c6d49f79ac */
	public Integer  是否附加费用;
	/** @pdOid 16cc4cba-00e9-44d3-a0ca-e03461c9a07e */
	public String 附加费用名称;
	/** @pdOid 76333b5b-a549-4fb6-a3f6-0e77c316fee2 */
	public BigDecimal 附加费用单价;
	/** @pdOid 445ceb95-b525-4e3d-b73e-bf9d7dee6559 */
	public String 管理处;
}