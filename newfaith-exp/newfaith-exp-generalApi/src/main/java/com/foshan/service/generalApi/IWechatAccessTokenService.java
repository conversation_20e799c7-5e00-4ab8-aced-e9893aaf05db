package com.foshan.service.generalApi;

/**
 * 微信访问令牌服务接口
 * 用于管理微信访问令牌的获取和验证
 */
public interface IWechatAccessTokenService {
    
    /**
     * 获取微信访问令牌
     * 如果缓存中存在有效的令牌，则返回缓存的令牌
     * 如果缓存中不存在有效的令牌，则从微信服务器获取新的令牌
     * 
     * @return 访问令牌，如果获取失败则返回null
     */
    String getAccessToken();
    
    /**
     * 检查访问令牌是否过期
     * 
     * @return true表示已过期，false表示未过期
     */
    boolean isAccessTokenExpired();
    
    /**
     * 强制刷新访问令牌
     * 无论当前令牌是否过期，都从微信服务器获取新的令牌
     * 
     * @return 新的访问令牌，如果获取失败则返回null
     */
    String refreshAccessToken();
}
