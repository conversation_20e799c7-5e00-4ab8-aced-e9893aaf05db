package com.foshan.controller.generalApi;

import com.foshan.form.generalApi.request.WechatCodeReq;
import com.foshan.form.generalApi.request.WechatTemplateReq;
import com.foshan.form.response.IResponse;
import com.foshan.service.generalApi.IWechatTemplateMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "微信接口模块")
@RestController
public class WeChatApiController extends BaseGeneralApiController {
	
    @Autowired
    private IWechatTemplateMessageService wechatTemplateMessageService;

    @ApiOperation(value = "获取用户openid", notes = "通过微信授权code获取用户openid")
    @PostMapping("/getOpenid")
    public IResponse getOpenid(@RequestBody WechatCodeReq req) {
        return wechatTemplateMessageService.getOpenidByCode(req.getCode());
    }

    @ApiOperation(value = "发送模板消息", notes = "发送微信模板消息")
    @PostMapping("/sendTemplateMessage")
    public IResponse sendTemplateMessage(@RequestBody WechatTemplateReq req) {
        return wechatTemplateMessageService.sendTemplateMessage(req);
    }
}
