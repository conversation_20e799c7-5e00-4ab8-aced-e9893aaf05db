package com.foshan.form.generalApi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@Data
@ApiModel(description = "微信模板消息表单")
public class WechatTemplateForm {
    
    @ApiModelProperty(value = "用户openid", required = true)
    private String openId;
    
    @ApiModelProperty(value = "模板ID", required = true)
    private String templateId;
    
    @ApiModelProperty(value = "跳转链接")
    private String url;
    
    @ApiModelProperty(value = "模板数据", required = true)
    private Map<String, String> data;
} 