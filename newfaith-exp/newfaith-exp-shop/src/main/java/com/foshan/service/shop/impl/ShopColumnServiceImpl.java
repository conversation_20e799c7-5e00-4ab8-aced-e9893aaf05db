package com.foshan.service.shop.impl;

import static java.util.Comparator.comparingInt;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.jdbc.Work;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.foshan.dao.generic.Page;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.ColumnEntity;
import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.RegionEntity;
import com.foshan.entity.ServiceEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.shop.MemberEntity;
import com.foshan.entity.shop.ProductCategoryEntity;
import com.foshan.entity.shop.ProductEntity;
import com.foshan.entity.shop.ProductGroupEntity;
import com.foshan.entity.shop.ProductOrderEntity;
import com.foshan.entity.shop.ProductSpecificationDetailEntity;
import com.foshan.entity.shop.ProductSpecificationEntity;
import com.foshan.entity.shop.ShopColumnEntity;
import com.foshan.entity.shop.ShopUserEntity;
import com.foshan.entity.shop.StoreEntity;
import com.foshan.form.AssetForm;
import com.foshan.form.RegionForm;
import com.foshan.form.request.ServiceReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.shop.ProductForm;
import com.foshan.form.shop.ProductGroupForm;
import com.foshan.form.shop.ProductPriceForm;
import com.foshan.form.shop.ProductSpecificationForm;
import com.foshan.form.shop.ShopColumnForm;
import com.foshan.form.shop.StoreForm;
import com.foshan.form.shop.request.GetProductListReq;
import com.foshan.form.shop.request.GetSpecificationListReq;
import com.foshan.form.shop.request.ShopColumnReq;
import com.foshan.form.shop.response.column.AddShopColumnRes;
import com.foshan.form.shop.response.column.GetServiceShopRootColumnRes;
import com.foshan.form.shop.response.column.GetShopColumnInfoRes;
import com.foshan.form.shop.response.column.GetShopColumnListRes;
import com.foshan.form.shop.response.column.GetShopColumnProductGroupListRes;
import com.foshan.form.shop.response.column.GetShopColumnValidStoreListRes;
import com.foshan.form.shop.response.column.GetShopRootColumnVisitNumRes;
import com.foshan.form.shop.response.column.ModifyShopColumnRes;
import com.foshan.form.shop.response.product.GetProductListRes;
import com.foshan.form.shop.response.specification.GetProductSpecificationListRes;
import com.foshan.service.annotation.ResourcePermit;
import com.foshan.service.shop.IShopColumnService;
import com.foshan.util.CodeUtil;
import com.foshan.util.ContextInfo;
import com.foshan.util.ShopContextInfo;
import com.foshan.util.SpringHandler;


@Service("shopColumnService")
@Transactional
public class ShopColumnServiceImpl extends GenericShopService implements IShopColumnService {
	private final static Logger logger = LoggerFactory.getLogger(ShopColumnServiceImpl.class);

	//@ResourcePermit
	@Transactional
	@Override
	public IResponse addShopColumn(ShopColumnReq req,HttpServletRequest request) {
		AddShopColumnRes res = new AddShopColumnRes();
		// 获取登录用户信息
		Object userObj = getPrincipal(false);
		if (null != userObj && (userObj instanceof PlatformUserEntity)) {
			ShopColumnEntity column = new ShopColumnEntity();
			if (null != req.getServiceId() && StringUtils.isNotEmpty(req.getColumnName())
					&& StringUtils.isNotEmpty(req.getRegionIds())) {
				ServiceEntity service = serviceDao.get(req.getServiceId());
				if (null != service) {
					Integer columnId = null;
					Integer columnCode = (Integer) columnDao
							.createQuery("select max(substring(a.columnCode,5)+0)+1 from ColumnEntity a")
							.uniqueResult();
					BeanUtils.copyProperties(req, column);
					column.setColumnCode(StringUtils.isNotEmpty(req.getColumnCode()) ? req.getColumnCode()
							: CodeUtil.codeGenerator(null != columnCode ? columnCode : 1, 10, "MANU"));
					column.setColumnType(
							null != req.getColumnType() ? req.getColumnType() : EntityContext.COLUMN_TYPE_SHOP);
					column.setColumnState(
							null != req.getColumnState() ? req.getColumnState() : EntityContext.RECORD_STATE_VALID);
					column.setCommendFlag(
							null != req.getCommendFlag() ? req.getCommendFlag() : EntityContext.COMMENT_FLAG_INVALID);
					column.setTargetType(null != req.getTargetType() ? req.getTargetType()
							: 7);
					column.setMappingSystem(
							null != req.getMappingSystem() ? req.getMappingSystem() : EntityContext.COLUMN_MAPPING_BO);
					if (null != req.getColumnImageId()) {
						column.setColumnImage(assetDao.get(req.getColumnImageId()));
					}

					if (null != req.getColumnPhoneImageId()) {
						column.setColumnPhoneImage(assetDao.get(req.getColumnPhoneImageId()));
					}
					column.setIsGlobal(
							null != req.getIsGlobal() ? req.getIsGlobal() : EntityContext.RECORD_STATE_VALID);
					column.setState(null != req.getState() ? req.getState() : EntityContext.RECORD_STATE_VALID);
					column.setService(service);

					// 设置栏目绑定区域，默认省节点区域
					if (StringUtils.isNotEmpty(req.getRegionIds())) {
						String[] regionIds = req.getRegionIds().split(",");
						List<RegionEntity> regionList = new ArrayList<RegionEntity>();
						for (String regionId : regionIds) {
							regionList.add(regionDao.get(Integer.parseInt(regionId)));
						}
						column.setRegionList(regionList);
					} else {
						RegionEntity region = regionDao.getUniqueByHql("from RegionEntity a where a.regionLevel=1");
						column.getRegionList().add(region);
					}

					if (null != req.getParentColumnId()) {

						ColumnEntity parentColumn = columnDao.get(req.getParentColumnId());

						if (null != parentColumn) {
							Integer columnOrder = (Integer) columnDao.createQuery(
									"select max(a.orders)+1 from ColumnEntity a inner join a.parentColumn b where b.id="
											+ parentColumn.getId())
									.uniqueResult();
							column.setColumnLevel(parentColumn.getColumnLevel() + 1);
							column.setColumnPath(parentColumn.getColumnPath() + "/" + req.getColumnName());
							column.setOrders(
									null != req.getOrders() ? req.getOrders() : null != columnOrder ? columnOrder : 0);
							column.setParentColumn(parentColumn);
							columnId = (Integer) shopColumnDao.save(column);
						} else {
							res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
							res.setRetInfo("新增栏目的父栏目不存在！！！");
							return res;
						}
					} else {
						if (null == service.getColumn()) {
							column.setColumnLevel(1);
							column.setColumnPath(req.getColumnName());
							column.setOrders(0);
							columnId = (Integer) shopColumnDao.save(column);
							service.setColumn(column);
						} else {
							res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
							res.setRetInfo("当前业务已经存在根栏目，确认后再添加！！！");
							return res;
						}

					}
					// 构造返回值
					res.setColumnId(columnId);
					res.setServiceId(req.getServiceId());
					res.setColumnCode(req.getColumnCode());
					res.setColumnName(req.getColumnName());
					res.setParentColumnId(req.getParentColumnId());

					column.getRegionList().forEach(b -> {
						res.getRegionList().add(new RegionForm(b.getId(), b.getRegionCode(), b.getRegionName(),
								b.getStartRegionCode(), b.getEndRegionCode()));
					});
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} else {
					res.setServiceId(req.getServiceId());
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("商城业务不存在，不能增加商城栏目！！！");
					return res;
				}
			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_PERM_RELOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_RELOGIN_INFO);
		}
		logger.info("返回数据：" + res.toString());
		return res;
	}

	//@ResourcePermit
	@Transactional
	@Override
	public IResponse modifyShopColumn(ShopColumnReq req,HttpServletRequest request) {
		// TODO Auto-generated method stub
		ModifyShopColumnRes res = new ModifyShopColumnRes();
		// 获取登录用户信息
		Object userObj = getPrincipal(false);
		if (null != userObj && (userObj instanceof PlatformUserEntity)) {

			if (null != req.getColumnId()) {
				ShopColumnEntity column = shopColumnDao.get(req.getColumnId());
				if (null != column) {

					column.setColumnCode(
							StringUtils.isNotEmpty(req.getColumnCode()) ? req.getColumnCode() : column.getColumnCode());
					column.setColumnName(
							StringUtils.isNotEmpty(req.getColumnName()) ? req.getColumnName() : column.getColumnName());
					column.setColumnInfo(
							StringUtils.isNotEmpty(req.getColumnInfo()) ? req.getColumnInfo() : column.getColumnInfo());
					column.setColumnPath(
							StringUtils.isNotEmpty(req.getColumnPath()) ? req.getColumnPath() : column.getColumnPath());
					column.setColumnType(null != req.getColumnType() ? req.getColumnType() : column.getColumnType());
					column.setColumnState(
							null != req.getColumnState() ? req.getColumnState() : column.getColumnState());
					column.setCommendFlag(
							null != req.getCommendFlag() ? req.getCommendFlag() : column.getCommendFlag());
					column.setOrders(null != req.getOrders() ? req.getOrders() : 0);
					column.setTargetType(null != req.getTargetType() ? req.getTargetType()
							: column.getTargetType());
					column.setMappingFolderId(
							StringUtils.isNotEmpty(req.getMappingFolderId()) ? req.getMappingFolderId()
									: column.getMappingFolderId());
					column.setMappingSystem(
							null != req.getMappingSystem() ? req.getMappingSystem() : EntityContext.COLUMN_MAPPING_BO);
					column.setMappingFolderName(
							StringUtils.isNotEmpty(req.getMappingFolderName()) ? req.getMappingFolderName()
									: column.getMappingFolderName());
					column.setIsGlobal(null != req.getIsGlobal() ? req.getIsGlobal() : column.getIsGlobal());
					column.setState(null != req.getState() ? req.getState() : column.getState());
					if (null != req.getColumnImageId()
							&& (null == column.getColumnImage() || (null != column.getColumnImage()
									&& column.getColumnImage().getId() != req.getColumnImageId()))) {
						column.setColumnImage(assetDao.get(req.getColumnImageId()));
					}

					if (null != req.getColumnPhoneImageId()
							&& ((null == column.getColumnPhoneImage()) || (null != column.getColumnPhoneImage()
									&& column.getColumnPhoneImage().getId() != req.getColumnPhoneImageId()))) {
						column.setColumnPhoneImage(assetDao.get(req.getColumnPhoneImageId()));
					}

					if (null != req.getParentColumnId()) {
						ColumnEntity parent = columnDao.get(req.getParentColumnId());
						if (null != parent) {
							column.setParentColumn(parent);
							column.setColumnLevel(parent.getColumnLevel() + 1);
							column.setColumnPath(parent.getColumnPath() + "/" + column.getColumnName());
						} else {
							res.setRet("0001");
							res.setRetInfo("要修改的栏目父栏目不存在！！！");
							return res;
						}
					}
					// 修改栏目绑定区域
					if (StringUtils.isNotEmpty(req.getRegionIds())) {
						column.setRegionList(null);
						String[] regionIds = req.getRegionIds().split(",");
						List<RegionEntity> regionList = new ArrayList<RegionEntity>();
						for (String regionId : regionIds) {
							regionList.add(regionDao.get(Integer.parseInt(regionId)));
						}
						column.setRegionList(regionList);
					}

					columnDao.saveOrUpdate(column);
					res.setColumnCode(req.getColumnCode());
					res.setColumnName(req.getColumnName());
					res.setParentColumnId(req.getParentColumnId());
					column.getRegionList().forEach(b -> {
						res.getRegionList().add(new RegionForm(b.getId(), b.getRegionCode(), b.getRegionName(),
								b.getStartRegionCode(), b.getEndRegionCode()));
					});
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} else {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("要修改的栏目不存在！！！");
				}
			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_PERM_RELOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_RELOGIN_INFO);
		}
		logger.info("返回数据：" + res.toString());
		return res;
	}

	//@ResourcePermit
	@Transactional
	@Override
	public IResponse deleteShopColumn(ShopColumnReq req,HttpServletRequest request) {
		// TODO Auto-generated method stub
		GenericResponse res = new GenericResponse();
		if (null != req.getColumnId()) {
			ColumnEntity column = columnDao.get(req.getColumnId());
			if (null != column && column.getState() == EntityContext.RECORD_STATE_VALID) {
				column.getParentColumn().getSubColumnList().remove(column);
				column.setParentColumn(null);
				column.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo("栏目不存在！！！");
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		logger.info("返回数据：" + res.toString());
		return res;
	}

	@SuppressWarnings("unchecked")
	@Override
	public IResponse getShopColumnList(ShopColumnReq req) {
		GetShopColumnListRes res = new GetShopColumnListRes();
		// 获取登录用户信息
		Object userObj = getPrincipal(false);
		String regionCode = StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "1";

		ColumnEntity parentColumn = null;
	
		if (null != req.getColumnId()) {
			Date d1 =  new Date();
			String parentHql = "from ColumnEntity a where a.id=" + req.getColumnId() ;
//					+ " and a.state="
//					+ (null!=req.getState() ? req.getState() : EntityContext.RECORD_STATE_VALID) 
//					+(null!=req.getColumnType() ? " and a.columnType="+req.getColumnType():"")+
//					(null!=req.getColumnState() ? " and a.columnState="+req.getColumnState():
//						" AND a.columnState=" + EntityContext.RECORD_STATE_VALID)+" order by a.orders";

			List<ColumnEntity> list = columnDao.getListByHql(parentHql);
			Integer serviceId = null;
			if (list.size() == 0) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo("要获取列表的栏目不存在！！！");
				return res;
			} else {
				parentColumn = list.get(0);
				BeanUtils.copyProperties(parentColumn, res);
				res.setColumnId(parentColumn.getId());
				res.setParentColumnId(
						null != parentColumn.getParentColumn() ? parentColumn.getParentColumn().getId() : null);
				serviceId = parentColumn.getService().getId();
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}
			Date d2 =  new Date();
			logger.info("getParent:"+ (d2.getTime()-d1.getTime()));
			// 获取该主栏目下所有栏目信息
			StringBuilder sql = new StringBuilder("");
			String targetType=(StringUtils.isNotEmpty(req.getTargetTypeList()) ? " and a.targetType in("+req.getTargetTypeList()+") " :"");
			if (null != userObj && (userObj instanceof PlatformUserEntity || userObj instanceof ShopUserEntity)) {
				sql.append("select distinct a.* from t_column a right join t_column_region b on a.id = b.columnId "
						+ "left join t_region c on b.regionId = c.id where a.parentColumnId=" + req.getColumnId() + " and a.state="
						+ (null!=req.getState() ? req.getState() : EntityContext.RECORD_STATE_VALID)
						+(null!=req.getColumnType() ? " and a.columnType="+req.getColumnType():""));
				if(null != req.getRegionId()) {
					RegionEntity region = regionDao.get(req.getRegionId());
					if (null == region) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "regionId无效！");
						return res;
					}
					sql.append(" and c.regionCode between " + region.getStartRegionCode() + " and " + region.getEndRegionCode());
				}
				sql.append(null!=req.getColumnState() ? " and a.columnState="+req.getColumnState():"")
				.append(" AND cflag='S' order by a.orders");
			} else {
				sql.append("select distinct a.* FROM t_column a INNER JOIN t_column_region b INNER JOIN t_region c ON "
						+ "a.parentColumnId=" + req.getColumnId() 
						 +targetType+ " AND a.state=" + (null!=req.getState() ? req.getState() : EntityContext.RECORD_STATE_VALID)
						+ (null!=req.getColumnType() ? " and a.columnType="+req.getColumnType():"")
						+ " AND a.id=b.columnId AND b.regionId=c.id AND (c.startRegionCode+0)<=(" + regionCode
						+ "+0) AND (c.endRegionCode+0)>=(" + regionCode + "+0) "+
						(null!=req.getColumnState() ? " and a.columnState="+req.getColumnState():"")+" AND cflag='S' order by a.orders");
			}
			List<ColumnEntity> columnList = columnDao.getListBySql(sql.toString());
			Date d3 =  new Date();
			logger.info("getParentDirectSubColumn:"+ (d3.getTime()-d2.getTime()));
			if (null != columnList && columnList.size() > 0) {
				// ColumnEntity shopColumn = columnList.get(0).getParentColumn();
			
				for(ColumnEntity o : columnList ) {
				//columnList.forEach(o -> {
					Date d4 =  new Date();
					
					ShopColumnForm columnForm = new ShopColumnForm();
					columnForm.setColumnCode(o.getColumnCode());
					columnForm.setColumnInfo(o.getColumnInfo());
					columnForm.setColumnLevel(o.getColumnLevel());
					columnForm.setColumnName(o.getColumnName());
					columnForm.setColumnPath(o.getColumnPath());
					columnForm.setColumnState(o.getColumnState());
					columnForm.setColumnType(o.getColumnType());
					columnForm.setCommendFlag(o.getCommendFlag());
					columnForm.setColumnImage(getAsset(o.getColumnImage(),EntityContext.PRODUCT_LIST_DETAIL_FLAG_TV));
					columnForm.setColumnPhoneImage(getAsset(o.getColumnPhoneImage(),EntityContext.PRODUCT_LIST_DETAIL_FLAG_MOBILE));
					columnForm.setMappingFolderId(o.getMappingFolderId());
					columnForm.setMappingFolderName(o.getMappingFolderName());
					columnForm.setMappingSystem(o.getMappingSystem());
					columnForm.setOrders(o.getOrders());
					columnForm.setTargetType(o.getTargetType());
					columnForm.setColumnId(o.getId());
					columnForm.setIsGlobal(o.getIsGlobal());
					columnForm.setState(o.getState());
					o.getRegionList().forEach(b -> {
						columnForm.getRegionList().add(new RegionForm(b.getId(), b.getRegionCode(), b.getRegionName(),
								b.getStartRegionCode(), b.getEndRegionCode()));
					});
					try {
						columnForm.setIsOperation((null != userObj && userObj instanceof ShopUserEntity
								&& ((ShopColumnEntity) o).getStoreList().contains(((ShopUserEntity) userObj).getStore())
								&& o.getColumnLevel() > 1) ? true
										: (null != userObj && userObj instanceof PlatformUserEntity) ? true : false);
					} catch (Exception ex) {
						columnForm.setIsOperation(false);
					}
					columnForm.setParentColumnId(null != o.getParentColumn() ? o.getParentColumn().getId() : null);

					if (null == req.getDepth() || (null != req.getDepth() && req.getDepth() > 1)) {

						StringBuilder subSql = new StringBuilder("");
						if (null != userObj
								&& (userObj instanceof PlatformUserEntity || userObj instanceof ShopUserEntity)) {
							subSql.append("select a.* from t_column a where a.parentColumnId=" + o.getId() +" AND a.state="
									+ (null!=req.getState() ? req.getState() : EntityContext.RECORD_STATE_VALID)+targetType+
									(null!=req.getColumnState() ? " and a.columnState="+req.getColumnState():"")
									+ " AND cflag='S' order by a.orders");
						} else {
							subSql.append(
									"select distinct a.* FROM t_column a INNER JOIN t_column_region b INNER JOIN t_region c ON "
											+ "a.parentColumnId=" + o.getId() 
											+ (null!=req.getColumnType() ? " and a.columnType="+req.getColumnType():"")+ " AND a.state="
											+ (null!=req.getState() ? req.getState() : EntityContext.RECORD_STATE_VALID)
											+ " AND a.id=b.columnId AND b.regionId=c.id AND (c.startRegionCode+0)<=("
											+ regionCode + "+0) AND (c.endRegionCode+0)>=(" + regionCode
											+ "+0) "+targetType+
											(null!=req.getColumnState() ? " and a.columnState="+req.getColumnState():"")+" AND cflag='S' order by a.orders");
						}
						Date d41 =  new Date();
						List<ColumnEntity> subColumnList = columnDao.getListBySql(subSql.toString());

						// 查找子栏目
						columnForm.getSubColumnList()
								.addAll((List<ShopColumnForm>) columnList(userObj,
										subColumnList, serviceId, o.getId(),
										(null != req.getDepth() && req.getDepth() > 1) ? req.getDepth()
												: contextInfo.defaultGetDataDepth,
										EntityContext.COUNT_VISIT_OFF, null, null, 
										regionCode,req.getTargetTypeList(),req.getColumnType(),
										req.getColumnState(),req.getState()).get("columnList"));
						Date d42 =  new Date();
						logger.info("querySubForm:"+ (d42.getTime()-d41.getTime()));
					}
					Date d5 =  new Date();
					logger.info("createForm:"+ (d5.getTime()-d4.getTime()));
					res.getSubColumnList().add(columnForm);
				}

			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getServiceShopRootColumn(ServiceReq req) {
		// TODO Auto-generated method stub
		GetServiceShopRootColumnRes res = new GetServiceShopRootColumnRes();
		// 获取登录用户信息
		Object userObj = getPrincipal(false);
		if (null != req.getServiceId()) {
			ServiceEntity service = serviceDao.get(req.getServiceId());
			if (null != service) {
				if (null != service.getColumn()) {
					Integer rootColumnId = service.getColumn().getId();
					// 获取该主栏目下所有栏目信息
					StringBuilder sql = new StringBuilder("");
					if (null != userObj
							&& (userObj instanceof PlatformUserEntity || userObj instanceof ShopUserEntity)) {
						sql.append("select a.* from t_column a where a.parentColumnId=" + rootColumnId + " and a.state="
								+ EntityContext.RECORD_STATE_VALID + " order by a.orders");
					} else {
						String regionCode = StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "1";
						if(null==req.getTargetType()) req.setTargetType(3);
						sql.append(
								"select distinct a.* FROM t_column a INNER JOIN t_column_region b INNER JOIN t_region c ON "
										+ "a.parentColumnId=" + rootColumnId 
										+" AND a.columnState="
										+ EntityContext.RECORD_STATE_VALID + " and a.state="
										+ EntityContext.RECORD_STATE_VALID
										+ " AND a.id=b.columnId AND b.regionId=c.id AND (c.startRegionCode+0)<=("
										+ regionCode + "+0) AND (c.endRegionCode+0)>=(" + regionCode
										+ "+0) ")
						.append(null!=req.getColumnType() ? " AND a.columnType="+req.getColumnType():" AND a.columnType="+EntityContext.COLUMN_TYPE_SHOP )
						.append(null!=req.getTargetType() ? " and (targetType='"+req.getTargetType() +"' or targetType='7')": " and targetType=7")
						.append(" order by a.orders");
						
					}

					List<ColumnEntity> columnList = columnDao.getListBySql(sql.toString());

					columnList.forEach(o -> {
						ShopColumnForm columnForm = new ShopColumnForm();
						columnForm.setColumnCode(o.getColumnCode());
						columnForm.setColumnInfo(o.getColumnInfo());
						columnForm.setColumnLevel(o.getColumnLevel());
						columnForm.setColumnName(o.getColumnName());
						columnForm.setColumnPath(o.getColumnPath());
						columnForm.setColumnState(o.getColumnState());
						columnForm.setColumnType(o.getColumnType());
						columnForm.setCommendFlag(o.getCommendFlag());
						columnForm.setMappingFolderId(o.getMappingFolderId());
						columnForm.setMappingFolderName(o.getMappingFolderName());
						columnForm.setMappingSystem(o.getMappingSystem());
						columnForm.setOrders(o.getOrders());
						columnForm.setColumnImage(getAsset(o.getColumnImage(),EntityContext.PRODUCT_LIST_DETAIL_FLAG_TV));
						columnForm.setColumnPhoneImage(getAsset(o.getColumnPhoneImage(),EntityContext.PRODUCT_LIST_DETAIL_FLAG_MOBILE));
						columnForm.setTargetType(o.getTargetType());
						columnForm.setColumnId(o.getId());
						columnForm.setParentColumnId(rootColumnId);
						columnForm.setIsGlobal(o.getIsGlobal());
						o.getRegionList().forEach(b -> {
							columnForm.getRegionList().add(new RegionForm(b.getId(), b.getRegionCode(),
									b.getRegionName(), b.getStartRegionCode(), b.getEndRegionCode()));
						});
						try {
							columnForm.setIsOperation((null != userObj && userObj instanceof ShopUserEntity
									&& ((ShopColumnEntity) o).getStoreList()
											.contains(((ShopUserEntity) userObj).getStore())) ? true
													: (null != userObj && userObj instanceof PlatformUserEntity) ? true
															: false);
						} catch (Exception ex) {
							columnForm.setIsOperation(false);
						}
						res.getSubColumnList().add(columnForm);
					});
					res.getSubColumnList().sort(comparingInt(ShopColumnForm::getOrders));
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} else {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
				}
			} else {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
			}
		} else

		{
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	@Override
	public IResponse getShopRootColumnVisitNum(ServiceReq req) {
		GetShopRootColumnVisitNumRes res = new GetShopRootColumnVisitNumRes();
		// 获取登录用户信息
		Object userObj = getPrincipal(false);
		if (null == userObj || (null != userObj && !(userObj instanceof PlatformUserEntity)
				&& !(userObj instanceof ShopUserEntity))) {
			try {
				SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
				Session session = sessionFactory.openSession();
				Transaction tx = session.beginTransaction();

				session.doWork(new Work() {
					@Override
					public void execute(Connection connection) throws SQLException {
						Statement st = (Statement) connection.createStatement();

						ResultSet totalRS = st.executeQuery(
								"select sum(visitnum) as totalVisit from t_bi_visit_year a where a.serviceId="
										+ req.getServiceId());

						while (totalRS.first()) {
							res.setTotalVisits(totalRS.getInt("totalVisit"));
							break;
						}

						if (StringUtils.isNotEmpty(req.getRegionCode())
								&& !"undefined".equalsIgnoreCase(req.getRegionCode())) {
							ResultSet regionRS = st
									.executeQuery("SELECT SUM(a.visitnum) AS regionVisit,b.regionName "
											+ "FROM t_bi_visit_year a,t_region b WHERE a.serviceid="
											+ req.getServiceId() + " AND b.regionLevel=2 "
											+ "AND (a.regionCode+0)>=(b.startRegionCode+0) AND (a.regionCode+0)<=(b.endRegionCode) "
											+ "AND (b.startRegionCode+0)<=(" + req.getRegionCode()
											+ "+0) and (b.endRegionCode+0)>=(" + req.getRegionCode()
											+ "+0)");
							while (regionRS.first()) {
								res.setRegionName(regionRS.getString("regionName"));
								res.setRegionVisits(regionRS.getInt("regionVisit"));
								break;
							}
							
							regionRS.close();
						}
						
						totalRS.close();
						st.close();
					}
				});

				tx.commit();
				if (session != null) {
					session.close();
				}

			} catch (Exception ex) {
				ex.printStackTrace();
			}
		}
		res.setServiceId(req.getServiceId());;
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public IResponse getShopColumnProductGroupList(ShopColumnReq req) {
		// TODO Auto-generated method stub
		GetShopColumnProductGroupListRes res = new GetShopColumnProductGroupListRes();
		Object userObj = getPrincipal(false);
		if (null != userObj && !(userObj instanceof MemberEntity)) {
			if (null != req.getColumnId()) {
				ShopColumnEntity column = shopColumnDao.get(req.getColumnId());
				if (null != column) {

					Page<ProductGroupEntity> page = new Page<ProductGroupEntity>();
					page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
					page.setBeginCount(
							(null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
					page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

					StringBuilder hql = new StringBuilder("");
					if (userObj instanceof PlatformUserEntity) {
						hql.append("select a from ProductGroupEntity a inner join a.columnList b where b.id="
								+ req.getColumnId() + " order by a.orders");
					} else if (userObj instanceof ShopUserEntity) {
						StoreEntity store = ((ShopUserEntity) userObj).getStore();
						hql.append(
								"select a from ProductGroupEntity a inner join a.columnList b inner join a.store c where b.id="
										+ req.getColumnId() + " and c.id=" + store.getId());
					}

					page = productGroupDao.queryPage(page, hql.toString());

					page.getResultList().forEach(o -> {
						StoreEntity store = o.getStore();
						ProductGroupForm groupForm = new ProductGroupForm(o.getId(), o.getGroupName(), o.getGroupType(),
								store.getId(), store.getStoreName(), o.getOrders());
						groupForm.setIsSelfSupport(store.getIsSelfSupport());
						groupForm.setBusinessModel(store.getBusinessModel());
						if (null != o.getAsset()) {
							AssetEntity asset = o.getAsset();
							groupForm.setAssetForm(new AssetForm(asset.getId(), null, null, null, null, null, null,
									asset.getImageFile(), null, null));
						}
						groupForm.setSubProductCount(o.getProductList().size());
						res.getProductGroupList().add(groupForm);
					});

					res.setColumnCode(column.getColumnCode());
					res.setColumnId(column.getId());
					res.setColumnName(column.getColumnName());
					res.setTotalResult(page.getTotalCount());
					res.setPageSize(page.getPageSize());
					res.setCurrentPage(page.getCurrentPage());
					res.setTotal(page.getTotalPage());
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} else {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
				}
			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
		}
		return res;
	}

	@Override
	public IResponse getShopColumnInfo(ShopColumnReq req) {
		// TODO Auto-generated method stub
		GetShopColumnInfoRes res = new GetShopColumnInfoRes();
		// 获取登录用户信息
		Object userObj = getPrincipal(false);
		if (null != req.getColumnId()) {
			ColumnEntity column = columnDao.get(req.getColumnId());
			if (null != column) {
				res.setColumnCode(column.getColumnCode());
				res.setColumnInfo(column.getColumnInfo());
				res.setColumnLevel(column.getColumnLevel());
				res.setColumnName(column.getColumnName());
				res.setColumnPath(column.getColumnPath());
				res.setColumnImage(getAsset(column.getColumnImage(),EntityContext.PRODUCT_LIST_DETAIL_FLAG_TV));
				res.setColumnPhoneImage(getAsset(column.getColumnPhoneImage(),EntityContext.PRODUCT_LIST_DETAIL_FLAG_MOBILE));
				res.setColumnState(column.getColumnState());
				res.setColumnType(column.getColumnType());
				res.setCommendFlag(column.getCommendFlag());
				res.setMappingFolderId(column.getMappingFolderId());
				res.setMappingFolderName(column.getMappingFolderName());
				res.setMappingSystem(column.getMappingSystem());
				res.setOrders(column.getOrders());
				res.setColumnId(column.getId());
				res.setParentColumnId(null != column.getParentColumn() ? column.getParentColumn().getId() : null);
				res.setServiceId(null != column.getService() ? column.getService().getId() : null);
				res.setIsGlobal(column.getIsGlobal());
				res.setTargetType(column.getTargetType());
				try {
					res.setIsOperation(
							(null != userObj && userObj instanceof ShopUserEntity && ((ShopColumnEntity) column)
									.getStoreList().contains(((ShopUserEntity) userObj).getStore())) ? true : false);
				} catch (Exception ex) {
					res.setIsOperation(false);
				}
				// 获取栏目区域属性
				column.getRegionList().forEach(b -> {
					res.getRegionList().add(new RegionForm(b.getId(), b.getRegionCode(), b.getRegionName(),
							b.getStartRegionCode(), b.getEndRegionCode()));
				});
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	/*
	 * 移动端
	 */
	@Override
	public IResponse getShopColumnProductList(GetProductListReq req) {
		GetProductListRes res = new GetProductListRes();
//		if (null != req.getColumnId()) {

		// 获取登录用户信息
		Integer needDetail = EntityContext.PRODUCT_LIST_DETAIL_FLAG_MOBILE;
		Object userObj = getPrincipal(false);
		
		if (null == userObj || (null != userObj && userObj instanceof MemberEntity)) {
			req.setAuditState(EntityContext.RECORD_AUDIT_PASS);
			req.setState(EntityContext.RECORD_STATE_VALID);
			req.setRegionCode(StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "1600");
		
		} else if (null != userObj && userObj instanceof ShopUserEntity) {
			StoreEntity store = ((ShopUserEntity) userObj).getStore();
			req.setStoreId(store.getId());
		}
		
		req.setOrderStr(StringUtils.isNotEmpty(req.getOrderStr())?req.getOrderStr():"createTime|desc");
		
		//Date d1 =  new Date();
		HashMap<String, Object> hm = getProductEntityList(req, userObj);
		//Date d2 =  new Date();
		//logger.info("getProductEntityList:"+ (d2.getTime()-d1.getTime()));
		res.setProductList(getProductList( req, needDetail, userObj,hm,(null!=req.getDistinct() ? (req.getDistinct()==0? false:true) : true)));
		//Date d3 =  new Date();
		//logger.info("getProductList:"+ (d3.getTime()-d2.getTime()));
		res.setTotalResult(res.getProductList().size() > 0 ? (Integer) hm.get("totalResult") : 0);
		res.setPageSize((Integer) hm.get("pageSize"));
		res.setCurrentPage((Integer) hm.get("currentPage"));
		res.setTotal((Integer) hm.get("total"));
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		return res;
	}
	
	@SuppressWarnings("unchecked")
	public  List<ProductForm> getProductList(GetProductListReq req,Integer needDetail,Object userObj,HashMap<String, Object> hm,Boolean minimumPrice){
		List<ProductForm> list = new ArrayList<ProductForm>();
		List<ProductEntity> productList = (List<ProductEntity>) hm.get("productList");
		if(EntityContext.PRODUCT_LIST_DETAIL_FLAG_MOBILE == needDetail){
			//Object obj = getPrincipal(false);
			// 移动互联网端列表 
			for(ProductEntity product: productList) {
				ProductSpecificationEntity specification = product.getProductSpecification();
				//ProductSpecificationDetailEntity detail = specification.getDetail();
				
/*				Integer productId=null;
				Map<Integer,ProductEntity> productMap = new HashMap<Integer,ProductEntity>();
				List<ProductPriceForm> productPriceList = new ArrayList<ProductPriceForm>();
				if(minimumPrice) {
					for(ProductEntity p :specification.getProductList()) {
						if(p.getIsMarketable()==EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL || 
								p.getIsMarketable()==EntityContext.UP_SHELF_STATE_UP_FOR_ALL) {
							ProductPriceForm price=null;
							Integer type = isEnterpriseMember(obj) ? 
									EntityContext.PRODUCT_PRICE_TYPE_COMPANY : EntityContext.PRODUCT_PRICE_TYPE_NORMAL;
							price = getProductPrice(null, null, p.getId(),type);
							productMap.put(p.getId(), p);
							productPriceList.add(price);
						}
					}
					getMinimumPrice(productPriceList);
					productId = productPriceList.get(0).getProductId();
				}
								ProductForm productForm = getProductForm(req.getColumnId(), null, specification, 
						(minimumPrice ? productMap.get(productId) : product), needDetail);*/


				ProductForm productForm = getProductForm(req.getColumnId(), null, specification, product, needDetail);
				if(specification.getProductType()==EntityContext.IS_VIDEO_PRODUCT && 
						(null != userObj && userObj instanceof MemberEntity)) {
					MemberEntity member = (MemberEntity) userObj;	
					Map<String,Object> map=getPurchasedInfo(member.getId(),product.getId());
					productForm.setPurchased((Integer)map.get("purchased"));
					productForm.setVideoExpirationTime((String)map.get("videoExpirationTime"));
				}
				
				ProductSpecificationForm specificationForm = getProductSpecificationForm(specification,
						Boolean.parseBoolean(shopContextInfo.getMobileAudit()),
						Boolean.parseBoolean(shopContextInfo.getMobileSpecifications()),
						Boolean.parseBoolean(shopContextInfo.getMobileTemplete()),needDetail);

				productForm.setProductSpecificationForm(specificationForm);
				list.add(productForm);
			}
		}
		else if(EntityContext.PRODUCT_LIST_DETAIL_FLAG_TV == needDetail) {
			// 返回TV端列表
		}
		else {
			// 返回全部字段
			for(ProductEntity product: productList) {
				ProductSpecificationEntity specification = product.getProductSpecification();
				ProductSpecificationDetailEntity detail = specification.getDetail();
				
				ProductForm productForm = getProductForm(req.getColumnId(), null, specification, product, needDetail);
				if(specification.getProductType()==EntityContext.IS_VIDEO_PRODUCT && 
						(null != userObj && userObj instanceof MemberEntity)) {
					MemberEntity member = (MemberEntity) userObj;	
					Map<String,Object> map=getPurchasedInfo(member.getId(),product.getId());
					productForm.setPurchased((Integer)map.get("purchased"));
					productForm.setVideoExpirationTime((String)map.get("videoExpirationTime"));
				}

				ProductSpecificationForm specificationForm = getProductSpecificationForm(specification,
						Boolean.parseBoolean(shopContextInfo.getMobileAudit()),
						Boolean.parseBoolean(shopContextInfo.getMobileSpecifications()),
						Boolean.parseBoolean(shopContextInfo.getMobileTemplete()),needDetail);

				if (Boolean.parseBoolean(shopContextInfo.getMobileSku())) {
					try {
						List<LinkedHashMap<String, String>> paraList = getSpecificationSkuParameterList(
								specialTemplateToList(detail.getSpecialTemplate()));
						paraList.forEach(b -> {
							b.keySet().forEach(c -> {
								try {
									ProductForm pf = new ProductForm();
									pf.setSpecificationIndex(c.endsWith("_") ? c.substring(0, c.length() - 1) : c);
									pf.setSpecificationId(specification.getId());
									pf.setImportFlag(EntityContext.PRODUCT_IMPORT_FLAG_NONE);
									specificationForm.getSkuList().add(pf);
								} catch (Exception ex) {
									logger.error("sku列表装载错误" + ex.getMessage());
								}
							});
						});
						// 判断在库状态

						StringBuilder hql = new StringBuilder(
								"select a from ProductEntity a where a.productSpecification.id="
										+ specification.getId());
						List<ProductForm> productFormList = parseProductFormList(req.getColumnId(),
								req.getProductGroupId(), productDao.getListByHql(hql.toString()), false, false, false,
								false, null);
						productFormList.forEach(b -> {
							List<ProductForm> resFormList = specificationForm.getSkuList();
							boolean flag = false;
							for (ProductForm c : resFormList) {
								if (c.equals(b)) {
									flag = true;
									resFormList.remove(c);
									break;
								}
							}
							if (flag) {
								b.setImportFlag(EntityContext.PRODUCT_IMPORT_FLAG_IN);
								resFormList.add(b);
							}
						});

					} catch (Exception ex) {
						logger.error("getSkuListBySpecification方法构造sku列表出错！！！" + ex.getMessage());
					}
				} else {
					specificationForm.getSkuList().addAll(parseProductFormList(req.getColumnId(),
							req.getProductGroupId(), specification.getProductList(), false, false, false, false, null));
				}

				productForm.setProductSpecificationForm(specificationForm);
				list.add(productForm);
			}
		}
		
		
		
		return list;
	}

	/*
	 * 电视端
	 */
	@SuppressWarnings("unchecked")
	@Override
	public IResponse getShopColumnProductSpecificationList(GetSpecificationListReq req) {
		GetProductSpecificationListRes res = new GetProductSpecificationListRes();
		// 获取登录用户信息
		Object userObj = getPrincipal(false);
		Integer needDetail =  EntityContext.PRODUCT_LIST_DETAIL_FLAG_TV;
		if (null == userObj || (null != userObj && userObj instanceof MemberEntity)) {
			req.setAuditState(EntityContext.RECORD_AUDIT_PASS);
			//req.setIsMarketable(EntityContext.UP_SHELF_STATE_UP);
			req.setState(EntityContext.RECORD_STATE_VALID);
			req.setProductState(EntityContext.RECORD_STATE_VALID);
			req.setStoreId(null);
			req.setRegionCode(StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "1");

			req.setIsMarketableList(isEnterpriseMember(userObj) ? "("+EntityContext.UP_SHELF_STATE_UP_FOR_COMPANY+","+EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL_AND_COMPANY +")" :
				"("+EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL+","+EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL_AND_COMPANY +")");
	

		} else if (null != userObj && userObj instanceof ShopUserEntity) {
			StoreEntity store = ((ShopUserEntity) userObj).getStore();
			RegionEntity region = regionDao.get(store.getRegionId());
			req.setStoreId(store.getId());
			req.setRegionCode(null != region ? region.getRegionCode() : "1");
		}else {
			req.setIsMarketableList(StringUtils.isNotEmpty(req.getIsMarketableList()) ? "("+req.getIsMarketableList()+")" : (null!=req.getIsMarketable() ? req.getIsMarketable()+"" : ""));
		}
		req.setOrderStr(StringUtils.isNotEmpty(req.getOrderStr())?req.getOrderStr():"createTime|desc");
		HashMap<String, Object> hm = getSpecificationList(req, userObj);
		List<ProductSpecificationEntity> specificationList = (List<ProductSpecificationEntity>) hm
				.get("specificationList");

		res.setSpecificationList(parseProductSpecificationFormList(null, null, specificationList,
				Boolean.parseBoolean(shopContextInfo.getColumnAudit()),
				Boolean.parseBoolean(shopContextInfo.getColumnSpecifications()),
				Boolean.parseBoolean(shopContextInfo.getColumnTemplete()),
				Boolean.parseBoolean(shopContextInfo.getColumnSku()),req.getIsMarketableList(),needDetail));
		res.setTotalResult(res.getSpecificationList().size() > 0 ? (Integer) hm.get("totalResult") : 0);
		res.setPageSize((Integer) hm.get("pageSize"));
		res.setCurrentPage((Integer) hm.get("currentPage"));
		res.setTotal((Integer) hm.get("total"));
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	//@ResourcePermit
	@Transactional
	@Override
	public IResponse addShopColumnStore(ShopColumnReq req,HttpServletRequest request) {
		GenericResponse res = new GenericResponse();
		// 获取登录用户信息
		Object userObj = getPrincipal(false);
		if (null != userObj && userObj instanceof PlatformUserEntity) {
			if (null != req.getColumnId() && StringUtils.isNotEmpty(req.getStoreIdList())) {
				ShopColumnEntity column = shopColumnDao.get(req.getColumnId());
				if (null != column && column.getColumnLevel() > 1) {
					String[] storeIds = req.getStoreIdList().split(",");
					for (String storeId : storeIds) {
						StoreEntity store = storeDao.get(Integer.parseInt(storeId));
						List<StoreEntity> storeList = column.getStoreList();
						if(!storeList.contains(store)) {
							column.getStoreList().add(store);
						}
						
//						column.getSubColumnList().forEach(o -> {
//							try {
//								((ShopColumnEntity) o).getStoreList().add(store);
//							} catch (Exception ex) {
//								ex.getStackTrace();
//							}
//						});
					}
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} else {
					res.setRet("0001");
					res.setRetInfo("栏目不存在或商城根栏目不允许绑定商铺！！！");
				}
			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
		}
		return res;
	}

	//@ResourcePermit
	@Transactional
	@Override
	public IResponse deleteShopColumnStore(ShopColumnReq req,HttpServletRequest request) {
		GenericResponse res = new GenericResponse();
		// 获取登录用户信息
		Object userObj = getPrincipal(false);
		if (null != userObj && userObj instanceof PlatformUserEntity) {
			if (null != req.getColumnId() && StringUtils.isNotEmpty(req.getStoreIdList())) {
				ShopColumnEntity column = shopColumnDao.get(req.getColumnId());
				if (null != column) {
					String[] storeIds = req.getStoreIdList().split(",");
					for (String storeId : storeIds) {
						StoreEntity store = storeDao.get(Integer.parseInt(storeId));
						column.getStoreList().remove(store);
					}
					
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} else {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
				}
			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
		}
		return res;
	}

	@Override
	public IResponse getShopColumnStoreList(ShopColumnReq req) {
		// TODO Auto-generated method stub
		GetShopColumnValidStoreListRes res = new GetShopColumnValidStoreListRes();
		// 获取登录用户信息
		Object userObj = getPrincipal(false);
		if (null != userObj && userObj instanceof PlatformUserEntity) {
			if (null != req.getColumnId()) {
				ShopColumnEntity column = shopColumnDao.get(req.getColumnId());
				if (null != column) {
					Page<StoreEntity> page = new Page<StoreEntity>();
					page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
					page.setBeginCount(
							(null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
					page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
					StringBuilder hql = new StringBuilder(
							"select a from StoreEntity a inner join a.columnList b where b.id=" + req.getColumnId());
					page = storeDao.queryPage(page, hql.toString());

					ShopColumnForm columnForm = new ShopColumnForm();
					columnForm.setColumnId(column.getId());
					columnForm.setColumnName(column.getColumnName());
					page.getResultList().forEach(o -> {
						columnForm.getStoreList().add(new StoreForm(o.getId(), o.getStoreName()));
					});
					res.setShopColumnForm(columnForm);
					res.setPageSize(page.getPageSize());
					res.setCurrentPage(page.getCurrentPage());
					res.setTotalResult(page.getTotalCount());
					res.setTotal(page.getTotalPage());
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} else {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
				}
			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
		}
		return res;
	}
	public boolean whetherPurchased(Integer memberId,Integer productId) {
		StringBuilder hql = new StringBuilder("select a from ProductOrderEntity a  "
				+ "inner join a.orderItemList b inner join b.product c inner join a.orderPaymentEntity d where b.product.id=? "
				+ "and a.member.id=?  and b.state="+EntityContext.RECORD_STATE_VALID+" and (a.orderState="+
				EntityContext.PRODUCT_ORDER_STATE_CLOSE_NO_APPRAISE+" or a.orderState="+EntityContext.PRODUCT_ORDER_STATE_CLOSE_APPRAISE
				+") and a.state="+EntityContext.RECORD_STATE_VALID+" and d is not null  ORDER BY d.createTime desc");
		List<ProductOrderEntity> list = productOrderDao.query(hql.toString(), productId,memberId);
		if(null!=list && list.size()>0) {
			ProductOrderEntity order = list.get(0);
			Date nowDate = new Date();
			if(null!=order.getOrderPaymentEntity()) {
				Date date=new Date(order.getOrderPaymentEntity().getCreateTime().getTime());
				Calendar calendar=Calendar.getInstance();
				calendar.setTime(date);
				calendar.add(Calendar.DATE, shopContextInfo.videoValidTime);
				int compareTo = nowDate.compareTo(calendar.getTime());
				if(compareTo<=0) {               
					return true;
				}else {
					return false;
				}
			}else {
				return false;
			}
		}else {
			return false;
		}
	}
	public Map<String,Object> getPurchasedInfo(Integer memberId,Integer productId) {
		Map<String,Object> map = new HashMap<String,Object>();
		Integer purchased=0;
		String videoExpirationTime="";
		StringBuilder hql = new StringBuilder("select a from ProductOrderEntity a  "
				+ "inner join a.orderItemList b inner join b.product c inner join a.orderPaymentEntity d where b.product.id=? "
				+ "and a.member.id=?  and b.state="+EntityContext.RECORD_STATE_VALID+" and (a.orderState="+
				EntityContext.PRODUCT_ORDER_STATE_CLOSE_NO_APPRAISE+" or a.orderState="+EntityContext.PRODUCT_ORDER_STATE_CLOSE_APPRAISE
				+") and a.state="+EntityContext.RECORD_STATE_VALID+" and d is not null  ORDER BY d.createTime desc");
		List<ProductOrderEntity> list = productOrderDao.query(hql.toString(), productId,memberId);
		if(null!=list && list.size()>0) {
			ProductOrderEntity order = list.get(0);
			Date nowDate = new Date();
			if(null!=order.getOrderPaymentEntity()) {
				Date date=new Date(order.getOrderPaymentEntity().getCreateTime().getTime());
				Calendar calendar=Calendar.getInstance();
				calendar.setTime(date);
				calendar.add(Calendar.DATE, shopContextInfo.videoValidTime);
				int compareTo = nowDate.compareTo(calendar.getTime());
				videoExpirationTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((calendar.getTime()));
				if(compareTo<=0) {               
					purchased=1;
				}else {
					purchased=2;
				}
			}
		}
		map.put("purchased", purchased);
		map.put("videoExpirationTime", videoExpirationTime);
		return map;
	}

	@Override
	public IResponse getStratificationColumn(ShopColumnReq req) {
		GetServiceShopRootColumnRes res = new GetServiceShopRootColumnRes();
		// 获取登录用户信息
		Object userObj = getPrincipal(true);
		if (!isEnterpriseMember(userObj)) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		} 
		if (null != req.getServiceId()) {
			ServiceEntity service = serviceDao.get(req.getServiceId());
			if (null != service) {
				if (null != service.getColumn()) {
					Integer rootColumnId = service.getColumn().getId();
					// 获取该主栏目下所有栏目信息
					StringBuilder sql = new StringBuilder("");
					if (null != userObj
							&& (userObj instanceof PlatformUserEntity || userObj instanceof ShopUserEntity)) {
						sql.append("select a.* from t_column a where a.parentColumnId=" + rootColumnId + " and a.state="
								+ EntityContext.RECORD_STATE_VALID + " order by a.orders");
					} else {
						String regionCode = StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "1";
						sql.append(
								"select distinct a.* FROM t_column a INNER JOIN t_column_region b INNER JOIN t_region c ON "
										+ "a.parentColumnId=" + rootColumnId + " AND a.columnType=8"
										+ " AND a.columnState="
										+ EntityContext.RECORD_STATE_VALID + " and a.state="
										+ EntityContext.RECORD_STATE_VALID
										+ " AND a.id=b.columnId AND b.regionId=c.id AND (c.startRegionCode+0)<=("
										+ regionCode + "+0) AND (c.endRegionCode+0)>=(" + regionCode
										+ "+0)  and (targetType=").append(null!=req.getTargetType() ? req.getTargetType() : "3").append(" or targetType=7) order by a.orders");
						
					}

					List<ColumnEntity> columnList = columnDao.getListBySql(sql.toString());

					columnList.forEach(o -> {
						ShopColumnForm columnForm = new ShopColumnForm();
						columnForm.setColumnCode(o.getColumnCode());
						columnForm.setColumnInfo(o.getColumnInfo());
						columnForm.setColumnLevel(o.getColumnLevel());
						columnForm.setColumnName(o.getColumnName());
						columnForm.setColumnPath(o.getColumnPath());
						columnForm.setColumnState(o.getColumnState());
						columnForm.setColumnType(o.getColumnType());
						columnForm.setCommendFlag(o.getCommendFlag());
						columnForm.setMappingFolderId(o.getMappingFolderId());
						columnForm.setMappingFolderName(o.getMappingFolderName());
						columnForm.setMappingSystem(o.getMappingSystem());
						columnForm.setOrders(o.getOrders());
						columnForm.setColumnImage(getAsset(o.getColumnImage(),EntityContext.PRODUCT_LIST_DETAIL_FLAG_TV));
						columnForm.setColumnPhoneImage(getAsset(o.getColumnPhoneImage(),EntityContext.PRODUCT_LIST_DETAIL_FLAG_MOBILE));
						columnForm.setTargetType(o.getTargetType());
						columnForm.setColumnId(o.getId());
						columnForm.setParentColumnId(rootColumnId);
						columnForm.setIsGlobal(o.getIsGlobal());
						o.getRegionList().forEach(b -> {
							columnForm.getRegionList().add(new RegionForm(b.getId(), b.getRegionCode(),
									b.getRegionName(), b.getStartRegionCode(), b.getEndRegionCode()));
						});
						try {
							columnForm.setIsOperation((null != userObj && userObj instanceof ShopUserEntity
									&& ((ShopColumnEntity) o).getStoreList()
											.contains(((ShopUserEntity) userObj).getStore())) ? true
													: (null != userObj && userObj instanceof PlatformUserEntity) ? true
															: false);
						} catch (Exception ex) {
							columnForm.setIsOperation(false);
						}
						
						
						
						GetProductListReq getProductListReq = new GetProductListReq();
						getProductListReq.setPageSize(req.getProductPageSize());
						Integer needDetail = EntityContext.PRODUCT_LIST_DETAIL_FLAG_ALL;
						if (null == userObj || (null != userObj && userObj instanceof MemberEntity)) {
							getProductListReq.setAuditState(EntityContext.RECORD_AUDIT_PASS);
							getProductListReq.setState(EntityContext.RECORD_STATE_VALID);
							getProductListReq.setRegionCode(StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "1");
							needDetail = EntityContext.PRODUCT_LIST_DETAIL_FLAG_MOBILE;
						} 
						getProductListReq.setOrderStr(StringUtils.isNotEmpty(getProductListReq.getOrderStr())?getProductListReq.getOrderStr():"createTime|desc");
						getProductListReq.setOrderStr(StringUtils.isNotEmpty(getProductListReq.getOrderStr())?getProductListReq.getOrderStr():"createTime|desc");
						HashMap<String, Object> pel = getProductEntityList(getProductListReq, userObj);
						columnForm.setProductList(getProductList( getProductListReq, needDetail, userObj,pel,false));
						
						
						res.getSubColumnList().add(columnForm);
					});

					res.getSubColumnList().sort(comparingInt(ShopColumnForm::getOrders));
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} else {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
				}
			} else {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
			}
		} else{
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	public IResponse searchProductSpecificationList(GetProductListReq req) {
		GetProductListRes res = new GetProductListRes();
		if(!StringUtils.isNotEmpty(req.getProductName())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			return res;
		}
//		if (null != req.getColumnId()) {

		// 获取登录用户信息
		Integer needDetail = EntityContext.PRODUCT_LIST_DETAIL_FLAG_ALL;
		Object userObj = getPrincipal(true);
		if (null == userObj || (null != userObj && userObj instanceof MemberEntity)) {
			req.setAuditState(EntityContext.RECORD_AUDIT_PASS);
			//req.setIsMarketable(EntityContext.UP_SHELF_STATE_UP);
			req.setState(EntityContext.RECORD_STATE_VALID);
			//req.setStoreId(null);
			req.setRegionCode(StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "1");
			needDetail = EntityContext.PRODUCT_LIST_DETAIL_FLAG_MOBILE;
			MemberEntity member = (MemberEntity)userObj;
			if(member.getType() == EntityContext.ACCOUNT_TYPE_PUBLIC_ACCOUNT) {
				req.setRegionCode(member.getRegionCode());
			}
			
		} else if (null != userObj && userObj instanceof ShopUserEntity) {
			
			StoreEntity store = ((ShopUserEntity) userObj).getStore();
			req.setStoreId(store.getId());
		}
		
		req.setOrderStr(StringUtils.isNotEmpty(req.getOrderStr())?req.getOrderStr():"createTime|desc");
		//HashMap<String, Object> hm = getProductEntityList(req, userObj);
		HashMap<String, Object> hm = new HashMap<String, Object>();
		Page<ProductEntity> page = new Page<ProductEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

		StringBuilder baseHql = new StringBuilder("select distinct ")
				.append("a from ProductEntity a inner join a.productSpecification b ");

		// 根据申请条件构造对象关联
		baseHql.append("inner join b.brand c ")
				.append("inner join a.productGroupList d inner join d.columnList e ")
				.append(null != req.getStoreId() ? "inner join b.store f " : "")
				.append((null==req.getIsFavorite() || EntityContext.IS_NOT_FAVORITE==req.getIsFavorite()) ? 
						((null != userObj && userObj instanceof MemberEntity) || null == userObj
						? "inner join b.regionList g ": "") : "inner join a.memberList m ")
				.append(" where");

		// 根据申请条件构造查询语句
		StringBuilder searchHql = new StringBuilder();
		
		MemberEntity shiroMember = null != userObj && userObj instanceof MemberEntity ? (MemberEntity) userObj : null;
		searchHql.append(
						(null==req.getIsFavorite() || EntityContext.IS_NOT_FAVORITE==req.getIsFavorite()) ? 
						((null != userObj && userObj instanceof MemberEntity) || null == userObj
								? " and (g.startRegionCode+0)<="
										+ (StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "1")
										+ " and (g.endRegionCode+0)>="
										+ (StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "1")
								: "") :" and m.id="+shiroMember.getId()+ " ");
		
		// 组合查询语句
		baseHql.append(
				baseHql.toString().endsWith("where") && searchHql.length() > 0 ? searchHql.toString().substring(4)
						: "");

		List<ProductCategoryEntity> categoryList = productCategoryDao.getListByHql("from ProductCategoryEntity where categoryKeywords like'%"+req.getProductName()+"%' or categoryName like'%"+req.getProductName()+"%'", "");
		if(null!=categoryList && categoryList.size()>0) {
			baseHql.append(
					baseHql.toString().endsWith("where") ? "(" :" and (");
			for(ProductCategoryEntity category : categoryList) {
				baseHql.append(category.getCategoryLevel() == 3 ? " b.level3Id=" + category.getId() +" or"
										: (category.getCategoryLevel() == 2 ? " b.level2Id=" + category.getId()+" or"
												: (category.getCategoryLevel() == 1 ? " b.level1Id=" + category.getId()+" or"
														: "")));
			}
			baseHql.append( " b.productName like'%" + req.getProductName() +  "%' or b.productKeywords like'%"+ req.getProductName()+"%'")
			.append(" or c.brandKeywords like'%"+req.getProductName()+"%' or c.brandName like'%"+req.getProductName()+"%')");
			//baseHql.delete(baseHql.length()-2, baseHql.length()).append(")");
		}else {
			baseHql.append( " and ( b.productName like'%" + req.getProductName() + "%' or b.productKeywords like'%"+ req.getProductName()+"%'")
			.append(" or c.brandKeywords like'%"+req.getProductName()+"%' or c.brandName like'%"+req.getProductName()+"%')");
		}

		baseHql.append(
				null != req.getAuditState()
						? (baseHql.toString().endsWith("where") ? " b.auditState=" + req.getAuditState()
								: " and b.auditState=" + req.getAuditState())
						: "")
				.append(null != req.getState() ? (baseHql.toString().endsWith("where") ? " b.state=" + req.getState()
						: " and b.state=" + req.getState()) : "");
		if(null != userObj && userObj instanceof MemberEntity) {
			MemberEntity member = (MemberEntity) userObj;
			String isMarketableList =member.getType() == EntityContext.ACCOUNT_TYPE_PUBLIC_ACCOUNT ? 
					"a.isMarketable in('"+EntityContext.UP_SHELF_STATE_UP_FOR_COMPANY+"','"+EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL_AND_COMPANY +"')" :
				"a.isMarketable in('"+EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL+"','"+EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL_AND_COMPANY +"')";
			baseHql.append(baseHql.toString().endsWith("where") ? isMarketableList
					: " and " + isMarketableList);

		}else if(null == userObj) {
			String isMarketableList ="a.isMarketable in('"+EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL+
					"','"+EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL_AND_COMPANY +"')";
			baseHql.append(baseHql.toString().endsWith("where") ? isMarketableList
					: " and " + isMarketableList);
		}else {
			baseHql.append(null != req.getIsMarketable()
			? (baseHql.toString().endsWith("where") ? " a.isMarketable=" + req.getIsMarketable()
			: " and a.isMarketable=" + req.getIsMarketable()):"");
		}
		// 根据申请的排序字段要求进行结果排序

		String[] orderStrs = req.getOrderStr().split(",");
		StringBuilder orderHql = new StringBuilder(" order by ");
		for (String orderStr : orderStrs) {
			String s[] = orderStr.split("\\|");
			orderHql.append("a." + s[0] + " " + s[1] + ",");
		}
		baseHql.append(orderHql.substring(0, orderHql.length() - 1));
		logger.info("本次查询hql====>>>" + baseHql.toString());

		page = productDao.queryPage(page, baseHql.toString());
		hm.put("productList", page.getResultList());
		hm.put("totalResult", page.getTotalCount());
		hm.put("pageSize", page.getPageSize());
		hm.put("currentPage", page.getCurrentPage());
		hm.put("total", page.getTotalPage());
		
		res.setProductList(getProductList( req, needDetail, userObj,hm,false));

		res.setTotalResult(res.getProductList().size() > 0 ? (Integer) hm.get("totalResult") : 0);
		res.setPageSize((Integer) hm.get("pageSize"));
		res.setCurrentPage((Integer) hm.get("currentPage"));
		res.setTotal((Integer) hm.get("total"));
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		return res;
	}
	public void getMinimumPrice(List<ProductPriceForm> productPriceList) {
		//先根据有活动价的规格排，没有再按销售价排
		Collections.sort(productPriceList,new Comparator<ProductPriceForm>(){
			public int compare(ProductPriceForm o1, ProductPriceForm o2) {
				if(null==o1.getPromotionPrice() || null==o2.getPromotionPrice()) {
					return 1;
				}
				if(o1.getPromotionPrice().compareTo(o2.getPromotionPrice())==-1){
					return -1;
				}
				if(o1.getPromotionPrice().compareTo(o2.getPromotionPrice())==0)
					return 0;
				return 1;
			}
		});
		if(null!=productPriceList.get(0) && 
				null==productPriceList.get(0).getPromotionPrice()) {
			Collections.sort(productPriceList,new Comparator<ProductPriceForm>(){
				public int compare(ProductPriceForm o1, ProductPriceForm o2) {
					if(null==o1.getSalesPrice() || null==o2.getSalesPrice()) {
						return 1;
					}
					if(o1.getSalesPrice().compareTo(o2.getSalesPrice())==-1){
						return -1;
					}
					if(o1.getSalesPrice().compareTo(o2.getSalesPrice())==0)
						return 0;
					return 1;
				}
			});
		}
	}
}
