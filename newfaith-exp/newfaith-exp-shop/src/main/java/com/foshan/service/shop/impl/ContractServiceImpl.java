package com.foshan.service.shop.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.dao.generic.Page;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.RegionEntity;
import com.foshan.entity.UserEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.shop.ContractEntity;
import com.foshan.entity.shop.MemberEntity;
import com.foshan.entity.shop.ProductOrderEntity;
import com.foshan.entity.shop.PurchasingCompanyEntity;
import com.foshan.form.AssetForm;
import com.foshan.form.RegionForm;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.shop.ContractDeductionDetailsForm;
import com.foshan.form.shop.ContractForm;
import com.foshan.form.shop.ContractRecordForm;
import com.foshan.form.shop.PurchasingCompanyForm;
import com.foshan.form.shop.request.ContractReq;
import com.foshan.form.shop.response.contract.GetContractDeductionDetailsListRes;
import com.foshan.form.shop.response.contract.GetContractInfoRes;
import com.foshan.form.shop.response.contract.GetContractListRes;
import com.foshan.service.shop.IContractService;
import com.foshan.util.DateUtil;

@Transactional
@Service("contractService")
public class ContractServiceImpl extends GenericShopService implements IContractService {
		private final static Logger logger = LoggerFactory.getLogger(ContractServiceImpl.class);
		@Override
		public IResponse getContractList(ContractReq req) {
			GetContractListRes res = new GetContractListRes();
			Object userObj = getPrincipal(true);
			if (null != userObj && (userObj instanceof MemberEntity || userObj instanceof UserEntity)) {
				Page<ContractEntity> page = new Page<ContractEntity>();
				page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
				page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
				page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
				String nowTime ="";
				StringBuilder hql = new StringBuilder("select distinct a from ContractEntity a inner join a.company c ");
				if(userObj instanceof MemberEntity) {
					MemberEntity member = (MemberEntity) userObj;
					if (null == member.getCompany()) {
						res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
						res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
						return res;
					}
					//req.setCompanyId(member.getCompany().getId());
					req.setCompanyIdList(member.getPurchasingCompany().getId().toString());
					req.setState(EntityContext.RECORD_STATE_VALID);
					nowTime = DateUtil.formatLongFormat(new Timestamp(System.currentTimeMillis()));
				}else if(userObj instanceof UserEntity) {
					UserEntity user = (UserEntity) userObj;
					hql.append(" inner join c.regionList r where (");
					StringBuilder regionCode = new StringBuilder();
					for(RegionEntity region : user.getRegionList()) {
						regionCode.append(" r.regionCode between '" + region.getStartRegionCode() + "' and '" + region.getEndRegionCode() + "' or");
					}
					hql.append(regionCode.substring(0, regionCode.length()-2)+")");
				}

				
				hql.append(null!=req.getState() ? ( hql.toString().contains("where") ? " and a.state="+req.getState() : " where a.state="+req.getState()):"")
				.append(StringUtils.isNotEmpty(req.getContractCode()) ? 
						(hql.toString().contains("where") ? " and a.contractCode like'%"+req.getContractCode()+"%'" :"where a.contractCode like'%"+req.getContractCode()+"%'") : "")
				.append(StringUtils.isNotEmpty(req.getContractName()) ? 
						(hql.toString().contains("where") ? " and a.contractName like'%"+req.getContractName()+"%'" :"where a.contractName like'%"+req.getContractName()+"%'") : "")
				.append(StringUtils.isNotEmpty(nowTime) ?
						(hql.toString().contains("where") ? " and a.startTime<= '"+nowTime+"' AND a.endTime>='"+nowTime+"'" : " where  a.startTime<= '"+nowTime+"' AND a.endTime>='"+nowTime+"'") : "")
				.append(StringUtils.isNotEmpty(req.getCompanyIdList()) ? 
						(hql.toString().contains("where") ? " and c.id in("+req.getCompanyIdList()+")" :"where c.id("+req.getCompanyIdList()+")") : "")
				.append(StringUtils.isNotEmpty(req.getCompanyName()) ? 
						(hql.toString().contains("where") ? " and c.name like'%"+req.getCompanyName()+"%'" :"where c.name like'%"+req.getCompanyName()+"%'") : "");
				
				page = contractDao.queryPage(page, hql.toString());

				page.getResultList().forEach(o -> {
					ContractForm contractForm = new ContractForm(o.getId(), o.getContractName() ,o.getContractCode() ,
							new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(o.getStartTime()),
							new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(o.getEndTime()) ,o.getContractAccount().toString(),
							o.getOverplusLimit().toString(),o.getConsumeType());
					
					PurchasingCompanyForm purchasingCompanyForm = new PurchasingCompanyForm
							(o.getCompany().getId(),o.getCompany().getName(),o.getCompany().getCode(),o.getCompany().getAddress(),o.getCompany().getTel(),o.getCompany().getContacts(),o.getCompany().getEmail());

					if (null != o.getCompany().getLogo()) {
						AssetForm assetForm = new AssetForm(o.getCompany().getLogo().getId(), o.getCompany().getLogo().getAssetCode(),
								o.getCompany().getLogo().getAssetName(), o.getCompany().getLogo().getImageFile(), o.getCompany().getLogo().getAssetType(), null,
								null);
						purchasingCompanyForm.setLogoForm(assetForm);
					}
					o.getCompany().getRegionList().forEach(r->{
						RegionForm regionForm = new RegionForm(r.getId(), r.getRegionCode() , r.getRegionName() ,r.getParentRegion().getId() ,
								r.getParentRegion().getRegionName() , r.getStartRegionCode() ,r.getEndRegionCode() , r.getRegionLevel() ) ;
						purchasingCompanyForm.getRegionList().add(regionForm);
					});
					contractForm.setPurchasingCompanyForm(purchasingCompanyForm);
					if (null != o.getEnclosure()) {
						AssetForm assetForm = new AssetForm(o.getEnclosure().getId(), o.getEnclosure().getAssetCode(),
								o.getEnclosure().getAssetName(), o.getEnclosure().getImageFile(), o.getEnclosure().getAssetType(), null,
								null);
						contractForm.setEnclosure(assetForm);
					}
					res.getContractList().add(contractForm);
				});

				res.setTotalResult(page.getTotalCount());
				res.setPageSize(page.getPageSize());
				res.setCurrentPage(page.getCurrentPage());
				res.setTotal(page.getTotalPage());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

			} else {
				res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
			}

			return res;
		}

		@Override
		public IResponse addContract(ContractReq req) {
			GenericResponse res = new GenericResponse();
			// 获取登录用户信息
			Object userObj = getPrincipal(true);
			if (null != userObj && userObj instanceof UserEntity) {
				if (null!=req.getCompanyId() && null != req.getEnclosureId() &&
						StringUtils.isNotEmpty(req.getContractName()) && StringUtils.isNotEmpty(req.getContractCode())) {
					PurchasingCompanyEntity company = purchasingCompanyDao.get(req.getCompanyId());
					AssetEntity enclosure = assetDao.get(req.getEnclosureId());
					if (null != company && null!= enclosure) {
						ContractEntity contract=null;
						contract = contractDao.getUniqueByHql("from ContractEntity where contractCode='"+req.getContractCode()+"'", "");
						if(null != contract) {
							res.setRet(ResponseContext.RES_DATA_DUPLICATE_CODE);
							res.setRetInfo(ResponseContext.RES_DATA_DUPLICATE_INFO+"合同编号不能有重复！");
							return res;
						}
						try {
							Timestamp startTime = new Timestamp(DateUtil.parseLongFormat(req.getStartTime()).getTime());
							Timestamp endTime = new Timestamp(DateUtil.parseLongFormat(req.getEndTime()).getTime());
							contract=new ContractEntity();
							contract.setCompany(company);
							contract.setConsumeType(req.getConsumeType());
							contract.setContractAccount(new BigDecimal(req.getContractAccount()));
							contract.setContractCode(req.getContractCode());
							contract.setContractName(req.getContractName());
							contract.setEnclosure(enclosure);
							contract.setEndTime(endTime);
							contract.setStartTime(startTime);
							contract.setState(EntityContext.RECORD_STATE_VALID);
							contract.setOverplusLimit(new BigDecimal(req.getContractAccount()));
							contractDao.save(contract);
							res.setRet(ResponseContext.RES_SUCCESS_CODE);
							res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
						} catch (ParseException e) {
							res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
							res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
						}
						
					} else {
						res.setRet(ResponseContext.RES_DATA_NULL_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
					}
				} else {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				}
			} else {
				res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			}
			return res;
		}

		@Override
		public IResponse modifyContract(ContractReq req) {
			GenericResponse res = new GenericResponse();
			// 获取登录用户信息
			Object userObj = getPrincipal(true);
			if (null != userObj && userObj instanceof UserEntity) {
				if (null!=req.getContractId() &&
						StringUtils.isNotEmpty(req.getContractName()) && StringUtils.isNotEmpty(req.getContractCode())) {
					ContractEntity contract = contractDao.get(req.getContractId());
					if(!contract.getContractCode().equals(req.getContractCode())) {
						ContractEntity c = contractDao.getUniqueByHql("from ContractEntity where contractCode='"+req.getContractCode()+"'", "");
						if(null != c) {
							res.setRet(ResponseContext.RES_DATA_DUPLICATE_CODE);
							res.setRetInfo(ResponseContext.RES_DATA_DUPLICATE_INFO+"合同编号不能有重复！");
							return res;
						}
					}
					if (null != contract) {
						try {
							Timestamp startTime = new Timestamp(DateUtil.parseLongFormat(req.getStartTime()).getTime());
							Timestamp endTime = new Timestamp(DateUtil.parseLongFormat(req.getEndTime()).getTime());
							//contract.setCompany(company);
							if(null!=req.getContractAccount()) {
								BigDecimal consumption = contract.getContractAccount().subtract(contract.getOverplusLimit());
								if(consumption.compareTo(new BigDecimal(req.getContractAccount()))==1) {
									res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
									res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO+"合同额度不能少于已消费额度！");
									return res;
								}
								contract.setContractAccount(new BigDecimal(req.getContractAccount()));
								contract.setOverplusLimit(new BigDecimal(req.getContractAccount()).subtract(consumption));
							}
							contract.setConsumeType(req.getConsumeType());
							contract.setContractCode(req.getContractCode());
							contract.setContractName(req.getContractName());
							contract.setEnclosure(contract.getEnclosure().getId()!=req.getEnclosureId() ? 
									assetDao.get(req.getEnclosureId()) : contract.getEnclosure());
							contract.setEndTime(endTime);
							contract.setStartTime(startTime);
							contract.setState(EntityContext.RECORD_STATE_VALID);

							
							res.setRet(ResponseContext.RES_SUCCESS_CODE);
							res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
						} catch (ParseException e) {
							res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
							res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
						}
						
					} else {
						res.setRet(ResponseContext.RES_DATA_NULL_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
					}
				} else {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				}
			} else {
				res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			}
			return res;
		}

		@Override
		public IResponse getContractInfo(ContractReq req) {
			GetContractInfoRes res = new GetContractInfoRes();
			Object userObj = getPrincipal(true);
			if (null != userObj && userObj instanceof UserEntity) {
				if (null!=req.getContractId()) {
					ContractEntity contract = contractDao.get(req.getContractId());
					if (null != contract) {
						ContractForm contractForm = new ContractForm(contract.getId(), contract.getContractName() ,contract.getContractCode() ,
								new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(contract.getStartTime()),
								new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(contract.getEndTime()) ,contract.getContractAccount().toString(),
								contract.getOverplusLimit().toString(),contract.getConsumeType());

						if (null != contract.getEnclosure()) {
							AssetForm assetForm = new AssetForm(contract.getEnclosure().getId(), contract.getEnclosure().getAssetCode(),
									contract.getEnclosure().getAssetName(), contract.getEnclosure().getImageFile(), contract.getEnclosure().getAssetType(), null,
									null);
							contractForm.setEnclosure(assetForm);
						}
						PurchasingCompanyForm purchasingCompanyForm = new PurchasingCompanyForm
								(contract.getCompany().getId(),contract.getCompany().getName(),contract.getCompany().getCode(),contract.getCompany().getAddress(),
										contract.getCompany().getTel(),contract.getCompany().getContacts(),contract.getCompany().getEmail());

						if (null != contract.getCompany().getLogo()) {
							AssetForm assetForm = new AssetForm(contract.getCompany().getLogo().getId(), contract.getCompany().getLogo().getAssetCode(),
									contract.getCompany().getLogo().getAssetName(), contract.getCompany().getLogo().getImageFile(), contract.getCompany().getLogo().getAssetType(), null,
									null);
							purchasingCompanyForm.setLogoForm(assetForm);
						}
						contract.getCompany().getRegionList().forEach(r->{
							RegionForm regionForm = new RegionForm(r.getId(), r.getRegionCode() , r.getRegionName() ,r.getParentRegion().getId() ,
									r.getParentRegion().getRegionName() , r.getStartRegionCode() ,r.getEndRegionCode() , r.getRegionLevel() ) ;
							purchasingCompanyForm.getRegionList().add(regionForm);
						});
						contractForm.setPurchasingCompanyForm(purchasingCompanyForm);
						res.setContractForm(contractForm);
						res.setRet(ResponseContext.RES_SUCCESS_CODE);
						res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
					} else {
						res.setRet(ResponseContext.RES_DATA_NULL_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
					}
				} else {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				}
			} else {
				res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			}
			return res;
		}

		@Override
		public IResponse deleteContract(ContractReq req) {
			GenericResponse res = new GenericResponse();
			Object userObj = getPrincipal(true);
			if (null != userObj && userObj instanceof UserEntity) {
				if (null!=req.getContractId()) {
					ContractEntity contract = contractDao.get(req.getContractId());
					if (null != contract) {
						contract.setState(EntityContext.RECORD_STATE_INVALID);
						res.setRet(ResponseContext.RES_SUCCESS_CODE);
						res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
					} else {
						res.setRet(ResponseContext.RES_DATA_NULL_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
					}
				} else {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				}
			} else {
				res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			}
			return res;
		}

		public IResponse getContractDeductionDetailsList(ContractReq req) {
			GetContractDeductionDetailsListRes res = new GetContractDeductionDetailsListRes();
			Object userObj = getPrincipal(true);
			if (null != userObj && userObj instanceof UserEntity) {
				if (null!=req.getContractId()) {
					Page<ProductOrderEntity> page = new Page<ProductOrderEntity>();
					page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
					page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
					page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
					StringBuilder hql = new StringBuilder();
		
					hql.append("select a from ProductOrderEntity a inner join a.contract c where c.id="+req.getContractId());
	
					// 增加排序
					hql.append(" and a.state=" + EntityContext.RECORD_STATE_VALID + "  order by a.createTime desc");
		
					page = productOrderDao.queryPage(page, hql.toString());
					// 根据查询结果队列循环构造返回列表
					page.getResultList().forEach(o -> {
						ContractRecordForm contractRecordForm = null;
						if(StringUtils.isNoneEmpty(o.getContractRecord())) {
							ObjectMapper mapper = new ObjectMapper();
							try {
								contractRecordForm =mapper.readValue(o.getContractRecord(), ContractRecordForm.class);
							} catch (JsonParseException e) {
								e.printStackTrace();
							} catch (JsonMappingException e) {
								e.printStackTrace();
							} catch (JSONException e) {
								e.printStackTrace();
							} catch (IOException e) {
								e.printStackTrace();
							}
						}
						res.getContractDeductionDetailsFormList().add(new ContractDeductionDetailsForm(o.getOrderCode() ,
								new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(o.getCreateTime()),
								o.getOrderTotalAmount().toString(),o.getOrderState() ,o.getConsumeType() ,
								(null!=contractRecordForm ? contractRecordForm.getOverplusLimit().toString():"") ,
								(null!=contractRecordForm ? contractRecordForm.getContractAccount() : "") ,o.getStore().getStoreName(),
								(null!=contractRecordForm ? contractRecordForm.getConsumeAccount() : ""),
								(null!=contractRecordForm ? contractRecordForm.getConsumeAfterOverplusLimit() : ""),o.getId()));
					});
					res.setTotalResult(page.getTotalCount());
					res.setPageSize(page.getPageSize());
					res.setCurrentPage(page.getCurrentPage());
					res.setTotal(page.getTotalPage());
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} else {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				}
			} else {
				res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			}
			return res;
		}
}
