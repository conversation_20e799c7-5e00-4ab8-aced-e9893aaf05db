package com.foshan.service.shop.impl;

import java.util.List;
import java.util.Map;

import javax.transaction.Transactional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.shop.LiveReplayForm;
import com.foshan.form.shop.request.LiveRequest;
import com.foshan.form.shop.response.live.GetLiveReplayListRes;
import com.foshan.service.IWeiXinApiService;
import com.foshan.service.shop.ILiveReplayService;
import com.foshan.util.DateUtil;
import com.foshan.util.SpringHandler;
import com.foshan.util.WeiXinApiUtil;
import com.foshan.util.WeiXinApiUtil.LiveReplayResult;

@Transactional
@Service("liveReplayService")
public class LiveReplayServiceImpl extends GenericShopService implements ILiveReplayService {
	protected Logger logger = LoggerFactory.getLogger(getClass());

	@Override
	public IResponse getLiveReplayList(LiveRequest req) {
		GetLiveReplayListRes res = new GetLiveReplayListRes();
		
		if(null!=req.getRoomId()) {
			res.setCurrentPage(req.getRequestPage());
			res.setPageSize(req.getPageSize());
			IWeiXinApiService weiXinApiService =  (IWeiXinApiService) SpringHandler.getBean("weiXinApiService");
		    String token =  weiXinApiService.getAccessToken(2);
			Map<String,Object> map = WeiXinApiUtil.getLiveInfo(token, req.getRoomId(), 
					req.getRequestPage(), req.getPageSize());
			res.setPageSize(req.getPageSize());
			res.setTotal(map.containsKey("total") ? Integer.valueOf(map.get("total")+""):0);
			if(map.containsKey("liveReplayResult")) {
				List<LiveReplayResult> liveReplayResult = (List<LiveReplayResult>) map.get("liveReplayResult");
				liveReplayResult.forEach(o->{
					LiveReplayForm liveReplay = new LiveReplayForm();
					liveReplay.setCreateTime(DateUtil.format(o.getCreate_time(),1));
					liveReplay.setExpireTime(DateUtil.format(o.getExpire_time(),1));
					liveReplay.setMediaUrl(o.getMedia_url());
					res.getLiveReplayList().add(liveReplay);
				});
			}
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		}else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			return res;
		}
		return res;
	}

}
