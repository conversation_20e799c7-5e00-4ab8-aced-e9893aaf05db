package com.foshan.service.shop;


import com.foshan.entity.shop.ProductOrderEntity;
import com.foshan.form.response.IResponse;
import com.foshan.form.shop.request.ProductOrderReq;
import org.springframework.transaction.annotation.Transactional;

public interface IProductOrderService {
	public IResponse addProductOrder(ProductOrderReq req) throws RuntimeException;
	public IResponse modifyProductOrder(ProductOrderReq req);

    @SuppressWarnings("unused")
    @Transactional
    IResponse modifyProductOrderInvoice(ProductOrderReq req);

    public IResponse deleteProductOrder(ProductOrderReq req);
	public IResponse cancelProductOrder(ProductOrderReq req);
	public IResponse getProductOrderList(ProductOrderReq req);
	public IResponse getProductOrderInfo(ProductOrderReq req);
	public IResponse addReminder(ProductOrderReq req);
	public IResponse modifyProductOrderState(ProductOrderReq req);
	public IResponse getProductOrderTraceList(ProductOrderReq req);
	public IResponse setReminder(ProductOrderReq req);
	public IResponse sendAgain(ProductOrderReq req);
	public IResponse excellentResults(ProductOrderReq req);
	public IResponse getLogisticsInfo(ProductOrderReq req);
	public IResponse getProductOrderCountByOrderState(ProductOrderReq req);
	public void checkCustomerServiceResults(ProductOrderReq req); 
	public void confirmReceiptOrder(ProductOrderEntity order, String logisiticsNumber);
	//void autoCancelProductOrder();
	//void autoConfirmReceiptOrder();
	public IResponse externalSystemGetOrderList(ProductOrderReq req);
	public IResponse selfHelpOrder();
	public IResponse bulkShipments(ProductOrderReq req);
	public IResponse confirmPayment(ProductOrderReq req);
	public IResponse drawInvoice(ProductOrderReq req);
	public IResponse getDistributionOrderList(ProductOrderReq req);
	public IResponse modifyProductOrderDeveloper(ProductOrderReq req);
	public IResponse initializationDeveloperDepartmentInfo(ProductOrderReq req);
	public IResponse userConfirmReceipt(ProductOrderReq req);
}
