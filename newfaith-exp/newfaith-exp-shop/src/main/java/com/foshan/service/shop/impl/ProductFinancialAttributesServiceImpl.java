package com.foshan.service.shop.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.LinkedHashMap;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.dao.generic.Page;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.shop.ProductEntity;
import com.foshan.entity.shop.ProductFinancialAttributesEntity;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.shop.ProductFinancialAttributesForm;
import com.foshan.form.shop.ProductForm;
import com.foshan.form.shop.ProductSpecificationForm;
import com.foshan.form.shop.request.ProductFinancialAttributesReq;
import com.foshan.form.shop.response.product.GetProductListRes;
import com.foshan.form.shop.response.productFinancialAttributes.AddProductFinancialAttributesRes;
import com.foshan.form.shop.response.productFinancialAttributes.ModifyProductFinancialAttributesRes;
import com.foshan.service.shop.IProductFinancialAttributesService;


@Transactional
@Service("productFinancialAttributesService")
public class ProductFinancialAttributesServiceImpl extends GenericShopService implements IProductFinancialAttributesService {

	@Override
	public IResponse addProductFinancialAttributes(ProductFinancialAttributesReq req) {
		// TODO Auto-generated method stub
		AddProductFinancialAttributesRes res = new AddProductFinancialAttributesRes();
		if (null!=req.getProductId() && StringUtils.isNotEmpty(req.getProductType()) && null!=req.getSalesType() && 
				StringUtils.isNotEmpty(req.getSecIncomeItems()) && StringUtils.isNotEmpty(req.getThirdIncomeItems())
				&& StringUtils.isNotEmpty(req.getForthIncomeItems()) && StringUtils.isNotEmpty(req.getSupplier()) &&
				((req.getSalesType()==1 && StringUtils.isNotEmpty(req.getSaleTax()) && StringUtils.isNotEmpty(req.getCostPrice())
					&& StringUtils.isNotEmpty(req.getCostTax()) && StringUtils.isNotEmpty(req.getCostStartTime()) 
					&& StringUtils.isNotEmpty(req.getCostEndTime())) 
					|| (req.getSalesType()==2&& StringUtils.isNotEmpty(req.getRevenueShareTax()) 
					&& StringUtils.isNotEmpty(req.getShareProportion())&& StringUtils.isNotEmpty(req.getShareProportionStartTime())
					&& StringUtils.isNotEmpty(req.getShareProportionEndTime())))) {

			ProductEntity product = productDao.get(req.getProductId());
			if(null== product ) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			StringBuilder hql = new StringBuilder("select a from ProductFinancialAttributesEntity a  where a.salesType="+req.getSalesType() +" and  a.product.id="+req.getProductId());
			if(1==req.getSalesType()) {
				hql.append(" and (")
					.append("( a.costStartTime <='"+req.getCostStartTime()+"' and a.costEndTime>='"+req.getCostEndTime()+"' ) or ")
					.append("( a.costStartTime >='"+req.getCostStartTime()+"' and a.costEndTime<='"+req.getCostEndTime()+"' ) or ")
					.append("( a.costStartTime >='"+req.getCostStartTime()+"' and a.costStartTime<= '"+req.getCostEndTime()+"' and a.costEndTime>='"+req.getCostEndTime()+"' ) or ")
					.append("( a.costStartTime <='"+req.getCostStartTime()+"' and a.costEndTime>= '"+req.getCostStartTime()+"' and a.costEndTime<='"+req.getCostEndTime()+"' )")
					.append(")");
			}else if(2==req.getSalesType()) {
				hql.append(" and (")
				.append("( a.shareProportionStartTime <='"+req.getShareProportionStartTime()+"' and a.shareProportionEndTime>='"+req.getShareProportionEndTime()+"' ) or ")
				.append("( a.shareProportionStartTime >='"+req.getShareProportionStartTime()+"' and a.shareProportionEndTime<='"+req.getShareProportionEndTime()+"' ) or ")
				.append("( a.shareProportionStartTime >='"+req.getShareProportionStartTime()+"' and a.shareProportionStartTime<= '"+req.getShareProportionEndTime()+"' and a.shareProportionEndTime>='"+req.getShareProportionEndTime()+"' ) or ")
				.append("( a.shareProportionStartTime <='"+req.getShareProportionStartTime()+"' and a.shareProportionEndTime>= '"+req.getShareProportionStartTime()+"' and a.shareProportionEndTime<='"+req.getShareProportionEndTime()+"' )")
				.append(")");
			}
			
			List<ProductFinancialAttributesEntity> financialAttributesList = productFinancialAttributesDao.getListByHql(hql.toString(), "");
			if(null != financialAttributesList && financialAttributesList.size()>0) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo("对不起，该日期与现有数据的日期有重叠；请更正后再提交！");
				return res;
			}
					
			ProductFinancialAttributesEntity financialAttributes = new ProductFinancialAttributesEntity();
			financialAttributes.setIncomeType(req.getIncomeType());
			financialAttributes.setProduct(product);
			financialAttributes.setProductType(req.getProductType());
			financialAttributes.setSalesType(req.getSalesType());
			financialAttributes.setState(EntityContext.RECORD_STATE_VALID);		
			financialAttributes.setForthIncomeItems(StringUtils.isNotEmpty(req.getForthIncomeItems()) ? 
					req.getForthIncomeItems() : financialAttributes.getForthIncomeItems());
			financialAttributes.setSecIncomeItems(StringUtils.isNotEmpty(req.getSecIncomeItems()) ? 
					req.getSecIncomeItems() : financialAttributes.getSecIncomeItems());
			financialAttributes.setSupplier(StringUtils.isNotEmpty(req.getSupplier()) ? 
					req.getSupplier() : financialAttributes.getSupplier());
			financialAttributes.setThirdIncomeItems(StringUtils.isNotEmpty(req.getThirdIncomeItems()) ? 
					req.getThirdIncomeItems() : financialAttributes.getThirdIncomeItems());
			financialAttributes.setSaleTax(StringUtils.isNotEmpty(req.getSaleTax()) ? new BigDecimal(req.getSaleTax()) : null);
			financialAttributes.setCostPrice(StringUtils.isNotEmpty(req.getCostPrice()) ? new BigDecimal(req.getCostPrice()):null);
			financialAttributes.setCostTax(StringUtils.isNotEmpty(req.getCostTax()) ? new BigDecimal(req.getCostTax()) :null);
			financialAttributes.setCostStartTime(StringUtils.isNotEmpty(req.getCostStartTime()) ? Timestamp.valueOf(req.getCostStartTime()) : null);
			financialAttributes.setCostEndTime(StringUtils.isNotEmpty(req.getCostEndTime()) ? Timestamp.valueOf(req.getCostEndTime()) : null);
			
			financialAttributes.setRevenueShareTax(StringUtils.isNotEmpty(req.getRevenueShareTax()) ?new BigDecimal(req.getRevenueShareTax()) :null);
			financialAttributes.setShareProportion(StringUtils.isNotEmpty(req.getShareProportion()) ? new BigDecimal(req.getShareProportion()) :
					null);
			financialAttributes.setShareProportionStartTime(StringUtils.isNotEmpty(req.getShareProportionStartTime()) ? Timestamp.valueOf(req.getShareProportionStartTime()): null);
			financialAttributes.setShareProportionEndTime(StringUtils.isNotEmpty(req.getShareProportionEndTime()) ? Timestamp.valueOf(req.getShareProportionEndTime()): null);
			
			productFinancialAttributesDao.save(financialAttributes);
			
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@SuppressWarnings("unchecked")
	@Override
	public IResponse modifyProductFinancialAttributes(ProductFinancialAttributesReq req) {
		// TODO Auto-generated method stub
		ModifyProductFinancialAttributesRes res = new ModifyProductFinancialAttributesRes();
		if (StringUtils.isNotEmpty(req.getProductType()) && null!=req.getSalesType() && 
			StringUtils.isNotEmpty(req.getSecIncomeItems()) && StringUtils.isNotEmpty(req.getThirdIncomeItems())
			&& StringUtils.isNotEmpty(req.getForthIncomeItems()) && StringUtils.isNotEmpty(req.getSupplier()) &&
			((req.getSalesType()==1 && StringUtils.isNotEmpty(req.getSaleTax()) && StringUtils.isNotEmpty(req.getCostPrice())
				&& StringUtils.isNotEmpty(req.getCostTax()) && StringUtils.isNotEmpty(req.getCostStartTime()) 
				&& StringUtils.isNotEmpty(req.getCostEndTime())) 
				|| (req.getSalesType()==2&& StringUtils.isNotEmpty(req.getRevenueShareTax()) 
				&& StringUtils.isNotEmpty(req.getShareProportion())&& StringUtils.isNotEmpty(req.getShareProportionStartTime())
				&& StringUtils.isNotEmpty(req.getShareProportionEndTime())))) {
			
			StringBuilder hql = new StringBuilder("select a from ProductFinancialAttributesEntity a  where a.salesType="+req.getSalesType() +" and  a.product.id="+req.getProductId());
			if(1==req.getSalesType()) {
				hql.append(" and (")
					.append("( a.costStartTime <='"+req.getCostStartTime()+"' and a.costEndTime>='"+req.getCostEndTime()+"' ) or ")
					.append("( a.costStartTime >='"+req.getCostStartTime()+"' and a.costEndTime<='"+req.getCostEndTime()+"' ) or ")
					.append("( a.costStartTime >='"+req.getCostStartTime()+"' and a.costStartTime<= '"+req.getCostEndTime()+"' and a.costEndTime>='"+req.getCostEndTime()+"' ) or ")
					.append("( a.costStartTime <='"+req.getCostStartTime()+"' and a.costEndTime>= '"+req.getCostStartTime()+"' and a.costEndTime<='"+req.getCostEndTime()+"' )")
					.append(")");
			}else if(2==req.getSalesType()) {
				hql.append(" and (")
				.append("( a.shareProportionStartTime <='"+req.getShareProportionStartTime()+"' and a.shareProportionEndTime>='"+req.getShareProportionEndTime()+"' ) or ")
				.append("( a.shareProportionStartTime >='"+req.getShareProportionStartTime()+"' and a.shareProportionEndTime<='"+req.getShareProportionEndTime()+"' ) or ")
				.append("( a.shareProportionStartTime >='"+req.getShareProportionStartTime()+"' and a.shareProportionStartTime<= '"+req.getShareProportionEndTime()+"' and a.shareProportionEndTime>='"+req.getShareProportionEndTime()+"' ) or ")
				.append("( a.shareProportionStartTime <='"+req.getShareProportionStartTime()+"' and a.shareProportionEndTime>= '"+req.getShareProportionStartTime()+"' and a.shareProportionEndTime<='"+req.getShareProportionEndTime()+"' )")
				.append(")");
			}
			hql.append(" and a.id!="+req.getFinancialAttributesId());
			List<ProductFinancialAttributesEntity> financialAttributesList = productFinancialAttributesDao.getListByHql(hql.toString(), "");
			if(null != financialAttributesList && financialAttributesList.size()>0) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo("对不起，该日期与现有数据的日期有重叠；请更正后再提交！");
				return res;
			}
			
			ProductFinancialAttributesEntity financialAttributes = productFinancialAttributesDao.get(req.getFinancialAttributesId());
			if(null== financialAttributes) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			financialAttributes.setForthIncomeItems(StringUtils.isNotEmpty(req.getForthIncomeItems()) ? 
					req.getForthIncomeItems() : financialAttributes.getForthIncomeItems());
			financialAttributes.setIncomeType(null != req.getIncomeType() ? 
					req.getIncomeType() : financialAttributes.getIncomeType());
			financialAttributes.setProductType(StringUtils.isNotEmpty(req.getProductType()) ? 
					req.getProductType() : financialAttributes.getProductType());
			financialAttributes.setSalesType(null!=req.getSalesType() ? 
					req.getSalesType() : financialAttributes.getSalesType());
			financialAttributes.setSecIncomeItems(StringUtils.isNotEmpty(req.getSecIncomeItems()) ? 
					req.getSecIncomeItems() : financialAttributes.getSecIncomeItems());
			financialAttributes.setSupplier(StringUtils.isNotEmpty(req.getSupplier()) ? 
					req.getSupplier() : financialAttributes.getSupplier());
			financialAttributes.setThirdIncomeItems(StringUtils.isNotEmpty(req.getThirdIncomeItems()) ? 
					req.getThirdIncomeItems() : financialAttributes.getThirdIncomeItems());
			
			financialAttributes.setSaleTax(StringUtils.isNotEmpty(req.getSaleTax()) ? new BigDecimal(req.getSaleTax()) :
					financialAttributes.getSaleTax());
			financialAttributes.setCostPrice(StringUtils.isNotEmpty(req.getCostPrice()) ? new BigDecimal(req.getCostPrice()):
					financialAttributes.getCostPrice());
			financialAttributes.setCostTax(StringUtils.isNotEmpty(req.getCostTax()) ? new BigDecimal(req.getCostTax()) :
					financialAttributes.getCostTax());
			financialAttributes.setCostStartTime(StringUtils.isNotEmpty(req.getCostStartTime()) ? Timestamp.valueOf(req.getCostStartTime()) : financialAttributes.getCostStartTime());
			financialAttributes.setCostEndTime(StringUtils.isNotEmpty(req.getCostEndTime()) ? Timestamp.valueOf(req.getCostEndTime()) : financialAttributes.getCostEndTime());
			financialAttributes.setRevenueShareTax(StringUtils.isNotEmpty(req.getRevenueShareTax()) ? new BigDecimal(req.getRevenueShareTax()) :
					financialAttributes.getRevenueShareTax());
			financialAttributes.setShareProportion(StringUtils.isNotEmpty(req.getShareProportion()) ? new BigDecimal(req.getShareProportion()) :
					financialAttributes.getShareProportion());
			financialAttributes.setShareProportionStartTime(null!= req.getShareProportionStartTime() ? Timestamp.valueOf(req.getShareProportionStartTime()) :financialAttributes.getShareProportionStartTime());
			financialAttributes.setShareProportionEndTime(null != req.getShareProportionEndTime() ?  Timestamp.valueOf(req.getShareProportionEndTime()):financialAttributes.getShareProportionEndTime());
			
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse deleteProductFinancialAttributes(ProductFinancialAttributesReq req) {
		// TODO Auto-generated method stub
		GenericResponse res = new GenericResponse();
		if (null!=req.getFinancialAttributesId()) {
			ProductFinancialAttributesEntity financialAttributes = productFinancialAttributesDao.get(req.getFinancialAttributesId());
			if(null== financialAttributes) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			financialAttributes.setProduct(null);
			productFinancialAttributesDao.delete(financialAttributes);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	
	@SuppressWarnings("unchecked")
	public GetProductListRes getProductList(ProductFinancialAttributesReq req) {
		GetProductListRes res = new GetProductListRes();
		Page<ProductEntity> page = new Page<ProductEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);


		StringBuilder hql = new StringBuilder("select distinct a from ProductEntity a inner join a.productSpecification b ");
		hql.append(null!=req.getFinancialAttributesIsNull() &&req.getFinancialAttributesIsNull()==EntityContext.FINANCIAL_ATTRIBUTES_IS_NOT_NULL ? 
				(StringUtils.isNotEmpty(req.getSalesTypeList()) ?
				" INNER JOIN a.financialAttributesList c where 1=1 " +
				" and c.salesType in("+req.getSalesTypeList()+")":"  INNER JOIN a.financialAttributesList c where 1=1 ")
				:" where 1=1"  );
		hql.append(StringUtils.isNotEmpty(req.getProductName()) ? " and b.productName like'%"+req.getProductName()+"%'":"")
			.append(StringUtils.isNotEmpty(req.getProductCode()) ? " and a.productCode like'%"+req.getProductCode()+"%'":"")
			.append(null!=req.getFinancialAttributesIsNull() &&req.getFinancialAttributesIsNull()==EntityContext.FINANCIAL_ATTRIBUTES_IS_NULL ? 
					" and a.id not in(select a1.id from ProductEntity a1 INNER JOIN a1.financialAttributesList c1)":"")
			.append(null!=req.getStoreId() ? " and b.store.id="+req.getStoreId() :"")
			.append(null!=req.getState() ? " and a.state= "+req.getState()+" and b.state="+req.getState() :"");
		hql.append(" order by a.id desc");
		page = productDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		ObjectMapper mapper = new ObjectMapper();
		try {
			DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
			for(ProductEntity o : page.getResultList()){
				ProductSpecificationForm productSpecification = new ProductSpecificationForm();
				productSpecification.setStoreName(o.getProductSpecification().getStore().getStoreName());
				ProductForm productForm = new ProductForm();
				productForm.setProductSpecificationForm(productSpecification);
				productForm.setProductCode(o.getProductCode());
				productForm.setProductName(o.getProductSpecification().getProductName());
				productForm.setProductId(o.getId());
				productForm.setProductPrice(o.getProductPrice());
				productForm.setSkuSpecification(mapper.readValue(o.getSkuSpecification(), LinkedHashMap.class));
				productForm.setProductImage(getAsset(o.getProductImage(), EntityContext.PRODUCT_LIST_DETAIL_FLAG_ALL));
				o.getFinancialAttributesList().forEach(p->{
					ProductFinancialAttributesForm productFinancialAttributesForm = new ProductFinancialAttributesForm();
					productFinancialAttributesForm.setForthIncomeItems(p.getForthIncomeItems());
					productFinancialAttributesForm.setId(p.getId());
					productFinancialAttributesForm.setIncomeType(p.getIncomeType());
					productFinancialAttributesForm.setProductType(p.getProductType());
					productFinancialAttributesForm.setSalesType(p.getSalesType());
					productFinancialAttributesForm.setSecIncomeItems(p.getSecIncomeItems());
					productFinancialAttributesForm.setSupplier(p.getSupplier());
					productFinancialAttributesForm.setThirdIncomeItems(p.getThirdIncomeItems());
					productFinancialAttributesForm.setSaleTax(p.getSaleTax());
					productFinancialAttributesForm.setCostPrice(p.getCostPrice());
					productFinancialAttributesForm.setCostTax(p.getCostTax());
					productFinancialAttributesForm.setCostStartTime(null!=p.getCostStartTime() ? sdf.format(p.getCostStartTime()) :"");
					productFinancialAttributesForm.setCostEndTime(null!=p.getCostEndTime() ? sdf.format(p.getCostEndTime()) : "");
					productFinancialAttributesForm.setRevenueShareTax(p.getRevenueShareTax());
					productFinancialAttributesForm.setShareProportion(p.getShareProportion());
					productFinancialAttributesForm.setShareProportionStartTime(null!=p.getShareProportionStartTime() ? sdf.format(p.getShareProportionStartTime()) :"");
					productFinancialAttributesForm.setShareProportionEndTime(null!=p.getShareProportionEndTime() ? sdf.format(p.getShareProportionEndTime()) :"");
					productForm.getProductFinancialAttributesFormList().add(productFinancialAttributesForm);
				});
				res.getProductList().add(productForm);
			}
		} catch (Exception e) {
			res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
}
