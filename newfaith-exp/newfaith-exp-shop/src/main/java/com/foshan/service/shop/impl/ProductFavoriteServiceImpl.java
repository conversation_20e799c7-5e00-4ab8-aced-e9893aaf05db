package com.foshan.service.shop.impl;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.shop.MemberEntity;
import com.foshan.entity.shop.ProductEntity;
import com.foshan.entity.shop.ProductFavoriteEntity;
import com.foshan.entity.shop.ProductSpecificationDetailEntity;
import com.foshan.entity.shop.ProductSpecificationEntity;
import com.foshan.entity.shop.ShopUserEntity;
import com.foshan.entity.shop.StoreEntity;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.shop.ProductForm;
import com.foshan.form.shop.ProductSpecificationForm;
import com.foshan.form.shop.request.GetProductListReq;
import com.foshan.form.shop.request.ProductFavoriteReq;
import com.foshan.form.shop.response.product.GetProductListRes;
import com.foshan.service.shop.IProductFavoriteService;
import com.foshan.util.ShopContextInfo;

@Transactional
@Service("productFavoriteService")
public class ProductFavoriteServiceImpl extends GenericShopService implements IProductFavoriteService {
	private final static Logger logger = LoggerFactory.getLogger(ProductFavoriteServiceImpl.class);
	@Override
	public IResponse getProductFavoriteList(GetProductListReq req) {
	
		GetProductListRes res = new GetProductListRes();

		Object userObj = getPrincipal(false);
		if (null == userObj || !(userObj instanceof MemberEntity)) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;

		} 
		req.setIsFavorite(EntityContext.IS_FAVORITE);
		req.setOrderStr(StringUtils.isNotEmpty(req.getOrderStr())?req.getOrderStr():"createTime|desc");
		HashMap<String, Object> hm =getProductFavoriteList(req, userObj);
		List<ProductEntity> productList = (List<ProductEntity>) hm.get("productList");
		productList.forEach(product -> {
			ProductSpecificationEntity specification = product.getProductSpecification();
			ProductSpecificationDetailEntity detail = specification.getDetail();
			ProductForm productForm = getProductForm(req.getColumnId(), null, specification, product);

			ProductSpecificationForm specificationForm = getProductSpecificationForm(specification,
					Boolean.parseBoolean(shopContextInfo.getMobileAudit()),
					Boolean.parseBoolean(shopContextInfo.getMobileSpecifications()),
					Boolean.parseBoolean(shopContextInfo.getMobileTemplete()));
			if (Boolean.parseBoolean(shopContextInfo.getMobileSku())) {
				try {
					List<LinkedHashMap<String, String>> paraList = getSpecificationSkuParameterList(
							specialTemplateToList(detail.getSpecialTemplate()));
					paraList.forEach(b -> {
						b.keySet().forEach(c -> {
							try {
								ProductForm pf = new ProductForm();
								pf.setSpecificationIndex(c.endsWith("_") ? c.substring(0, c.length() - 1) : c);
								pf.setSpecificationId(specification.getId());
								pf.setImportFlag(EntityContext.PRODUCT_IMPORT_FLAG_NONE);
								specificationForm.getSkuList().add(pf);
							} catch (Exception ex) {
								logger.error("sku列表装载错误" + ex.getMessage());
							}
						});
					});

					// 判断在库状态
					StringBuilder hql = new StringBuilder(
							"select a from ProductEntity a where a.productSpecification.id=" + specification.getId());
					List<ProductForm> productFormList = parseProductFormList(req.getColumnId(), req.getProductGroupId(),
							productDao.getListByHql(hql.toString()), false, false, false, false,null);

					productFormList.forEach(b -> {
						List<ProductForm> resFormList = specificationForm.getSkuList();
						boolean flag = false;
						for (ProductForm c : resFormList) {
							if (c.equals(b)) {
								flag = true;
								resFormList.remove(c);
								break;
							}
						}
						if (flag) {
							b.setImportFlag(EntityContext.PRODUCT_IMPORT_FLAG_IN);
							resFormList.add(b);
						}
					});
				} catch (Exception ex) {
					logger.error("getSkuListBySpecification方法构造sku列表出错！！！" + ex.getMessage());
				}
			} else {
				specificationForm.getSkuList().addAll(parseProductFormList(req.getColumnId(), req.getProductGroupId(),
						specification.getProductList(), false, false, false, false,null));
			}
			productForm.setProductSpecificationForm(specificationForm);
			res.getProductList().add(productForm);
		});

		res.setTotalResult(res.getProductList().size() > 0 ? (Integer) hm.get("totalResult") : 0);
		res.setPageSize((Integer) hm.get("pageSize"));
		res.setCurrentPage((Integer) hm.get("currentPage"));
		res.setTotal((Integer) hm.get("total"));
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	protected HashMap<String, Object> getProductFavoriteList(GetProductListReq req, Object userObj) {
		HashMap<String, Object> hm = new HashMap<String, Object>();
		Page<ProductEntity> page = new Page<ProductEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		MemberEntity shiroMember = null != userObj && userObj instanceof MemberEntity ? (MemberEntity) userObj : null;
		page = productDao.queryPage(page, "select distinct a from ProductEntity a,ProductFavoriteEntity pf inner "
				+ "join a.productSpecification b inner join a.productGroupList d inner join d.columnList e "
				+ "inner join a.memberList m  where m.id="+shiroMember.getId()+" and pf.product.Id=a.Id  order by pf.createTime desc");
		hm.put("productList", page.getResultList());
		hm.put("totalResult", page.getTotalCount());
		hm.put("pageSize", page.getPageSize());
		hm.put("currentPage", page.getCurrentPage());
		hm.put("total", page.getTotalPage());
		return hm;
		
	}

	@Override
	public IResponse addProductFavorite(ProductFavoriteReq req) {
		GenericResponse res = new GenericResponse();
		Object userObj = getPrincipal(false);
		if (null != userObj) {
			MemberEntity member = (MemberEntity) userObj;

			ProductFavoriteEntity productFavorite = productFavoriteDao.getUniqueBySql(
					"SELECT p.* FROM t_product_favorite p WHERE " + " p.memberId=? AND p.productId=?", member.getId(),
					req.getProductId());
			if (null != productFavorite) {
				if (productFavorite.getState() == EntityContext.RECORD_STATE_VALID) {
					res.setRet(ResponseContext.RES_FAVORITE_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_FAVORITE_ERROR_INFO);
					return res;
				}
				productFavorite.setState(EntityContext.RECORD_STATE_VALID);
				productFavoriteDao.update(productFavorite);
				;
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				return res;
			}
			ProductEntity product = productDao.get(req.getProductId());
			// MemberEntity member = memberDao.get(req.getMemberId());
			if (null == product || null == member) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			MemberEntity m = memberDao.get(member.getId());
			productFavorite = new ProductFavoriteEntity(null, m, product);
			productFavorite.setState(EntityContext.RECORD_STATE_VALID);
			productFavoriteDao.save(productFavorite);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
		}
		return res;
	}

	@Override
	public IResponse deletelProductFavorite(ProductFavoriteReq req) {
		GenericResponse res = new GenericResponse();
		Object userObj = getPrincipal(false);
		if (null != userObj) {
			MemberEntity member = (MemberEntity) userObj;
			if (null != req.getProductId()) {
				
				ProductEntity product = productDao.getUniqueByHql("SELECT a FROM ProductEntity a INNER JOIN a.productSpecification "
						+ "b INNER JOIN a.productGroupList d INNER JOIN d.columnList e INNER JOIN a.productGroupList d "
						+ "INNER JOIN a.memberList m  WHERE m.id=? and a.id=?", member.getId(),req.getProductId());
				if(null!=product){
					product.getMemberList().remove(member);
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				}else{
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				}
				
			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
		}
		return res;
	}

}
