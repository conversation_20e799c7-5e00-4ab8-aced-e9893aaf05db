package com.foshan.service.shop.impl;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.entity.shop.LogisticsEntity;
import com.foshan.form.response.IResponse;
import com.foshan.form.shop.request.LogisticsReq;
import com.foshan.form.shop.response.logistics.LogisticsRes;
import com.foshan.service.shop.ILogisticsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.foshan.form.response.context.ResponseContext;

import javax.transaction.Transactional;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;


/**
 * 获取物流信息
 */
@Transactional
@Service("logisticsService")
public class LogisticsServiceImpl extends GenericShopService implements ILogisticsService {
    protected Logger logger = LoggerFactory.getLogger(getClass());


    @Override
    public IResponse getLogisticsInfo(LogisticsReq req) {
        LogisticsEntity logisticsInfo = new LogisticsEntity();
        LogisticsRes res = new LogisticsRes();
        Map<String, Object> json = new HashMap<>();
        ObjectMapper objectMapper = new ObjectMapper();
        LogisticsEntity historyData;
        //查看数据库中有没有记录
        try {
            historyData = logisticsDao.getUniqueByHql(

                    "select a from LogisticsEntity a where a.logisticsNumber=\'" + req.getLogisticsNumber() + "\'"
            );
        } catch (Exception e) {
            historyData = null;
        }
        if (null != historyData) {          //如数据库中有记录
            long cureentTime = System.currentTimeMillis();
            if ((cureentTime - historyData.getLastQueryTime()) / 1440000 < 1 | (cureentTime - historyData.getLastQueryTime()) / 1440000 > 240) {            //查询间隔时长太短从数据库读取数据
                res.setCompany(historyData.getLogisticsCompany());
                res.setNumber(historyData.getLogisticsNumber());
                try {
                    res.setResultList(objectMapper.readValue(historyData.getLogisticsInfo(), ArrayList.class));
                } catch (Exception e) {
                    res.setResultList(new ArrayList());
                    e.getMessage();
                }
                res.setStatus(historyData.getResponseCode());
                res.setProvider(historyData.getLogisticsProvider());
                res.setUpdateTime(historyData.getUpdateTime());
                res.setTakeTime(historyData.getTakeTime());
                res.setIssign(historyData.getIssign());
                res.setDeliverystatus(historyData.getDeliverystatus());
                res.setCourierPhone(historyData.getCourierPhone());
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

                logger.info("从数据库中获取");

                return res;
            } else {            //更新数据后返回
                try {
                    json = getInfo(req);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (json.get("status").equals("0")) {
                    historyData.setLogisticsProvider((String) json.get("provider"));
                    historyData.setLogisticsInfo(json.get("resultList").toString());
                    historyData.setCourier((String) json.get("courier"));
                    historyData.setCourierPhone((String) json.get("courierPhone"));
                    historyData.setDeliverystatus((String) json.get("deliverystatus"));
                    historyData.setIssign((String) json.get("issign").toString());
                    historyData.setTakeTime((String) json.get("takeTime"));
                    historyData.setUpdateTime((String) json.get("updateTime"));
                    logisticsDao.saveOrUpdate(historyData);
                    logger.info("已更新");
                    res.setCompany((String) json.get("company"));
                    res.setNumber(historyData.getLogisticsNumber());
                    try {
                        res.setResultList(objectMapper.readValue(json.get("resultList").toString(), ArrayList.class));
                    } catch (Exception e) {
                        res.setResultList(new ArrayList());
                        e.getMessage();
                    }                         res.setStatus((String) json.get("status"));
                    res.setProvider((String) json.get("provider"));
                    res.setCourier((String) json.get("courier"));
                    res.setCourierPhone((String) json.get("courierPhone"));
                    res.setDeliverystatus((String) json.get("deliverystatus"));
                    res.setIssign((String) json.get("issign").toString());
                    res.setTakeTime((String) json.get("takeTime"));
                    res.setUpdateTime((String) json.get("updateTime"));
                    res.setRet(ResponseContext.RES_SUCCESS_CODE);
                    res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
                    logger.info("获取新信息");
                    return res;
                } else {
                    res.setCompany((String) json.get("company"));
                    res.setNumber(historyData.getLogisticsNumber());
                    try {
                        res.setResultList(objectMapper.readValue(json.get("resultList").toString(), ArrayList.class));
                    } catch (Exception e) {
                        res.setResultList(new ArrayList());
                        e.getMessage();
                    }                         res.setStatus((String) json.get("status"));
                    res.setProvider((String) json.get("provider"));
                    res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                    res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
                    logger.info("查询错误");
                    return res;
                }
            }
        } else {            //数据库无信息，新建查询
            try {
                json = getInfo(req);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (!json.get("status").equals("0")) {
                res.setCompany(req.getLogisticsCompany());
                res.setNumber(req.getLogisticsNumber());
                try {
                    res.setResultList((ArrayList)json.get("resultList"));
                } catch (Exception e) {
                    res.setResultList(new ArrayList());
                    e.getMessage();
                }
                res.setStatus((String) json.get("status"));
                res.setRet(ResponseContext.RES_GET_LOGISTICS_INFO_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_GET_LOGISTICS_INFO_ERROR_INFO);
                logger.info("数据库无信息，查询无信息");
                return res;
            }

            //保存到数据库里
            logisticsInfo.setLogisticsCompany((String) json.get("company"));
            logisticsInfo.setLogisticsNumber((String) json.get("number"));
            logisticsInfo.setLogisticsProvider((String) json.get("provider"));
            logisticsInfo.setLogisticsInfo(json.get("resultList").toString());
            logisticsInfo.setResponseCode((String) json.get("status"));
            logisticsInfo.setLastQueryTime(System.currentTimeMillis());

            logisticsInfo.setCourier((String) json.get("courier"));
            logisticsInfo.setCourierPhone((String) json.get("courierPhone"));
            logisticsInfo.setDeliverystatus(String.valueOf(json.get("deliverystatus")));
            logisticsInfo.setIssign(String.valueOf(json.get("issign")));
            logisticsInfo.setTakeTime((String) json.get("takeTime"));
            logisticsInfo.setUpdateTime((String) json.get("updateTime"));
            logisticsDao.save(logisticsInfo);
            logger.info("数据已存入数据库");
            //获取信息后返回
            res.setCompany((String) json.get("company"));
            res.setNumber((String) json.get("number"));
            try {
                res.setResultList(objectMapper.readValue(json.get("resultList").toString(), ArrayList.class));
            } catch (Exception e) {
                res.setResultList(new ArrayList());
                e.getMessage();
            }
            res.setStatus((String) json.get("status"));
            res.setProvider((String) json.get("provider"));
            res.setCourier((String) json.get("courier"));
            res.setCourierPhone((String) json.get("courierPhone"));
            res.setDeliverystatus((String) json.get("deliverystatus"));
            res.setIssign((String) json.get("issign").toString());
            res.setTakeTime((String) json.get("takeTime"));
            res.setUpdateTime((String) json.get("updateTime"));
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        }
        return res;
    }


    /**
     * 从服务提供商获取物流信息
     *
     * @param req
     * @return res
     */
    private Map<String, Object> getInfo(LogisticsReq req) {
//        if ("juhe".equals(req.getProvider())) {   //获取聚合接口快递信息
//            return new JuheService().getLogisticsByJuhe(req);
//        } else if ("kuaidi100".equals(req.getProvider())) {    //获取快递100接口快递信息
//            return new Kuaidi100Service().getLogisticsByKuaidi100(req);
//        } else if ("kuaidiniao".equals(req.getProvider())) {    //获取快递鸟接口快递信息
//            return new KuaidiniaoService().getOrderTracesByJson(req);
//        } else if ("ali".equals(req.getProvider())) {    //获取快递鸟接口快递信息
//            return new AliyunService().getLogisticsByAliyun(req);
//        } else {
        return new AliyunService().getLogisticsByAliyun(req);   //无匹配则用ali获取快递信息
//        }
    }

    class AliyunService {
        Map<String, Object> getLogisticsByAliyun(LogisticsReq req) {
            Map<String, Object> res = new HashMap<>();
            ObjectMapper objectMapper = new ObjectMapper();
            String no = req.getLogisticsNumber().trim();
            String type = null;
            if (null != req.getLogisticsCompany()){
            type = getLogisticsCompany(req.getLogisticsCompany());
            type = null != type  ?  type : "";
            }
            if (type.contains("SF")) {
                String receNumber;
                if (null != req.getReceivePhone() && !req.getReceivePhone().isEmpty()) {
                    receNumber = req.getReceivePhone();
                } else {
                    receNumber = "0000";
                }
                no = no + ":" + receNumber.substring(receNumber.length() - 4);
            }
            System.out.println(type);
            String urlstring = "https://wuliu.market.alicloudapi.com/kdi?no=" + no + "&type=" + type;
            try {
                URL url = new URL(urlstring);
                HttpURLConnection urlCon = (HttpURLConnection) url.openConnection();
                urlCon.setRequestProperty("Authorization", "APPCODE 23fc56fe7ee5491a8d8d088125dae78e");
                InputStream response = urlCon.getInputStream();
                if (200 == urlCon.getResponseCode()) {
                    StringBuffer sbf = new StringBuffer();
                    BufferedReader reader = new BufferedReader(new InputStreamReader(response, "UTF-8"));
                    String strRead;
                    while ((strRead = reader.readLine()) != null) {
                        sbf.append(strRead);
                        sbf.append("\r\n");
                    }
                    reader.close();
                    String result = sbf.toString();
                    try {
                        Map<String, Object> object = objectMapper.readValue(result, Map.class);
                        if (String.valueOf(object.get("status")).equals("0")) {
                            Map<String, String> data = (Map<String, String>) object.get("result");
                            res.put("deliverystatus", data.get("deliverystatus"));
                            res.put("issign", data.get("issign"));
                            res.put("courier", data.get("courier"));
                            res.put("courierPhone", data.get("courierPhone"));
                            res.put("updateTime", data.get("updateTime"));
                            res.put("takeTime", data.get("takeTime"));
                            res.put("status", String.valueOf(object.get("status")));
                            res.put("provider", "ali");

                            res.put("number", data.get("number"));
                            res.put("company", data.get("expName"));
                            //格式化物流信息
                            Map<String, ArrayList<Map<String, String>>> temp = (Map<String, ArrayList<Map<String, String>>>) object.get("result");
                            ArrayList<Map<String, String>> resList = temp.get("list");
                            for (Map o : resList) {
                                o.put("remark", o.remove("status"));
                            }
                            Collections.reverse(resList);
                            res.put("resultList", objectMapper.writeValueAsString(resList));
                            return res;
                        } else {
                            res.put("provider", "ali");
                            res.put("number", req.getLogisticsNumber());
                            res.put("company", req.getLogisticsCompany());
                            res.put("status", String.valueOf(object.get("status")));
                            res.put("resultList", objectMapper.readValue("[{\"result\":\"" + object.get("msg") + "\"}]", ArrayList.class));
                            return res;
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    throw new Exception("连接失败");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            res.put("provider", "ali");
            res.put("number", req.getLogisticsNumber());
            res.put("company", req.getLogisticsCompany());
            res.put("status", "1128");
            try {
                res.put("resultList", objectMapper.readValue("[{\"result\":\"暂无物流信息\"}]", ArrayList.class));
            } catch (Exception e) {
                e.getMessage();
                res.put("resultList", "[{\"result\":\"" + e.getMessage() + "\"}]");
            }
            return res;
        }

        String getLogisticsCompany(String comp){
            ObjectMapper objectMapper = new ObjectMapper();
                String type = null;
            try {
                String company = "{\"AAE\":\"AAEWEB\",\"ACS雅仕快递\":\"ACS\",\"安达速递\":\"ADAPOST\",\"澳多多国际速递\":\"ADODOXOM\",\"ADP Express Tracking\":\"ADP\",\"安捷物流\":\"AJWL\",\"亚马逊\":\"AMAZON\",\"安能\":\"ANE\",\"安能快递\":\"ANEEX\",\"安圭拉邮政\":\"ANGUILAYOU\",\"安捷快递\":\"ANJELEX\",\"ANTS\":\"ANTS\",\"安信达\":\"ANXINDA\",\"安迅物流\":\"ANXL\",\"澳通速递\":\"AOL\",\"澳门邮政\":\"AOMENYZ\",\"澳天速运\":\"AOTSD\",\"APAC\":\"APAC\",\"全一\":\"APEX\",\"Aplus物流\":\"APLUSEX\",\"Aramex\":\"ARAMEX\",\"安世通快递\":\"ASTEXPRESS\",\"奥地利邮政\":\"AT\",\"澳邮中国快运\":\"AUEXPRESS\",\"澳世速递\":\"AUSEXPRESS\",\"Australia Post Tracking\":\"AUSTRALIA\",\"澳邮专线(澳邮中国快运)\":\"AYCA\",\"BCWELT\":\"BCWELT\",\"八达通\":\"BDT\",\"比利时邮政\":\"BEL\",\"八方安运\":\"BFAY\",\"贝海国际\":\"BHGJ\",\"BHT快递\":\"BHT\",\"秘鲁邮政\":\"BILUYOUZHE\",\"黑狗\":\"BLACKDOG\",\"蓝天\":\"BLUESKY\",\"笨鸟国际\":\"BN\",\"北青小红帽\":\"BQXHM\",\"巴西邮政\":\"BR\",\"亚风\":\"BROADASIA\",\"百世快运\":\"BSKY\",\"不丹邮政\":\"BUDANYOUZH\",\"加拿大邮政\":\"CA\",\"民航\":\"CAE\",\"CCES快递\":\"CCES\",\"成都善途速运\":\"CDSTKY\",\"邮政包裹\":\"CHINAPOST\",\"秦远物流\":\"CHINZ56\",\"城市100\":\"CITY100\",\"长江国际快递\":\"CJGJ\",\"城际快递\":\"CJKD\",\"中邮\":\"CNPL\",\"中国东方\":\"COE\",\"中铁快运\":\"CRE\",\"长沙创一\":\"CSCY\",\"联合运通\":\"CTG\",\"递四方速递\":\"D4PX\",\"丹鸟快递\":\"DANNIAO\",\"德邦\":\"DEPPON\",\"D速\":\"DEXP\",\"DHL国内件\":\"DHL\",\"DHL国际件\":\"DHL_EN\",\"东骏快捷\":\"DJ56\",\"丹麦邮政\":\"DK\",\"DPD\":\"DPD\",\"DPEX\":\"DPEX\",\"大田\":\"DTW\",\"百福东方\":\"EES\",\"平安快递\":\"EFSPOST\",\"EMS\":\"EMS\",\"EShipper\":\"ESHIPPER\",\"易通达\":\"ETD\",\"欧亚专线\":\"EUASIA\",\"EWE\":\"EWE\",\"万象\":\"EWINSHINE\",\"安鲜达\":\"EXFRESH\",\"快捷\":\"FASTEXPRESS\",\"速派快递\":\"FASTGO\",\"联邦快递\":\"FEDEX\",\"FedEx国际\":\"FEDEXIN\",\"联邦快递国际\":\"FEDEX_GJ\",\"飞康达\":\"FKD\",\"程光\":\"FLYWAYEX\",\"富腾达\":\"FTD\",\"飞洋\":\"GCE\",\"冠达\":\"GD\",\"广东邮政\":\"GDEMS\",\"国际e邮宝\":\"GJEYB\",\"GLS\":\"GLS\",\"共速达\":\"GSD\",\"国通\":\"GTO\",\"高铁速递\":\"GTSD\",\"文捷航空\":\"GZWENJIE\",\"河马动力\":\"HEMA\",\"恒路\":\"HENGLU\",\"汇丰物流\":\"HFWL\",\"天地华宇\":\"HOAU\",\"鸿桥供应链\":\"HOTSCM\",\"海派通物流公司\":\"HPTEX\",\"华企\":\"HQKY\",\"环球速运\":\"HQSY\",\"锦程快递\":\"HREX\",\"百世快递\":\"HTKY\",\"汇森快运\":\"HUISEN\",\"华夏龙物流\":\"HXLWL\",\"豪翔物流\":\"HXWL\",\"鸿远\":\"HYE\",\"好来运快递\":\"HYLSD\",\"安的列斯群岛邮政\":\"IADLSQDYZ\",\"澳大利亚邮政\":\"IADLYYZ\",\"阿尔巴尼亚邮政\":\"IAEBNYYZ\",\"阿尔及利亚邮政\":\"IAEJLYYZ\",\"阿富汗邮政\":\"IAFHYZ\",\"安哥拉邮政\":\"IAGLYZ\",\"阿根廷邮政\":\"IAGTYZ\",\"埃及邮政\":\"IAJYZ\",\"阿鲁巴邮政\":\"IALBYZ\",\"奥兰群岛邮政\":\"IALQDYZ\",\"阿联酋邮政\":\"IALYYZ\",\"阿曼邮政\":\"IAMYZ\",\"阿塞拜疆邮政\":\"IASBJYZ\",\"埃塞俄比亚邮政\":\"IASEBYYZ\",\"爱沙尼亚邮政\":\"IASNYYZ\",\"阿森松岛邮政\":\"IASSDYZ\",\"博茨瓦纳邮政\":\"IBCWNYZ\",\"波多黎各邮政\":\"IBDLGYZ\",\"冰岛邮政\":\"IBDYZ\",\"白俄罗斯邮政\":\"IBELSYZ\",\"波黑邮政\":\"IBHYZ\",\"保加利亚邮政\":\"IBJLYYZ\",\"巴基斯坦邮政\":\"IBJSTYZ\",\"黎巴嫩邮政\":\"IBLNYZ\",\"便利速递\":\"IBLSD\",\"玻利维亚邮政\":\"IBLWYYZ\",\"巴林邮政\":\"IBLYZ\",\"百慕达邮政\":\"IBMDYZ\",\"波兰邮政\":\"IBOLYZ\",\"宝通达\":\"IBTD\",\"爱拜物流\":\"IBUY8\",\"贝邮宝\":\"IBYB\",\"出口易\":\"ICKY\",\"达方物流\":\"IDFWL\",\"德国邮政\":\"IDGYZ\",\"爱尔兰邮政\":\"IE\",\"厄瓜多尔邮政\":\"IEGDEYZ\",\"俄罗斯邮政\":\"IELSYZ\",\"厄立特里亚邮政\":\"IELTLYYZ\",\"飞特物流\":\"IFTWL\",\"瓜德罗普岛EMS\":\"IGDLPDEMS\",\"瓜德罗普岛邮政\":\"IGDLPDYZ\",\"俄速递\":\"IGJESD\",\"哥伦比亚邮政\":\"IGLBYYZ\",\"格陵兰邮政\":\"IGLLYZ\",\"哥斯达黎加邮政\":\"IGSDLJYZ\",\"韩国邮政\":\"IHGYZ\",\"华翰物流\":\"IHHWL\",\"互联易\":\"IHLY\",\"哈萨克斯坦邮政\":\"IHSKSTYZ\",\"黑山邮政\":\"IHSYZ\",\"津巴布韦邮政\":\"IJBBWYZ\",\"吉尔吉斯斯坦邮政\":\"IJEJSSTYZ\",\"捷克邮政\":\"IJKYZ\",\"加纳邮政\":\"IJNYZ\",\"柬埔寨邮政\":\"IJPZYZ\",\"克罗地亚邮政\":\"IKNDYYZ\",\"肯尼亚邮政\":\"IKNYYZ\",\"科特迪瓦EMS\":\"IKTDWEMS\",\"科特迪瓦邮政\":\"IKTDWYZ\",\"卡塔尔邮政\":\"IKTEYZ\",\"利比亚邮政\":\"ILBYYZ\",\"林克快递\":\"ILKKD\",\"罗马尼亚邮政\":\"ILMNYYZ\",\"卢森堡邮政\":\"ILSBYZ\",\"拉脱维亚邮政\":\"ILTWYYZ\",\"立陶宛邮政\":\"ILTWYZ\",\"列支敦士登邮政\":\"ILZDSDYZ\",\"马尔代夫邮政\":\"IMEDFYZ\",\"摩尔多瓦邮政\":\"IMEDWYZ\",\"马耳他邮政\":\"IMETYZ\",\"孟加拉国EMS\":\"IMJLGEMS\",\"摩洛哥邮政\":\"IMLGYZ\",\"毛里求斯邮政\":\"IMLQSYZ\",\"马来西亚EMS\":\"IMLXYEMS\",\"马来西亚邮政\":\"IMLXYYZ\",\"马其顿邮政\":\"IMQDYZ\",\"马提尼克EMS\":\"IMTNKEMS\",\"马提尼克邮政\":\"IMTNKYZ\",\"墨西哥邮政\":\"IMXGYZ\",\"南非邮政\":\"INFYZ\",\"尼日利亚邮政\":\"INRLYYZ\",\"邮政国际包裹\":\"INTMAIL\",\"挪威邮政\":\"INWYZ\",\"葡萄牙邮政\":\"IPTYYZ\",\"全球快递\":\"IQQKD\",\"全通物流\":\"IQTWL\",\"苏丹邮政\":\"ISDYZ\",\"萨尔瓦多邮政\":\"ISEWDYZ\",\"塞尔维亚邮政\":\"ISEWYYZ\",\"斯洛伐克邮政\":\"ISLFKYZ\",\"斯洛文尼亚邮政\":\"ISLWNYYZ\",\"塞内加尔邮政\":\"ISNJEYZ\",\"塞浦路斯邮政\":\"ISPLSYZ\",\"沙特阿拉伯邮政\":\"ISTALBYZ\",\"土耳其邮政\":\"ITEQYZ\",\"泰国邮政\":\"ITGYZ\",\"特立尼达和多巴哥EMS\":\"ITLNDHDBGE\",\"突尼斯邮政\":\"ITNSYZ\",\"坦桑尼亚邮政\":\"ITSNYYZ\",\"危地马拉邮政\":\"IWDMLYZ\",\"乌干达邮政\":\"IWGDYZ\",\"乌克兰EMS\":\"IWKLEMS\",\"乌克兰邮政\":\"IWKLYZ\",\"乌拉圭邮政\":\"IWLGYZ\",\"文莱邮政\":\"IWLYZ\",\"乌兹别克斯坦EMS\":\"IWZBKSTEMS\",\"乌兹别克斯坦邮政\":\"IWZBKSTYZ\",\"西班牙邮政\":\"IXBYYZ\",\"小飞龙物流\":\"IXFLWL\",\"新喀里多尼亚邮政\":\"IXGLDNYYZ\",\"新加坡EMS\":\"IXJPEMS\",\"新加坡邮政\":\"IXJPYZ\",\"叙利亚邮政\":\"IXLYYZ\",\"希腊邮政\":\"IXLYZ\",\"夏浦世纪\":\"IXPSJ\",\"夏浦物流\":\"IXPWL\",\"新西兰邮政\":\"IXXLYZ\",\"匈牙利邮政\":\"IXYLYZ\",\"意大利邮政\":\"IYDLYZ\",\"印度尼西亚邮政\":\"IYDNXYYZ\",\"印度邮政\":\"IYDYZ\",\"英国邮政\":\"IYGYZ\",\"伊朗邮政\":\"IYLYZ\",\"亚美尼亚邮政\":\"IYMNYYZ\",\"也门邮政\":\"IYMYZ\",\"越南邮政\":\"IYNYZ\",\"以色列邮政\":\"IYSLYZ\",\"易通关\":\"IYTG\",\"燕文物流\":\"IYWWL\",\"直布罗陀邮政\":\"IZBLTYZ\",\"智利邮政\":\"IZLYZ\",\"捷安达\":\"JAD\",\"京东\":\"JD\",\"景光物流\":\"JGWL\",\"佳吉\":\"JIAJI\",\"佳怡\":\"JIAYI\",\"极兔速递\":\"JITU\",\"九曳\":\"JIUYESCM\",\"急先达\":\"JOUST\",\"日本邮政\":\"JP\",\"捷特快递\":\"JTKD\",\"嘉里物流\":\"KERRY\",\"京广\":\"KKE\",\"跨越速运\":\"KYEXPRESS\",\"龙邦\":\"LBEX\",\"联昊通\":\"LTS\",\"澳洲迈速快递\":\"MAXEEDEXPRESS\",\"民邦快递\":\"MB\",\"美快\":\"MK\",\"明亮物流\":\"MLWL\",\"能达\":\"ND56\",\"南方\":\"NF\",\"荷兰邮政\":\"NL\",\"ONTRAC\":\"ONTRAC\",\"昂威物流\":\"ONWAY\",\"平安达腾飞快递\":\"PADTF\",\"泛捷快递\":\"PANEX\",\"PCA\":\"PCA\",\"配思航宇\":\"PEISI\",\"晋越\":\"PEWKEE\",\"凤凰\":\"PHOENIXEXP\",\"品骏快递\":\"PJKD\",\"全晨\":\"QCKD\",\"易达通\":\"QEXPRESS\",\"全峰\":\"QFKD\",\"全球邮政\":\"QQYZ\",\"全日通快递\":\"QRT\",\"全信通\":\"QXT\",\"瑞典邮政\":\"RDSE\",\"如风达\":\"RFD\",\"瑞丰速递\":\"RFEX\",\"日日顺物流\":\"RRS\",\"赛澳递\":\"SAD\",\"圣安物流\":\"SAWL\",\"盛邦物流\":\"SBWL\",\"顺达快递\":\"SDEX\",\"速递e站\":\"SDEZ\",\"上大物流\":\"SDWL\",\"三态速递\":\"SFC\",\"顺丰\":\"SFEXPRESS\",\"盛丰\":\"SFWL\",\"盛辉\":\"SHENGHUI\",\"速通物流\":\"ST\",\"申通\":\"STO\",\"速腾快递\":\"STWL\",\"速必达物流\":\"SUBIDA\",\"苏宁\":\"SUNING\",\"速尔\":\"SURE\",\"瑞士邮政\":\"SWCH\",\"顺心捷达\":\"SXJE\",\"台湾邮政\":\"TAIWANYZ\",\"加运美\":\"TMS\",\"TNT\":\"TNT\",\"天天快递\":\"TTKDEX\",\"优速\":\"UC56\",\"UEQ Express\":\"UEQ\",\"UEX\":\"UEX\",\"UPS\":\"UPS\",\"USPS美国邮政\":\"USPS\",\"万庚\":\"VANGEN\",\"万家物流\":\"WANJIA\",\"万家康\":\"WJK\",\"新邦\":\"XBWL\",\"迅驰物流\":\"XCWL\",\"信丰\":\"XFEXPRESS\",\"新杰物流\":\"XJ\",\"希优特\":\"XYT\",\"源安达\":\"YADEX\",\"日本大和运输(Yamato)\":\"YAMA\",\"远成\":\"YCGWL\",\"义达国际物流\":\"YDH\",\"越丰\":\"YFEXPRESS\",\"原飞航\":\"YFHEX\",\"宜送\":\"YIEXPRESS\",\"壹米滴答\":\"YIMIDIDA\",\"YODEL\":\"YODEL\",\"运通\":\"YTEXPRESS\",\"圆通\":\"YTO\",\"约旦邮政\":\"YUEDANYOUZ\",\"韵达快递\":\"YUNDA\",\"韵达快运\":\"YUNDA56\",\"运东西\":\"YUNDX\",\"亿翔快递\":\"YXKD\",\"宇鑫物流\":\"YXWL\",\"增益快递\":\"ZENY\",\"汇强快递\":\"ZHQKD\",\"宅急送\":\"ZJS\",\"芝麻开门\":\"ZMKMEX\",\"众通快递\":\"ZTE\",\"中铁物流\":\"ZTKY\",\"中通\":\"ZTO\",\"中通快运\":\"ZTO56\",\"众邮快递\":\"ZYKD\",\"中邮物流\":\"ZYWL\",\"爱购转运\":\"ZY_AG\",\"爱欧洲\":\"ZY_AOZ\",\"AXO\":\"ZY_AXO\",\"澳转运\":\"ZY_AZY\",\"八达网\":\"ZY_BDA\",\"蜜蜂速递\":\"ZY_BEE\",\"贝海速递\":\"ZY_BH\",\"百利快递\":\"ZY_BL\",\"斑马物流\":\"ZY_BM\",\"败欧洲\":\"ZY_BOZ\",\"百通物流\":\"ZY_BT\",\"贝易购\":\"ZY_BYECO\",\"策马转运\":\"ZY_CM\",\"赤兔马转运\":\"ZY_CTM\",\"CUL中美速递\":\"ZY_CUL\",\"德国海淘之家\":\"ZY_DGHT\",\"德运网\":\"ZY_DYW\",\"EFS POST\":\"ZY_EFS\",\"宜送转运\":\"ZY_ESONG\",\"ETD\":\"ZY_ETD\",\"飞碟快递\":\"ZY_FD\",\"飞鸽快递\":\"ZY_FG\",\"风雷速递\":\"ZY_FLSD\",\"风行快递\":\"ZY_FX\",\"皓晨快递\":\"ZY_HC\",\"皓晨优递\":\"ZY_HCYD\",\"海带宝\":\"ZY_HDB\",\"汇丰美中速递\":\"ZY_HFMZ\",\"豪杰速递\":\"ZY_HJSD\",\"360hitao转运\":\"ZY_HTAO\",\"海淘村\":\"ZY_HTCUN\",\"365海淘客\":\"ZY_HTKE\",\"华通快运\":\"ZY_HTONG\",\"海星桥快递\":\"ZY_HXKD\",\"华兴速运\":\"ZY_HXSY\",\"海悦速递\":\"ZY_HYSD\",\"君安快递\":\"ZY_JA\",\"时代转运\":\"ZY_JD\",\"骏达快递\":\"ZY_JDKD\",\"骏达转运\":\"ZY_JDZY\",\"久禾快递\":\"ZY_JH\",\"金海淘\":\"ZY_JHT\",\"联邦转运FedRoad\":\"ZY_LBZY\",\"领跑者快递\":\"ZY_LPZ\",\"龙象快递\":\"ZY_LX\",\"量子物流\":\"ZY_LZWL\",\"明邦转运\":\"ZY_MBZY\",\"美国转运\":\"ZY_MGZY\",\"美嘉快递\":\"ZY_MJ\",\"美速通\":\"ZY_MST\",\"美西转运\":\"ZY_MXZY\",\"168 美中快递\":\"ZY_MZ\",\"欧e捷\":\"ZY_OEJ\",\"欧洲疯\":\"ZY_OZF\",\"欧洲GO\":\"ZY_OZGO\",\"全美通\":\"ZY_QMT\",\"QQ-EX\":\"ZY_QQEX\",\"润东国际快线\":\"ZY_RDGJ\",\"瑞天快递\":\"ZY_RT\",\"瑞天速递\":\"ZY_RTSD\",\"SCS国际物流\":\"ZY_SCS\",\"速达快递\":\"ZY_SDKD\",\"四方转运\":\"ZY_SFZY\",\"SOHO苏豪国际\":\"ZY_SOHO\",\"Sonic-Ex速递\":\"ZY_SONIC\",\"上腾快递\":\"ZY_ST\",\"通诚美中快递\":\"ZY_TCM\",\"天际快递\":\"ZY_TJ\",\"天马转运\":\"ZY_TM\",\"滕牛快递\":\"ZY_TN\",\"TrakPak\":\"ZY_TPAK\",\"太平洋快递\":\"ZY_TPY\",\"唐三藏转运\":\"ZY_TSZ\",\"天天海淘\":\"ZY_TTHT\",\"TWC转运世界\":\"ZY_TWC\",\"同心快递\":\"ZY_TX\",\"天翼快递\":\"ZY_TY\",\"同舟快递\":\"ZY_TZH\",\"UCS合众快递\":\"ZY_UCS\",\"文达国际DCS\":\"ZY_WDCS\",\"星辰快递\":\"ZY_XC\",\"迅达快递\":\"ZY_XDKD\",\"信达速运\":\"ZY_XDSY\",\"先锋快递\":\"ZY_XF\",\"新干线快递\":\"ZY_XGX\",\"西邮寄\":\"ZY_XIYJ\",\"信捷转运\":\"ZY_XJ\",\"优购快递\":\"ZY_YGKD\",\"友家速递(UCS)\":\"ZY_YJSD\",\"云畔网\":\"ZY_YPW\",\"云骑快递\":\"ZY_YQ\",\"一柒物流\":\"ZY_YQWL\",\"优晟速递\":\"ZY_YSSD\",\"易送网\":\"ZY_YSW\",\"运淘美国\":\"ZY_YTUSA\",\"至诚速递\":\"ZY_ZCSD\",\"华强物流\":\"hq568\"}";
                Map<String, String> data = objectMapper.readValue(company,Map.class);
                if (null != data.get(comp)){
                    type = data.get(comp);
                    return type;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }return type;
    }


//    class JuheService {
//
//        //配置KEY
//        final String APPKEY = "ba7eccf8b384e69c3b680eec4f6abdf5";
//
//        //2.快递公司编号对照表
//        JSONArray getRequest2() {
//            String result;
//            String url = "http://v.juhe.cn/exp/com";//请求接口地址
//            Map<String, String> params = new HashMap<String, String>();//请求参数
//            params.put("key", APPKEY);//应用APPKEY(应用详细页查询)
//
//            try {
//                result = GetResponseUtil.net(url, params, "GET");
//                JSONObject object = JSONObject.parseObject(result);
//                if (object.getInteger("error_code") == 0) {
//                    return object.getJSONArray("result");
//                } else {
//                    System.out.println(object.get("error_code") + ":" + object.get("reason"));
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            return null;
//        }
//
//        //1.快递查询API
//        JSONObject getLogisticsByJuhe(LogisticsReq req) {
//            String result;
//            String url = "http://v.juhe.cn/exp/index";//请求接口地址
//            Map<String, String> params = new HashMap<>();//请求参数
////        params.put("senderPhone", req.getSenderPhone().substring(req.getSenderPhone().length() - 4, req.getSenderPhone().length()));//发件人手机后四位
//            params.put("receivePhone", req.getReceivePhone().substring(req.getReceivePhone().length() - 4));//收件人手机后四位
//
//            String com = "";//根据快递公司列表获取快递公司编号
//            JSONArray comList = JSONArray.parseArray(getRequest2().toString());
//            for (int i = 0; i < comList.size(); i++) {
//                JSONObject jo = comList.getJSONObject(i);
//                if (jo.getString("com").equals(req.getLogisticsCompany())) {
//                    com = jo.getString("no");
//                }
//            }
//            params.put("com", com);//需要查询的快递公司编号
//            params.put("no", req.getLogisticsNumber());//需要查询的订单号
//            params.put("key", APPKEY);//应用APPKEY(应用详细页查询)
//            params.put("dtype", "");//返回数据的格式,xml或json，默认json
//            JSONObject res = new JSONObject();
//
//            try {
//                result = GetResponseUtil.net(url, params, "GET");
//                JSONObject object = JSONObject.parseObject(result);
//                if (object.getInteger("error_code") == 0) {
//                    object = object.getJSONObject("result");
//                    res.put("status", "200");
//                    res.put("provider", "juhe");
//                    res.put("number", object.getString("no"));
//                    res.put("company", object.getString("com"));
//
//                    //格式化物流信息
//                    JSONArray resList = object.getJSONArray("list");
//                    for (Object aResList : resList) {
//                        JSONObject jsonParamData = (JSONObject) aResList;
//                        jsonParamData.remove("zone");
//                        jsonParamData.put("time", jsonParamData.getString("datetime"));
//                        jsonParamData.remove("datetime");
//                    }
//                    res.put("resultList", resList);
//                    return res;
//                } else {
//                    System.out.println(object.get("error_code") + ":" + object.get("reason"));
//                    res.put("provider", "juhe");
//                    res.put("number", req.getLogisticsNumber());
//                    res.put("company", req.getLogisticsCompany());
//                    res.put("status", object.get("error_code"));
//                    res.put("resultList", JSONArray.parseArray("[{\"result\":\"" + object.get("reason") + "\"}]"));
//                    return res;
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            res.put("provider", "juhe");
//            res.put("number", req.getLogisticsNumber());
//            res.put("company", req.getLogisticsCompany());
//            res.put("status", "0");
//            res.put("resultList", JSONArray.parseArray("[{\"result\":\"暂无物流信息\"}]"));
//            return res;
//        }
//
//    }

//    class Kuaidi100Service {
//        char[] HEX_DIGITS = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'}; //HEX_DIGITS为16进制字符，用于MD5
//
//        /**
//         * 获取物流信息
//         *
//         * @param req
//         * @return
//         */
//        JSONObject getLogisticsByKuaidi100(LogisticsReq req) {
//            String url = "http://poll.kuaidi100.com/poll/query.do";         //接口地址
//            String param = "{\"com\":\"" + PinYinUtil.parseSimplePYFull(req.getLogisticsCompany()) + "\",\"num\":\"" + req.getLogisticsNumber() + "\"}";         //接口参数
//            String customer = "6D732223A0753201EDFA91DF72F3C447";           //客户标识，官网获取
//            String key = "PUgwhyBX558";         //key，官网获取
//            String sign = new String();
//            try {
//                byte[] btInput = (param + key + customer).getBytes();
//                // 获得MD5摘要算法的 MessageDigest 对象
//                MessageDigest mdInst = MessageDigest.getInstance("MD5");
//                // 使用指定的字节更新摘要
//                mdInst.update(btInput);
//                // 获得密文
//                byte[] md = mdInst.digest();
//                // 把密文转换成十六进制的字符串形式，一定要换成大写英文字符，坑
//                int j = md.length;
//                char str[] = new char[j * 2];
//                int k = 0;
//                for (int i = 0; i < j; i++) {
//                    byte byte0 = md[i];
//                    str[k++] = HEX_DIGITS[byte0 >>> 4 & 0xf];
//                    str[k++] = HEX_DIGITS[byte0 & 0xf];
//                }
//                sign = new String(str);
//            } catch (Exception e) {
//                e.printStackTrace();
//                return null;
//            }
//            HashMap<String, String> params = new HashMap<String, String>();
//            //把参数丢到参数map里
//            params.put("param", param);
//            params.put("sign", sign);
//            params.put("customer", customer);
//            params.put("phone", req.getReceivePhone());
//            JSONObject res = new JSONObject();
//            try {
//                String logisticsInfo = GetResponseUtil.net(url, params, "GET");
//                JSONObject object = JSONObject.parseObject(logisticsInfo);
//                if ("ok".equals(object.getString("message"))) {
//                    res.put("status", "200");
//                    res.put("provider", "kuaidi100");
//                    res.put("number", object.getString("nu"));
//                    res.put("company", object.getString("com"));
//
//                    //格式化物流信息
//                    JSONArray resList = object.getJSONArray("data");
//                    resList.stream().map(o -> (JSONObject) o).forEach(jsonParamData -> {
//                        jsonParamData.remove("ftime");
//                        jsonParamData.put("remark", jsonParamData.getString("context"));
//                        jsonParamData.remove("context");
//                    });
//
//                    res.put("resultList", resList);
//                } else {
//                    res.put("provider", "kuaidi100");
//                    res.put("number", req.getLogisticsNumber());
//                    res.put("company", req.getLogisticsCompany());
//                    res.put("status", object.get("returnCode"));
//                    res.put("resultList", JSONArray.parseArray("[{\"result\":\"" + object.getString("message") + "\"}]"));
//                    return res;
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            return res;
//        }
//
//    }

//    class KuaidiniaoService {
//        LogisticsUtil logisticsUtil = new LogisticsUtil();
//
//        //电商ID
//        private String EBusinessID = "1454827";
//        //电商加密私钥
//        private String AppKey = "d9e029fd-07a0-4b8f-9db1-d3fc39546360";
//        //请求url
//        private String ReqURL = "http://api.kdniao.com/Ebusiness/EbusinessOrderHandle.aspx";

    /**
     * Json方式 查询订单物流轨迹
     *
     * @throws Exception
     */
//        public String[] getShipperByLogisticCode(String logisticCode) throws Exception {
//
//            String requestData = "{'LogisticCode':'" + logisticCode + "'}";
//
//            Map<String, String> params = new HashMap<String, String>();
//            params.put("RequestData", logisticsUtil.urlEncoder(requestData, "UTF-8"));
//            params.put("EBusinessID", EBusinessID);
//            params.put("RequestType", "2002");
//            String dataSign = logisticsUtil.encrypt(requestData, AppKey, "UTF-8");
//            params.put("DataSign", logisticsUtil.urlEncoder(dataSign, "UTF-8"));
//            params.put("DataType", "2");
//            try {
//                String result = logisticsUtil.sendPost(ReqURL, params);
//                JSONObject json = JSONObject.parseObject(result);
//                if (json.getJSONArray("Shippers").size() != 0) {
//                    JSONArray shippers = json.getJSONArray("Shippers");
//                    String shipperss = "";
//                    for (int i = 0; i < shippers.size(); i++) {
//                        String shipperCode = shippers.getJSONObject(i).getString("ShipperCode");
//                        shipperss += shipperCode + "-";
//                    }
//                    return shipperss.split("-");
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            String[] ship = new String[1];
//            return ship;
//        }


//        public JSONObject getOrderTracesByJson(LogisticsReq req) {
//            String[] expCode = new String[0];
//            try {
//                expCode = getShipperByLogisticCode(req.getLogisticsNumber());
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            for (int i = 0; i < expCode.length; i++) {
//                try {
//                    JSONObject ret = getOrderTracesByJsons(req, expCode[i]);
//                    if (!ret.getJSONArray("resultList").isEmpty()) {
//                        return ret;
//                    }
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//            JSONObject res = new JSONObject();
//            res.put("provider", "kuaidiniao");
//            res.put("number", req.getLogisticsNumber());
//            res.put("company", req.getLogisticsCompany());
//            res.put("status", "200");
//            res.put("resultList", JSONArray.parseArray("[{\"result\":\"" + "暂无物流信息" + "\"}]"));
//            return res;
//        }

    /**
     * Json方式 查询订单物流轨迹
     *
     * @throws Exception
     */
//        public JSONObject getOrderTracesByJsons(LogisticsReq req, String expCode) throws Exception {
//            String expNo = req.getLogisticsNumber();
//            String requestData = "{'OrderCode':'','ShipperCode':'" + expCode + "','LogisticCode':'" + expNo + "'}";
//
//            Map<String, String> params = new HashMap<String, String>();
//            params.put("RequestData", logisticsUtil.urlEncoder(requestData, "UTF-8"));
//            params.put("EBusinessID", EBusinessID);
//            params.put("RequestType", "1002");
//            String dataSign = logisticsUtil.encrypt(requestData, AppKey, "UTF-8");
//            params.put("DataSign", logisticsUtil.urlEncoder(dataSign, "UTF-8"));
//            params.put("DataType", "2");
//            JSONObject res = new JSONObject();
//
//            try {
//                String result = logisticsUtil.sendPost(ReqURL, params);
//                JSONObject object = JSONObject.parseObject(result);
//                JSONArray resList = object.getJSONArray("Traces");
//                for (int i = 0; i < resList.size(); i++) {
//                    JSONObject jsonParamData = resList.getJSONObject(i);
//                    jsonParamData.put("remark", jsonParamData.getString("AcceptStation"));
//                    jsonParamData.remove("AcceptStation");
//                    jsonParamData.put("time", jsonParamData.getString("AcceptTime"));
//                    jsonParamData.remove("AcceptTime");
//                }
//                if ("true".equals(object.getString("Success"))) {
//                    res.put("status", "200");
//                    res.put("provider", "kuaidiniao");
//                    res.put("number", object.getString("LogisticCode"));
//                    res.put("company", object.getString("ShipperCode"));
//                    res.put("resultList", resList);
//                    return res;
//                } else {
//                    res.put("provider", "kuaidiniao");
//                    res.put("number", req.getLogisticsNumber());
//                    res.put("company", req.getLogisticsCompany());
//                    res.put("status", object.get("State"));
//                    res.put("resultList", JSONArray.parseArray("[{\"result\":\"" + object.getString("Reason") + "\"}]"));
//                    return res;
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            res.put("provider", "kuaidiniao");
//            res.put("number", req.getLogisticsNumber());
//            res.put("company", req.getLogisticsCompany());
//            res.put("status", "0");
//            res.put("resultList", JSONArray.parseArray("[{\"result\":\"" + "error" + "\"}]"));
//            return res;
//        }

//    }
}}
