package com.foshan.service.shop.task;


import java.util.List;

import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;

import com.foshan.entity.context.EntityContext;
import com.foshan.entity.shop.ProductEntity;
import com.foshan.entity.shop.ProductLimitEntity;
import com.foshan.service.quartz.IFaithJob;
import com.foshan.util.ProductLimit;
import com.foshan.util.SpringHandler;

public class ProductLimitTask implements IFaithJob {

	public void updateInventroyAndSales() {
		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();
		Transaction tx = session.beginTransaction();

		//更新产品库存
		ProductLimit.limit.values().forEach(o -> {
			o.values().forEach(b -> {
				if ((b.getLimitType().equals(EntityContext.PRODUCT_LIMIT_TYPE_INVENTORY_ONLY)
						|| b.getLimitType().equals(EntityContext.PRODUCT_LIMIT_TYPE_TIME_INVENTORY)
						|| b.getLimitType().equals(EntityContext.PRODUCT_LIMIT_TYPE_TIME_LIMIT_INVENTORY))
						&& (b.getProductInventoryAmount() < b.getInventoryAmount())) {
					ProductEntity product = (ProductEntity) session
							.createQuery("from ProductEntity a where a.id=" + b.getProductId()).uniqueResult();
					if (!product.getInventoryAmount().equals(b.getProductInventoryAmount())) {
						product.setInventoryAmount(b.getProductInventoryAmount());
						session.update(product);
					}
				}
			});
		});

		// 更新产品销量
		ProductLimit.sales.keySet().forEach(o -> {
			ProductEntity product = (ProductEntity) session.createQuery("from ProductEntity a where a.id=" + o)
					.uniqueResult();
			product.setSalesNumber(product.getSalesNumber() + ProductLimit.sales.get(o));
			session.update(product);
			ProductLimit.sales.remove(o);
		});

		tx.commit();
		if (session != null) {
			session.close();
		}
	}

	//检查过期的产品限购策略
	@SuppressWarnings("unchecked")
	public void checkInvalidLimit() {
		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();
		Transaction tx = session.beginTransaction();

		List<ProductLimitEntity> limitList = session.createQuery("select a from ProductLimitEntity a where a.limitType>="+EntityContext.PRODUCT_LIMIT_TYPE_TIME_INVENTORY+" and a.endTime<now()").list();
		
		
		limitList.forEach(o->{
//			if(ProductLimit.limit.containsKey(o.getSpecificationId())) {
//				ProductLimit.limit.remove(o.getSpecificationId());
//				ProductLimit.purchase.remove(o.getProduct().getId());
//			}
			
			if (ProductLimit.limit.containsKey(o.getSpecificationId())) {
				ProductLimit.limit.get(o.getSpecificationId()).remove(o.getProduct().getId());
				if (ProductLimit.limit.get(o.getSpecificationId()).size() == 0) {
					ProductLimit.limit.remove(o.getSpecificationId());
				}
			}
//			o.setLimitState(EntityContext.RECORD_STATE_INVALID);
//			o.setState(EntityContext.RECORD_STATE_INVALID);
			o.setProduct(null);
			session.delete(o);
		});
		
		tx.commit();
		if (session != null) {
			session.close();
		}
	}
}
