package com.foshan.service.shop.impl;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.transaction.Transactional;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.jdbc.Work;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.dao.generic.Page;
import com.foshan.entity.ArticleEntity;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.ColumnEntity;
import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.RegionEntity;
import com.foshan.entity.UpshelfColumnEntity;
import com.foshan.entity.UrlEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.shop.MemberEntity;
import com.foshan.entity.shop.ProductEntity;
import com.foshan.entity.shop.ShopColumnEntity;
import com.foshan.entity.shop.ShopUserEntity;
import com.foshan.form.ArticleAssetForm;
import com.foshan.form.ArticleForm;
import com.foshan.form.AssetForm;
import com.foshan.form.RegionForm;
import com.foshan.form.UrlForm;
import com.foshan.form.request.UpshelfColumnReq;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.shop.ProductForm;
import com.foshan.form.shop.ProductPriceForm;
import com.foshan.form.shop.ShopColumnForm;
import com.foshan.form.shop.ShopUpshelfColumnForm;
import com.foshan.form.shop.UpshelfContentForm;
import com.foshan.form.shop.response.upshelfColumn.GetShopUpshelfColumnInfoRes;
import com.foshan.form.shop.response.upshelfColumn.GetShopUpshelfColumnListRes;
import com.foshan.form.shop.response.upshelfColumn.GetUpshelfContentListRes;
import com.foshan.service.shop.IShopUpshelfColumnService;
import com.foshan.util.DateUtil;
import com.foshan.util.SpringHandler;

@Transactional
@Service("shopUpshelfColumnService")
public class ShopUpshelfColumnServiceImpl extends GenericShopService implements IShopUpshelfColumnService {

	public IResponse getUpshelfColumnList(UpshelfColumnReq req) {
		GetShopUpshelfColumnListRes res = new GetShopUpshelfColumnListRes();
		if (null != req.getColumnId() || StringUtils.isNotEmpty(req.getColumnCode())) {
			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
			Session session = sessionFactory.openSession();
			Transaction tx = session.beginTransaction();
			try {
				session.doWork(new Work() {
					@Override
					public void execute(Connection connection) throws SQLException {
						// TODO Auto-generated method stub
						Statement st = (Statement) connection.createStatement();

						Object userObj = getPrincipal(false);
						StringBuilder sql = new StringBuilder();
//						if((null != userObj && userObj instanceof MemberEntity) || null == userObj) {
						Date date = new Date();
						SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						String time = sf.format(date);
//					        if(null != userObj && userObj instanceof MemberEntity) {
//					        	MemberEntity member = (MemberEntity)userObj;
//					        	req.setRegionCode(StringUtils.isNotEmpty(member.getRegionCode()) ? member.getRegionCode() : req.getRegionCode());
//					        }
						// 文章
						sql.append("SELECT a.*,b.assetName resourceName FROM t_upshelf_column a "
								+ "INNER JOIN t_column co ON co.id=a.columnId "
								+ "INNER JOIN t_asset b ON b.id=a.resourceId   "
//								+ "  INNER JOIN t_article_region ar1 ON ar1.articleId=b.id "
//								+ "INNER JOIN t_region r1 ON r1.id=ar1.regionId WHERE a.type=1 ")
								+ " WHERE a.type=1 ")
							.append(null != req.getColumnId() ? " and a.columnId=" + req.getColumnId() :  
								" AND co.columnCode='" + req.getColumnCode()+"'" )
							.append(StringUtils.isNotEmpty(req.getTerminalTypeList()) ? " and a.terminalType in("+req.getTerminalTypeList()+")":"" ) 
//						 	.append((null != userObj && userObj instanceof MemberEntity) || null == userObj
//						 		? " and (r1.startRegionCode+0)<="
//								+ (StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode(): "1")
//								+ " and (r1.endRegionCode+0)>="
//								+ (StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode(): "1")
//								+" and b.state=" + EntityContext.RECORD_STATE_VALID + " and b.status="
//								+ EntityContext.RECORD_AUDIT_PASS+
//								" and a.startTime<='" + time + "' and a.endTime>='" + time + "'" :"");
						 	.append(" and b.state="+EntityContext.RECORD_STATE_VALID +" and b.assetState=2"+
								" and a.startTime<='" + time + "' and a.endTime>='" + time + "'" +
								" and b.assetType=5");

						// 栏目
						sql.append(" UNION SELECT a.*,c.columnName FROM t_upshelf_column a "
								+ "INNER JOIN t_column co ON co.id=a.columnId "
								+ "INNER JOIN t_column c ON c.id=a.resourceId INNER JOIN  "
								+ "t_column_region ar2 ON ar2.columnId=c.id "
								+ "INNER JOIN t_region r2 ON r2.id=ar2.regionId WHERE a.type=9 ")
							.append(null != req.getColumnId() ? " and a.columnId=" + req.getColumnId() :  
								" AND co.columnCode='" + req.getColumnCode()+"'" )
							.append((null != userObj && userObj instanceof MemberEntity) || null == userObj
								? " and (r2.startRegionCode+0)<="
								+ (StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode(): "1")
								+ " and (r2.endRegionCode+0)>="
								+ (StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode(): "1")
							    +" and c.state=" + EntityContext.RECORD_STATE_VALID + " and c.columnState="
								+ EntityContext.RECORD_AUDIT_PASS+
								" and a.startTime<='" + time + "' and a.endTime>='" + time + "'" : "");
						// 产品
						sql.append(
								" UNION SELECT a.*,e.productName FROM t_upshelf_column a "
								+ "INNER JOIN t_product d ON d.id=a.resourceId "
								+ "INNER JOIN t_column co ON co.id=a.columnId "
								+ "INNER JOIN t_product_specification e on e.id=d.specificationId  INNER JOIN  "
								+ "t_product_specification_region ar3 ON ar3.specificationId=e.id "
								+ "INNER JOIN t_region r3 ON r3.id=ar3.regionId WHERE a.type=8 ")
							.append(null != req.getColumnId() ? " and a.columnId=" + req.getColumnId() :  
								" AND co.columnCode='" + req.getColumnCode()+"'" )
							.append((null != userObj && userObj instanceof MemberEntity) || null == userObj
								? " and (r3.startRegionCode+0)<="
								+ (StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "1")
								+ " and (r3.endRegionCode+0)>="
								+ (StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode(): "1")
								+" and e.state=" + EntityContext.RECORD_STATE_VALID + " and e.auditState="
								+ EntityContext.RECORD_AUDIT_PASS + " and d.state="+ EntityContext.RECORD_STATE_VALID
								+" and a.startTime<='" + time + "' and a.endTime>='" + time + "'" : "");
						// 链接
						sql.append(" UNION SELECT a.*,f.title resourceName FROM t_upshelf_column a "
								+ "INNER JOIN t_url f ON f.id=a.resourceId INNER JOIN "
								+ "t_url_region ar4 ON ar4.urlId=f.id "
								+ "INNER JOIN t_column co ON co.id=a.columnId "
								+ "INNER JOIN t_region r4 ON r4.id=ar4.regionId WHERE a.type=10 ")
							.append(null != req.getColumnId() ? " and a.columnId=" + req.getColumnId() :  
								" AND co.columnCode='" + req.getColumnCode()+"'" )
							.append((null != userObj && userObj instanceof MemberEntity) || null == userObj
								? " and (r4.startRegionCode+0)<="
								+ (StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode(): "1")
								+ " and (r4.endRegionCode+0)>="
								+ (StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode(): "1")
								+" and f.state=" + EntityContext.RECORD_STATE_VALID + " and f.auditState="
								+ EntityContext.RECORD_AUDIT_PASS+" and a.startTime<='" 
								+ time + "' and a.endTime>='" + time + "'" :"");
						// }
	
						StringBuilder totalSql = new StringBuilder("select count(1) as totalResult from (").append(sql)
								.append(") d");

						ResultSet totalResultSet = st.executeQuery(totalSql.toString());
						Integer totalResult = 0;
						while (totalResultSet.next()) {
							totalResult = totalResultSet.getInt("totalResult");
						}
						res.setTotal(totalResult == 0 ? 0 : (totalResult - 1) / req.getPageSize() + 1);
						sql.append(" ) t ");
						if(StringUtils.isNotEmpty(req.getTitle())) {
							sql.append("where t.title like '%" + req.getTitle() + "%' ");
						}
						sql.append("ORDER BY t.isTop DESC, t.orderNumber DESC limit "
							+ (req.getRequestPage() <= 1 ? 0 : (req.getRequestPage() - 1) 
							* req.getPageSize()) + ","+ req.getPageSize());
						// .getUniqueBySql(sql, values)

						res.setTotalResult(totalResult);
						res.setPageSize(req.getPageSize());
						res.setCurrentPage(req.getRequestPage());
						// List<UpshelfColumnEntity> list = upshelfColumnDao.getListByHql("SELECT * FROM
						// ("+sql.toString(), "");

						ResultSet rs = st.executeQuery("SELECT * FROM (" + sql.toString());
						while (rs.next()) {
							Integer type = rs.getInt("type");
							Integer resourceId = rs.getInt("resourceId");
							ShopUpshelfColumnForm upshelfColumnForm = new ShopUpshelfColumnForm(null, rs.getInt("id"),
									rs.getInt("isRecommend"), rs.getInt("orderNumber"), resourceId,
									new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(rs.getTimestamp("upshelfTime")),
									rs.getInt("terminalType"), type, null,rs.getString("attributeValue1"),rs.getString("attributeValue2"),
									rs.getString("attributeValue3"),rs.getString("attributeValue4"),rs.getString("attributeValue5"));
							upshelfColumnForm.setIsTop(rs.getInt("isTop"));
							upshelfColumnForm.setEndTime(
									new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(rs.getTimestamp("endTime")));
							upshelfColumnForm.setStartTime(
									new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(rs.getTimestamp("startTime")));
							upshelfColumnForm.setTargetUrl(rs.getString("targetUrl"));
							upshelfColumnForm.setTitle(rs.getString("title"));
							upshelfColumnForm.setResourceName(rs.getString("resourceName"));
							upshelfColumnForm.setColumnId(rs.getInt("columnId"));
							upshelfColumnForm.setUpshelfColumnId(rs.getInt("id"));
							upshelfColumnForm.setRemarks(rs.getString("remarks"));
							Integer assetId = rs.getInt("assetId");
							if (null != assetId) {
								AssetForm assetForm = new AssetForm();
								AssetEntity asset = assetDao.get(assetId);
								if (null != asset) {
									BeanUtils.copyProperties(asset, assetForm);
									assetForm.setAssetId(asset.getId());
									upshelfColumnForm.setAssetForm(assetForm);
								}
							}
							res.getShopUpshelfColumnList().add(upshelfColumnForm);
						}
						res.setRet(ResponseContext.RES_SUCCESS_CODE);
						res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
						st.close();
					}
				});
				tx.commit();
				if (session != null) {
					session.close();
				}
			} catch (Exception ex) {
				if (session != null) {
					session.close();
				}
				ex.printStackTrace();
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getUpshelfColumnInfo(UpshelfColumnReq req) {
		GetShopUpshelfColumnInfoRes res = new GetShopUpshelfColumnInfoRes();
		if (null != req.getUpshelfColumnId()) {
			UpshelfColumnEntity upshelfColumn = upshelfColumnDao.get(req.getUpshelfColumnId());
			if (null != upshelfColumn) {
				Object userObj = getPrincipal(false);
				ShopUpshelfColumnForm upshelfColumnForm = new ShopUpshelfColumnForm(null, upshelfColumn.getId(),
						upshelfColumn.getIsRecommend(), upshelfColumn.getOrderNumber(), upshelfColumn.getResourceId(),
						new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(upshelfColumn.getUpshelfTime()),
						upshelfColumn.getTerminalType(), upshelfColumn.getType(), null,upshelfColumn.getAttributeValue1(),
						upshelfColumn.getAttributeValue2(),upshelfColumn.getAttributeValue3(),
						upshelfColumn.getAttributeValue4(),upshelfColumn.getAttributeValue5());
				upshelfColumnForm.setType(upshelfColumn.getType());
				upshelfColumnForm.setIsTop(upshelfColumn.getIsTop());
				upshelfColumnForm
						.setEndTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(upshelfColumn.getEndTime()));
				upshelfColumnForm
						.setStartTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(upshelfColumn.getStartTime()));
				if (upshelfColumn.getType() == 1) {
					upshelfColumnForm.setArticleForm(getArticleForm(upshelfColumn.getResourceId()));
				} else if (upshelfColumn.getType() == 8) {
					upshelfColumnForm.setProductForm(getProductForm(upshelfColumn.getResourceId()));
				} else if (upshelfColumn.getType() == 9) {
					ShopColumnForm columnForm = getShopColumnForm(upshelfColumn.getResourceId(), userObj);
					upshelfColumnForm.setShopColumnForm(columnForm);
				} else if (upshelfColumn.getType() == 10) {
					UrlEntity url = urlDao.get(upshelfColumn.getResourceId());
					UrlForm form = new UrlForm();
					form.setAuditState(url.getAuditState());
					form.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(url.getCreateTime()));
					form.setIdea(url.getIdea());
					form.setLastModifyTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(url.getLastModifyTime()));
					form.setState(url.getState());
					form.setTargetUrl(url.getTargetUrl());
					form.setTitle(url.getTitle());
					form.setAuditTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(url.getAuditTime()));
					form.setUrlId(url.getId());

					for (RegionEntity b : url.getRegionList()) {
						form.getRegionList().add(new RegionForm(b.getId(), b.getRegionCode(), b.getRegionName(),
								b.getStartRegionCode(), b.getEndRegionCode()));
					}
					upshelfColumnForm.setUrlForm(form);
				}
				upshelfColumnForm.setTargetUrl(upshelfColumn.getTargetUrl());
				upshelfColumnForm.setTitle(upshelfColumn.getTitle());
				upshelfColumnForm.setRemarks(upshelfColumn.getRemarks());
				if (null != upshelfColumn.getAsset()) {
					AssetForm assetForm = new AssetForm();
					BeanUtils.copyProperties(upshelfColumn.getAsset(), assetForm);
					assetForm.setAssetId(upshelfColumn.getAsset().getId());
					upshelfColumnForm.setAssetForm(assetForm);
				}
				res.setShopUpshelfColumnForm(upshelfColumnForm);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	public ProductForm getProductForm(Integer productId) {
		ProductForm productForm = null;
		if (null != productId) {
			ProductEntity product = productDao.get(productId);
			if (null != product) {
				productForm = getProductForm(null, null, product.getProductSpecification(), product, null);
				productForm.setProductName(product.getProductSpecification().getProductName());
				productForm.setProductSpecificationForm(
						getProductSpecificationForm(product.getProductSpecification(), true, false, false, null));
			} else {
				productForm = new ProductForm();
			}
		}

		return productForm;
	}

	public ArticleForm getArticleForm(Integer articleId) {
		ArticleForm articleForm = null;
		if (null != articleId) {
			ArticleEntity article = articleDao.get(articleId);

			if (null != article) {
				/*articleForm = new ArticleForm(article.getId(), article.getTitle(), article.getSubtitle(),
						article.getContent(), article.getUserName(), article.getIsRecommend(), article.getOrderNumber(),
						article.getStatus(), article.getIdea(), article.getContentType(), article.getContentUrl(),
						article.getSource(), article.getResourceName(),
						new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(article.getCreateTime()),
						new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(article.getLastModifyTime()),
						new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(article.getPublishTime()),
						article.getState(), null);
				List<ArticleAssetForm> articleAssetList = new ArrayList<ArticleAssetForm>();
				article.getArticleAssetList().forEach(o -> {
					AssetForm assetForm = new AssetForm();
					BeanUtils.copyProperties(o.getAsset(), assetForm);
					assetForm.setAssetId(o.getAsset().getId());
					ArticleAssetForm articleAssetForm = new ArticleAssetForm(o.getId(), o.getImageType(),
							o.getOrderNumber(), assetForm);
					articleAssetList.add(articleAssetForm);
				});
				// 子分类按OrderNumber升序排序
				Collections.sort(articleAssetList, new Comparator<ArticleAssetForm>() {
					public int compare(ArticleAssetForm o1, ArticleAssetForm o2) {
						if (null == o1.getOrderNumber() || null == o2.getOrderNumber()) {
							return -1;
						}
						if (o1.getOrderNumber() < o2.getOrderNumber()) {
							return -1;
						}
						if (o1.getOrderNumber() == o2.getOrderNumber())
							return 0;
						return 1;
					}
				});
				articleForm.setArticleAssetList(articleAssetList);
				for (RegionEntity b : article.getRegionList()) {
					articleForm.getRegionList().add(new RegionForm(b.getId(), b.getRegionCode(), b.getRegionName(),
							b.getStartRegionCode(), b.getEndRegionCode()));
				}*/
			}
		}

		return articleForm;
	}

	public ShopColumnForm getShopColumnForm(Integer columnId, Object userObj) {
		ShopColumnForm columnForm = null;
		if (null != columnId) {
			ColumnEntity column = columnDao.get(columnId);
			if (null != column) {
				columnForm = new ShopColumnForm();
				columnForm.setColumnCode(column.getColumnCode());
				columnForm.setColumnInfo(column.getColumnInfo());
				columnForm.setColumnLevel(column.getColumnLevel());
				columnForm.setColumnName(column.getColumnName());
				columnForm.setColumnPath(column.getColumnPath());
				columnForm.setColumnState(column.getColumnState());
				columnForm.setColumnType(column.getColumnType());
				columnForm.setCommendFlag(column.getCommendFlag());
				columnForm.setColumnImage(getAsset(column.getColumnImage()));
				columnForm.setColumnPhoneImage(getAsset(column.getColumnPhoneImage()));
				columnForm.setMappingFolderId(column.getMappingFolderId());
				columnForm.setMappingFolderName(column.getMappingFolderName());
				columnForm.setMappingSystem(column.getMappingSystem());
				columnForm.setOrders(column.getOrders());
				columnForm.setTargetType(column.getTargetType());
				columnForm.setColumnId(column.getId());
				columnForm.setIsGlobal(column.getIsGlobal());
				for (RegionEntity b : column.getRegionList()) {
					columnForm.getRegionList().add(new RegionForm(b.getId(), b.getRegionCode(), b.getRegionName(),
							b.getStartRegionCode(), b.getEndRegionCode()));
				}
				;
				try {
					columnForm.setIsOperation((null != userObj && userObj instanceof ShopUserEntity
							&& ((ShopColumnEntity) column).getStoreList()
									.contains(((ShopUserEntity) userObj).getStore())
							&& column.getColumnLevel() > 1) ? true
									: (null != userObj && userObj instanceof PlatformUserEntity) ? true : false);
				} catch (Exception ex) {
					columnForm.setIsOperation(false);
				}
			}
		}

		return columnForm;
	}

	@Override
	public IResponse getContentList(UpshelfColumnReq req) {
		GetUpshelfContentListRes res = new GetUpshelfContentListRes();
		if (null != req.getType() && null != req.getColumnId()) {
			if (req.getType() == 1) {
/*				
				Page<ArticleEntity> page = new Page<ArticleEntity>();
				setUpshelfContentPage(req, page);
				StringBuilder hql = new StringBuilder(
						"select a from ArticleEntity a    where a.state=1 and a.status=1   "
								+ " and a.id not in(select u.resourceId from UpshelfColumnEntity u where u.type="
								+ req.getType() + " and u.column=" + req.getColumnId() + ")");
				hql.append(StringUtils.isNotEmpty(req.getTitle()) ? " and a.title like'%"+req.getTitle()+"%'" : "")
					.append("order by a.createTime desc");
				page = articleDao.queryPage(page, hql.toString());

				page.getResultList().forEach(o -> {
					UpshelfContentForm UpshelfContentForm = new UpshelfContentForm(o.getId(), o.getTitle(),
							req.getType());
					res.getUpshelfContentList().add(UpshelfContentForm);
				});
				setGetUpshelfContentListRes(page, res);
*/
			} else if (req.getType() == 8) {
				Page<ProductEntity> page = new Page<ProductEntity>();
				setUpshelfContentPage(req, page);
				Object userObj = getPrincipal(false);

				StringBuilder hql = new StringBuilder("select a from ProductEntity a inner join "
						+ "a.productSpecification b inner join b.visibleRegionList r  where a.state=1 and b.auditState=1  "
						+ " and a.id not in(select u.resourceId from UpshelfColumnEntity u "
						+ "where u.type=" + req.getType()+ " and u.column.id=" + req.getColumnId() + ")");

				hql.append(StringUtils.isNotEmpty(req.getTitle()) ? " and b.productName like'%"+req.getTitle()+"%'" : "")
					.append(StringUtils.isNotEmpty(req.getRegionCode()) ? " and a.productCode like'%"+req.getResourceCode()+"%'" : "")
					.append(null!=req.getStoreId() ? " and b.store.id="+req.getStoreId() : "")
				    .append((StringUtils.isNotEmpty(req.getRegionCode())
						? " and (r.startRegionCode+0)<="
						+ (StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode(): "1")
						+ " and (r.endRegionCode+0)>="
						+ (StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode(): "1"): ""))
				    .append(" and (a.isMarketable="+EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL +
				        " or a.isMarketable="+EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL_AND_COMPANY +")")
				    .append(" order by a.createTime desc");

				page = productDao.queryPage(page, hql.toString());

				page.getResultList().forEach(o -> {
					UpshelfContentForm upshelfContentForm = new UpshelfContentForm(o.getId(),
							o.getProductSpecification().getProductName(), req.getType());
					res.getUpshelfContentList().add(upshelfContentForm);
					ProductPriceForm price = getProductPrice(null, null, o.getId(),EntityContext.PRODUCT_PRICE_TYPE_NORMAL);
					
					upshelfContentForm.setPrice(null != price.getSalesPrice() ? price.getSalesPrice().toString() 
							: o.getProductPrice().toString());
					upshelfContentForm.setSkuSpecification(o.getSkuSpecification());
					upshelfContentForm.setResourceCode(o.getProductCode());
				
					upshelfContentForm.setAttribute1(o.getProductSpecification().getStore().getStoreName());
					upshelfContentForm.setAttribute2(price.getPromotionPrice() == null ? null : price.getPromotionPrice().toString());
					//upshelfContentForm.setAttribute3(null!=o.getProductPhoneImage() ? o.getProductPhoneImage().getId().toString() : "");
					Map<String,AssetForm> map = new HashMap<String,AssetForm>();
					if(null!=o.getProductPhoneImage()) {
						AssetForm form = new AssetForm();
						BeanUtils.copyProperties(o.getProductPhoneImage(), form);
						form.setAssetId(o.getProductPhoneImage().getId());
						map.put("productPhoneImage", form);
					}
					if(null!=o.getProductImage()) {
						AssetForm form = new AssetForm();
						BeanUtils.copyProperties(o.getProductImage(), form);
						form.setAssetId(o.getProductImage().getId());
						map.put("productImage", form);
					}
					if(null!=o.getVideo()) {
						AssetForm form = new AssetForm();
						BeanUtils.copyProperties(o.getVideo(), form);
						form.setAssetId(o.getVideo().getId());
						map.put("video", form);
					}
					
					ObjectMapper mapper = new ObjectMapper(); 
					String json="";
					try {
						json = mapper.writeValueAsString(map);
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}
					upshelfContentForm.setAttribute3(json);
					for (RegionEntity b : o.getProductSpecification().getRegionList()) {
						upshelfContentForm.getRegionList().add(new RegionForm(b.getId(), b.getRegionCode(), b.getRegionName(),
								b.getStartRegionCode(), b.getEndRegionCode()));
					}
				});
				setGetUpshelfContentListRes(page, res);
			}else if(req.getType() == 9) {
				Page<ColumnEntity> page = new Page<ColumnEntity>();
				setUpshelfContentPage(req, page);
				StringBuilder hql = new StringBuilder("select a from ColumnEntity a " + " where  a.columnState=1 and a.state=1 "
						+ " and a.id not in(select u.resourceId from UpshelfColumnEntity u where u.type=" + req.getType()
						+ " and u.column.id=" + req.getColumnId() + ") and a.columnType in(7,8) ");
				hql.append(StringUtils.isNotEmpty(req.getTitle()) ? " and a.columnName like'%"+req.getTitle()+"%'" : "");
				page = columnDao.queryPage(page, hql.toString());

				page.getResultList().forEach(o -> {
					UpshelfContentForm UpshelfContentForm = new UpshelfContentForm(o.getId(),
							o.getColumnName(), req.getType());
					res.getUpshelfContentList().add(UpshelfContentForm);
				});
				
			}else if(req.getType() == 10) {
				Page<UrlEntity> page = new Page<UrlEntity>();
				setUpshelfContentPage(req, page);
				StringBuilder hql = new StringBuilder("select a from UrlEntity a " + " where  a.auditState=1 and a.state=1 "
						+ " and a.id not in(select u.resourceId from UpshelfColumnEntity u where u.type=" + req.getType()
						+ " and u.column.id=" + req.getColumnId() + ")");
				hql.append(StringUtils.isNotEmpty(req.getTitle()) ? " and a.title like'%"+req.getTitle()+"%'" : "");
				page = urlDao.queryPage(page, hql.toString());

				page.getResultList().forEach(o -> {
					UpshelfContentForm UpshelfContentForm = new UpshelfContentForm(o.getId(),
							o.getTitle(), req.getType());
					res.getUpshelfContentList().add(UpshelfContentForm);
				});
			}
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	public IResponse addUpshelfResources(UpshelfColumnReq req) {
		GetUpshelfContentListRes res = new GetUpshelfContentListRes();
		if (StringUtils.isNotEmpty(req.getResourceIdList()) && null != req.getColumnId() && null != req.getType()
				&& null != req.getTerminalType() && StringUtils.isNotEmpty(req.getStartTime())
				&& StringUtils.isNotEmpty(req.getEndTime())) {

			Timestamp startTime = null;
			Timestamp endTime = null;
			try {
				startTime = new Timestamp(DateUtil.parseLongFormat(req.getStartTime()).getTime());
				endTime = new Timestamp(DateUtil.parseLongFormat(req.getEndTime()).getTime());
			} catch (ParseException e) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}

			if (endTime.before(startTime)) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "结束时间不能比开始时间早！");
				return res;
			}
			ColumnEntity column = columnDao.get(req.getColumnId());
			if (null == column) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			for (String id : req.getResourceIdList().split(",")) {
				Integer resourceId = Integer.valueOf(id);
				UpshelfColumnEntity upshelfColumn = upshelfColumnDao.getUniqueByHql(
						"select a from UpshelfColumnEntity a where a.column.id=? and a.resourceId=? and a.type=?",
						req.getColumnId(), resourceId, req.getType());
				if (null != upshelfColumn) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "资源ID为：" + resourceId + ";已在该栏目上架存在！");
					return res;
				} else {
					upshelfColumn = new UpshelfColumnEntity();
				}
				if (req.getType() == 1) {
					ArticleEntity article = articleDao.get(resourceId);
					if (null == article) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "资源ID为：" + resourceId + ";资源不存在！");
						return res;
					}
				} else if (req.getType() == 8) {
					ProductEntity productSpecification = productDao.get(resourceId);
					if (null == productSpecification) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "资源ID为：" + resourceId + ";资源不存在！");
						return res;
					}
				} else if (req.getType() == 9) {
					ColumnEntity resource = columnDao.get(resourceId);
					if (null == resource) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "资源ID为：" + resourceId + ";资源不存在！");
						return res;
					}
				}
				if (null != req.getAssetId()) {
					upshelfColumn.setAsset(assetDao.get(req.getAssetId()));
				}
				upshelfColumn.setColumn(column);
				upshelfColumn.setOrderNumber(0);
				upshelfColumn.setIsRecommend(null != req.getIsRecommend() ? req.getIsRecommend() : 0);
				upshelfColumn.setResourceId(resourceId);
				upshelfColumn.setType(req.getType());
				upshelfColumn.setTerminalType(req.getTerminalType());
				upshelfColumn.setEndTime(endTime);
				upshelfColumn.setStartTime(startTime);
				upshelfColumn.setIsTop(null != req.getIsTop() ? req.getIsTop() : 0);
				upshelfColumn.setTitle(StringUtils.isNotEmpty(req.getTitle()) ? req.getTitle() : "");
				upshelfColumn.setTargetUrl(StringUtils.isNotEmpty(req.getTargetUrl()) ? req.getTargetUrl() : "");
				upshelfColumn.setRemarks(StringUtils.isNotEmpty(req.getRemarks()) ? req.getRemarks() : "");
				upshelfColumn.setAttributeValue1(StringUtils.isNotEmpty(req.getAttributeValue1()) 
						? req.getAttributeValue1() : "");
				upshelfColumn.setAttributeValue2(StringUtils.isNotEmpty(req.getAttributeValue2()) 
						? req.getAttributeValue2() : "");
				upshelfColumn.setAttributeValue3(StringUtils.isNotEmpty(req.getAttributeValue3()) 
						? req.getAttributeValue3() : "");
				upshelfColumn.setAttributeValue4(StringUtils.isNotEmpty(req.getAttributeValue4()) 
						? req.getAttributeValue4() : "");
				upshelfColumn.setAttributeValue5(StringUtils.isNotEmpty(req.getAttributeValue5()) 
						? req.getAttributeValue5() : "");
				upshelfColumnDao.save(upshelfColumn);
			}
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	public <T> Page<T> setUpshelfContentPage(UpshelfColumnReq req, Page<T> page) {
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		return page;
	}

	public <T> Page<T> setGetUpshelfContentListRes(Page<T> page, GetUpshelfContentListRes res) {
		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		return page;
	}

}
