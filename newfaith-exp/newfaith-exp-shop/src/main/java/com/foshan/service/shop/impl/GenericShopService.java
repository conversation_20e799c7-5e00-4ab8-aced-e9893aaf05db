package com.foshan.service.shop.impl;

/*
此类中还包含对演唱会订单的操作
*/

import static java.util.Comparator.comparingInt;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.MathContext;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.query.NativeQuery;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.annotation.Cacheable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.foshan.dao.IPluginDao;
import com.foshan.dao.generic.Page;
import com.foshan.dao.shop.IBrandDao;
import com.foshan.dao.shop.IBuyerInformationDao;
import com.foshan.dao.shop.ICartDao;
import com.foshan.dao.shop.ICartItemDao;
import com.foshan.dao.shop.ICompanyInvoiceInfoDao;
import com.foshan.dao.shop.IContractDao;
import com.foshan.dao.shop.ICouponDao;
import com.foshan.dao.shop.ICouponSettingDao;
import com.foshan.dao.shop.ICourierCompanyDao;
import com.foshan.dao.shop.ICourierFeeScheduleDao;
import com.foshan.dao.shop.ICourierFeeScheduleItemDao;
import com.foshan.dao.shop.IDetailItemDao;
import com.foshan.dao.shop.IExpressPackageDao;
import com.foshan.dao.shop.IExpressPackageOrderItemDao;
import com.foshan.dao.shop.IFinancialAccountingDao;
import com.foshan.dao.shop.IInventoryVoDao;
import com.foshan.dao.shop.IInvoiceDao;
import com.foshan.dao.shop.IInvoiceResultsDao;
import com.foshan.dao.shop.ILogisticsDao;
import com.foshan.dao.shop.IMemberAddressDao;
import com.foshan.dao.shop.IMemberDao;
import com.foshan.dao.shop.IMemberInvoiceInfoDao;
import com.foshan.dao.shop.INumberOfProductsOnShelfVoDao;
import com.foshan.dao.shop.IOrderPaymentDao;
import com.foshan.dao.shop.IOrderRefundDao;
import com.foshan.dao.shop.IPhoneLocationDao;
import com.foshan.dao.shop.IPickupPointDao;
import com.foshan.dao.shop.IProductAppraiseDao;
import com.foshan.dao.shop.IProductAppraiseItemDao;
import com.foshan.dao.shop.IProductCategoryDao;
import com.foshan.dao.shop.IProductDao;
import com.foshan.dao.shop.IProductFavoriteDao;
import com.foshan.dao.shop.IProductFinancialAttributesDao;
import com.foshan.dao.shop.IProductGroupDao;
import com.foshan.dao.shop.IProductLimitDao;
import com.foshan.dao.shop.IProductOrderDao;
import com.foshan.dao.shop.IProductOrderInvoiceDao;
import com.foshan.dao.shop.IProductOrderItemDao;
import com.foshan.dao.shop.IProductOrderTraceDao;
import com.foshan.dao.shop.IProductPriceDao;
import com.foshan.dao.shop.IProductSalesDao;
import com.foshan.dao.shop.IProductSpecificationDao;
import com.foshan.dao.shop.IProductSpecificationDetailDao;
import com.foshan.dao.shop.IProductStockDao;
import com.foshan.dao.shop.IPromotionDao;
import com.foshan.dao.shop.IPurchasingCompanyDao;
import com.foshan.dao.shop.ISalesReturnDao;
import com.foshan.dao.shop.ISalesReturnOrderItemDao;
import com.foshan.dao.shop.IShopColumnDao;
import com.foshan.dao.shop.IShopPaymentRecordDao;
import com.foshan.dao.shop.IShopUserDao;
import com.foshan.dao.shop.IStoreDao;
import com.foshan.dao.shop.IStorePluginConfigDao;
import com.foshan.dao.shop.IThirdPartyInterfaceDao;
import com.foshan.dao.shop.ITotalPurchaseAmountVoDao;
import com.foshan.dao.shop.IVerificaterDao;
import com.foshan.entity.AccountEntity;
import com.foshan.entity.ColumnEntity;
import com.foshan.entity.DepartmentEntity;
import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.PointsMemberAccountEntity;
import com.foshan.entity.RegionEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.shop.BrandEntity;
import com.foshan.entity.shop.CartItemEntity;
import com.foshan.entity.shop.CouponEntity;
import com.foshan.entity.shop.CouponSettingEntity;
import com.foshan.entity.shop.ExpressPackageEntity;
import com.foshan.entity.shop.ExpressPackageOrderItemEntity;
import com.foshan.entity.shop.MemberEntity;
import com.foshan.entity.shop.OrderPaymentEntity;
import com.foshan.entity.shop.OrderRefundEntity;
import com.foshan.entity.shop.ProductCategoryEntity;
import com.foshan.entity.shop.ProductEntity;
import com.foshan.entity.shop.ProductOrderEntity;
import com.foshan.entity.shop.ProductOrderItemEntity;
import com.foshan.entity.shop.ProductOrderTraceEntity;
import com.foshan.entity.shop.ProductSpecificationDetailEntity;
import com.foshan.entity.shop.ProductSpecificationEntity;
import com.foshan.entity.shop.PromotionEntity;
import com.foshan.entity.shop.SalesReturnEntity;
import com.foshan.entity.shop.SalesReturnOrderItemEntity;
import com.foshan.entity.shop.ShopColumnEntity;
import com.foshan.entity.shop.ShopUserEntity;
import com.foshan.entity.shop.StoreEntity;
import com.foshan.entity.shop.VerificaterEntity;
import com.foshan.form.ColumnForm;
import com.foshan.form.RegionForm;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.shop.LiveTicketsForm;
import com.foshan.form.shop.OperationDiaryForm;
import com.foshan.form.shop.Parameters;
import com.foshan.form.shop.ProductCategoryForm;
import com.foshan.form.shop.ProductFinancialAttributesForm;
import com.foshan.form.shop.ProductForm;
import com.foshan.form.shop.ProductLimitForm;
import com.foshan.form.shop.ProductPriceForm;
import com.foshan.form.shop.ProductSpecificationForm;
import com.foshan.form.shop.PromotionForm;
import com.foshan.form.shop.ShopColumnForm;
import com.foshan.form.shop.SpecialParameter;
import com.foshan.form.shop.Specifications;
import com.foshan.form.shop.StoreForm;
import com.foshan.form.shop.request.GetProductListReq;
import com.foshan.form.shop.request.GetSpecificationListReq;
import com.foshan.form.shop.request.LiveTicketsReq;
import com.foshan.form.shop.response.liveTicket.LiveTicketRes;
import com.foshan.form.shop.response.refund.OrderRefundApplyRes;
import com.foshan.model.permssion.AccountPrincipalModel;
import com.foshan.model.permssion.PrincipalModel;
import com.foshan.service.impl.GenericService;
import com.foshan.shop.promotion.CaculatePromotionResult;
import com.foshan.shop.promotion.PromotionFactory;
import com.foshan.shop.promotion.PromotionInstances;
import com.foshan.shop.promotion.PromotionItem;
import com.foshan.shop.promotion.StrategyValue;
import com.foshan.util.CodeUtil;
import com.foshan.util.DateUtil;
import com.foshan.util.DigestUtil;
import com.foshan.util.HttpClientUtil;
import com.foshan.util.ProductLimit;
import com.foshan.util.ShopContextInfo;
import com.foshan.util.SpringHandler;
import com.hazelcast.spring.cache.HazelcastCacheManager;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GenericShopService extends GenericService {

	@Autowired
	protected ShopContextInfo shopContextInfo;
    @Resource(name = "brandDao")
    protected IBrandDao brandDao;
    @Resource(name = "cartDao")
    protected ICartDao cartDao;
    @Resource(name = "cartItemDao")
    protected ICartItemDao cartItemDao;
    @Resource(name = "memberAddressDao")
    protected IMemberAddressDao memberAddressDao;
    @Resource(name = "memberDao")
    protected IMemberDao memberDao;
    @Resource(name = "productAppraiseDao")
    protected IProductAppraiseDao productAppraiseDao;
    @Resource(name = "productAppraiseItemDao")
    protected IProductAppraiseItemDao productAppraiseItemDao;
    @Resource(name = "productCategoryDao")
    protected IProductCategoryDao productCategoryDao;
    @Resource(name = "productDao")
    protected IProductDao productDao;
    @Resource(name = "productGroupDao")
    protected IProductGroupDao productGroupDao;
    @Resource(name = "productOrderDao")
    protected IProductOrderDao productOrderDao;
    @Resource(name = "productOrderItemDao")
    protected IProductOrderItemDao productOrderItemDao;
    @Resource(name = "productPriceDao")
    protected IProductPriceDao productPriceDao;
    @Resource(name = "shopColumnDao")
    protected IShopColumnDao shopColumnDao;
    @Resource(name = "storeDao")
    protected IStoreDao storeDao;
    @Resource(name = "shopPaymentRecordDao")
    protected IShopPaymentRecordDao shopPaymentRecordDao;
    @Resource(name = "orderPaymentDao")
    protected IOrderPaymentDao orderPaymentDao;
    @Resource(name = "productOrderTraceDao")
    protected IProductOrderTraceDao productOrderHistoryDao;
    @Resource(name = "productStockDao")
    protected IProductStockDao productStockDao;
    @Resource(name = "shopUserDao")
    protected IShopUserDao shopUserDao;
//    @Resource(name = "paymentPluginDao")
//    protected IPaymentPluginDao paymentPluginDao;
    @Resource(name = "orderRefundDao")
    protected IOrderRefundDao orderRefundDao;
    @Resource(name = "productFavoriteDao")
    protected IProductFavoriteDao productFavoriteDao;
    @Resource(name = "productOrderTraceDao")
    protected IProductOrderTraceDao productOrderTraceDao;
    @Resource(name = "logisticsDao")
    protected ILogisticsDao logisticsDao;
    @Resource(name = "salesReturnDao")
    protected ISalesReturnDao salesReturnDao;
    @Resource(name = "pickupDao")
    protected IPickupPointDao pickupDao;
    @Resource(name = "storePluginConfigDao")
    protected IStorePluginConfigDao storePluginConfigDao;
    @Resource(name = "pluginDao")
    protected IPluginDao pluginDao;
    @Resource(name = "productOrderInvoiceDao")
    protected IProductOrderInvoiceDao productOrderInvoiceDao;
    @Resource(name = "productSpecificationDetailDao")
    protected IProductSpecificationDetailDao productSpecificationDetailDao;
    @Resource(name = "productSpecificationDao")
    protected IProductSpecificationDao productSpecificationDao;
    @Resource(name = "courierCompanyDao")
    protected ICourierCompanyDao courierCompanyDao;
    @Resource(name = "courierFeeScheduleDao")
    protected ICourierFeeScheduleDao courierFeeScheduleDao;
    @Resource(name = "promotionDao")
    protected IPromotionDao promotionDao;
    @Resource(name = "buyerInformationDao")
    public IBuyerInformationDao buyerInformationDao;
    @Resource(name = "detailItemDao")
    public IDetailItemDao detailItemDao;
    @Resource(name = "invoiceDao")
    public IInvoiceDao invoiceDao;
    @Resource(name = "invoiceResultsDao")
    public IInvoiceResultsDao invoiceResultsDao;
//    @Resource(name = "sellerInformationDao")
//    public ISellerInformationDao sellerInformationDao;
    @Resource(name = "financialAccountingDao")
    public IFinancialAccountingDao financialAccountingDao;
    @Resource(name = "verificaterDao")
    public IVerificaterDao verificaterDao;
    @Resource(name = "numberOfProductsOnShelfVoDao")
    public INumberOfProductsOnShelfVoDao numberOfProductsOnShelfVoDao;
    @Resource(name = "couponSettingDao")
    public ICouponSettingDao couponSettingDao;
    @Resource(name = "couponDao")
    public ICouponDao couponDao;
    @Resource(name = "thirdPartyInterfaceDao")
    public IThirdPartyInterfaceDao thirdPartyInterfaceDao;
    @Resource(name = "inventoryVoDao")
    public IInventoryVoDao inventoryVoDao;
    @Resource(name = "companyInvoiceInfoDao")
    public ICompanyInvoiceInfoDao companyInvoiceInfoDao;
    @Resource(name = "contractDao")
    public IContractDao contractDao;
    @Resource(name = "totalPurchaseAmountVoDao")
    public ITotalPurchaseAmountVoDao totalPurchaseAmountVoDao;
    @Resource(name = "courierFeeScheduleItemDao")
    public ICourierFeeScheduleItemDao courierFeeScheduleItemDao;
    @Resource(name = "purchasingCompanyDao")
    public IPurchasingCompanyDao purchasingCompanyDao;
    @Resource(name = "memberInvoiceInfoDao")
    public IMemberInvoiceInfoDao memberInvoiceInfoDao;
    @Resource(name = "salesReturnOrderItemDao")
    public ISalesReturnOrderItemDao salesReturnOrderItemDao;
    @Resource(name = "expressPackageDao")
    public IExpressPackageDao expressPackageDao;
    @Resource(name = "expressPackageOrderItemDao")
    public IExpressPackageOrderItemDao expressPackageOrderItemDao;
    @Resource(name = "phoneLocationDao")
    public IPhoneLocationDao phoneLocationDao;
    @Resource(name = "productLimitDao")
    public IProductLimitDao productLimitDao;
    @Resource(name = "productSalesDao")
    public IProductSalesDao productSalesDao;
    @Resource(name = "productFinancialAttributesDao")
    public IProductFinancialAttributesDao productFinancialAttributesDao;
	@Resource
	private HazelcastCacheManager hazelcastCacheManager;

    @SuppressWarnings("unchecked")
    protected HashMap<String, Object> columnList(Object userObj, List<ColumnEntity> columnSet, Integer serviceId,
                                                 Integer columnId, Integer depth, Integer visitFlag, String startDate, String endDate, String regionCode,
                                                 String targetTypeList, Integer columnType, Integer columnState, Integer state) {
        HashMap<String, Object> columnList = new HashMap<String, Object>();
        Integer serviceVisits = 0;
        List<ShopColumnForm> columnFormList = new ArrayList<ShopColumnForm>();
        depth--;
        for (ColumnEntity o : columnSet) {
            ShopColumnForm columnForm = new ShopColumnForm();
            StringBuilder subSql = new StringBuilder("");
            if (null != userObj && (userObj instanceof PlatformUserEntity || userObj instanceof ShopUserEntity)) {
                subSql.append("select a.* from t_column a where a.parentColumnId=" + o.getId() + " order by a.orders");
            } else {
                subSql.append(
                                "select distinct a.* FROM t_column a INNER JOIN t_column_region b INNER JOIN t_region c ON "
                                        + "a.parentColumnId=" + o.getId()
                                        + (null != columnType ? " and a.columnType=" + columnType
                                        : " and a.columnType=" + EntityContext.COLUMN_TYPE_SHOP)
                                        //+ " AND a.columnState=" + EntityContext.RECORD_STATE_VALID
                                        + " AND a.id=b.columnId AND b.regionId=c.id AND (c.startRegionCode+0)<=(" + regionCode
                                        + "+0) AND (c.endRegionCode+0)>=(" + regionCode + "+0) ")
                        .append(StringUtils.isNotEmpty(targetTypeList) ? " and a.targetType in(" + targetTypeList + ") " : "")
                        .append(null != columnState ? " and a.columnState=" + columnState : "")
                        .append(" and a.state=" + (null != state ? state : EntityContext.RECORD_STATE_VALID))
                        .append(" AND cflag='S' order by a.orders");
            }
            //Set<ColumnEntity> columnSet1 = new HashSet<ColumnEntity>(columnDao.getListBySql(subSql.toString()));
            List<ColumnEntity> columnSet1 = columnDao.getListBySql(subSql.toString());

            if (o.getColumnState() == EntityContext.RECORD_STATE_VALID || (null != userObj
                    && (userObj instanceof PlatformUserEntity || userObj instanceof ShopUserEntity))) {
                columnForm.setColumnId(o.getId());
                columnForm.setColumnName(o.getColumnName());
                columnForm.setServiceId(serviceId);
                columnForm.setColumnCode(o.getColumnCode());
                columnForm.setColumnInfo(o.getColumnInfo());
                columnForm.setColumnType(o.getColumnType());
                columnForm.setParentColumnId(columnId);
                columnForm.setColumnInfo(o.getColumnInfo());
                columnForm.setColumnPath(o.getColumnPath());
                columnForm.setColumnImage(getAsset(o.getColumnImage()));
                columnForm.setColumnPhoneImage(getAsset(o.getColumnPhoneImage()));
                columnForm.setIsGlobal(o.getIsGlobal());
                columnForm.setMappingFolderId(o.getMappingFolderId());
                columnForm.setMappingFolderName(o.getMappingFolderName());
                columnForm.setOrders(o.getOrders());
                columnForm.setState(o.getState());
                o.getRegionList().forEach(b -> {
                    columnForm.getRegionList().add(new RegionForm(b.getId(), b.getRegionCode(), b.getRegionName(),
                            b.getStartRegionCode(), b.getEndRegionCode()));
                });
                try {
                    columnForm.setIsOperation((null != userObj && userObj instanceof ShopUserEntity
                            && ((ShopColumnEntity) o).getStoreList().contains(((ShopUserEntity) userObj).getStore())
                            && o.getColumnLevel() > 1) ? true
                            : (null != userObj && userObj instanceof PlatformUserEntity) ? true : false);
                } catch (Exception ex) {
                    columnForm.setIsOperation(false);
                }
                // 查询子栏目
                if (columnSet1.size() > 0 && depth >= 1) {
                    columnForm.getSubColumnList()
                            .addAll((List<ColumnForm>) columnList(userObj, columnSet1, serviceId, o.getId(), depth,
                                    visitFlag, startDate, endDate, regionCode, targetTypeList, columnType, columnState, state)
                                    .get("columnList"));
                }
                columnFormList.add(columnForm);
            }

            // 根据访问量统计开关统计当前栏目下的分组访问量，且访问量统计与查询深度无关
            if (visitFlag == EntityContext.COUNT_VISIT_ON) {
                columnForm.setTotalVisits(getColumnVisit(serviceId, o.getId(), startDate, endDate));
            }
        }
        columnFormList.sort(comparingInt(ColumnForm::getColumnId));
        columnList.put("columnList", columnFormList);
        columnList.put("serviceVisits", serviceVisits);
        return columnList;
    }

    /*
     * 根据categoryId获取CategoryPath
     */
    @Cacheable(value = "default", key = "#categoryPath")
    protected String getCategoryPath(ProductSpecificationEntity specification) {
        String categoryPath = "";
        // 首先根据产品规格的3级分类id获取分类，如果不存在则以此获取上级分类值
        ProductCategoryEntity category = productCategoryDao.get(null != specification.getLevel3Id()
                ? specification.getLevel3Id()
                : null != specification.getLevel2Id() ? specification.getLevel2Id() : specification.getLevel1Id());
        if (null != category) {
            categoryPath = category.getCategoryPath();
        }
        return categoryPath;
    }

    /*
     * 获取单独产品规格列表，不包含关联的产品信息，根据产品规格所属的商铺、品牌、区域及分类等条件进行筛选
     */
    protected HashMap<String, Object> getSingleSpecificationList(GetSpecificationListReq req, Object userObj) {
        HashMap<String, Object> hm = new HashMap<String, Object>();
        Page<ProductSpecificationEntity> page = new Page<ProductSpecificationEntity>();
        page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
        page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
        page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

        StringBuilder baseHql = new StringBuilder("select distinct a from ProductSpecificationEntity a ");

        /*
         * 根据产品规格关联的商铺、品牌及区域构建Hql的关联数据
         */
        baseHql.append(StringUtils.isNotEmpty(req.getBrandIdList()) ? "inner join a.brand c " : "")
                .append(null != req.getStoreId() ? "inner join a.store f " : "")
                .append((null != userObj && userObj instanceof MemberEntity) || null == userObj
                        ? "inner join a.regionList g "
                        : "")
                .append(" where");

        // 根据申请条件构造查询语句
        StringBuilder searchHql = new StringBuilder();

        searchHql.append(null != req.getSpecificationId() ? " and a.id=" + req.getSpecificationId() : "")
                .append(StringUtils.isNotEmpty(req.getBrandIdList()) ? " and c.id in(" + req.getBrandIdList() + ") "
                        : "")
                .append(null != req.getStoreId() ? " and f.id=" + req.getStoreId() : "").append(
                        (null != userObj && userObj instanceof MemberEntity) || null == userObj
                                ? " and (g.startRegionCode+0)<="
                                + (StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "1")
                                + " and (g.endRegionCode+0)>="
                                + (StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "1")
                                : "")
                .append(null != req.getProductType() ? " and a.productType=" + req.getProductType() : "");

        // 组合查询语句
        baseHql.append(
                baseHql.toString().endsWith("where") && searchHql.length() > 0 ? searchHql.toString().substring(4)
                        : "");

        // 根据申请的产品分类进行筛选
        if (null != req.getCategoryId()) {
            ProductCategoryEntity category = productCategoryDao.get(req.getCategoryId());

            baseHql.append(
                    baseHql.toString().endsWith("where")
                            ? (category.getCategoryLevel() == 3 ? " a.level3Id=" + req.getCategoryId()
                            : (category.getCategoryLevel() == 2 ? " a.level2Id=" + req.getCategoryId()
                            : (category.getCategoryLevel() == 1 ? " a.level1Id=" + req.getCategoryId()
                            : "")))
                            : (category.getCategoryLevel() == 3 ? " and a.level3Id=" + req.getCategoryId()
                            : (category.getCategoryLevel() == 2 ? " and a.level2Id=" + req.getCategoryId()
                            : (category.getCategoryLevel() == 1
                            ? " and a.level1Id=" + req.getCategoryId()
                            : ""))));
        }

        // 根据申请的审核状态及数据状态进行筛选
        baseHql.append(
                        null != req.getAuditState()
                                ? (baseHql.toString().endsWith("where") ? " a.auditState=" + req.getAuditState()
                                : " and a.auditState=" + req.getAuditState())
                                : "")
                .append(null != req.getState() ? (baseHql.toString().endsWith("where") ? " a.state=" + req.getState()
                        : " and a.state=" + req.getState()) : "")
                .append(StringUtils.isNotEmpty(req.getProductName())
                        ? (baseHql.toString().endsWith("where") ? " a.productName like('%" + req.getProductName() + "%'"
                        : " and a.productName like('%" + req.getProductName() + "%')")
                        : "");

        baseHql = new StringBuilder(
                baseHql.toString().endsWith("where") ? baseHql.substring(0, baseHql.length() - 5) : baseHql.toString());
        if (StringUtils.isNotEmpty(req.getOrderStr())) {
            String[] orderStrs = req.getOrderStr().split(",");
            StringBuilder orderHql = new StringBuilder(" order by ");
            for (String orderStr : orderStrs) {
                String s[] = orderStr.split("\\|");
                orderHql.append("a." + s[0] + " " + s[1] + ",");
            }
            baseHql.append(orderHql.substring(0, orderHql.length() - 1));
        }
        log.info("本次查询hql====>>>" + baseHql.toString());
        page = productSpecificationDao.queryPage(page, baseHql.toString());
        hm.put("specificationList", page.getResultList());
        hm.put("totalResult", page.getTotalCount());
        hm.put("pageSize", page.getPageSize());
        hm.put("currentPage", page.getCurrentPage());
        hm.put("total", page.getTotalPage());
        return hm;
    }

    /*
     * 获取产品分类列表，并根据产品规格的运营参数进行数据筛选
     */
    protected HashMap<String, Object> getSpecificationList(GetSpecificationListReq req, Object userObj) {
        HashMap<String, Object> hm = new HashMap<String, Object>();
        Page<ProductSpecificationEntity> page = new Page<ProductSpecificationEntity>();
        page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
        page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
        page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
        String isMarketableList = "";
        if (null != userObj && userObj instanceof MemberEntity) {
            MemberEntity member = (MemberEntity) userObj;
            isMarketableList = member.getType() == EntityContext.ACCOUNT_TYPE_PUBLIC_ACCOUNT  // 企业用户
                    ? "(" + EntityContext.UP_SHELF_STATE_UP_FOR_COMPANY + "," + EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL_AND_COMPANY
                    + "," + EntityContext.UP_SHELF_STATE_UP_FOR_POINTS_AND_COMPANY + ")"
                    : "(" + EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL + ","
                    + EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL_AND_COMPANY + EntityContext.UP_SHELF_STATE_UP_FOR_POINTS_AND_INDIVIDUAL + ")";
        } else if (null == userObj) {
            isMarketableList = "(" + EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL + ","
                    + EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL_AND_COMPANY+ ","+ EntityContext.UP_SHELF_STATE_UP_FOR_POINTS_AND_INDIVIDUAL + ")";
        } else {
            isMarketableList = StringUtils.isNotEmpty(req.getIsMarketableList())
                    ? (req.getIsMarketableList().startsWith("(") ? req.getIsMarketableList()
                    : "(" + req.getIsMarketableList() + ")")
                    : "";
        }
        StringBuilder baseHql = new StringBuilder(
                "select distinct a from ProductSpecificationEntity a inner join a.productList b ");

        // 根据申请条件构造对象关联
        baseHql.append(StringUtils.isNotEmpty(req.getBrandIdList()) ? "inner join a.brand c " : "")
                .append(null != req.getColumnId() ? "inner join b.productGroupList d inner join d.columnList e " : "")
                .append(null != req.getProductGroupId() && null == req.getColumnId()
                        ? "inner join b.productGroupList d "
                        : "")
                .append(null != req.getStoreId() ? "inner join a.store f " : "")
                .append((null != userObj && userObj instanceof MemberEntity) || null == userObj
                        ? "inner join a.visibleRegionList g "
                        : "")
                .append(null != req.getFinancialAttributesIsNull() && req.getFinancialAttributesIsNull() == EntityContext.FINANCIAL_ATTRIBUTES_IS_NULL ?
                        " inner join b.financialAttributesList g " : "")
                .append(" where");

        // 根据申请条件构造查询语句
        StringBuilder searchHql = new StringBuilder();

        searchHql
                .append(StringUtils.isNotEmpty(req.getBrandIdList()) ? " and c.id in(" + req.getBrandIdList() + ") "
                        : "")
                .append(null != req.getColumnId() && null == req.getProductGroupId()
                        ? " and e.id=" + req.getColumnId() + " "
                        : "")
                .append(null != req.getProductGroupId() && null != req.getColumnId()
                        ? " and e.id=" + req.getColumnId() + " and d.id=" + req.getProductGroupId() + " "
                        : "")
                .append(null != req.getProductGroupId() && null == req.getColumnId()
                        ? " and d.id=" + req.getProductGroupId() + " "
                        : "")
                .append(null != req.getStoreId() ? " and f.id=" + req.getStoreId() + " " : "").append(
                        (null != userObj && userObj instanceof MemberEntity) || null == userObj
                                ? " and (g.startRegionCode+0)<=" + req.getRegionCode() + " and (g.endRegionCode+0)>="
                                + req.getRegionCode()
                                : "")
                .append(StringUtils.isNotEmpty(isMarketableList) ? " and b.isMarketable in" + isMarketableList + " "
                        : (null != req.getIsMarketable() ? " and b.isMarketable ='" + req.getIsMarketable() + "' "
                        : ""))
                .append(StringUtils.isNotEmpty(req.getProductTypeList()) ? " and a.productType in (" + req.getProductTypeList() + ") "
                        : (null != req.getProductType() ? " and a.productType ='" + req.getProductType() + "' "
                        : ""))
                .append(null != req.getFinancialAttributesIsNull() && req.getFinancialAttributesIsNull() == EntityContext.FINANCIAL_ATTRIBUTES_IS_NULL ?
                        " and g is null" : "");

        // 组合查询语句
        baseHql.append(
                baseHql.toString().endsWith("where") && searchHql.length() > 0 ? searchHql.toString().substring(4)
                        : "");

        if (null != req.getCategoryId()) {
            ProductCategoryEntity category = productCategoryDao.get(req.getCategoryId());

            baseHql.append(
                    baseHql.toString().endsWith("where")
                            ? (category.getCategoryLevel() == 3 ? " a.level3Id=" + req.getCategoryId()
                            : (category.getCategoryLevel() == 2 ? " a.level2Id=" + req.getCategoryId()
                            : (category.getCategoryLevel() == 1 ? " a.level1Id=" + req.getCategoryId()
                            : "")))
                            : (category.getCategoryLevel() == 3 ? " and a.level3Id=" + req.getCategoryId()
                            : (category.getCategoryLevel() == 2 ? " and a.level2Id=" + req.getCategoryId()
                            : (category.getCategoryLevel() == 1
                            ? " and a.level1Id=" + req.getCategoryId()
                            : ""))));
        }

        baseHql.append(
                        null != req.getAuditState()
                                ? (baseHql.toString().endsWith("where") ? " a.auditState=" + req.getAuditState()
                                : " and a.auditState=" + req.getAuditState())
                                : "")
//				.append(StringUtils.isNotEmpty(isMarketableList)
//						? (baseHql.toString().endsWith("where") ? " b.isMarketable in" + isMarketableList
//								: " and b.isMarketable in" + isMarketableList)
//						: "")
                .append(null != req.getState()
                        ? (baseHql.toString().endsWith("where") ? " a.state=" + req.getState()
                        : " and a.state=" + req.getState())
                        : "")
                .append(null != req.getProductState()
                        ? (baseHql.toString().endsWith("where") ? " b.state=" + req.getProductState()
                        : " and b.state=" + req.getProductState())
                        : "")
                .append(StringUtils.isNotEmpty(req.getProductName())
                        ? (baseHql.toString().endsWith("where") ? " a.productName like('%" + req.getProductName() + "%'"
                        : " and a.productName like('%" + req.getProductName() + "%')")
                        : "");

        baseHql = new StringBuilder(
                baseHql.toString().endsWith("where") ? baseHql.substring(0, baseHql.length() - 5) : baseHql.toString());
        if (StringUtils.isNotEmpty(req.getOrderStr())) {
            String[] orderStrs = req.getOrderStr().split(",");
            StringBuilder orderHql = new StringBuilder(" order by ");
            for (String orderStr : orderStrs) {
                String s[] = orderStr.split("\\|");
                orderHql.append("b." + s[0] + " " + s[1] + ",");
            }
            baseHql.append(orderHql.substring(0, orderHql.length() - 1));
        }
        log.info("本次查询hql====>>>" + baseHql.toString());
        page = productSpecificationDao.queryPage(page, baseHql.toString());
        hm.put("specificationList", page.getResultList());
        hm.put("totalResult", page.getTotalCount());
        hm.put("pageSize", page.getPageSize());
        hm.put("currentPage", page.getCurrentPage());
        hm.put("total", page.getTotalPage());
        return hm;
    }

    /*
     * 根据查询条件获取Sku产品的列表，支持按品牌、类别、栏目、产品包、商铺以及相应的产品属性返回产品列表
     * 当发起请求的终端属于用户侧请求时，接口返回改用户所在区域的有效产品列表；
     *
     */
    protected HashMap<String, Object> getProductEntityList(GetProductListReq req, Object userObj) {
        HashMap<String, Object> hm = new HashMap<String, Object>();
        Page<ProductEntity> page = new Page<ProductEntity>();
        page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
        page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
        page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

        StringBuilder baseHql = new StringBuilder("select distinct ")
                .append("a from ProductEntity a inner join a.productSpecification b ");

        // 根据申请条件构造对象关联
        baseHql.append(StringUtils.isNotEmpty(req.getBrandIdList()) ? "inner join b.brand c " : "")
                .append("inner join a.productGroupList d inner join d.columnList e ")
                .append(null != req.getStoreId() ? "inner join b.store f "
                        : "")
                .append((null == req.getIsFavorite() || EntityContext.IS_NOT_FAVORITE == req.getIsFavorite())
                        ? ((null != userObj && userObj instanceof MemberEntity) || null == userObj
                        ? "inner join b.visibleRegionList g "
                        : "")
                        : "inner join a.memberList m ")
                .append(" where");

        // 根据申请条件构造查询语句
        StringBuilder searchHql = new StringBuilder();

        MemberEntity shiroMember = null != userObj && userObj instanceof MemberEntity ? (MemberEntity) userObj : null;
        searchHql
                .append(StringUtils.isNotEmpty(req.getBrandIdList()) ? " and c.id in(" + req.getBrandIdList() + ") "
                        : "")
                .append(null != req.getColumnId() && null == req.getProductGroupId()
                        ? " and e.id =" + req.getColumnId() + " "
                        : "")
                .append(null != req.getProductGroupId() && null != req.getColumnId()
                        ? " and e.id=" + req.getColumnId() + " and d.id=" + req.getProductGroupId() + " "
                        : "")
                .append(null != req.getProductGroupId() && null == req.getColumnId()
                        ? " and d.id=" + req.getProductGroupId() + " "
                        : "")
                .append(null != req.getStoreId() ? " and f.id=" + req.getStoreId() + " " : "")
                .append((null == req.getIsFavorite() || EntityContext.IS_NOT_FAVORITE == req.getIsFavorite())
                        ? ((null != userObj && userObj instanceof MemberEntity) || null == userObj
                        ? " and (g.startRegionCode+0)<="
                        + (StringUtils.isNotEmpty(req.getRegionCode())
                        ? req.getRegionCode() + (null != req.getOnlyRegionData()
                        && req.getOnlyRegionData() == 1
                        ? " and g.regionLevel<>1 "
                        : (null != req.getOnlyRegionData()
                        && req.getOnlyRegionData() == 2
                        ? " and g.regionLevel=1 "
                        : ""))
                        : "1")
                        + " and (g.endRegionCode+0)>="
                        + (StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "1")
                        : "")
                        : " and m.id=" + shiroMember.getId() + " ");

        // 组合查询语句
        baseHql.append(
                baseHql.toString().endsWith("where") && searchHql.length() > 0 ? searchHql.toString().substring(4)
                        : "");

        if (null != req.getCategoryId()) {
            ProductCategoryEntity category = productCategoryDao.get(req.getCategoryId());
            baseHql.append(
                    baseHql.toString().endsWith("where")
                            ? (category.getCategoryLevel() == 3 ? " b.level3Id=" + req.getCategoryId()
                            : (category.getCategoryLevel() == 2 ? " b.level2Id=" + req.getCategoryId()
                            : (category.getCategoryLevel() == 1 ? " b.level1Id=" + req.getCategoryId()
                            : "")))
                            : (category.getCategoryLevel() == 3 ? " and b.level3Id=" + req.getCategoryId()
                            : (category.getCategoryLevel() == 2 ? " and b.level2Id=" + req.getCategoryId()
                            : (category.getCategoryLevel() == 1
                            ? " and b.level1Id=" + req.getCategoryId()
                            : ""))));
        }

        baseHql.append(
                        null != req.getAuditState()
                                ? (baseHql.toString().endsWith("where") ? " b.auditState=" + req.getAuditState()
                                : " and b.auditState=" + req.getAuditState())
                                : "")
//				.append(null != req.getIsMarketable()
//						? (baseHql.toString().endsWith("where") ? " a.isMarketable=" + req.getIsMarketable()
//								: " and a.isMarketable=" + req.getIsMarketable())
//						: "")
                .append(null != req.getState() ? (baseHql.toString().endsWith("where") ? " b.state=" + req.getState()
                        : " and b.state=" + req.getState()) : "")
                .append(StringUtils.isNotEmpty(req.getProductName())
                        ? (baseHql.toString().endsWith("where") ? " b.productName like('%" + req.getProductName() + "%'"
                        : " and b.productName like('%" + req.getProductName() + "%')")
                        : "");
        if (null != userObj && userObj instanceof MemberEntity) {
            MemberEntity member = (MemberEntity) userObj;
            //企业账号
            String isMarketableList = member.getType().intValue() == EntityContext.ACCOUNT_TYPE_PUBLIC_ACCOUNT
                    .intValue()
                    ? "a.isMarketable in('" + EntityContext.UP_SHELF_STATE_UP_FOR_COMPANY + "','"
                    + EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL_AND_COMPANY + "','"
                    + EntityContext.UP_SHELF_STATE_UP_FOR_POINTS_AND_COMPANY + "','"
                    + EntityContext.UP_SHELF_STATE_UP_FOR_ALL + "')"
                    : "a.isMarketable in('" + EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL + "','"
                    + EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL_AND_COMPANY + "','"
                    + EntityContext.UP_SHELF_STATE_UP_FOR_POINTS + "','"
                    + EntityContext.UP_SHELF_STATE_UP_FOR_POINTS_AND_INDIVIDUAL + "','"
                    + EntityContext.UP_SHELF_STATE_UP_FOR_ALL + "')";
            baseHql.append(baseHql.toString().endsWith("where") ? isMarketableList : " and " + isMarketableList);

        } else if (null == userObj) {
            String isMarketableList = "a.isMarketable in('" + EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL + "','"
                    + EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL_AND_COMPANY + "','"
                    + EntityContext.UP_SHELF_STATE_UP_FOR_POINTS + "','"
                    + EntityContext.UP_SHELF_STATE_UP_FOR_POINTS_AND_INDIVIDUAL + "','"
                    + EntityContext.UP_SHELF_STATE_UP_FOR_POINTS_AND_COMPANY + "','"
                    + EntityContext.UP_SHELF_STATE_UP_FOR_ALL + "')";
            baseHql.append(baseHql.toString().endsWith("where") ? isMarketableList : " and " + isMarketableList);
        } else {
            baseHql.append(
                    null != req.getIsMarketable()
                            ? (baseHql.toString().endsWith("where") ? " a.isMarketable=" + req.getIsMarketable()
                            : " and a.isMarketable=" + req.getIsMarketable())
                            : "");
        }
        // 根据申请的排序字段要求进行结果排序

        String[] orderStrs = req.getOrderStr().split(",");
        StringBuilder orderHql = new StringBuilder(" order by ");
        for (String orderStr : orderStrs) {
            String s[] = orderStr.split("\\|");
            orderHql.append("a." + s[0] + " " + s[1] + ",");
        }
        baseHql.append(orderHql.substring(0, orderHql.length() - 1));
        log.info("本次查询hql====>>>" + baseHql.toString());

        page = productDao.queryPage(page, baseHql.toString());
        hm.put("productList", page.getResultList());
        hm.put("totalResult", page.getTotalCount());
        hm.put("pageSize", page.getPageSize());
        hm.put("currentPage", page.getCurrentPage());
        hm.put("total", page.getTotalPage());
        return hm;
    }

    /*
     * 获取产品价格（对象） 根据产品规格Id和产品的SKU属性索引以及所需要的价格类别来获取产品价格 如果没有获取的产品价格记录，则返回该产品的默认价格
     */
    @SuppressWarnings("unchecked")
    protected ProductPriceForm getProductPrice(Integer specificationId, String specificationIndex, Integer productId,
                                               Integer priceType) {
        ProductPriceForm priceForm = new ProductPriceForm();

        SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
        Session session = sessionFactory.openSession();
        // Transaction tx = session.beginTransaction();
        StringBuilder sql = new StringBuilder();
        // Integer type = isEnterpriseUser() ? 2 : 0;

        // 查询语句返回字段：产品Id、产品规格Id、sku属性索引、产品默认价格、价格单位、价格Id、价格类型、销售价格、活动价格
        // 根据产品规格ID和产品id及产品所在栏目Id，获取产品价格
//		if (null != productId) {
//			sql.append("select bb.* from (select a.id,a.specificationId,a.specificationIndex,"
//					+ "a.productPrice,c.id AS priceId,c.salesPrice,c.promotionPrice,c.priceUnit,c.priceType,c.enableTime,"
//					+ "c.disableTime,(TIMESTAMPDIFF(SECOND,c.enableTime,'" + nowTime + "')+TIMESTAMPDIFF(SECOND," + "'"
//					+ nowTime + "',c.disableTime)) AS aa , a.enterpriseProductPrice from t_product a,t_product_specification b,t_product_price c"
//					+ " where a.specificationId = b.id and c.productId = a.id and a.id=" + productId
//					+ " and c.state=1 and" + (priceType != null ?  (" c.priceType = " + priceType +" and") : "") + " c.enableTime<='" + nowTime + "'" + " and c.disableTime>='" + nowTime
//					+ "') bb where bb.aa=(select Min(TIMESTAMPDIFF(SECOND,c.enableTime," + "'" + nowTime
//					+ "')+TIMESTAMPDIFF(SECOND,'" + nowTime + "',c.disableTime)) FROM t_product a,"
//					+ "t_product_specification b,t_product_price c WHERE a.specificationId = b.id AND c.productId = a.id "
//					+ "AND a.id = " + productId + " AND c.state = 1" + (priceType != null ?  (" AND c.priceType = " + priceType) : "") + " AND c.enableTime<='" + nowTime
//					+ "' AND c.disableTime>='" + nowTime + "')");
//			// 根据sku产品属性索引，产品规格ID及所在栏目ID，获取产品价格
//		} else {
//			sql.append("select bb.* from (select a.id,a.specificationId,a.specificationIndex,"
//					+ "a.productPrice,c.id AS priceId,c.salesPrice,c.promotionPrice,c.priceUnit,c.priceType,c.enableTime,"
//					+ "c.disableTime,(TIMESTAMPDIFF(SECOND,c.enableTime,'" + nowTime + "')+TIMESTAMPDIFF(SECOND," + "'"
//					+ nowTime + "',c.disableTime)) AS aa ,a.enterpriseProductPrice from t_product a,t_product_specification b,t_product_price c"
//					+ " where a.specificationId = b.id" + (priceType != null ?  (" and c.priceType = " + priceType) : "") + " and c.productId = a.id and a.specificationId=" + specificationId
//					+ "" + " and a.specificationIndex='" + specificationIndex + "' and c.state=1 and c.enableTime<='"
//					+ nowTime + "'" + " and c.disableTime>='" + nowTime
//					+ "') bb where bb.aa=(select Min(TIMESTAMPDIFF(SECOND,c.enableTime," + "'" + nowTime
//					+ "')+TIMESTAMPDIFF(SECOND,'" + nowTime + "',c.disableTime)) FROM t_product a,"
//					+ "t_product_specification b,t_product_price c WHERE a.specificationId = b.id AND c.productId = a.id "
//					+ "AND a.specificationId=" + specificationId + " and a.specificationIndex='" + specificationIndex + "'" 
//					+ (priceType != null ?  (" and c.priceType = " + priceType) : "") + " AND c.state = 1 AND c.enableTime<='" + nowTime + "' AND c.disableTime>='" + nowTime + "')");
//		}

        if (null != productId) {
            sql.append("select a.id,a.specificationId,a.specificationIndex,"
                    + "a.productPrice,c.id AS priceId,c.salesPrice,c.promotionPrice,c.priceUnit,c.priceType,c.enableTime,"
                    + "c.disableTime, a.enterpriseProductPrice, c.pointsPoolId from t_product a,t_product_specification b,t_product_price c"
                    + " where a.specificationId = b.id and c.productId = a.id and a.id=" + productId + " and c.state=1 "
                    + (priceType != null ? ("and c.priceType = " + priceType + "") : ""));
            // 根据sku产品属性索引，产品规格ID及所在栏目ID，获取产品价格
        } else {
            sql.append("select a.id,a.specificationId,a.specificationIndex,"
                    + "a.productPrice,c.id AS priceId,c.salesPrice,c.promotionPrice,c.priceUnit,c.priceType,c.enableTime,"
                    + "c.disableTime,a.enterpriseProductPrice, c.pointsPoolId from t_product a,t_product_specification b,t_product_price c"
                    + " where a.specificationId = b.id" + (priceType != null ? (" and c.priceType = " + priceType) : "")
                    + " and c.productId = a.id and a.specificationId=" + specificationId + ""
                    + " and a.specificationIndex='" + specificationIndex + "' and c.state=1");

        }

        List<Object[]> priceList = (List<Object[]>) session.createSQLQuery(sql.toString()).list();
        Object[] price = null;
        Long minSpan = Long.MAX_VALUE;
        // 获取接口调用的当前时间
        Date nowTime = new Date();
        // 获取区域最少且最接近当前时间的价格
        if (priceList.size() > 0) {
            if (priceList.size() == 1) {
                Date enableTime = new Date(((Timestamp) priceList.get(0)[9]).getTime());
                Date disableTime = new Date(((Timestamp) priceList.get(0)[10]).getTime());
                if (nowTime.after(enableTime) && nowTime.before(disableTime)) {
                    price = priceList.get(0);
                }
            } else {
                for (Object[] row : priceList) {
                    if (row[9] == null || row[10] == null) {
                        price = row;
                        break;
                    }
                    Date enableTime = new Date(((Timestamp) row[9]).getTime());
                    Date disableTime = new Date(((Timestamp) row[10]).getTime());
                    if (nowTime.after(enableTime) && nowTime.before(disableTime)) {
                        Long curSpan = DateUtil.subDate(nowTime, enableTime) + DateUtil.subDate(disableTime, nowTime);
                        if (minSpan >= curSpan) {
                            minSpan = curSpan;
                            price = row;
                        }
                    }
                }
            }
        }

        if (null != price) {
            priceForm.setProductId((Integer) price[0]);
            priceForm.setSpecificationId((Integer) price[1]);
            priceForm.setSpecificationIndex((String) price[2]);
            priceForm.setProductPrice(((BigDecimal) price[3]));
            priceForm.setPriceId(null != price[4] ? (Integer) price[4] : null);
            priceForm.setSalesPrice(null != price[5] ? ((BigDecimal) price[5]) : null);
            priceForm.setPromotionPrice(null != price[6] ? ((BigDecimal) price[6]) : null);
            priceForm.setPriceUnit((String) price[7]);
            priceForm.setPriceType(null != price[8] ? (Integer) price[8] : null);
            priceForm.setEnableTime(null != price[9] ? DateUtil.formatLongFormat((Timestamp) price[9]) : null);
            priceForm.setDisableTime(null != price[10] ? DateUtil.formatLongFormat((Timestamp) price[10]) : null);
            priceForm.setEnterpriseProductPrice(null != price[11] ? ((BigDecimal) price[11]) : null);
            priceForm.setPointsPoolId(null != price[12] ? (Integer) price[12] : null);

        }
        // tx.commit();
        if (session != null) {
            session.close();
        }

        return priceForm;
    }

    /*
     * 获取产品价格（值）
     */
    protected BigDecimal getProductPriceValue(Integer specificationId, String specificationIndex, Integer productId,
                                              Integer priceType) {
        // 获取产品价格对象
        ProductPriceForm price = getProductPrice(specificationId, specificationIndex, productId, priceType);
        log.info("get product price result: priceType {},priceForm {}", priceType, price.toString());
        BigDecimal productPrice = null;
        if (priceType == EntityContext.PRODUCT_PRICE_TYPE_NORMAL) {
            // 如果价格对象有活动价格，就返回活动价格，否则返回产品的销售价格，如果产品销售价格也不存在，则返回产品默认价格。
            productPrice = null != price.getPromotionPrice() ? price.getPromotionPrice()
                    : (null != price.getSalesPrice() ? price.getSalesPrice() : price.getProductPrice());
        } else if (priceType == EntityContext.PRODUCT_PRICE_TYPE_COMPANY) {
            // 如果价格对象有企业购活动价格，否则返回产品的企业购的销售价格，如果产品销售价格也不存，则返回产品企业购默认价格。
            productPrice = null != price.getPromotionPrice() ? price.getPromotionPrice()
                    : (null != price.getSalesPrice() ? price.getSalesPrice() : price.getEnterpriseProductPrice());

        }   else if (priceType == EntityContext.PRODUCT_PRICE_TYPE_POINTS) {
            productPrice = price.getSalesPrice();
        }
        return productPrice;
    }

    /*
     * 根据产品规格的模版json文件，转换成产品属性对象列表
     */
    protected ArrayList<SpecialParameter> specialTemplateToList(String specialTemplate) {
        ArrayList<SpecialParameter> specialParameterList = new ArrayList<SpecialParameter>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            // 根据SPU属性模版获取产品专属属性列表
            SpecialParameter[] specialParameters = mapper.readValue(specialTemplate, SpecialParameter[].class);
            for (SpecialParameter special : specialParameters) {
                specialParameterList.add(special);
            }
        } catch (Exception ex) {
        	log.error("specialTemplateToList方法出错！！！" + ex.getMessage());
        }
        return specialParameterList;
    }

    @SuppressWarnings("unchecked")
    protected ArrayList<Specifications> specificationsToList(String specifications) {
        ArrayList<Specifications> specificationsList = new ArrayList<Specifications>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            specificationsList = mapper.readValue(specifications, ArrayList.class);
        } catch (Exception ex) {
        	log.error("specificationsToList方法出错！！！" + ex.getMessage());
        }
        return specificationsList;
    }

    protected ArrayList<SpecialParameter> specificationsToTempleteList(String specifications) {
        ArrayList<SpecialParameter> templateList = new ArrayList<SpecialParameter>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            for (Specifications specification : (Specifications[]) mapper.readValue(specifications,
                    Specifications[].class)) {
                for (Parameters para : specification.getParameters()) {
                    if (null != para.getGlobal() && para.getGlobal() == 0) {
                        SpecialParameter special = new SpecialParameter();
                        special.setName(para.getName());
                        Arrays.parallelSort(para.getOptions());
                        special.setOptions(para.getOptions());
                        templateList.add(special);
                    }
                }
            }
        } catch (Exception ex) {
        	log.error("specificationsToTempleteList方法出错！！！" + ex.getMessage());
        }
        return templateList;
    }

    /*
     * 根据选择产品规格(SPU)生成相应的产品(SKU)笛卡尔积列表
     */
    @SuppressWarnings("unchecked")
    protected static List<LinkedHashMap<String, String>> getSpecificationSkuParameterList(
            List<SpecialParameter> specialParameterList) throws JsonParseException, JsonMappingException, IOException {
        ObjectMapper mapper = new ObjectMapper();
        List<LinkedHashMap<String, String>> paraList = new ArrayList<LinkedHashMap<String, String>>();
        for (int i = 0; i < specialParameterList.size(); i++) {
            SpecialParameter sp = specialParameterList.get(i);
            int hmListSize = paraList.size();
            if (i > 0) {
                /*
                 * 根据队列专有属性数量循环构造属性列表
                 */
                for (int idx = 0; idx < hmListSize; idx++) {
                    LinkedHashMap<String, String> o = paraList.get(0);
                    for (int j = 0; j < sp.getOptions().length; j++) {
                        for (String key : o.keySet()) {
                            LinkedHashMap<String, String> hm = new LinkedHashMap<String, String>();
                            LinkedHashMap<String, String> temp = mapper.readValue(o.get(key), LinkedHashMap.class);
                            temp.put(sp.getName(), sp.getOptions()[j]);
                            hm.put(key + j + "_", mapper.writeValueAsString(temp));
                            paraList.add(hm);
                        }
                    }
                    paraList.remove(o);
                }
            } else {
                /*
                 * 初始构造专有属性列表
                 */
                for (int j = 0; j < sp.getOptions().length; j++) {
                    LinkedHashMap<String, String> hm = new LinkedHashMap<String, String>();
                    LinkedHashMap<String, String> temp = new LinkedHashMap<String, String>();
                    temp.put(sp.getName(), sp.getOptions()[j]);
                    hm.put(j + "_", mapper.writeValueAsString(temp));
                    paraList.add(hm);
                }
            }

        }

        return paraList;

    }

    protected ProductSpecificationForm getProductSpecificationForm(ProductSpecificationEntity specification,
                                                                   Boolean auditFlag, Boolean specifitionsFlag, Boolean templeteFlag) {
        return getProductSpecificationForm(specification, auditFlag, specifitionsFlag, templeteFlag,
                EntityContext.PRODUCT_LIST_DETAIL_FLAG_ALL);
    }

    protected ProductSpecificationForm getProductSpecificationForm(ProductSpecificationEntity specification,
                                                                   Boolean auditFlag, Boolean specifitionsFlag, Boolean templeteFlag, Integer needDetail) {

        // 构造返回产品规格对象
        ProductSpecificationForm specificationForm = new ProductSpecificationForm();
        if (EntityContext.PRODUCT_LIST_DETAIL_FLAG_MOBILE == needDetail) {
            // 返回移动互联网端的字段
            if (auditFlag) {
                specificationForm.setAllowReturn(specification.getAllowReturn());
            }
            specificationForm
                    .setProductType(null != specification.getProductType() ? specification.getProductType() : 0);
            specificationForm.setSpecificationId(specification.getId());
            specificationForm.setCategoryPath(getCategoryPath(specification));
            specificationForm.setIsDelivery(specification.getIsDelivery());
            specificationForm.setIsCourier(specification.getIsCourier());
            specificationForm.setIsGlobal(specification.getIsGlobal());
            specificationForm.setIsSelfSupport(specification.getIsSelfSupport());
            specificationForm.setIsTop(specification.getIsTop());
            specificationForm.setOrderFlowType(specification.getOrderFlowType());
            specificationForm.setPickupSupport(specification.getPickupSupport());
            specificationForm.setProductName(specification.getProductName());
            specificationForm.setProductSubName(specification.getProductSubName());
//			specificationForm.setDiary(StringUtils.isNotEmpty(specification.getDiary()) ? specification.getDiary():"");
            // specificationForm.setSkuTotalCount(specification.getProductList().size());

//			if(specification.getIsCourier() == EntityContext.PRODUCT_IS_NEED_COURIER_NEED && specification.getCourierFeeSchedule() != null) {
//				specificationForm.setCourierFeeScheduleId(specification.getCourierFeeSchedule().getId());
//				specificationForm.setCourierFeeScheduleName(specification.getCourierFeeSchedule().getName());
//			}
            specificationForm
                    .setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(specification.getCreateTime()));

            specificationForm.setAllowReturn(specification.getAllowReturn());

        } else if (EntityContext.PRODUCT_LIST_DETAIL_FLAG_TV == needDetail) {
            // 返回TV端列表
            if (auditFlag) {
                specificationForm.setAllowReturn(specification.getAllowReturn());
            }
            specificationForm
                    .setProductType(null != specification.getProductType() ? specification.getProductType() : 0);
            specificationForm.setSpecificationId(specification.getId());
            specificationForm.setCategoryPath(getCategoryPath(specification));
            specificationForm.setIsCourier(specification.getIsCourier());
            specificationForm.setProductName(specification.getProductName());
            specificationForm.setProductSubName(specification.getProductSubName());
            specificationForm.setRealVideo(specification.getRealVideo());
//			specificationForm.setDiary(StringUtils.isNotEmpty(specification.getDiary()) ? specification.getDiary():"");
            specificationForm.setSkuTotalCount(specification.getProductList().size());
            specificationForm.setAssetCode(specification.getAssetCode());
            specificationForm
                    .setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(specification.getCreateTime()));

        } else {
            // 返回全部字段
            // 获取产品规格关联对象
            ProductSpecificationDetailEntity detail = specification.getDetail();

            if (auditFlag) {
                specificationForm.setAllowReturn(specification.getAllowReturn());
                specificationForm.setAuditReason(specification.getAuditReason());
                specificationForm.setAuditState(specification.getAuditState());
                specificationForm.setAuditTime(
                        null != specification.getAuditTime() ? DateUtil.formatLongFormat(specification.getAuditTime())
                                : "");
            }

            if (specifitionsFlag) {
                specificationForm.setSpecifications(specificationsToList(detail.getSpecifications()));
            }

            if (templeteFlag) {
                specificationForm.setSpecialTemplate(specialTemplateToList(detail.getSpecialTemplate()));
            }

            specificationForm
                    .setProductType(null != specification.getProductType() ? specification.getProductType() : 0);
            specificationForm.setSpecificationId(specification.getId());
            specificationForm.setCategoryPath(getCategoryPath(specification));
            specificationForm.setIsDelivery(specification.getIsDelivery());
            specificationForm.setIsCourier(specification.getIsCourier());
            specificationForm.setIsGlobal(specification.getIsGlobal());
            specificationForm.setIsSelfSupport(specification.getIsSelfSupport());
            specificationForm.setIsTop(specification.getIsTop());
            specificationForm.setLevel1Id(specification.getLevel1Id());
            specificationForm.setLevel2Id(specification.getLevel2Id());
            specificationForm.setLevel3Id(specification.getLevel3Id());
            specificationForm.setOrderFlowType(specification.getOrderFlowType());
            specificationForm.setMobileProductInfo(detail.getMobileProductInfo());
            specificationForm.setPackingList(detail.getPackingList());
            specificationForm.setPickupSupport(specification.getPickupSupport());
            specificationForm.setProductInfo(detail.getProductInfo());
            specificationForm.setProductName(specification.getProductName());
            specificationForm.setProductSubName(specification.getProductSubName());
            specificationForm.setRealVideo(specification.getRealVideo());
            specificationForm.setReviewerId(specification.getReviewerId());
            specificationForm
                    .setDiary(StringUtils.isNotEmpty(specification.getDiary()) ? specification.getDiary() : "");
            specificationForm.setSkuTotalCount(specification.getProductList().size());
            specificationForm.setAssetCode(specification.getAssetCode());
            specificationForm.setSalesService(detail.getSalesService());
            if (specification.getIsCourier() == EntityContext.PRODUCT_IS_NEED_COURIER_NEED
                    && specification.getCourierFeeSchedule() != null) {
                specificationForm.setCourierFeeScheduleId(specification.getCourierFeeSchedule().getId());
                specificationForm.setCourierFeeScheduleName(specification.getCourierFeeSchedule().getName());
            }
            specificationForm.setTaxRate(specification.getTaxRate());
            specificationForm.setCommodityClassificationCode(specification.getCommodityClassificationCode());
            specificationForm.setPackingList(detail.getPackingList());
            specificationForm.setSalesService(detail.getSalesService());
            specificationForm
                    .setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(specification.getCreateTime()));

            specificationForm.setAllowReturn(specification.getAllowReturn());

            BrandEntity brand = specification.getBrand();
            StoreEntity store = specification.getStore();
            specificationForm.setStoreId(store.getId());
            specificationForm.setStoreName(store.getStoreName());
            specificationForm.setBrandId(brand.getId());
            specificationForm.setBrandName(brand.getBrandName());
            specificationForm.setServicePhone(StringUtils.isNotEmpty(specification.getStore().getServicePhone())
                    ? specification.getStore().getServicePhone()
                    : "");
            specificationForm.setStoreAddress(StringUtils.isNotEmpty(specification.getStore().getStoreAddress())
                    ? specification.getStore().getStoreAddress()
                    : "");
            specification.getRegionList().forEach(b -> {
                specificationForm.getRegionList().add(new RegionForm(b.getId(), b.getRegionCode(), b.getRegionName(),
                        b.getStartRegionCode(), b.getEndRegionCode()));
            });
            specification.getVisibleRegionList().forEach(b -> {
                specificationForm.getVisibleRegionList().add(new RegionForm(b.getId(), b.getRegionCode(),
                        b.getRegionName(), b.getStartRegionCode(), b.getEndRegionCode()));
            });

            specificationForm.setSkuList(getSkuList(null, null, specification, true, null));

        }

        return specificationForm;
    }

    protected ProductForm getProductForm(Integer columnId, Integer productGroupId,
                                         ProductSpecificationEntity specification, ProductEntity product) {
        return getProductForm(columnId, productGroupId, specification, product,
                EntityContext.PRODUCT_LIST_DETAIL_FLAG_ALL);
    }

    @SuppressWarnings("unchecked")
    protected ProductForm getProductForm(Integer columnId, Integer productGroupId,
                                         ProductSpecificationEntity specification, ProductEntity product, Integer needDetail) {
        ProductForm productForm = new ProductForm();
        // Date d4 = new Date();
        if (EntityContext.PRODUCT_LIST_DETAIL_FLAG_MOBILE == needDetail) {
            ObjectMapper mapper = new ObjectMapper();
            productForm.setPayType(product.getPayType());
            productForm.setBeginSale(product.getBeginSale());
            productForm.setIsMarketable(product.getIsMarketable());
            productForm.setIsPromotion(product.getIsPromotion());
            productForm.setMappingThirdCode(product.getMappingThirdCode());
            productForm.setOrders(product.getOrders());
            productForm.setProductCode(product.getProductCode());
            productForm.setSpecificationIndex(product.getSpecificationIndex());
            productForm.setProductMemo(product.getProductMemo());
            productForm.setSalesNumber(product.getSalesNumber()
                    + (ProductLimit.sales.containsKey(product.getId()) ? ProductLimit.sales.get(product.getId()) : 0));
            productForm.setProductId(product.getId());
            productForm.setColumnId(columnId);
            productForm.setState(product.getState());
            productForm.setProductGroupId(productGroupId);
            productForm.setSpecificationId(null != specification ? specification.getId() : null);
            productForm.setProductPhoneImage(getAsset(product.getProductPhoneImage(), needDetail));
            productForm.setProductImage(getAsset(product.getProductImage(), needDetail));
            productForm.setProductCode(product.getProductCode());
            productForm.setCycle(product.getCycle());
            productForm.setPeriodUnit(product.getPeriodUnit());

            ProductPriceForm price = null;
            Object obj = getPrincipal(false);

            if (obj instanceof MemberEntity) {
                Integer type = isEnterpriseMember(obj) ? EntityContext.PRODUCT_PRICE_TYPE_COMPANY
                        : EntityContext.PRODUCT_PRICE_TYPE_NORMAL;
                price = getProductPrice(null, null, product.getId(), type);
                if (type == EntityContext.PRODUCT_PRICE_TYPE_COMPANY) {
                    productForm.setSalesPrice(null != price.getSalesPrice() ? price.getSalesPrice()
                            : product.getEnterpriseProductPrice());
                } else {
                    productForm.setSalesPrice(
                            null != price.getSalesPrice() ? price.getSalesPrice() : product.getProductPrice());
                }
                productForm.setPromotionPrice(price.getPromotionPrice());
            } else if (obj instanceof ShopUserEntity) {

                price = getProductPrice(null, null, product.getId(), EntityContext.PRODUCT_PRICE_TYPE_NORMAL);
                productForm.setSalesPrice(price.getSalesPrice());
                productForm.setPromotionPrice(price.getPromotionPrice());
                if (isEnterpriseUser(obj)) {
                    price = getProductPrice(null, null, product.getId(), EntityContext.PRODUCT_PRICE_TYPE_COMPANY);
                    productForm.setEnterpriseSalesPrice(price.getSalesPrice());
                    productForm.setEnterprisePromotionPrice(price.getPromotionPrice());
                }
            } else {
                price = getProductPrice(null, null, product.getId(), EntityContext.PRODUCT_PRICE_TYPE_NORMAL);
                productForm.setSalesPrice(price.getSalesPrice());
                productForm.setPromotionPrice(price.getPromotionPrice());

                price = getProductPrice(null, null, product.getId(), EntityContext.PRODUCT_PRICE_TYPE_COMPANY);
                productForm.setEnterpriseSalesPrice(price.getSalesPrice());
                productForm.setEnterprisePromotionPrice(price.getPromotionPrice());
            }
            productForm.setProductPrice(product.getProductPrice());
            productForm.setEnterpriseProductPrice(product.getEnterpriseProductPrice());
            productForm.setPriceUnit(product.getPriceUnit());
            productForm.setInventoryAmount(ProductLimit.limit.containsKey(productForm.getSpecificationId())
                    && ProductLimit.limit.get(productForm.getSpecificationId()).containsKey(product.getId())
                    ? ProductLimit.limit.get(specification.getId()).get(product.getId())
                    .getProductInventoryAmount()
                    : product.getInventoryAmount());

            //有上架积分时，显示上架积分价格
            if (product.getIsMarketable() >= EntityContext.UP_SHELF_STATE_UP_FOR_POINTS && product.getIsMarketable() <= EntityContext.UP_SHELF_STATE_UP_FOR_ALL) {
                ProductPriceForm pointsPrice = getProductPrice(null, null, product.getId(), EntityContext.PRODUCT_PRICE_TYPE_POINTS);
                if (null != pointsPrice.getPointsPoolId()) {
                    productForm.setPointsPrice(pointsPrice.getSalesPrice());
                    productForm.setPointsPriceUnit(pointsPrice.getPriceUnit().replace("元", "分"));
                    productForm.setPricePointsPoolId(pointsPrice.getPointsPoolId());
                    productForm.setPricePointsPoolName(pointsPoolDao.get(pointsPrice.getPointsPoolId()).getPoolName());
                    if (obj instanceof MemberEntity) {
                        List<PointsMemberAccountEntity> pointsMemberAccounts = pointsMemberAccountDao.getListByHql("select p from " +
                                "PointsMemberAccountEntity p where p.account.id = ?", ((MemberEntity) obj).getId());

                        if (pointsMemberAccounts.size() > 0) {
                            pointsMemberAccounts.forEach(b -> {
                                if (null != b.getPointsPool() && b.getPointsPool().getId().equals(pointsPrice.getPointsPoolId())) {
                                    productForm.setAvailablePoints(b.getActualTotalPoints());
                                }
                            });
                        }
                        productForm.setAvailablePoints(null != productForm.getAvailablePoints() ? productForm.getAvailablePoints() : 0);

                    }
                }
            }


            List<PromotionForm> promotionFormList = new ArrayList<PromotionForm>();
            Set<PromotionEntity> allValidPromotions = product.getAllValidPromotions();
            for (PromotionEntity promotionEntity : allValidPromotions) {
                promotionFormList.add(PromotionForm.fromPromotionEntity(promotionEntity, getPrincipal(false)));
            }

            ArrayList<String> couponTipsArray = new ArrayList<String>();
            Set<CouponSettingEntity> allValidCouponSettings = product.getAllValidCouponSettings();
            for (CouponSettingEntity couponSettingEntity : allValidCouponSettings) {
                couponTipsArray.add(couponSettingEntity.getTips());
            }
            productForm.setCouponTips(StringUtils.join(couponTipsArray, ","));
            productForm.setPromotions(promotionFormList);

            try {
                productForm.setSkuSpecification(mapper.readValue(product.getSkuSpecification(), LinkedHashMap.class));
            } catch (Exception ex) {
            	log.error(ex.getMessage());
            }
        } else if (EntityContext.PRODUCT_LIST_DETAIL_FLAG_TV == needDetail) {
            // 返回TV端列表
            ObjectMapper mapper = new ObjectMapper();
            productForm.setPayType(product.getPayType());

            productForm.setOrders(product.getOrders());
            productForm.setProductCode(product.getProductCode());
            productForm.setSalesNumber(product.getSalesNumber());
            productForm.setProductId(product.getId());
            productForm.setColumnId(columnId);
            productForm.setProductGroupId(productGroupId);
            productForm.setSpecificationId(null != specification ? specification.getId() : null);
            productForm.setProductCode(product.getProductCode());
            productForm.setCycle(product.getCycle());
            productForm.setPeriodUnit(product.getPeriodUnit());
            ProductPriceForm price = null;
            Object obj = getPrincipal(false);

            if (obj instanceof MemberEntity) {
                Integer type = isEnterpriseMember(obj) ? EntityContext.PRODUCT_PRICE_TYPE_COMPANY
                        : EntityContext.PRODUCT_PRICE_TYPE_NORMAL;
                price = getProductPrice(null, null, product.getId(), type);
                if (type == EntityContext.PRODUCT_PRICE_TYPE_COMPANY) {
                    productForm.setSalesPrice(null != price.getSalesPrice() ? price.getSalesPrice()
                            : product.getEnterpriseProductPrice());
                } else {
                    productForm.setSalesPrice(
                            null != price.getSalesPrice() ? price.getSalesPrice() : product.getProductPrice());
                }
                productForm.setPromotionPrice(price.getPromotionPrice());

            } else if (obj instanceof ShopUserEntity) {

                price = getProductPrice(null, null, product.getId(), EntityContext.PRODUCT_PRICE_TYPE_NORMAL);
                productForm.setSalesPrice(price.getSalesPrice());
                productForm.setPromotionPrice(price.getPromotionPrice());
                if (isEnterpriseUser(obj)) {
                    price = getProductPrice(null, null, product.getId(), EntityContext.PRODUCT_PRICE_TYPE_COMPANY);
                    productForm.setEnterpriseSalesPrice(price.getSalesPrice());
                    productForm.setEnterprisePromotionPrice(price.getPromotionPrice());
                }
            } else {
                price = getProductPrice(null, null, product.getId(), EntityContext.PRODUCT_PRICE_TYPE_NORMAL);
                productForm.setSalesPrice(price.getSalesPrice());
                productForm.setPromotionPrice(price.getPromotionPrice());

                price = getProductPrice(null, null, product.getId(), EntityContext.PRODUCT_PRICE_TYPE_COMPANY);
                productForm.setEnterpriseSalesPrice(price.getSalesPrice());
                productForm.setEnterprisePromotionPrice(price.getPromotionPrice());
            }
            productForm.setProductPrice(product.getProductPrice());
            productForm.setEnterpriseProductPrice(product.getEnterpriseProductPrice());
            productForm.setPriceUnit(product.getPriceUnit());
            productForm.setProductImage(getAsset(product.getProductImage(), needDetail));

            if (product.getIsMarketable() >= EntityContext.UP_SHELF_STATE_UP_FOR_POINTS && product.getIsMarketable() <= EntityContext.UP_SHELF_STATE_UP_FOR_ALL) {
                ProductPriceForm pointsPrice = getProductPrice(null, null, product.getId(), EntityContext.PRODUCT_PRICE_TYPE_POINTS);
                if (null != pointsPrice.getPointsPoolId()) {
                    productForm.setPointsPrice(pointsPrice.getSalesPrice());
                    productForm.setPointsPriceUnit(pointsPrice.getPriceUnit().replace("元", "分"));
                    productForm.setPricePointsPoolId(pointsPrice.getPointsPoolId());
                    productForm.setPricePointsPoolName(pointsPoolDao.get(pointsPrice.getPointsPoolId()).getPoolName());
                    if (obj instanceof MemberEntity) {
                        List<PointsMemberAccountEntity> pointsMemberAccounts = pointsMemberAccountDao.getListByHql("select p from " +
                                "PointsMemberAccountEntity p where p.account.id = ?", ((MemberEntity) obj).getId());

                        if (pointsMemberAccounts.size() > 0) {
                            pointsMemberAccounts.forEach(b -> {
                                if (null != b.getPointsPool() && b.getPointsPool().getId().equals(pointsPrice.getPointsPoolId())) {
                                    productForm.setAvailablePoints(b.getActualTotalPoints());
                                }
                            });
                        }
                        productForm.setAvailablePoints(null != productForm.getAvailablePoints() ? productForm.getAvailablePoints() : 0);

                    }
                }
            }

            List<PromotionForm> promotionFormList = new ArrayList<PromotionForm>();
            Set<PromotionEntity> allValidPromotions = product.getAllValidPromotions();
            for (PromotionEntity promotionEntity : allValidPromotions) {
                promotionFormList.add(PromotionForm.fromPromotionEntity(promotionEntity, getPrincipal(false)));
            }

            productForm.setAssetForm(null != product.getVideo() ? getAsset(product.getVideo(), needDetail) : null);
            productForm.setPromotions(promotionFormList);
            productForm.setInventoryAmount(ProductLimit.limit.containsKey(productForm.getSpecificationId())
                    && ProductLimit.limit.get(productForm.getSpecificationId()).containsKey(product.getId())
                    ? ProductLimit.limit.get(specification.getId()).get(product.getId())
                    .getProductInventoryAmount()
                    : product.getInventoryAmount());
            try {
                productForm.setSkuSpecification(mapper.readValue(product.getSkuSpecification(), LinkedHashMap.class));
            } catch (Exception ex) {
            	log.error(ex.getMessage());
            }
        } else if (needDetail == EntityContext.PRODUCT_LIST_DETAIL_FLAG_CUSTOMER_SERVICE) {
            ObjectMapper mapper = new ObjectMapper();
            productForm.setProductPrice(product.getProductPrice());
            try {
                productForm.setSkuSpecification(mapper.readValue(product.getSkuSpecification(), LinkedHashMap.class));
            } catch (Exception ex) {
            	log.error(ex.getMessage());
            }
        } else {
            // 返回全部字段
            ObjectMapper mapper = new ObjectMapper();
            productForm.setPayType(product.getPayType());
            productForm.setBeginSale(product.getBeginSale());
            productForm.setIsMarketable(product.getIsMarketable());
            productForm.setIsPromotion(product.getIsPromotion());
            productForm.setMappingThirdCode(product.getMappingThirdCode());
            productForm.setOrders(product.getOrders());
            productForm.setProductCode(product.getProductCode());
            productForm.setSpecificationIndex(product.getSpecificationIndex());
            productForm.setProductMemo(product.getProductMemo());
            productForm.setSalesNumber(product.getSalesNumber()
                    + (ProductLimit.sales.containsKey(product.getId()) ? ProductLimit.sales.get(product.getId()) : 0));
            productForm.setProductId(product.getId());
            productForm.setColumnId(columnId);
            productForm.setState(product.getState());
            productForm.setCycle(product.getCycle());
            productForm.setPeriodUnit(product.getPeriodUnit());
            productForm.setProductGroupId(productGroupId);
            productForm.setSpecificationId(null != specification ? specification.getId() : null);
            productForm.setProductPhoneImage(getAsset(product.getProductPhoneImage(), needDetail));
            productForm.setProductCode(product.getProductCode());
            productForm.setSupplier(product.getSupplier());
            DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            product.getFinancialAttributesList().forEach(p -> {
                ProductFinancialAttributesForm productFinancialAttributesForm = new ProductFinancialAttributesForm();
                productFinancialAttributesForm.setForthIncomeItems(p.getForthIncomeItems());
                productFinancialAttributesForm.setId(p.getId());
                productFinancialAttributesForm.setIncomeType(p.getIncomeType());
                productFinancialAttributesForm.setProductType(p.getProductType());
                productFinancialAttributesForm.setSalesType(p.getSalesType());
                productFinancialAttributesForm.setSecIncomeItems(p.getSecIncomeItems());
                productFinancialAttributesForm.setSupplier(p.getSupplier());
                productFinancialAttributesForm.setThirdIncomeItems(p.getThirdIncomeItems());
                productFinancialAttributesForm.setSaleTax(p.getSaleTax());
                productFinancialAttributesForm.setCostPrice(p.getCostPrice());
                productFinancialAttributesForm.setCostTax(p.getCostTax());
                productFinancialAttributesForm.setCostStartTime(sdf.format(p.getCostStartTime()));
                productFinancialAttributesForm.setCostEndTime(sdf.format(p.getCostEndTime()));
                productFinancialAttributesForm.setRevenueShareTax(p.getRevenueShareTax());
                productFinancialAttributesForm.setShareProportion(p.getShareProportion());
                productFinancialAttributesForm.setShareProportionStartTime(sdf.format(p.getShareProportionStartTime()));
                productFinancialAttributesForm.setShareProportionEndTime(sdf.format(p.getShareProportionEndTime()));
                productForm.getProductFinancialAttributesFormList().add(productFinancialAttributesForm);
            });
            ProductPriceForm price = null;
            Object obj = getPrincipal(false);

            if (obj instanceof MemberEntity) {
                Integer type = isEnterpriseMember(obj) ? EntityContext.PRODUCT_PRICE_TYPE_COMPANY
                        : EntityContext.PRODUCT_PRICE_TYPE_NORMAL;
                price = getProductPrice(null, null, product.getId(), type);
                if (type == EntityContext.PRODUCT_PRICE_TYPE_COMPANY) {
                    productForm.setSalesPrice(null != price.getSalesPrice() ? price.getSalesPrice()
                            : product.getEnterpriseProductPrice());
                } else {
                    productForm.setSalesPrice(
                            null != price.getSalesPrice() ? price.getSalesPrice() : product.getProductPrice());
                }
                productForm.setPromotionPrice(price.getPromotionPrice());

            } else if (obj instanceof ShopUserEntity) {

                price = getProductPrice(null, null, product.getId(), EntityContext.PRODUCT_PRICE_TYPE_NORMAL);
                productForm.setSalesPrice(price.getSalesPrice());
                productForm.setPromotionPrice(price.getPromotionPrice());
                if (isEnterpriseUser(obj)) {
                    price = getProductPrice(null, null, product.getId(), EntityContext.PRODUCT_PRICE_TYPE_COMPANY);
                    productForm.setEnterpriseSalesPrice(price.getSalesPrice());
                    productForm.setEnterprisePromotionPrice(price.getPromotionPrice());
                }
            } else {
                price = getProductPrice(null, null, product.getId(), EntityContext.PRODUCT_PRICE_TYPE_NORMAL);
                productForm.setSalesPrice(price.getSalesPrice());
                productForm.setPromotionPrice(price.getPromotionPrice());

                price = getProductPrice(null, null, product.getId(), EntityContext.PRODUCT_PRICE_TYPE_COMPANY);
                productForm.setEnterpriseSalesPrice(price.getSalesPrice());
                productForm.setEnterprisePromotionPrice(price.getPromotionPrice());
            }
            productForm.setProductPrice(product.getProductPrice());
            productForm.setEnterpriseProductPrice(product.getEnterpriseProductPrice());
            productForm.setPriceUnit(product.getPriceUnit());

            if (product.getIsMarketable() >= EntityContext.UP_SHELF_STATE_UP_FOR_POINTS && product.getIsMarketable() <= EntityContext.UP_SHELF_STATE_UP_FOR_ALL) {
                ProductPriceForm pointsPrice = getProductPrice(null, null, product.getId(), EntityContext.PRODUCT_PRICE_TYPE_POINTS);
                if (null != pointsPrice.getProductPrice()) {
                    productForm.setPointsPrice(pointsPrice.getSalesPrice());
                    productForm.setPointsPriceUnit(pointsPrice.getPriceUnit().replace("元", "分"));
                    productForm.setPricePointsPoolId(pointsPrice.getPointsPoolId());
                    productForm.setPricePointsPoolName(pointsPoolDao.get(pointsPrice.getPointsPoolId()).getPoolName());
                    if (obj instanceof MemberEntity) {
                        List<PointsMemberAccountEntity> pointsMemberAccounts = pointsMemberAccountDao.getListByHql("select p from " +
                                "PointsMemberAccountEntity p where p.account.id = ?", ((MemberEntity) obj).getId());

                        if (pointsMemberAccounts.size() > 0) {
                            pointsMemberAccounts.forEach(b -> {
                                if (null != b.getPointsPool() && b.getPointsPool().getId().equals(pointsPrice.getPointsPoolId())) {
                                    productForm.setAvailablePoints(b.getActualTotalPoints());
                                }
                            });
                        }
                        productForm.setAvailablePoints(null != productForm.getAvailablePoints() ? productForm.getAvailablePoints() : 0);

                    }
                }
            }

            productForm.setInventoryWarningAmount(product.getInventoryWarningAmount());
            productForm.setInventoryWarning(
                    product.getInventoryAmount() - product.getInventoryWarningAmount() > 0 ? 0 : 1);
            productForm.setProductImage(getAsset(product.getProductImage()));
            productForm.setProductWeight(product.getProductWeight());

            List<PromotionForm> promotionFormList = new ArrayList<PromotionForm>();
            Set<PromotionEntity> allValidPromotions = product.getAllValidPromotions();
            for (PromotionEntity promotionEntity : allValidPromotions) {
                promotionFormList.add(PromotionForm.fromPromotionEntity(promotionEntity, getPrincipal(false)));
            }

            ArrayList<String> couponTipsArray = new ArrayList<String>();
            Set<CouponSettingEntity> allValidCouponSettings = product.getAllValidCouponSettings();
            for (CouponSettingEntity couponSettingEntity : allValidCouponSettings) {
                couponTipsArray.add(couponSettingEntity.getTips());
            }
            productForm.setCouponTips(StringUtils.join(couponTipsArray, ","));
            productForm.setPromotions(promotionFormList);

            productForm.setAssetForm(null != product.getVideo() ? getAsset(product.getVideo()) : null);
            productForm.setProductVolume(product.getProductVolume());
            productForm.setPickupSupport(product.getPickupSupport());
            productForm.setInventoryAmount(ProductLimit.limit.containsKey(productForm.getSpecificationId())
                    && ProductLimit.limit.get(productForm.getSpecificationId()).containsKey(product.getId())
                    ? ProductLimit.limit.get(specification.getId()).get(product.getId())
                    .getProductInventoryAmount()
                    : product.getInventoryAmount());
            try {
                productForm.setSkuSpecification(mapper.readValue(product.getSkuSpecification(), LinkedHashMap.class));
            } catch (Exception ex) {
            	log.error(ex.getMessage());
            }
        }
        try {
            if (ProductLimit.limit.containsKey(productForm.getSpecificationId()) && ProductLimit.limit
                    .get(productForm.getSpecificationId()).containsKey(productForm.getProductId())) {
                ProductLimitForm limitForm = ProductLimit.limit.get(productForm.getSpecificationId())
                        .get(productForm.getProductId());
                Timestamp now = Timestamp.valueOf(LocalDateTime.now());
                Timestamp startTime = Timestamp.valueOf(LocalDateTime.parse(limitForm.getStartTime(),
                        DateTimeFormatter.ofPattern(DateUtil.longFormat)));
                Timestamp endTime = Timestamp.valueOf(
                        LocalDateTime.parse(limitForm.getEndTime(), DateTimeFormatter.ofPattern(DateUtil.longFormat)));
                if (limitForm.getLimitType().equals(EntityContext.PRODUCT_LIMIT_TYPE_INVENTORY_ONLY)
                        || limitForm.getLimitType().equals(EntityContext.PRODUCT_LIMIT_TYPE_LIMIT_ONLY)) {
                    productForm.setLimitFlag(EntityContext.PRODUCT_LIMIT_FLAG_VALID);
                    productForm.setProductLimitForm(
                            ProductLimit.limit.get(productForm.getSpecificationId()).get(productForm.getProductId()));
                } else if (now.after(startTime) && now.before(endTime)) {
                    productForm.setLimitFlag(EntityContext.PRODUCT_LIMIT_FLAG_VALID);
                    productForm.setProductLimitForm(
                            ProductLimit.limit.get(productForm.getSpecificationId()).get(productForm.getProductId()));
                } else {
                    productForm.setLimitFlag(EntityContext.PRODUCT_LIMIT_FLAG_INVALID);
                    productForm.setProductLimitForm(null);
                }
            } else {
                productForm.setLimitFlag(EntityContext.PRODUCT_LIMIT_FLAG_INVALID);
                productForm.setProductLimitForm(null);
            }
        } catch (Exception e) {
        	log.error(e.getMessage());
            productForm.setLimitFlag(EntityContext.PRODUCT_LIMIT_FLAG_INVALID);
        }
        return productForm;
    }

    protected List<ProductSpecificationForm> parseProductSpecificationFormList(Integer columnId, Integer productGroupId,
                                                                               List<ProductSpecificationEntity> specificationList, Boolean auditFlag, Boolean specificationsFlag,
                                                                               Boolean templeteFlag, Boolean skuFlag, String isMarketableList) {
        return parseProductSpecificationFormList(columnId, productGroupId, specificationList, auditFlag,
                specificationsFlag, templeteFlag, skuFlag, isMarketableList,
                EntityContext.PRODUCT_LIST_DETAIL_FLAG_ALL);
    }

    protected List<ProductSpecificationForm> parseProductSpecificationFormList(Integer columnId, Integer productGroupId,
                                                                               List<ProductSpecificationEntity> specificationList, Boolean auditFlag, Boolean specificationsFlag,
                                                                               Boolean templeteFlag, Boolean skuFlag, String isMarketableList, Integer needDetail) {
        List<ProductSpecificationForm> specificationFormList = new ArrayList<ProductSpecificationForm>();
        for (ProductSpecificationEntity o : specificationList) {
            ProductSpecificationForm specificationForm = getProductSpecificationForm(o, auditFlag, specificationsFlag,
                    templeteFlag, needDetail);
            List<ProductForm> skuList = getSkuList(columnId, productGroupId, o, skuFlag, isMarketableList, needDetail);
            specificationForm.setSkuList(skuList);
            specificationFormList.add(specificationForm);
            if (needDetail == EntityContext.PRODUCT_LIST_DETAIL_FLAG_TV) {
                specificationForm.distinctSku();
            }
        }
        return specificationFormList;
    }

    protected List<ProductForm> parseProductFormList(Integer columnId, Integer productGroupId,
                                                     List<ProductEntity> productList, Boolean productSpecificationFormFlag, Boolean auditFlag,
                                                     Boolean specificationsFlag, Boolean templeteFlag, String isMarketableList) {
        return parseProductFormList(columnId, productGroupId, productList, productSpecificationFormFlag, auditFlag,
                specificationsFlag, templeteFlag, isMarketableList, EntityContext.PRODUCT_LIST_DETAIL_FLAG_ALL);
    }

    protected List<ProductForm> parseProductFormList(Integer columnId, Integer productGroupId,
                                                     List<ProductEntity> productList, Boolean productSpecificationFormFlag, Boolean auditFlag,
                                                     Boolean specificationsFlag, Boolean templeteFlag, String isMarketableList, Integer needDetail) {
        List<ProductForm> productFormList = new ArrayList<ProductForm>();
        for (ProductEntity o : productList) {
            try {
                ProductForm productForm = getProductForm(columnId, productGroupId, o.getProductSpecification(), o,
                        needDetail);
                if (productSpecificationFormFlag) {
                    productForm.setProductSpecificationForm(getProductSpecificationForm(o.getProductSpecification(),
                            auditFlag, specificationsFlag, templeteFlag));
                }
                if (StringUtils.isNotEmpty(isMarketableList)) {
                    isMarketableList = isMarketableList.replace("(", "").replace(")", "");
                    for (String isMarketable : isMarketableList.split(",")) {
                        if ((null == isMarketable)
                                || (null != isMarketable && Integer.valueOf(isMarketable) == o.getIsMarketable())) {
                            productFormList.add(productForm);
                        }
                    }
                } else {
                    productFormList.add(productForm);
                }

            } catch (Exception e) {
            	log.error("构造商品信息ProductId:{} FORM时出错：{}", o.getId(), e);
                e.printStackTrace();
                continue;
            }

        }
        return productFormList;
    }

    protected List<ProductForm> getSkuList(Integer columnId, Integer productGroupId,
                                           ProductSpecificationEntity specification, Boolean skuFlag, String isMarketableList) {
        return getSkuList(columnId, productGroupId, specification, skuFlag, isMarketableList,
                EntityContext.PRODUCT_LIST_DETAIL_FLAG_ALL);
    }

    @SuppressWarnings("unchecked")
    protected List<ProductForm> getSkuList(Integer columnId, Integer productGroupId,
                                           ProductSpecificationEntity specification, Boolean skuFlag, String isMarketableList, Integer needDetail) {
        ObjectMapper mapper = new ObjectMapper();
        List<ProductForm> skuList = new ArrayList<ProductForm>();
        if (EntityContext.PRODUCT_LIST_DETAIL_FLAG_TV == needDetail) {
            // 返回TV端列表
            if (skuFlag) {
                try {
                    List<LinkedHashMap<String, String>> paraList = getSpecificationSkuParameterList(
                            specialTemplateToList(specification.getDetail().getSpecialTemplate()));
                    paraList.forEach(b -> {
                        b.keySet().forEach(c -> {
                            try {
                                ProductForm productForm = new ProductForm();
                                // productForm.setSpecificationIndex(c.endsWith("_") ? c.substring(0, c.length()
                                // - 1) : c);
                                // productForm.setSkuSpecification(mapper.readValue(b.get(c),
                                // LinkedHashMap.class));
                                productForm.setSpecificationId(specification.getId());
                                productForm.setImportFlag(EntityContext.PRODUCT_IMPORT_FLAG_NONE);
                                skuList.add(productForm);
                            } catch (Exception ex) {
                            	log.error("sku列表装载错误" + ex.getMessage());
                            }
                        });
                    });

                    // 判断在库状态
                    StringBuilder hql = new StringBuilder(
                            "select a from ProductEntity a where a.productSpecification.id=" + specification.getId());
                    List<ProductForm> productFormList = parseProductFormList(columnId, productGroupId,
                            productDao.getListByHql(hql.toString()), false, false, false, false, isMarketableList);

                    productFormList.forEach(b -> {
                        boolean flag = false;
                        for (ProductForm c : skuList) {
                            if (c.equals(b)) {
                                flag = true;
                                skuList.remove(c);
                                break;
                            }
                        }
                        if (flag) {
                            b.setImportFlag(EntityContext.PRODUCT_IMPORT_FLAG_IN);
                            skuList.add(b);
                        }
                    });
                } catch (Exception ex) {
                	log.error("getSkuList方法构造sku列表出错！！！" + ex.getMessage());
                }

            } else {
                // TV端列表只需要显示一个规格商品
                // List<ProductForm> tmpList = new ArrayList<ProductForm>();

                List<ProductForm> parseProductFormList = parseProductFormList(columnId, productGroupId,
                        specification.getProductList(), false, false, false, false, isMarketableList, needDetail);
                // 只显示销售价格最小的规格商品。
//				if(parseProductFormList.size() > 0) {
//					ProductForm minPriceForm = parseProductFormList.get(0);
//					for(ProductForm form : parseProductFormList) {
//						if(form.getSalesPrice().compareTo(minPriceForm.getSalesPrice()) < 0) {
//							minPriceForm = form;
//						}
//					}
//					
//					tmpList.add(minPriceForm);
//				}
                skuList.addAll(parseProductFormList);
            }
        } else {
            if (skuFlag) {
                try {
                    List<LinkedHashMap<String, String>> paraList = getSpecificationSkuParameterList(
                            specialTemplateToList(specification.getDetail().getSpecialTemplate()));
                    paraList.forEach(b -> {
                        b.keySet().forEach(c -> {
                            try {
                                ProductForm productForm = new ProductForm();
                                productForm.setSpecificationIndex(c.endsWith("_") ? c.substring(0, c.length() - 1) : c);
                                productForm.setSkuSpecification(mapper.readValue(b.get(c), LinkedHashMap.class));
                                productForm.setSpecificationId(specification.getId());
                                productForm.setImportFlag(EntityContext.PRODUCT_IMPORT_FLAG_NONE);
                                skuList.add(productForm);
                            } catch (Exception ex) {
                            	log.error("sku列表装载错误" + ex.getMessage());
                            }
                        });
                    });

                    // 判断在库状态
                    StringBuilder hql = new StringBuilder(
                            "select a from ProductEntity a where a.productSpecification.id=" + specification.getId());
                    List<ProductForm> productFormList = parseProductFormList(columnId, productGroupId,
                            productDao.getListByHql(hql.toString()), false, false, false, false, isMarketableList);

                    productFormList.forEach(b -> {
                        boolean flag = false;
                        for (ProductForm c : skuList) {
                            if (c.equals(b)) {
                                flag = true;
                                skuList.remove(c);
                                break;
                            }
                        }
                        if (flag) {
                            b.setImportFlag(EntityContext.PRODUCT_IMPORT_FLAG_IN);
                            skuList.add(b);
                        }
                    });
                } catch (Exception ex) {
                	log.error("getSkuList方法构造sku列表出错！！！" + ex.getMessage());
                }

            } else {
                skuList.addAll(parseProductFormList(columnId, productGroupId, specification.getProductList(), false,
                        false, false, false, isMarketableList));
            }
        }

        return skuList;
    }

    protected StoreForm parseStoreTree(StoreForm storeForm, StoreEntity store) {
        storeForm.setParentStoreId(null != store.getParentStore() ? store.getParentStore().getId() : null);
        storeForm.setStoreId(store.getId());
        storeForm.setStoreAddress(store.getStoreAddress());
        storeForm.setStoreChargeName(store.getStoreChargeName());
        storeForm.setStoreChargePhone(store.getStoreChargePhone());
        storeForm.setServicePhone(store.getServicePhone());
        storeForm.setStoreMail(store.getStoreMail());
        storeForm.setStoreName(store.getStoreName());
        storeForm.setIsSelfSupport(store.getIsSelfSupport());
        storeForm.setAutoAudit(store.getAutoAudit());
        storeForm.setRegionId(store.getRegionId());
        storeForm.setLogisticsFeeScheduleSetting(store.getLogisticsFeeScheduleSetting());
        storeForm.setBusinessModel(store.getBusinessModel());
        storeForm.setRegionName(null != store.getRegion() ? store.getRegion().getRegionName() : null);
        storeForm.setStoreLogo(StringUtils.isNotEmpty(store.getStoreLogo()) ? store.getStoreLogo() : "");
        storeForm.setStoreImage(StringUtils.isNotEmpty(store.getStoreImage()) ? store.getStoreImage() : "");
        storeForm.setComment(StringUtils.isNotEmpty(store.getComment()) ? store.getComment() : "");
        storeForm.setAuditValidTime(
                null != store.getAuditValidTime() ? DateUtil.formatLongFormat(store.getAuditValidTime()) : null);
        storeForm.setPaymentMerchantCode(StringUtils.isNotEmpty(store.getPaymentMerchantCode()) ? store.getPaymentMerchantCode() :"");
        storeForm.setPaymentSecret(StringUtils.isNotEmpty(store.getPaymentSecret()) ? store.getPaymentSecret() :"");
        Integer size = store.getSubStoreList().size();
        while (size > 0) {
            for (StoreEntity o : store.getSubStoreList()) {
                if (o.getState() == EntityContext.RECORD_STATE_VALID) {
                    StoreForm subStoreForm = new StoreForm();
                    storeForm.getSubStoreList().add(parseStoreTree(subStoreForm, o));
                }
                size--;
            }
        }
        return storeForm;
    }

    protected StoreForm parseStoreTree(StoreForm storeForm, StoreEntity store, List<RegionEntity> regions) {
        RegionEntity storeRegion = store.getRegion();
        String storeRegionCode = storeRegion.getRegionCode();
        boolean includeTag = false;
        for (RegionEntity r : regions) {
            if (Integer.valueOf(r.getStartRegionCode()) <= Integer.valueOf(storeRegionCode)
                    && Integer.valueOf(r.getEndRegionCode()) >= Integer.valueOf(storeRegionCode)) {
                includeTag = true;
            }
        }

        storeForm.setParentStoreId(null != store.getParentStore() ? store.getParentStore().getId() : null);
        storeForm.setStoreId(store.getId());
        storeForm.setStoreAddress(store.getStoreAddress());
        storeForm.setStoreChargeName(store.getStoreChargeName());
        storeForm.setStoreChargePhone(store.getStoreChargePhone());
        storeForm.setServicePhone(store.getServicePhone());
        storeForm.setStoreMail(store.getStoreMail());
        storeForm.setStoreName(store.getStoreName());
        storeForm.setIsSelfSupport(store.getIsSelfSupport());
        storeForm.setAutoAudit(store.getAutoAudit());
        storeForm.setRegionId(store.getRegionId());
        storeForm.setRegionName(null != store.getRegion() ? store.getRegion().getRegionName() : null);
        storeForm.setAuditValidTime(
                null != store.getAuditValidTime() ? DateUtil.formatLongFormat(store.getAuditValidTime()) : null);
        storeForm.setStoreLogo(StringUtils.isNotEmpty(store.getStoreLogo()) ? store.getStoreLogo() : "");
        storeForm.setStoreImage(StringUtils.isNotEmpty(store.getStoreImage()) ? store.getStoreImage() : "");
        storeForm.setComment(StringUtils.isNotEmpty(store.getComment()) ? store.getComment() : "");
        storeForm.setStoreLogo(store.getStoreLogo());
        storeForm.setStoreImage(store.getStoreImage());
        storeForm.setPaymentMerchantCode(store.getPaymentMerchantCode());
        storeForm.setPaymentSecret(store.getPaymentSecret());
        storeForm.setReservedField(StringUtils.isNotEmpty(store.getReservedField()) ? store.getReservedField() : "");
        Integer size = store.getSubStoreList().size();
        while (size > 0) {
            for (StoreEntity o : store.getSubStoreList()) {
                if (o.getState() == EntityContext.RECORD_STATE_VALID) {
                    StoreForm subStoreForm = new StoreForm();
                    subStoreForm = parseStoreTree(subStoreForm, o, regions);
                    // storeForm.getSubStoreList().add(parseStoreTree(subStoreForm, o,regions));
                    if (subStoreForm != null) {
                        storeForm.getSubStoreList().add(subStoreForm);
                    }
                }
                size--;
            }
        }
        if (storeForm.getSubStoreList().size() == 0 && includeTag == false) {
            return null;
        }
        return storeForm;
    }

//	protected BigDecimal calculateLogisticsFee(AddressEntity address,List<ProductForm> orderItem) {
////
////		Timestamp now = new Timestamp(System.currentTimeMillis());
////		BigDecimal logisticsFee = new BigDecimal("0.00");
////		if (specification.getIsDelivery() != EntityContext.PRODUCT_DELIVERY_TYPE_NONE
////				&& specification.getIsCourier() == EntityContext.PRODUCT_IS_NEED_COURIER_NEED) {
////			StringBuilder hql = new StringBuilder(
////					"select c from StoreEntity a inner join a.courierList b inner join b.feeList c inner join a.productList d inner join d.productList e where a.id="
////							+ store.getId() + " and d.id=" + specification.getId() + " and e.id=" + product.getId()
////							+ " and c.startTime<='" + DateUtil.formatLongFormat(now) + "' and c.endTime>='"
////							+ DateUtil.formatLongFormat(now) + "' and c.feeType=d.isDelivery");
////
////			List<BigDecimal> feeList = new ArrayList<BigDecimal>();
////			List<CourierFeeScheduleEntity> courierFeeList = courierFeeScheduleDao.getListByHql(hql.toString());
////			courierFeeList.forEach(o -> {
////				feeList.add(o.getDefaultFirstHeavyCost().multiply(new BigDecimal(String.valueOf(o.getDefaultFirstHeavy())))
////						.add(o.getDefaultContinueHeavyCost().multiply((new BigDecimal(String.valueOf(Math
////								.ceil((product.getProductWeight() - o.getDefaultFirstHeavy()) / o.getDefaultContinueHeavy())))))));
////			});
////
////			BigDecimal[] fees = feeList.toArray(new BigDecimal[feeList.size()]);
////
////			Arrays.parallelSort(fees);
////
////			if (fees.length >= 1) {
////				logisticsFee = fees[fees.length - 1].setScale(2, BigDecimal.ROUND_HALF_UP);
////			}
////		}
//		
//		return logisticsFee;
//	}

    private Object getPrincipalObject(boolean returnFullEntity) {
        Subject curUser = SecurityUtils.getSubject();
        PrincipalCollection principals = curUser.getPrincipals();
        if (null != principals && !principals.isEmpty()) {
            @SuppressWarnings("unchecked")
            List<Object> principalList = principals.asList();
            Object principal = (null != principalList.get(1) ? principalList.get(1) : null);
            if (!returnFullEntity) {
                return principal;
            }
            if (principal instanceof AccountPrincipalModel) {
				AccountPrincipalModel partyUser = (AccountPrincipalModel) principal;
				String sql = "select * from t_account where id = '" + partyUser.getId() + "' and userState = '"
						+ EntityContext.RECORD_STATE_VALID + "'";
				Object account = memberDao.getUniqueBySql(sql);
				return account;
			}
            if (principal instanceof MemberEntity) {
                MemberEntity shiroMember = (MemberEntity) principal;
                String sql = "select * from t_account where id = '" + shiroMember.getId() + "' and phoneVerifyState = '"
                        + EntityContext.RECORD_STATE_VALID + "'";
                Object member = memberDao.getUniqueBySql(sql);
                return member;
            } else if (principal instanceof PlatformUserEntity) {
                PlatformUserEntity shiroPlatformUser = (PlatformUserEntity) principal;
                String sql = "select * from t_user where id = '" + shiroPlatformUser.getId() + "' and userState = '"
                        + EntityContext.RECORD_STATE_VALID + "'";
                Object platformUser = platformUserDao.getUniqueBySql(sql);
                return platformUser;
            } else if (principal instanceof ShopUserEntity) {
                ShopUserEntity shiroShopUser = (ShopUserEntity) principal;
                String sql = "select * from t_user where id = '" + shiroShopUser.getId() + "' and userState = '"
                        + EntityContext.RECORD_STATE_VALID + "'";
                Object shopUser = shopUserDao.getUniqueBySql(sql);
                return shopUser;
            } else if (principal instanceof VerificaterEntity) {
                VerificaterEntity shiroVerificater = (VerificaterEntity) principal;
                String sql = "select * from t_user where id = '" + shiroVerificater.getId() + "' and userState = '"
                        + EntityContext.RECORD_STATE_VALID + "'";
                Object verificater = verificaterDao.getUniqueBySql(sql);
                return verificater;
            } else if (principal instanceof PrincipalModel) {
				PrincipalModel shiroPlatformUser = (PrincipalModel) principal;
				String sql = "select userType from t_user where id = '" + shiroPlatformUser.getId() + "' and userState = '"
						+ EntityContext.RECORD_STATE_VALID + "'";
				String sql2 = "select * from t_user where id = '" + shiroPlatformUser.getId() + "' and userState = '"
						+ EntityContext.RECORD_STATE_VALID + "'";
				Object shopUser = null;
				SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
				Session session = sessionFactory.openSession();
				Object typeObj = session.createNativeQuery(sql).uniqueResult();
				session.close();				
				if(typeObj != null) {
					String userType = typeObj.toString();
					if(userType.equals("S")) {
						shopUser = shopUserDao.getUniqueBySql(sql2);
					}
					else if(userType.equals("V")){
						shopUser = verificaterDao.getUniqueBySql(sql2);
					}
					else {
						shopUser = platformUserDao.getUniqueBySql(sql2);
					}
				}
				else {
					shopUser = platformUserDao.getUniqueBySql(sql2);
				}
				return shopUser;
			}
            return null;
        } else {
            return null;
        }
    }
    
	protected Object getPrincipal(boolean returnFullEntity) {
		Subject curUser = SecurityUtils.getSubject();
		PrincipalCollection principals = curUser.getPrincipals();
		if (null != principals && !principals.isEmpty()) {
			@SuppressWarnings("unchecked")
			List<Object> principalList = principals.asList();
			Object principal = (null != principalList.get(1) ? principalList.get(1) : null);
		
//			if (!returnFullEntity) {
//				return principal;
//			}
			Cache cache =  hazelcastCacheManager.getCache("userObjectCache");
			Cache userIdcache =  hazelcastCacheManager.getCache("userIdCache");
			if (principal instanceof AccountPrincipalModel) {
				Object object =  cache.get(principal,MemberEntity.class);
				if(null == object || returnFullEntity) {
					AccountPrincipalModel partyUser = (AccountPrincipalModel) principal;
					String sql = "select * from t_account where id = '" + partyUser.getId() + "' and userState = '"
							+ EntityContext.RECORD_STATE_VALID + "'";
					Object account = memberDao.getUniqueBySql(sql);
					cache.put(principal,account);
					userIdcache.put("account_"+partyUser.getId(), principal);
					return account;
				}
				return object;
			}
			if (principal instanceof AccountEntity) {
				Object object =  cache.get(principal,AccountEntity.class);
				if(null == object || returnFullEntity) {
					AccountEntity shiroAccount = (AccountEntity) principal;
					String sql = "select * from t_account where id = '" + shiroAccount.getId() + "' and userState = '"
							+ EntityContext.RECORD_STATE_VALID + "'";
					Object account = accountDao.getUniqueBySql(sql);
					cache.put(principal,account);
					userIdcache.put("account_"+shiroAccount.getId(), principal);
					return account;
				}
				return object;
			} else if (principal instanceof PlatformUserEntity) {
				PlatformUserEntity object =  cache.get(principal,PlatformUserEntity.class);
				if(null == object || returnFullEntity) {
					PlatformUserEntity shiroPlatformUser = (PlatformUserEntity) principal;
					String sql = "select * from t_user where id = '" + shiroPlatformUser.getId() + "' and userState = '"
							+ EntityContext.RECORD_STATE_VALID + "'";
					Object platformUser = platformUserDao.getUniqueBySql(sql);
					cache.put(principal,platformUser);
					userIdcache.put("user_"+shiroPlatformUser.getId(), principal);
					return platformUser;
				}
				return object;
			} else if (principal instanceof PrincipalModel) {
				Object object =  cache.get(principal,Object.class);
				if(null == object || returnFullEntity) {
					PrincipalModel shiroPlatformUser = (PrincipalModel) principal;
					String sql = "select userType from t_user where id = '" + shiroPlatformUser.getId() + "' and userState = '"
							+ EntityContext.RECORD_STATE_VALID + "'";
					String sql2 = "select * from t_user where id = '" + shiroPlatformUser.getId() + "' and userState = '"
							+ EntityContext.RECORD_STATE_VALID + "'";
					Object shopUser = null;
					SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
					Session session = sessionFactory.openSession();
					Object typeObj = session.createNativeQuery(sql).uniqueResult();
					session.close();				
					if(typeObj != null) {
						String userType = typeObj.toString();
						if(userType.equals("S")) {
							shopUser = shopUserDao.getUniqueBySql(sql2);
						}else if(userType.equals("V")){
							shopUser = verificaterDao.getUniqueBySql(sql2);
						}else {
							shopUser = platformUserDao.getUniqueBySql(sql2);
						}
					}
					else {
						shopUser = platformUserDao.getUniqueBySql(sql2);
					}
					cache.put(principal,shopUser);
					userIdcache.put("user_"+shiroPlatformUser.getId(), principal);
					return shopUser;
				}
				return object;
			}

			return null;
		} else {
			return null;
		}
	}
    
//    protected Object getPrincipal(boolean returnFullEntity) {
//    	return getPrincipalObject(returnFullEntity);
//    }	

	protected MemberEntity getCurrentMember() {
		Object principal = getPrincipalObject(true);
		MemberEntity member = (principal == null) ? null : (MemberEntity) principal;
		return member;
    }
    
    // 用于判断前端会员是否为企业会员。
    protected boolean isEnterpriseMember(Object obj) {

        if (obj == null) {
            return false;
        }
        if (obj instanceof MemberEntity) {
            MemberEntity member = (MemberEntity) obj;
            if (member.getType().intValue() == EntityContext.ACCOUNT_TYPE_PUBLIC_ACCOUNT) {
                return true;
            }
        }
        return false;
    }

    // 用于判断商铺用户是否已经开通企业购。
    protected boolean isEnterpriseUser(Object obj) {

        if (obj == null) {
            return false;
        }
        if (obj instanceof ShopUserEntity) {
            ShopUserEntity shopUser = (ShopUserEntity) obj;
            if (shopUser.getStore().getBusinessModel().contains("1")) {
                return true;
            }
        }
        return false;
    }

    /**
     * 用于判断商铺用户是否已经开通积分商城。
     *
     * @param obj
     * @return
     */
    protected boolean isPointsUser(Object obj) {
        if (obj == null) {
            return false;
        }
        if (obj instanceof ShopUserEntity) {
            ShopUserEntity shopUser = (ShopUserEntity) obj;
            if (shopUser.getStore().getBusinessModel().contains("2")) {
                return true;
            }
        }
        return false;
    }

    /**
     * 用户申请售后发起的退款
     *
     * @param sre 售后记录
     * @return 当且仅当返回的OrderRefundApplyRes对象的ret字段为"0000"，退款申请成功。
     */

    @SuppressWarnings("unchecked")
	protected IResponse refundForSalesReturn(SalesReturnEntity sre) {
        OrderRefundApplyRes res = new OrderRefundApplyRes();
      
        if (sre == null) {
            return new OrderRefundApplyRes(ResponseContext.RES_INVALID_PARAM_CODE, "售后对象不能为空！");
        }

        BigDecimal refundAmount = sre.getApplyForAmount();
        if (refundAmount == null || refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return new OrderRefundApplyRes(ResponseContext.RES_INVALID_PARAM_CODE, "申请退款金额必须大于等于0！");
        }

        if (sre.getSalesReturnOrderItemList().size() == 0) {
            return new OrderRefundApplyRes(ResponseContext.RES_INVALID_PARAM_CODE, "查找不到对应的订单项！");
        }
        OrderRefundEntity orderRefundEntity = new OrderRefundEntity();
        ProductOrderEntity order = sre.getSalesReturnOrderItemList().get(0).getOrderItem().getProductOrder();

        if (order.getOrderState().equals(EntityContext.PRODUCT_ORDER_STATE_PAY)
                || order.getOrderState().equals(EntityContext.PRODUCT_ORDER_STATE_SENDGOODS)
                || order.getOrderState().equals(EntityContext.PRODUCT_ORDER_STATE_CLOSE_NO_APPRAISE)
                || order.getOrderState().equals(EntityContext.PRODUCT_ORDER_STATE_CLOSE_APPRAISE)
                || order.getOrderState().equals(EntityContext.PRODUCT_ORDER_STATE_SPLIT)
                || order.getOrderState().equals(EntityContext.PRODUCT_ORDER_STATE_CANCEL)) {

            orderRefundEntity.setOrder(order);

            for (SalesReturnOrderItemEntity item : sre.getSalesReturnOrderItemList()) {
                ProductOrderItemEntity orderItem = item.getOrderItem();
                BigDecimal adjustAmount = item.getAdjustAmount();
                // 获取可退款金额
                BigDecimal refundableAmount = orderRefundEntity.getRefundableAmount(orderItem.getSettlementAmount(),
                        orderItem.getRefundedAmount());

                if (refundableAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    return new OrderRefundApplyRes(ResponseContext.RES_REFUND_ERROR_CODE,
                            "该订单项[" + orderItem.getId() + "]已经完成退款");
                }

                if (refundableAmount.compareTo(adjustAmount) < 0) {
                    return new OrderRefundApplyRes(ResponseContext.RES_REFUND_ERROR_CODE,
                            "该订单项可退金额为[" + refundableAmount.toString() + "]，不能发起退退款");
                }
            }

            BigDecimal refundableAmount = order.getRefundableAmount();

            Timestamp now = new Timestamp(new Date().getTime());

            if (refundableAmount.compareTo(BigDecimal.ZERO) <= 0) {
                return new OrderRefundApplyRes(ResponseContext.RES_REFUND_ERROR_CODE,
                        "该订单[" + order.getId() + "]已经完成退款");
            }
            if (refundableAmount.compareTo(refundAmount) < 0) {
                return new OrderRefundApplyRes(ResponseContext.RES_REFUND_ERROR_CODE,
                        "该订单项可退金额为[" + refundableAmount.toString() + "]，不能发起退退款");
            }

            List<OrderPaymentEntity> orderPaymentList = orderPaymentDao.getOrderPaymentListByOrderId(order.getId());
            if (orderPaymentList == null || orderPaymentList.size() == 0) {
                return new OrderRefundApplyRes(ResponseContext.RES_REFUND_ERROR_CODE, "订单没有支付记录");
            }

            orderRefundEntity.setAmount(refundAmount);
            orderRefundEntity.setSalesReturn(sre);
            orderRefundEntity.setRefundReason("订单[" + order.getOrderCode() + "]售后退款");
            orderRefundEntity.setRefundStatus(EntityContext.REFUND_APPLY_STATE_INITAL);
            orderRefundEntity.setCreateTime(now);
            orderRefundEntity.setPayee(getMemberByOrder(order).getRegistName());
            orderRefundEntity.setSn(CodeUtil.getSNCode(shopContextInfo.getOrderRefundsPrefix()));
            orderRefundEntity.setState(1);
//			orderRefundEntity.setMemo("退款内容:"+ refundAmount.toString()+"元," +orderItem.getProduct().getProductName());
            orderRefundEntity.setMethod(OrderRefundEntity.Method.online);
//            orderRefundEntity.setPaymentMethod(paymentPlugin.getName());
//            orderRefundEntity.setPaymentPlunginSn(paymentPlugin.getId());
            orderRefundEntity.setPaymentSessionSn(orderPaymentList.get(0).getPaymentSessionSn());

            boolean result = false;
            String postResult = "{\"ret\":\"FAIL\"}";;
            if (shopContextInfo.isEnablePaymentDebugMode()) {
                // 如果为模拟退款模式，则直接绕过支付系统退款
                orderRefundEntity.setOutTradeNo(CodeUtil.getSNCode("TESTOR"));
                postResult = "{\"ret\":\"0000\"}";;
            }else if (2==orderRefundEntity.getOrder().getIsEnterpriseOrder()){
                // 如果为积分订单，则直接绕过支付系统退款
                orderRefundEntity.setOutTradeNo(CodeUtil.getSNCode("POINTSOR"));
                postResult = "{\"ret\":\"0000\"}";;
            }
            else {
                // 使用支付系统退款
            	Map<String, String> parameterMap = new TreeMap<String, String>();
				parameterMap.put("amount", orderRefundEntity.getAmount().toString());
				parameterMap.put("paymentSessionSn", orderRefundEntity.getPaymentSessionSn());
				parameterMap.put("clientTradeNo", orderRefundEntity.getSn());

				try {

					if (StringUtils.isNotEmpty(orderRefundEntity.getMemo()))
						parameterMap.put("refundReason", URLEncoder.encode(orderRefundEntity.getMemo(), "UTF-8"));

				} catch (UnsupportedEncodingException e1) {
					e1.printStackTrace();
					throw new RuntimeException(e1);
				}
				parameterMap.put("notifyUrl", shopContextInfo.getRefundNotifyUrl());

				// 增加签名字段
				try {
					DigestUtil.fillSignatureParam(parameterMap, orderRefundEntity.getOrder().getStore().getPaymentMerchantCode(),
							orderRefundEntity.getOrder().getStore().getPaymentSecret());
				} catch (Exception e1) {
					res.setRet(ResponseContext.RES_PAYMENT_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_PAYMENT_ERROR_INFO + "构造在线退款请求签名错误！");
					e1.printStackTrace();
					return res;
				}

				String jsonstr = JSONObject.valueToString(parameterMap);
				// 发起退款
				try {
					postResult = HttpClientUtil.jsonPost(shopContextInfo.getRefundServiceUrl(), "UTF-8", jsonstr,
							null);
				} catch (Exception e) {
					throw new RuntimeException(e);
				}
            }

            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
         // 处理支付模块返回的支付结果
			ObjectMapper mapper = new ObjectMapper();
			Map<String, Object> jsonObj = null;
			try {
				jsonObj = mapper.readValue(postResult, TreeMap.class);
				log.info("业务系统申请在线支付结果：{}", jsonObj);
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			if (jsonObj != null && jsonObj.get("ret") != null) {	
				if (jsonObj.get("ret").equals("0000")) {
	                res.setRet(ResponseContext.RES_SUCCESS_CODE);
	                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
	                for (SalesReturnOrderItemEntity item : sre.getSalesReturnOrderItemList()) { // 所有未退款的订单项都变为申请退款状态。
	                    if (item.getOrderItem()
	                            .getRefundState() != EntityContext.PRODUCT_ORDER_ITEM_REFUND_STATE_REFUND_SUCCESS) {
	                        item.getOrderItem().setRefundState(EntityContext.PRODUCT_ORDER_ITEM_REFUND_STATE_APPLY);// 订单退款状态变为已经完成退款
	                    }
	                }
	                orderRefundEntity.setRefundStatus(1);// 订单退款状态变为成功发起退款申请
	                log.info("售后{}发起了退款，订单{}成功向第三方支付系统发退款申请", sre.getId(), order.getOrderCode());
	                res.setMessage("成功向第三方支付系统发起退款申请");
	                orderRefundDao.save(orderRefundEntity);
	
	                cancelLive(sre.getId());// 判断是否演唱会，若是则注销票根码

	            } else {
	                String msg = "售后{" + sre.getId() + "}发起了退款,订单{" + order.getOrderCode() + "}向第三方支付系统退款申请失败!";
	                res.setRet(ResponseContext.RES_REFUND_ERROR_CODE);
	                res.setRetInfo(msg + orderRefundEntity.getFailReason());
	                res.setMessage(orderRefundEntity.getFailReason());
	            }
			}
            /*
             * ProductOrderTraceEntity traceEntity = new ProductOrderTraceEntity();
             * traceEntity.setCreateTime(now); traceEntity.setLastModifyTime(now);
             * traceEntity.setOrderFlowState(EntityContext.PRODUCT_ORDER_TRACE_REFUND);;
             * traceEntity.setOrderFlowContent("订单ID:"+order.getId()+"发起退款，退款金额："+
             * orderRefundEntity.getAmount().toString()+"元");
             * productOrderHistoryDao.save(traceEntity);
             */
            // order.setOrderState(EntityContext.PRODUCT_ORDER_STATE_REFUND);//更改订单状态为退款中
            res.setId(orderRefundEntity.getId());
            res.setOrderId(orderRefundEntity.getOrder().getId());
            // res.setOrderItemId(orderRefundEntity.getOrderItem().getId());
            res.setRefundResult(result ? 1 : 0);
            if (shopContextInfo.isEnablePaymentDebugMode()) {
                refundSuccess(orderRefundEntity);
                res.setRetInfo(res.getRetInfo() + "模拟退款操作！！");
            }
            return res;
        } else {
            return new OrderRefundApplyRes(ResponseContext.RES_REFUND_ERROR_CODE, "当前订单状态不能退款。");
        }
    }

    /**
     * @param orderRefundEntity @throws
     * @Title: 退款成功后，更改订单信息
     * @Description: TODO(退款成功后 ， 更改订单信息 ， 如查整个订单退款 ， 退款成功后订单状态变为取消)
     */
    protected void refundSuccess(OrderRefundEntity orderRefundEntity) {
    	log.info("进入退款成功业务逻辑处理");
        ProductOrderEntity order = orderRefundEntity.getOrder();
        SalesReturnEntity salesReturn = orderRefundEntity.getSalesReturn();
        ProductOrderItemEntity orderItem = orderRefundEntity.getOrderItem();
        Timestamp now = new Timestamp(new Date().getTime());
        String traceMsg = "";

        // 根据用户申请的售后退款
        if (salesReturn != null) {
            // 如果退款金额超出订单可退金额，就把退款金额设置为订单可退金额。
        	log.info("根据用户申请的售后退款，售后ID：{}", salesReturn.getId());
            if (orderRefundEntity.getRefundableAmount().compareTo(orderRefundEntity.getAmount()) < 0) {
            	log.warn("[{}]退款结果通知错误,订单{}可退金额为{}，申请退款金额{}已经超出订单的可退款金额，退款金额更正为可退款金额！", orderRefundEntity.getSn(),
                        order.getOrderCode(), orderRefundEntity.getRefundableAmount(), orderRefundEntity.getAmount());
                log.warn(orderRefundEntity.toString());
                orderRefundEntity.setAmount(orderRefundEntity.getRefundableAmount());
            }

            StringBuilder sb = new StringBuilder();
            // 本次退款成功金额
            BigDecimal refundAmount = orderRefundEntity.getAmount() == null ? BigDecimal.ZERO
                    : orderRefundEntity.getAmount();
            // 根据售后的详情表，逐个对订单面进行退款
            for (SalesReturnOrderItemEntity poi : salesReturn.getSalesReturnOrderItemList()) {
                // 订单项已退金额
                BigDecimal itemRefundedAmount = poi.getOrderItem().getRefundedAmount() == null ? BigDecimal.ZERO
                        : poi.getOrderItem().getRefundedAmount();
                // 订单项本次退款金额
                BigDecimal itemAdjustAmount = poi.getAdjustAmount() == null ? BigDecimal.ZERO : poi.getAdjustAmount();
                // 订单项可退款金额
                BigDecimal itemRefundableAmount = orderRefundEntity
                        .getRefundableAmount(poi.getOrderItem().getSettlementAmount(), itemRefundedAmount);
                // 如果订单项的可退款金额少于本次退款金额，把本次退款金额修正为订单项可退款金额
                if (itemRefundableAmount.compareTo(itemAdjustAmount) < 0) {
                	log.warn("[{}]退款结果通知错误,订单项{}可退金额为{}，申请退款金额{}已经超出订单项的可退款金额，退款金额更正为可退款金额！",
                            orderRefundEntity.getSn(), poi.getOrderItem().getId(), itemRefundableAmount,
                            itemAdjustAmount);
                    log.warn(orderRefundEntity.toString());
                    itemAdjustAmount = itemRefundableAmount;
                }
                // 更新订单项的已退款金额
                poi.getOrderItem().setRefundedAmount(itemRefundedAmount.add(itemAdjustAmount));
                // 更新订单项的退款状态
                if (poi.getOrderItem().getSettlementAmount().compareTo(poi.getOrderItem().getRefundedAmount()) > 0) {
                    poi.getOrderItem().setRefundState(EntityContext.PRODUCT_ORDER_ITEM_REFUND_STATE_REFUND_PART);// 订单退款状态变为部分退款状态
                } else {
                    poi.getOrderItem().setRefundState(EntityContext.PRODUCT_ORDER_ITEM_REFUND_STATE_REFUND_SUCCESS);// 订单退款状态变为已经完成退款
                }

                // 输出日志
                sb.append("产品[" + poi.getOrderItem().getProduct().getProductCode() + "]，退款["
                        + itemAdjustAmount.toString() + "]元");
                log.info("产品[" + poi.getOrderItem().getProduct().getProductCode() + "]，退款["
                        + itemAdjustAmount.toString() + "]元");
            }
            /*
             * // 更新订单的已退金额
             * order.setRefundAmount(order.getRefundAmount().add(refundAmount)); //
             * 更新售后记录的已退金额
             * salesReturn.setRefundedAmount(salesReturn.getRefundedAmount().add(
             * refundAmount)); // 更新售后记录的退款状态
             * salesReturn.setReturnType(EntityContext.SALES_RETURN_COMPLETE); // 更新售后日志
             * OperationDiaryForm salesReturnDiaryForm = new
             * OperationDiaryForm("退款结果","支付平台退款成功！",DateUtil.formatLongFormat(new
             * Date()),"",null,"U"); salesReturn.addDiaryItem(salesReturnDiaryForm);
             */
            // 修改订单和售后相关信息
            refundSuccessCallback(salesReturn, order, refundAmount);

            // 如果存在父订单，更新父订单的退款金额
            if (order.getParentOrder() != null) {
                BigDecimal parentRefundAmount = order.getParentOrder().getRefundAmount() == null ? BigDecimal.ZERO
                        : order.getParentOrder().getRefundAmount(); // 获取订单可退款金额
                order.getParentOrder().setRefundAmount(parentRefundAmount.add(orderRefundEntity.getAmount()));
            }

            traceMsg = "由于用户申售后退款[" + salesReturn.getId() + "],订单:[" + order.getOrderCode() + "];" + sb.toString()
                    + ",退款成功，总退款金额：" + orderRefundEntity.getAmount().toString() + "元";

        }
        // 整个订单全退
        else if (order != null && orderItem == null) {
            BigDecimal refundAmount = order.getRefundAmount() == null ? BigDecimal.ZERO : order.getRefundAmount(); // 获取订单已退款金额
            // 如果退款金额超出订单可退金额，就把退款金额设置为订单可退金额。
            if (orderRefundEntity.getRefundableAmount().compareTo(orderRefundEntity.getAmount()) < 0) {
            	log.warn("[{}]退款结果通知错误,订单{}可退金额为{}，申请退款金额{}已经超出订单的可退款金额，退款金额更正为可退款金额！", orderRefundEntity.getSn(),
                        order.getOrderCode(), orderRefundEntity.getRefundableAmount(), orderRefundEntity.getAmount());
            	log.warn(orderRefundEntity.toString());
                orderRefundEntity.setAmount(orderRefundEntity.getRefundableAmount());
            }
            // 该订单的所有订单项变成已经完成退款。
            for (ProductOrderItemEntity item : order.getOrderItemList()) {
                if (item.getRefundState() != EntityContext.PRODUCT_ORDER_ITEM_REFUND_STATE_REFUND_SUCCESS) {
                    item.setRefundedAmount(item.getSettlementAmount());
                    // 订单退款状态变为已经完成退款
                    item.setRefundState(EntityContext.PRODUCT_ORDER_ITEM_REFUND_STATE_REFUND_SUCCESS);
                }
            }
            // 更新订单的已经退款金额
            order.setRefundAmount(refundAmount.add(orderRefundEntity.getAmount()));
            // order.setOrderState(EntityContext.PRODUCT_ORDER_STATE_CANCEL);// 退款完成，取消订单。

            // 如果存在父订单，更新父订单的退款金额
            if (order.getParentOrder() != null) {
                BigDecimal parentRefundAmount = order.getParentOrder().getRefundAmount() == null ? BigDecimal.ZERO
                        : order.getParentOrder().getRefundAmount(); // 获取订单可退款金额
                order.getParentOrder().setRefundAmount(parentRefundAmount.add(orderRefundEntity.getAmount()));
            }

            traceMsg = "订单ID:" + order.getOrderCode() + ",退款成功,退款金额：" + orderRefundEntity.getAmount().toString() + "元";

        } else if (order != null && orderItem != null) {
            // 订单项退款
            // 订单项已经退款金额
            BigDecimal refundAmount = orderItem.getRefundedAmount() == null ? BigDecimal.ZERO
                    : orderItem.getRefundedAmount();
            // 订单项可退款金额
            BigDecimal refundableAmount = orderRefundEntity.getRefundableAmount(orderItem.getSettlementAmount(),
                    refundAmount);
            // 如果订单项的可退款金额少于本次退款金额，把本次退款金额修正为订单项可退款金额
            if (refundableAmount.compareTo(orderRefundEntity.getAmount()) < 0) {
            	log.warn("[{}]退款结果通知错误,订单项{}可退金额为{}，申请退款金额{}已经超出订单项的可退款金额，退款金额更正为可退款金额！", orderRefundEntity.getSn(),
                        orderItem.getId(), refundableAmount, orderRefundEntity.getAmount());
            	log.warn(orderRefundEntity.toString());
                orderRefundEntity.setAmount(refundableAmount);
            }
            // 更新订单项的已经退款金额
            orderItem.setRefundedAmount(refundAmount.add(orderRefundEntity.getAmount()));

            if (orderItem.getSettlementAmount().compareTo(orderItem.getRefundedAmount()) > 0) {
                // 订单退款状态变为部分退款状态
                orderItem.setRefundState(EntityContext.PRODUCT_ORDER_ITEM_REFUND_STATE_REFUND_PART);
            } else {
                // 订单退款状态变为已经完成退款
                orderItem.setRefundState(EntityContext.PRODUCT_ORDER_ITEM_REFUND_STATE_REFUND_SUCCESS);
            }

            BigDecimal orderRefundAmount = order.getRefundAmount() == null ? BigDecimal.ZERO : order.getRefundAmount(); // 获取订单已退款金额
            order.setRefundAmount(orderRefundAmount.add(orderRefundEntity.getAmount()));// 更新订单的已经退款金额

            // 如果存在父订单，更新父订单的退款金额
            if (order.getParentOrder() != null) {
                // 获取订单可退款金额
                BigDecimal parentRefundAmount = order.getParentOrder().getRefundAmount() == null ? BigDecimal.ZERO
                        : order.getParentOrder().getRefundAmount();
                order.getParentOrder().setRefundAmount(parentRefundAmount.add(orderRefundEntity.getAmount()));
            }

            traceMsg = "订单:[" + order.getOrderCode() + "]，产品:[" + orderItem.getProductSN() + "]退款成功，修改订单项的状态为退款成功，退款金额："
                    + orderRefundEntity.getAmount().toString() + "元";
        }
        // 记录订单变化轨迹
        Timestamp curTime = new Timestamp(new Date().getTime());
        ProductOrderTraceEntity traceEntity = new ProductOrderTraceEntity();
        traceEntity.setCreateTime(curTime);
        traceEntity.setLastModifyTime(curTime);
        traceEntity.setProductOrder(order);
        traceEntity.setOrderFlowState(EntityContext.PRODUCT_ORDER_TRACE_REFUND);
        traceEntity.setOrderFlowContent(traceMsg);
        traceEntity.setTraceOwner("卖家");

        productOrderHistoryDao.save(traceEntity);

        // order.setOrderState(EntityContext.PRODUCT_ORDER_STATE_END_REFUND);//订单退款状态变为已经完成退款
        orderRefundEntity.setLastModifyTime(now);
        orderRefundEntity.setRefundStatus(EntityContext.REFUND_APPLY_STATE_FINISH);// 订单退款状态变为已经完成退款
        orderRefundDao.save(orderRefundEntity);
    }

    protected void refundSuccessCallback(SalesReturnEntity salesReturn, ProductOrderEntity order,
                                         BigDecimal refundAmount) {
        /*
         * List<ProductOrderItemEntity> orderItemList = productOrderItemDao.getListByHql
         * ("from ProductOrderItemEntity a where a.productOrder.id=?", orderId);
         */
        List<ProductOrderItemEntity> orderItemList = order.getOrderItemList();
        if (salesReturn.getReturnType() == EntityContext.SALES_RETURN_PASS
                && salesReturn.getAfterSaleType() == EntityContext.AFTER_SALE_TYPE_IS_REFUNDMENT) {

            int delivery = 0;// 发货数量
            int notDelivery = 0;// 未发货数量
            int beforReturn = 0;// 发货前退款数量
            int afterReturn = 0;// 发货后退货退款数量
            int tatol = 0;// 总数量

            // 获取tatol、delivery、beforReturn、afterReturn
            for (ProductOrderItemEntity o : orderItemList) {
                delivery = delivery + o.getDeliveryAmount();
                if (null != o.getSalesReturnOrderItemList() && o.getSalesReturnOrderItemList().size() > 0) {
                    for (SalesReturnOrderItemEntity p : o.getSalesReturnOrderItemList()) {
                        if (p.getSalesReturn().getReturnType() == EntityContext.SALES_RETURN_PASS) {
                            if (p.getSalesReturn().getAfterSaleType() == 1) {
                                beforReturn = beforReturn + p.getAfterSalesAmount();
                            } else {
                                afterReturn = afterReturn + p.getAfterSalesAmount();
                            }
                        }
                    }
                }
                tatol = tatol + o.getOrderNumber();
            }
            // 获取notDelivery
            notDelivery = tatol - delivery - beforReturn - afterReturn;

            if (beforReturn == tatol) {
                order.setOrderState(EntityContext.PRODUCT_ORDER_STATE_CANCEL);
            } else if (delivery + afterReturn > 0 && tatol == afterReturn + beforReturn + delivery) {
                order.setOrderState(EntityContext.PRODUCT_ORDER_STATE_SENDGOODS);
                order.setLogisticsTime(new Timestamp(new Date().getTime()));
            }

        }
        // 更新订单的已退金额
        order.setRefundAmount(order.getRefundAmount().add(refundAmount));
        // 更新售后记录的已退金额
        salesReturn.setRefundedAmount(salesReturn.getRefundedAmount().add(refundAmount));
        // 更新售后记录的退款状态
        salesReturn.setReturnType(EntityContext.SALES_RETURN_COMPLETE);
        // 更新售后日志
        OperationDiaryForm salesReturnDiaryForm = new OperationDiaryForm("退款结果", "支付平台退款成功！",
                DateUtil.formatLongFormat(new Date()), "", null, "U");
        salesReturn.addDiaryItem(salesReturnDiaryForm);
    }

    /**
     * calculateCouponBenefitPrice 根据计算订单项计算优惠券优惠，根据优惠券优惠结果修改对应用的订单项小计
     *
     * @param coupon     优惠券
     * @param orderItems 订单项
     * @return 优惠金额
     */
    protected CaculatePromotionResult calculateCouponBenefitByOrderItems(CouponEntity coupon,
                                                                         Set<ProductOrderItemEntity> orderItems) {
        CaculatePromotionResult caculatePromotionResult = null;
        // 获取优惠券的优惠策略
        String strategyValue = coupon.getCouponSetting().getStrategyValue();
        List<StrategyValue> strategyValueList = StrategyValue.fromStrategyValueString(strategyValue);
        // 转换成为计算优惠的对象
        Set<PromotionItem> promotionItems = PromotionItem.fromProductOrderItems(orderItems);
        // 建立优惠计算实列
        PromotionInstances promotionInstances = PromotionFactory.getPromotionInstances(coupon.getCouponSetting(), null);
        // 计算优惠券是否满足使用条件
        if (promotionInstances.checkCollectionRestriction(promotionItems, strategyValueList.get(0))) {
            // 如果满足，则计算优惠结果
            caculatePromotionResult = promotionInstances.CalculateCollectionPromotion(promotionItems);

            BigDecimal benefitPrice = caculatePromotionResult.getBenefitPrice();
            if (benefitPrice.compareTo(BigDecimal.ZERO) > 0) {
                // 获取集合A原价
                BigDecimal originalPrice = caculatePromotionResult.getOriginalPrice();
                if (originalPrice != null && originalPrice.compareTo(BigDecimal.ZERO) > 0) {
                    // 计算组A优惠后价格
                    BigDecimal currentPrice = originalPrice.subtract(benefitPrice);
                    // 计算优惠价与原价的比率
                    BigDecimal rate = currentPrice.divide(originalPrice, MathContext.DECIMAL128);
                    // 修改集合A的每项的小计=原小计价格*r
                    for (ProductOrderItemEntity orderItem : orderItems) { // 修改订单项的小计价格
                        // 计算优惠券后金额
                        BigDecimal afterBenefitAmount = orderItem.getOrderAmount().multiply(rate);
                        BigDecimal discountAmount = orderItem.getCouponDiscountAmount()
                                .add(orderItem.getOrderAmount().subtract(afterBenefitAmount));
                        // 优惠后金额不能小于零
                        afterBenefitAmount = afterBenefitAmount.compareTo(BigDecimal.ZERO) > 0 ? afterBenefitAmount
                                : BigDecimal.ZERO;
                        // 优惠金额的最大值不能超过原价。
                        discountAmount = discountAmount.compareTo(orderItem.getOrderAmount()) > 0
                                ? orderItem.getOrderAmount()
                                : discountAmount;
                        // 修改订单项促销活动的优惠金额
                        orderItem.setCouponDiscountAmount(shopContextInfo.setScale(discountAmount));
                        // 修改订单项小计。
                        orderItem.setOrderAmount(shopContextInfo.setScale(afterBenefitAmount));
                    }
                }
            }

        }

        return caculatePromotionResult;
    }

    /**
     * calculateCouponBenefitPrice 根据购物车项计算优惠券优惠，根据优惠券优惠结果修改对应用的购物车项的小计
     *
     * @param coupon    优惠券
     * @param cartItems 购物车项
     * @return 优惠金额
     */
    protected CaculatePromotionResult calculateCouponBenefitByCartItems(CouponEntity coupon,
                                                                        Set<CartItemEntity> cartItems) {
        // TODO Auto-generated method stub

        CaculatePromotionResult caculatePromotionResult = new CaculatePromotionResult();
        if (coupon.isValid() == false || coupon.getCouponSetting().isNormalState() == false) {
            return caculatePromotionResult;
        }
        // 获取优惠券的优惠策略
        // String strategyValue = getCouponSetting().getStrategyValue();
        // List<StrategyValue> strategyValueList =
        // StrategyValue.fromStrategyValueString(strategyValue);
        // 转换成为计算优惠的对象
        Set<PromotionItem> promotionItems = PromotionItem.fromCartItems(cartItems);
        // 建立优惠计算实列
        PromotionInstances promotionInstances = PromotionFactory.getPromotionInstances(coupon.getCouponSetting(), null);
        // 计算优惠结果
        caculatePromotionResult = promotionInstances.CalculateCollectionPromotion(promotionItems);

        // 计算优惠券是否满足使用条件
        if (caculatePromotionResult != null && caculatePromotionResult.getSelectedStrategyValue() != null) {

            BigDecimal benefitPrice = caculatePromotionResult.getBenefitPrice();
            if (benefitPrice.compareTo(BigDecimal.ZERO) > 0) {
                // 获取集合A原价
                BigDecimal originalPrice = caculatePromotionResult.getOriginalPrice();
                if (originalPrice != null && originalPrice.compareTo(BigDecimal.ZERO) > 0) {
                    // 计算组A优惠后价格
                    BigDecimal currentPrice = originalPrice.subtract(benefitPrice);
                    // 计算优惠价与原价的比率
                    BigDecimal rate = currentPrice.divide(originalPrice, MathContext.DECIMAL128);
                    // 修改集合A的每项的小计=原小计价格*r
                    for (CartItemEntity orderItem : cartItems) { // 修改订单项的小计价格
                        orderItem.setAmount(orderItem.getAmount().multiply(rate));
                    }
                }
            }
        }
        return caculatePromotionResult;
    }

    /**
     * 根据计算订单项计算促销活动优惠，根据促销活动优惠结果修改对应用的订单项小计
     *
     * @param promotion  促销活动
     * @param orderItems 订单项
     * @return 优惠金额
     */
    protected CaculatePromotionResult calculatePromotionBenefitPrice(PromotionEntity promotion,
                                                                     Set<ProductOrderItemEntity> orderItems) {
        CaculatePromotionResult caculatePromotionResult = null;

        // 转换成为计算优惠的对象
        Set<PromotionItem> promotionItems = PromotionItem.fromProductOrderItems(orderItems);
        // 建立优惠计算实列
        PromotionInstances promotionInstances = PromotionFactory.getPromotionInstances(promotion, null);

        // 则计算优惠结果
        caculatePromotionResult = promotionInstances.CalculateCollectionPromotion(promotionItems);

        BigDecimal benefitPrice = caculatePromotionResult.getBenefitPrice();
        if (benefitPrice.compareTo(BigDecimal.ZERO) > 0) {
            // 获取集合A原价
            BigDecimal originalPrice = caculatePromotionResult.getOriginalPrice();
            if (originalPrice != null && originalPrice.compareTo(BigDecimal.ZERO) > 0) {
                // 计算组A优惠后价格
                BigDecimal currentPrice = originalPrice.subtract(benefitPrice);
                // 计算优惠价与原价的比率
                BigDecimal rate = currentPrice.divide(originalPrice, MathContext.DECIMAL128);
                // 修改集合A的每项的小计=原小计价格*r
                for (ProductOrderItemEntity orderItem : orderItems) { // 修改订单项的小计价格
                    // 计算优惠券后金额
                    BigDecimal afterBenefitAmount = orderItem.getOrderAmount().multiply(rate);
                    // 优惠的金额
                    BigDecimal discountAmount = orderItem.getDiscountAmount()
                            .add(orderItem.getOrderAmount().subtract(afterBenefitAmount));
                    // 优惠后金额不能小于零
                    afterBenefitAmount = afterBenefitAmount.compareTo(BigDecimal.ZERO) > 0 ? afterBenefitAmount
                            : BigDecimal.ZERO;
                    // 优惠金额的最大值不能超过原价。
                    discountAmount = discountAmount.compareTo(orderItem.getOrderAmount()) > 0
                            ? orderItem.getOrderAmount()
                            : discountAmount;
                    // 修改订单项促销活动的优惠金额
                    orderItem.setDiscountAmount(shopContextInfo.setScale(discountAmount));
                    // 修改订单项小计。
                    orderItem.setOrderAmount(shopContextInfo.setScale(afterBenefitAmount));
                }
            }
        }
        return caculatePromotionResult;
    }

    /**
     * isLive 判断是否演唱会，并生成同步票根
     *
     * @param order 订单
     */
    public void isLive(ProductOrderEntity order) {
        // 判断是否为演唱会商品
        try {
            List temp = order.getOrderItemList();
            ArrayList liveList = new ArrayList();
            ArrayList<ProductOrderItemEntity> orderList = new ArrayList();
            // 遍历订单中商品，判断是否含有演唱会商品
            for (int k = 0; k < temp.size(); k++) {
                ProductOrderItemEntity item = order.getOrderItemList().get(k);
                Integer id = item.getProduct().getProductSpecification().getId();
                ProductSpecificationDetailEntity productSpecificationDetailEntity;
                // 根据ProductSpecificationId获取productSpecificationDetailEntity中Specifications
                try {
                    productSpecificationDetailEntity = productSpecificationDetailDao.getUniqueByHql(

                            "select a from ProductSpecificationDetailEntity a where a.specificationId=\'" + id + "\'");
                } catch (Exception e) {
                    productSpecificationDetailEntity = null;
                    log.info("查询失败");
                }
                String spec = productSpecificationDetailEntity.getSpecifications();

                ObjectMapper mapper = new ObjectMapper();
                boolean isLive = false;// 默认为非演唱会
                try {
                    // 处理Specifications信息
                    ArrayList list = mapper.readValue(spec, ArrayList.class);
                    for (int i = 0; i < list.size(); i++) {
                        Map<String, Object> d = (Map<String, Object>) list.get(i);
                        try {
                            ArrayList paras = (ArrayList) d.get("parameters");

                            for (int j = 0; j < paras.size(); j++) {
                                Map<String, String> q = (Map<String, String>) paras.get(j);
                                // 若包含演唱会商品，商品信息加入待发送数据
                                if (q.get("name").equals("内容提供商") && q.get("value").equals("UTVGO")) {
                                    isLive = true;
                                    LiveTicketsForm form = new LiveTicketsForm();
                                    log.info(item.getProduct().getProductSpecification().toString());
                                    form.setLiveName(item.getProduct().getProductSpecification().getProductName());
                                    form.setFreeCycle("D");
                                    form.setLiveCode(item.getProduct().getMappingThirdCode());
                                    form.setOrderId(String.valueOf(order.getOrderCode()));
                                    form.setPhone(order.getReceiverPhone());
                                    for (int l = 0; l < item.getOrderNumber(); l++) {
                                        liveList.add(form);
                                        orderList.add(item);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            log.error(e.getMessage());
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error(e.getMessage());
                }
                // 若是演唱会商品，生成同步演唱会门票
                if (isLive) {
                	log.info("演唱会商品");
                	log.info("执行同步演唱会门票");
                    LiveTicketsReq req = new LiveTicketsReq();
                    req.setLiveTicketsList(liveList);
                    generateAndSyncLiveTicketCode(req, order, orderList);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }

    }

    protected IResponse generateAndSyncLiveTicketCode(LiveTicketsReq req, ProductOrderEntity order,
                                                      ArrayList<ProductOrderItemEntity> orderlist) {
        LiveTicketRes res = new LiveTicketRes();
        ObjectMapper objectMapper = new ObjectMapper();

        ArrayList liveDataList = new ArrayList();
        for (int i = 0; i < req.getLiveTicketsList().size(); i++) {
            Map<String, String> liveData = new HashMap<>();
            LiveTicketsForm t = req.getLiveTicketsList().get(i);
            String liveTicketCode = generateCode(t.getFreeCycle());
            liveData.put("liveCode", t.getLiveCode());
            liveData.put("code", liveTicketCode);
            liveData.put("phone", t.getPhone());
            liveData.put("freeCycle", t.getFreeCycle());
            liveData.put("keyNo", t.getKeyNo());
            liveData.put("orderId", t.getOrderId());
            liveData.put("liveName", t.getLiveName());
            liveDataList.add(liveData);

            ExpressPackageEntity expressPackage = new ExpressPackageEntity();

            List<ExpressPackageOrderItemEntity> expressPackageOrderItemList = new ArrayList<ExpressPackageOrderItemEntity>();
            ExpressPackageOrderItemEntity expressPackageOrderItem = new ExpressPackageOrderItemEntity();

            expressPackageOrderItem.setAmount(1);
            expressPackageOrderItem.setOrderItem(orderlist.get(i));
            expressPackageOrderItemList.add(expressPackageOrderItem);
            expressPackage.setProductOrder(order);
            expressPackage.setExpressType(2);
            expressPackage.setLogisiticsName("演唱会票根码");
            expressPackage.setLogisiticsNumber(liveTicketCode);
            expressPackage.setState(EntityContext.RECORD_STATE_VALID);
            expressPackage.setDeliveryStatus(1);
            expressPackage.setExpressPackageOrderItemList(expressPackageOrderItemList);

            // 写入数据库发货表
            try {
                expressPackageDao.save(expressPackage);
                for (ExpressPackageOrderItemEntity expressPackageOrderItem1 : expressPackageOrderItemList) {
                    expressPackageOrderItem1.setExpressPackage(expressPackage);
                    expressPackageOrderItemDao.save(expressPackageOrderItem1);
                    expressPackageOrderItem1.getOrderItem()
                            .setDeliveryAmount(expressPackageOrderItem1.getOrderItem().getDeliveryAmount()
                                    + expressPackageOrderItem.getAmount());
                    if (null != expressPackageOrderItem1.getOrderItem()) {
                        expressPackageOrderItem1.getOrderItem()
                                .setProductSN(expressPackageOrderItem.getOrderItem().getProductSN());
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("写入数据库失败:" + e.getMessage());
            }
        }
        // 同步数据到UTVGO
        try {
            String para = objectMapper.writeValueAsString(liveDataList);
            System.out.println(para);
            String result = HttpClientUtil.post(shopContextInfo.utvgoLiveSyncUrl, para);
            Map<String, String> object = objectMapper.readValue(result, Map.class);
            String retCode = object.get("code");
            String message = object.get("message");
            log.info("UTVGO接口返回：" + result);
            if (retCode.equals("1")) {
                res.setResultList(liveDataList);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(message);
            } else {
                res.setRetInfo(message);
                res.setRet(ResponseContext.RES_LIVE_TICKET_ERROR_CODE);
                res.setResultList(liveDataList);
            }
        } catch (Exception e) {
        	log.error(e.getMessage());
        }
        // 同步成功则发送短信到用户手机
        if (res.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
            for (int i = 0; i < liveDataList.size(); i++) {
                ObjectNode json = objectMapper.createObjectNode();

                Map<String, String> liveData = (Map<String, String>) liveDataList.get(i);

                String content = "您在U点购物平台购买的 " + liveData.get("liveName") + " 演唱会票根码为：【" + liveData.get("code")
                        + "】，联系电话：" + liveData.get("phone") + "。为保障您的权益，请勿向他人泄露提货码。";
                // 发送短信
                json.put("messageContent", content);
                json.put("userNumber", liveData.get("phone"));
                json.put("scheduleTime", DateUtil.format(new Date(), 1));
                json.put("f", "1");
                // Map<String, String> result = null;
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
                    HttpClientUtil.post(contextInfo.smsInterfaceUrl, mapper.writeValueAsString(json), "utf-8");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return res;
    }

    protected void cancelLive(Integer refundId) {

        Integer orderItemId = null;
        // 根据退货ID查找对应演唱会票根码等信息
        SalesReturnOrderItemEntity salesReturnOrderItemEntity;
        try {
            salesReturnOrderItemEntity = salesReturnOrderItemDao.getUniqueByHql(

                    "select a from SalesReturnOrderItemEntity a where a.salesReturn.id=\'" + refundId + "\'");
            orderItemId = salesReturnOrderItemEntity.getOrderItem().getId();

        } catch (Exception e) {
        	log.info("SalesReturnOrderItemEntity空的");

        }

        ProductOrderItemEntity productOrderItemEntity;
        try {
            productOrderItemEntity = productOrderItemDao.getUniqueByHql(

                    "select a from ProductOrderItemEntity a where a.id=\'" + orderItemId + "\'");
        } catch (Exception e) {
            productOrderItemEntity = null;
            log.info("productOrderItemEntity空的");
        }


        // 同一订单含有多张票根码时全退
        List<ExpressPackageOrderItemEntity> expressPackageOrderItemEntityList;
        try {
            expressPackageOrderItemEntityList = expressPackageOrderItemDao.getListByHql(

                    "select a from ExpressPackageOrderItemEntity a where a.orderItem.id=\'" + orderItemId + "\'");
        } catch (Exception e) {
            expressPackageOrderItemEntityList = null;
            log.info("expressPackageOrderItemEntity空的");
        }

        // 订单中所有商品写入退货list后一次性调用UTVGO接口注销
        ArrayList list = new ArrayList();
        for (int i = 0; i < expressPackageOrderItemEntityList.size(); i++) {
            ExpressPackageEntity expressPackageEntity;
            try {
                expressPackageEntity = expressPackageDao
                        .get(expressPackageOrderItemEntityList.get(i).getExpressPackage().getId());

            } catch (Exception e) {
                expressPackageEntity = null;
                log.info("expressPackageEntity空的");

            }
            log.info(expressPackageEntity.getExpressType().toString() + "########################################################");
            if (expressPackageEntity.getExpressType().equals(2)) {
                LiveTicketsForm liveTicketsForm = new LiveTicketsForm();
                liveTicketsForm.setLiveCode(expressPackageEntity.getLogisiticsNumber());
                ProductOrderEntity productOrderEntity;
                try {
                    productOrderEntity = productOrderDao.getUniqueByHql(

                            "select a from ProductOrderEntity a where a.id=\'"
                                    + expressPackageEntity.getProductOrder().getId() + "\'");
                } catch (Exception e) {
                    productOrderEntity = null;
                    log.info("productOrderEntity空的");
                }
                liveTicketsForm.setPhone(productOrderEntity.getReceiverPhone());
                liveTicketsForm.setOrderId(productOrderEntity.getOrderCode());
                liveTicketsForm
                        .setLiveName(productOrderItemEntity.getProduct().getProductSpecification().getProductName());
                list.add(liveTicketsForm);
                LiveTicketsReq req = new LiveTicketsReq();
                req.setLiveTicketsList(list);
                cancelLiveTicketCode(req);
            }
        }

    }

    protected IResponse cancelLiveTicketCode(LiveTicketsReq req) {
        LiveTicketRes res = new LiveTicketRes();
        ObjectMapper objectMapper = new ObjectMapper();

        String url = shopContextInfo.utvgoLiveCancelUrl;
        ArrayList liveDataList = new ArrayList();
        for (int i = 0; i < req.getLiveTicketsList().size(); i++) {
            Map<String, String> liveData = new HashMap<>();
            LiveTicketsForm t = req.getLiveTicketsList().get(i);

            liveData.put("code", t.getLiveCode());
            liveData.put("phone", t.getPhone());
            liveData.put("keyNo", t.getKeyNo());
            liveData.put("orderId", t.getOrderId());
            liveDataList.add(liveData);
            log.info(liveData.toString());
        }
        try {
            String para = objectMapper.writeValueAsString(liveDataList);
            String result = HttpClientUtil.post(url, para);
            Map<String, String> object = objectMapper.readValue(result, Map.class);
            String retCode = object.get("code");
            String message = object.get("message");
            log.info(result);
            if (retCode.equals("1")) {
                res.setResultList(liveDataList);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo("成功注销票根码");
                log.info("成功注销票根码");
            } else {
                res.setRetInfo(message);
                res.setRet(ResponseContext.RES_LIVE_TICKET_ERROR_CODE);
                res.setResultList(liveDataList);
                log.info("票根码注销失败");
                log.info(result);
            }
        } catch (Exception e) {
        	log.error(e.getMessage());
        }
        if (res.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
            for (int i = 0; i < liveDataList.size(); i++) {
                ObjectNode json = objectMapper.createObjectNode();
                LiveTicketsForm t = req.getLiveTicketsList().get(i);
                Map<String, String> liveData = (Map<String, String>) liveDataList.get(i);

                String content = "您在U点购物平台购买的 " + t.getLiveName() + " 已退货，演唱会票根码为：【" + liveData.get("code") + "】已注销。";
                // 发送短信
                json.put("messageContent", content);
                json.put("userNumber", liveData.get("phone"));
                json.put("scheduleTime", DateUtil.format(new Date(), 1));
                json.put("f", "1");
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
                    HttpClientUtil.post(contextInfo.smsInterfaceUrl, mapper.writeValueAsString(json), "utf-8");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return res;
    }

    private String generateCode(String freeCycle) {
        String code = createCodeNum(freeCycle);
        if (true == checkCode(code)) {
            return code;
        } else
            return generateCode(freeCycle);
    }

    private String createCodeNum(String freeCycle) {
        String codeNum = "";
        // 包年
        if ("Y".equals(freeCycle)) {
            codeNum = "1";
        }
        if ("H".equals(freeCycle)) {
            codeNum = "2";
        }
        if ("Q".equals(freeCycle)) {
            codeNum = "3";
        }
        if ("M".equals(freeCycle)) {
            codeNum = "4";
        }
        if ("D".equals(freeCycle)) {
            codeNum = "5";
        }
        // 2~5位(4位)
        Integer num2Str = (int) (Math.random() * 8998) + 1000 + 1;
        // 6位
        codeNum += num2Str;
        Integer sum1 = 0;
        for (int i = 0; i < codeNum.length(); i++) {
            if (Character.isDigit(codeNum.charAt(i))) {
                sum1 = sum1 + Integer.parseInt(codeNum.charAt(i) + "");
            }
        }
        codeNum = codeNum + (sum1 % 10);
        // 7~10（4位）
        Integer num3Str = (int) (Math.random() * 8998) + 1000 + 1;
        codeNum = codeNum + num3Str;
        // 11位
        Integer sum2 = 0;
        for (int i = 0; i < codeNum.length(); i++) {
            if (Character.isDigit(codeNum.charAt(i))) {
                sum2 = sum2 + Integer.parseInt(codeNum.charAt(i) + "");
            }
        }
        codeNum = codeNum + (sum2 % 10);
        // 12位
        Random rand = new Random();
        int randNum = rand.nextInt(9);
        codeNum = codeNum + randNum;
        return codeNum;
    }

    /**
     * 校验验证码合法性
     *
     * @param codeNum
     * @return
     */
    private static boolean checkCode(String codeNum) {
        if (StringUtils.isBlank(codeNum) || codeNum.length() != 12) {
            return false;
        }
        // 获取第1位数字
        String oneNum = codeNum.substring(0, 1);
        // 校验首位数字
        if (!"12345".contains(oneNum)) {
            return false;
        }
        String numStr = codeNum.substring(0, 5);
        String sixNun = codeNum.substring(5, 6);
        Integer sun1 = 0;
        for (int i = 0; i < numStr.length(); i++) {
            if (Character.isDigit(numStr.charAt(i))) {
                sun1 = sun1 + Integer.parseInt(numStr.charAt(i) + "");
            }
        }
        Integer sumSixNum = sun1 % 10;
        // 校验第6位数字
        if (!sixNun.equals(sumSixNum + "")) {
            return false;
        }
        // 获取第11位数字
        String numStr2 = codeNum.substring(0, 10);
        String elevenNun = codeNum.substring(10, 11);
        Integer sun2 = 0;
        for (int i = 0; i < numStr2.length(); i++) {
            if (Character.isDigit(numStr2.charAt(i))) {
                sun2 = sun2 + Integer.parseInt(numStr2.charAt(i) + "");
            }
        }
        Integer sumElevenNum = sun2 % 10;
        if (!elevenNun.equals(sumElevenNum + "")) {
            return false;
        }
        return true;
    }


    protected Map<String, String> orderBossProduct(Map requestContent) {
        Map<String, String> res = new HashMap<>();
        ObjectMapper objectMapper = new ObjectMapper();

        String url = contextInfo.bossOrderUrl;
        try {
            Map<String, Object> requestJson = signParas(jsonToMap(objectMapper.writeValueAsString(requestContent)));
            JSONObject jsonObj = new JSONObject(requestJson);

            Map<String, String> result = HttpClientUtil.post(url, jsonObj.toString(), "UTF-8");
            JSONObject response = new JSONObject(result.get("response"));

            if ("0".equals(response.getString("errorCode"))) {

                JSONObject data = response.getJSONObject("data").getJSONObject("output");
                res.put("orderId", data.getString("orderid"));
                res.put("orderType", data.getString("ordertype"));
                res.put("feeName", data.getString("feename"));
                res.put("sums", data.getString("sums"));
                res.put("areaId", data.getString("areaid"));
                res.put("city", data.getString("city"));
                res.put("errorCode", response.getString("errorCode"));
                res.put("errorMessage", response.getString("errorMessage"));

            } else {
                res.put("errorCode", response.getString("errorCode"));
                res.put("errorMessage", response.getString("errorMessage"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }


    protected Map<String, String> confirmBossOrder(Map requestContent) {
        Map<String, String> res = new HashMap<>();
        ObjectMapper objectMapper = new ObjectMapper();

        String url = contextInfo.bossConfirmOrderUrl;
        try {
            Map<String, Object> requestJson = signParas(jsonToMap(objectMapper.writeValueAsString(requestContent)));
            JSONObject jsonObj = new JSONObject(requestJson);

            Map<String, String> result = HttpClientUtil.post(url, jsonObj.toString(), "UTF-8");
            JSONObject response = new JSONObject(result.get("response"));

            if ("0".equals(response.getString("errorCode"))) {

                JSONObject data = response.getJSONObject("data").getJSONObject("output");
                res.put("code", data.getString("code"));
                res.put("errorCode", response.getString("errorCode"));
                res.put("errorMessage", response.getString("errorMessage"));
            } else {
                res.put("errorCode", response.getString("errorCode"));
                res.put("errorMessage", response.getString("errorMessage"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }

    private Map<String, Object> signParas(Map<String, Object> requestContent) {
        Map<String, Object> signMap = new HashMap<>();
        signMap.put("key", contextInfo.bossKey);
        signMap.put("requestContent", requestContent);
        signMap.put("timestamp", System.currentTimeMillis());
        signMap.put("dataSign", sign(signMap));
        return signMap;
    }

    public String sign(Map<String, Object> map) {
        if (map == null) {
            return null;
        }
        List<String> keyList = new ArrayList<>(map.keySet());
        Collections.sort(keyList);
        StringBuilder sb = new StringBuilder();
        for (String key : keyList) {
            Object value = map.get(key);
            ObjectMapper objectMapper = new ObjectMapper();

            if (value instanceof TreeMap) {
                try {
                    String json = objectMapper.writeValueAsString(value);
                    sb.append(json).append("|");
                    continue;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            sb.append(value).append("|");
        }
        String signStr = sb.substring(0, sb.length() - 1) + "|" + contextInfo.bossSecret;
        return DigestUtils.md5Hex(signStr).toUpperCase();
    }

    /**
     * JSON转顺序排序的Map
     *
     * @param jsonStr 原始json
     * @return 响应的map
     */
    public static Map<String, Object> jsonToMap(String jsonStr) {
        Map<String, Object> treeMap = new TreeMap();
        JSONObject json = new JSONObject(jsonStr);//Feature.OrderedField实现解析后保存不乱序
        for (String key : (Iterable<String>) json.keySet()) {
            Object value = json.get(key);
            //判断传入kay-value中是否含有""或null
            if (json.get(key) == null || value == null || value.toString().length() == 0) {
                //当JSON字符串存在null时,不将该kay-value放入Map中,即显示的结果不包括该kay-value
                continue;
            }
            // 判断是否为JSONArray(json数组)
            if (value instanceof JSONArray) {
                JSONArray jsonArray = (JSONArray) value;
                List<Object> arrayList = new ArrayList<>();
                for (int i = 0; i < jsonArray.length(); i++) {
//                for (Object object : jsonArray) {
                    // 判断是否为JSONObject，如果是 转化成TreeMap
                    Object object = jsonArray.get(i);
                    if (object instanceof JSONObject) {
                        object = jsonToMap(jsonArray.get(i).toString());
                    }
                    arrayList.add(object);
                }
                treeMap.put(key, arrayList);
            } else {
                //判断该JSON中是否嵌套JSON
                boolean flag = isJSONValid(value.toString());
                if (flag) {
                    //若嵌套json了,通过递归再对嵌套的json(即子json)进行排序
                    value = jsonToMap(value.toString());
                }
                // 其他基础类型直接放入treeMap
                // JSONObject可进行再次解析转换
                treeMap.put(key, value);
            }
        }
        return treeMap;
    }

    /**
     * 校验是否是JSON字符串
     *
     * @param jsonStr 传入数据
     * @return 是JSON返回true, 否则false
     */
    public static boolean isJSONValid(String jsonStr) {
        try {
            new JSONObject(jsonStr);
        } catch (JSONException ex) {
            return false;
        }
        return true;
    }
    
	/**
	 * 根据DepartmentCode 查询部门信息
	 * 
	 * @param deptCode 部门编号
	 * @return
	 */
	protected DepartmentEntity getDepartmentByCode(String deptCode) {
		return departmentDao.getUniqueByHql(
				"select d from DepartmentEntity d where d.state = 1 and d.departmentCode = '" + deptCode + "'");
	}
	
	/**
	 * 获取订单用户信息
	 * @param order
	 * @return
	 */
	protected MemberEntity getMemberByOrder(ProductOrderEntity order) {
		String hql = "select m.* from t_account m where m.id = (select o.memberId from t_product_order o where o.id = " + order.getId() + ")";
		return memberDao.getUniqueBySql(hql);
	}

}
