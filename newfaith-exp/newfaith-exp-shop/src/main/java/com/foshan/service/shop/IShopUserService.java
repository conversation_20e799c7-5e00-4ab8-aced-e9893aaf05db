package com.foshan.service.shop;

import javax.servlet.http.HttpServletRequest;

import com.foshan.form.response.IResponse;
import com.foshan.form.shop.request.ShopUserLoginReq;
import com.foshan.form.shop.request.ShopUserReq;

public interface IShopUserService {
	public IResponse login(ShopUserLoginReq req,HttpServletRequest request);
	public IResponse addShopUser(ShopUserReq req, HttpServletRequest request);
	public IResponse getShopUserList(ShopUserReq req);
	public IResponse getShopUserInfo(ShopUserReq req);
	public IResponse deleteShopUser(ShopUserReq req, HttpServletRequest request);
	public IResponse modifyShopUser(ShopUserReq req, HttpServletRequest request);
	public IResponse getShopSmsCode(ShopUserReq req);
	public IResponse unlockShopUser(ShopUserLoginReq req);
	public IResponse addShopUser(ShopUserReq req);
}
