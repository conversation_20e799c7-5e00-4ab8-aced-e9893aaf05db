package com.foshan.util;

import java.util.concurrent.ConcurrentHashMap;

import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.query.NativeQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.foshan.entity.shop.MemberEntity;
import com.foshan.entity.shop.ProductEntity;
import com.foshan.entity.shop.ProductOrderEntity;
import com.foshan.entity.shop.ProductOrderItemEntity;
import com.foshan.entity.shop.ProductSpecificationEntity;
import com.foshan.form.shop.ProductLimitForm;

public class ProductLimit {
	private final static Logger logger = LoggerFactory.getLogger(ProductLimit.class);

	//产品限购缓存<specificationId,<productid,productLimitForm>>
	public static ConcurrentHashMap<Integer, ConcurrentHashMap<Integer, ProductLimitForm>> limit = new ConcurrentHashMap<>();
	public static ConcurrentHashMap<Integer, ConcurrentHashMap<Integer, Integer>> purchase = new ConcurrentHashMap<>();
	public static ConcurrentHashMap<Integer, Integer> sales = new ConcurrentHashMap<>();

	public static void returnInventory(ProductOrderEntity productOrder) {
		for (ProductOrderItemEntity o : productOrder.getOrderItemList()) {
			returnInventory(o);
		}
	}


	public static void returnInventory(ProductOrderItemEntity productOrderItem) {

		ProductEntity product = productOrderItem.getProduct();
		ProductSpecificationEntity specification = product.getProductSpecification();

		if (ProductLimit.limit.containsKey(specification.getId())) {
			ConcurrentHashMap<Integer, ProductLimitForm> subLimit = ProductLimit.limit.get(specification.getId());
			if (subLimit.containsKey(product.getId())) {
				SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
				Session session = sessionFactory.openSession();
				String hql = "select m.* from t_account m where m.id = (select o.memberId from t_product_order o where o.id = " +productOrderItem.getProductOrder().getId() + ")";
				@SuppressWarnings("unchecked")
				NativeQuery<MemberEntity> query = session.createSQLQuery(hql).addEntity(MemberEntity.class);
				query.setCacheable(true);
				MemberEntity member = query.uniqueResult();
				try {
					ProductLimitForm limitForm = subLimit.get(product.getId());
					switch (limitForm.getLimitType()) {
					case 1:// 只限制购买数量
						if (ProductLimit.purchase.containsKey(product.getId())) {
							ConcurrentHashMap<Integer, Integer> subPurchase = ProductLimit.purchase
									.get(product.getId());
							if (subPurchase.containsKey(member.getId())) {
								if (productOrderItem.getOrderNumber() >= subPurchase.get(member.getId())) {
									subPurchase.remove(member.getId());
									if (subPurchase.size() == 0) {
										ProductLimit.purchase.remove(product.getId());
									}
								} else {
									subPurchase.put(member.getId(),
											subPurchase.get(member.getId()) - productOrderItem.getOrderNumber());
									ProductLimit.purchase.put(product.getId(), subPurchase);
								}

							}
						}
						break;
					case 2:// 限定时间内限定库存
						limitForm.setProductInventoryAmount(
								limitForm.getProductInventoryAmount() + productOrderItem.getOrderNumber());
						subLimit.put(product.getId(), limitForm);
						ProductLimit.limit.put(specification.getId(), subLimit);
						break;
					case 3:// 限定时间内限定购买数量
						if (ProductLimit.purchase.containsKey(product.getId())) {
							ConcurrentHashMap<Integer, Integer> subPurchase = ProductLimit.purchase
									.get(product.getId());
							if (subPurchase.containsKey(member.getId())) {
								if (productOrderItem.getOrderNumber() >= subPurchase.get(member.getId())) {
									subPurchase.remove(member.getId());
									if (subPurchase.size() == 0) {
										ProductLimit.purchase.remove(product.getId());
									}
								} else {
									subPurchase.put(member.getId(),
											subPurchase.get(member.getId()) - productOrderItem.getOrderNumber());
									ProductLimit.purchase.put(product.getId(), subPurchase);
								}
							}
						}
						break;
					case 4:// 限定时间内限定购买数量及限定库存
						if (ProductLimit.purchase.containsKey(product.getId())) {
							ConcurrentHashMap<Integer, Integer> subPurchase = ProductLimit.purchase
									.get(product.getId());
							if (subPurchase.containsKey(member.getId())) {
								if (productOrderItem.getOrderNumber() >= subPurchase.get(member.getId())) {
									subPurchase.remove(member.getId());
									if (subPurchase.size() == 0) {
										ProductLimit.purchase.remove(product.getId());
									}
								} else {
									subPurchase.put(member.getId(),
											subPurchase.get(member.getId()) - productOrderItem.getOrderNumber());
									ProductLimit.purchase.put(product.getId(), subPurchase);
								}
							}
						}
						limitForm.setProductInventoryAmount(
								limitForm.getProductInventoryAmount() + productOrderItem.getOrderNumber());
						subLimit.put(product.getId(), limitForm);
						ProductLimit.limit.put(specification.getId(), subLimit);
						break;
					default:// 只限定库存
						limitForm.setProductInventoryAmount(
								limitForm.getProductInventoryAmount() + productOrderItem.getOrderNumber());
						subLimit.put(product.getId(), limitForm);
						ProductLimit.limit.put(specification.getId(), subLimit);
					}

				} catch (Exception ex) {
					logger.error("产品限购恢复库存失败！！：{}" + ex);
				}
				session.close();
			}
		}

	}

}
