/*
 * Copyright 2005-2017 tvshop.net. All rights reserved.
 * Support: http://www.tvshop.net
 * License: http://www.tvshop.net/license
 */
package com.foshan.util;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import com.foshan.entity.context.EntityContext;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 
 * @ClassName: ShopContextInfo
 * @Description: TODO(商场系统运营参数设置工具类)
 * <AUTHOR>
 * @date 2019年3月12日 下午2:34:22
 *
 */
@Component
@PropertySource(value = "classpath:application.yml",encoding="utf-8")
@Getter
@Setter
@NoArgsConstructor
public class ShopContextInfo extends ContextInfo {
	/**
	 * 
	 */
	private static final long serialVersionUID = 8205174026796512791L;
	@Value("${shop.sn.productPrefix}")
	public  String productPrefix;
	@Value("${shop.sn.orderPrefix}")
	public  String orderPrefix;
	@Value("${shop.sn.orderPaymentPrefix}")
	public  String orderPaymentPrefix;
	@Value("${shop.sn.orderRefundsPrefix}")
	public  String orderRefundsPrefix;
	@Value("${shop.sn.orderShippingPrefix}")
	public  String orderShippingPrefix;
	@Value("${shop.sn.orderReturnsPrefix}")
	public  String orderReturnsPrefix;
	@Value("${shop.sn.paymentSessionPrefix}")
	public  String paymentSessionPrefix;
//	@Value("${shop.siteUrl}")
//	public  String siteUrl;

	public  String nginxWanIp;
	public  String wxH5PaymentCallBackUrl;
	public  String certFileRootDir;
	
	@Value("${shop.customerServiceUrl}")
	public  String customerServiceUrl;
	@Value("${shop.customerServiceName}")
	public  String customerServiceName;
	@Value("${shop.customerServicePasswd}")
	public  String customerServicePasswd;
	@Value("${shop.orderExpirationTime}")
	public  String orderExpirationTime;
	@Value("${shop.autoConfirmReceiptTime}")
	public  String autoConfirmReceiptTime;
	@Value("${shop.boss.integrateUrl}")
	public  String bossIntegrateUrl;
	@Value("${shop.boss.system}")
	public  String bossSystem;
	@Value("${shop.boss.deptid}")
	public  String bossDeptid;
	@Value("${shop.boss.clientcode}")
	public  String bossClientcode;
	@Value("${shop.boss.clientpwd}")
	public  String bossClientpwd;
	@Value("${shop.boss.version}")
	public  String bossVersion;
	@Value("${shop.dockingBoss}")
	public  String dockingBoss;
	@Value("${shop.invoiceServiceUrl}")
	public static String invoiceServiceUrl;
	@Value("${shop.utvgoLiveSyncUrl}")
	public String utvgoLiveSyncUrl;
	@Value("${shop.utvgoLiveCancelUrl}")
	public String utvgoLiveCancelUrl;
	@Value("${shop.utvgoGetLiveInfoUrl}")
	public String utvgoGetLiveInfoUrl;
	@Value("${shop.videoValidTime}")
	public Integer videoValidTime;
	
	
	public  String invoiceFileUploadPath;
	public  int priceScale = 2;
	public  Integer priceRoundType = EntityContext.ROUNDTYPE_ROUNDHALFUP;
	public  Integer stockAllocationTime = EntityContext.STOCK_ALLOCATION_TIME_ORDER;
	public  Integer memberLoginTypePassword;
	public  Integer memberLoginTypeSms;
	public  Integer memberLoginTypeWechat;
	
	@Value("${shop.defaultProductPriceValidPeriod}")
	public  Integer defaultProductPriceValidPeriod;
	@Value("${shop.brand.auditFlag}")
	public  String brandAudit;
	@Value("${shop.brand.specificationsFlag}")
	public  String brandSpecifications;
	@Value("${shop.brand.templeteFlag}")
	public  String brandTemplete;
	@Value("${shop.brand.skuFlag}")
	public  String brandSku;
	@Value("${shop.category.auditFlag}")
	public  String categoryAudit;
	@Value("${shop.category.specificationsFlag}")
	public  String categorySpecifications;
	@Value("${shop.category.templeteFlag}")
	public  String categoryTemplete;
	@Value("${shop.category.skuFlag}")
	public  String categorySku;
	@Value("${shop.store.auditFlag}")
	public  String storeAudit;
	@Value("${shop.store.specificationsFlag}")
	public  String storeSpecifications;
	@Value("${shop.store.templeteFlag}")
	public  String storeTemplete;
	@Value("${shop.store.skuFlag}")
	public  String storeSku;
	@Value("${shop.column.auditFlag}")
	public  String columnAudit;
	@Value("${shop.column.specificationsFlag}")
	public  String columnSpecifications;
	@Value("${shop.column.templeteFlag}")
	public  String columnTemplete;
	@Value("${shop.column.skuFlag}")
	public  String columnSku;
	@Value("${shop.group.auditFlag}")
	public  String groupAudit;
	@Value("${shop.group.specificationsFlag}")
	public  String groupSpecifications;
	@Value("${shop.group.templeteFlag}")
	public  String groupTemplete;
	@Value("${shop.group.skuFlag}")
	public  String groupSku;
	@Value("${shop.tv.auditFlag}")
	public  String tvAudit;
	@Value("${shop.tv.specificationsFlag}")
	public  String tvSpecifications;
	@Value("${shop.tv.templeteFlag}")
	public  String tvTemplete;
	@Value("${shop.tv.skuFlag}")
	public  String tvSku;
	@Value("${shop.mobile.auditFlag}")
	public  String mobileAudit;
	@Value("${shop.mobile.specificationsFlag}")
	public  String mobileSpecifications;
	@Value("${shop.mobile.templeteFlag}")
	public  String mobileTemplete;
	@Value("${shop.mobile.skuFlag}")
	public  String mobileSku;
	
	public  String enablePaymentDebugMode;
	@Value("${shop.paymentNotifyUrl}")
	public  String paymentNotifyUrl;
	@Value("${shop.paymentServiceUrl}")
	public  String paymentServiceUrl;
	
	@Value("${shop.refundNotifyUrl}")
	public  String refundNotifyUrl;
	@Value("${shop.refundServiceUrl}")
	public  String refundServiceUrl;
	
	@Value("${shop.queryPaymentResultServiceUrl}")
	public  String queryPaymentResultServiceUrl;
	@Value("${shop.queryRefundResultServiceUrl}")
	public  String queryRefundResultServiceUrl;
	
	@Value("${shop.firstLevelRefundAuditUserId}")
	public  Integer firstLevelRefundAuditUserId;
	@Value("${shop.secondLevelRefundAuditUserId}")
	public  Integer secondLevelRefundAuditUserId;
	@Value("${shop.thirdLevelRefundAuditUserId}")
	public  Integer thirdLevelRefundAuditUserId;
	
	
	
	
	/**
	 * 获取是否开启支付的沙箱测试模式
	 */
	public  boolean isEnablePaymentDebugMode() {
		return StringUtils.isNotEmpty(enablePaymentDebugMode) &&
				enablePaymentDebugMode.equals("1");
	}


	/**
	 * 设置精度
	 * 
	 * @param amount 数值
	 * @return 数值
	 */
	public  BigDecimal setScale(BigDecimal amount) {
		if (amount != null && priceScale != 0 && priceRoundType != null) {
			int priceRoundType = getPriceRoundType().intValue();
			if (priceRoundType == EntityContext.ROUNDTYPE_ROUNDUP.intValue()) {
				return amount.setScale(getPriceScale(), BigDecimal.ROUND_UP);
			} else if (priceRoundType == EntityContext.ROUNDTYPE_ROUNDDOWN.intValue()) {
				return amount.setScale(getPriceScale(), BigDecimal.ROUND_DOWN);
			} else if (priceRoundType == EntityContext.ROUNDTYPE_ROUNDHALFUP.intValue()) {
				return amount.setScale(getPriceScale(), BigDecimal.ROUND_HALF_UP);
			}
			return amount.setScale(getPriceScale(), BigDecimal.ROUND_HALF_UP);
		}
		return amount;
	}

}