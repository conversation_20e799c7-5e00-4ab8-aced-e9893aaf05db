
/** 
* @ClassName: NoneStrategy 
* @Description: TODO(没有优惠) 
* <AUTHOR>
* @date 2019年10月31日 上午11:45:23 
*  
*/
package com.foshan.shop.promotion.strategy;

import java.math.BigDecimal;
import java.util.Set;

import com.foshan.entity.shop.ProductEntity;
import com.foshan.shop.promotion.CaculatePromotionResult;
import com.foshan.shop.promotion.IPromotion;
import com.foshan.shop.promotion.PromotionItem;
import com.foshan.shop.promotion.IPromotionStrategy;

public class NonePromotionStrategy implements IPromotionStrategy{

	@Override
	public CaculatePromotionResult calculateCollectionPromotion(IPromotion promotion, Set<PromotionItem> items) {

		return new CaculatePromotionResult(BigDecimal.ZERO, null, null, null);
	}

	@Override
	public CaculatePromotionResult calculateProductPromotion(IPromotion promotion, ProductEntity product) {

		return new CaculatePromotionResult(BigDecimal.ZERO, null, null, null);
	}


} 