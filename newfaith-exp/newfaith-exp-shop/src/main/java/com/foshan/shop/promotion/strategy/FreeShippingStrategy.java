/** 
* @ClassName: PriceReductionStrategy
* @Description: TODO(降价促销策略) 
* <AUTHOR>
* @date 2019年10月31日 上午11:45:23 
*  
*/
package com.foshan.shop.promotion.strategy;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;

import com.foshan.entity.shop.ProductEntity;
import com.foshan.shop.promotion.CaculatePromotionResult;
import com.foshan.shop.promotion.IPromotion;
import com.foshan.shop.promotion.PromotionItem;
import com.foshan.shop.promotion.IPromotionStrategy;
import com.foshan.shop.promotion.StrategyValue;
import com.foshan.util.ShopContextInfo;
import com.foshan.util.ShopContextInfo;

public class FreeShippingStrategy implements IPromotionStrategy {
	@Autowired
	protected ShopContextInfo shopContextInfo;
	/**
	 * 集合商品满额免邮促销
	 * 
	 * @param promotion 促销活动
	 * @param promotionItemSet promotion活动对应的购物车项。
	 */
	@Override
	public CaculatePromotionResult calculateCollectionPromotion(IPromotion promotion, Set<PromotionItem> promotionItemSet) {
		CaculatePromotionResult result = new CaculatePromotionResult();
		List<StrategyValue> promotionStrategyValueCollection = promotion.getPromotionStrategyValueCollection();
		result.setBenefitPrice(BigDecimal.ZERO);

		if (promotionStrategyValueCollection != null) {
			// 排序，限制值倒序
			Collections.sort(promotionStrategyValueCollection);
			// 逐个访问阶梯满减系列
			for (StrategyValue strategyValue : promotionStrategyValueCollection) {
				BigDecimal benefitValue = BigDecimal.ZERO;
				// 记录集合商品总价。
				BigDecimal originalTotalAmount = PromotionItem.getCollectionTotalAmount(promotionItemSet);
				result.setOriginalPrice(originalTotalAmount);
				
				// 记录集合商品总数量。
				Integer originalQuantity = PromotionItem.getCollectionTotalQuantity(promotionItemSet); 
				result.setOriginalQantity(originalQuantity);

				// 是否符合当前阶梯的限制,且优惠金额必须大于0
				if (promotion.checkCollectionRestriction(promotionItemSet, strategyValue)) {
					// 记录命中的策略值
					result.setSelectedStrategyValue(strategyValue);
					benefitValue =  BigDecimal.ZERO;
//					for(PromotionItem promotionItem : promotionItemSet) {
//						//ProductSpecificationEntity productSpecification = promotionItem.getProduct().getProductSpecification();
//						if(promotion.getProductOrder() == null) {
//							benefitValue =  BigDecimal.ZERO; //benefitValue.add(calculateLogisticsFee(productSpecification.getStore(), productSpecification,promotionItem.getProduct(),promotionItem.getOrderQuantity(),null));
//						}
//						else {
//							BigDecimal logisticFee = BigDecimal.ZERO;//calculateLogisticsFee(productSpecification.getStore(), productSpecification,promotionItem.getProduct(),promotionItem.getOrderQuantity(),promotion.getProductOrder().getAddress());
//							benefitValue = benefitValue.add(logisticFee);
//						}
//					}
			        
					// 返回优惠金额，如果优惠金额小于零，则返回零
					if (benefitValue.compareTo(BigDecimal.ZERO) >= 0) {
						result.setBenefitPrice(benefitValue);
						
					} else {
						result.setBenefitPrice(BigDecimal.ZERO);
					}
					//命中策略后返回结果
					return result;
				}
				else {
					//这部分代码还有待优化
					result.setNextStrategyValue(strategyValue);
					result.setNeedPrice(shopContextInfo.setScale(strategyValue.getLimitValue()).subtract(originalTotalAmount));
					result.setNeedQuantity(strategyValue.getLimitValue().intValue() - originalQuantity);
				}
			}
		}
		return result;
//		
//		BigDecimal freeShippingPrice = BigDecimal.ZERO;
//		//HashSet<CartItemEntity>  freeShippingCartItemSet = new HashSet<CartItemEntity>();
//		List<PromotionResult> promotionResultList = (List<PromotionResult>)calculateDiscountResult.get("promotionResultList");
//
//		// 找出订单中参加促销活动并满足包邮条件的商品;
//		promotionResultList.forEach(o ->{
//			if(o.getPromotion().getIsFreeShipping() == true) {
//				if(o.getCartItemList() != null) {
//					freeShippingCartItemSet.addAll(o.getCartItemList());
//				}
//			}
//		});
//		
//		// 计算这些商品优惠的运费金额
//		for(CartItemEntity cartItem : freeShippingCartItemSet) {
//			 ProductSpecificationEntity productSpecification = cartItem.getProduct().getProductSpecification();
//			freeShippingPrice = freeShippingPrice.add(calculateLogisticsFee(store, productSpecification,cartItem.getProduct(),cartItem.getNumber(),address));
//		}
//			
//		return freeShippingPrice;
	}

	/**
	 * 单个商品满额免邮促销
	 * 
	 * @param promotion 促销活动
	 * @param cartItems promotion活动对应的购物车项。
	 */
	@Override
	public CaculatePromotionResult calculateProductPromotion(IPromotion promotion, ProductEntity product) {
		CaculatePromotionResult result = new CaculatePromotionResult();
		List<StrategyValue> promotionStrategyValueCollection = promotion.getPromotionStrategyValueCollection();
		result.setBenefitPrice(BigDecimal.ZERO);

		if (promotionStrategyValueCollection != null) {
			// 排序，限制值倒序
			Collections.sort(promotionStrategyValueCollection);
			// 逐个访问阶梯满减系列
			for (StrategyValue strategyValue : promotionStrategyValueCollection) {
				BigDecimal benefitValue = strategyValue.getBenefitValue();
				// 记录商品的原价。
				BigDecimal originalPrice = product.getProductPrice();
				result.setOriginalPrice(originalPrice);
				
				// 记录集合商品总数量。
				Integer originalQuantity = 1; 
				result.setOriginalQantity(originalQuantity);
				// 是否符合当前阶梯的限制,且优惠金额必须大于0
				if (promotion.checkProductRestriction(product, strategyValue)) {
					// 记录命中的策略值
					result.setSelectedStrategyValue(strategyValue);

					// 返回优惠金额，如果优惠金额小于零，则返回零
					if (benefitValue.compareTo(BigDecimal.ZERO) >= 0) {
						result.setBenefitPrice(benefitValue);
					} else {
						result.setBenefitPrice(BigDecimal.ZERO);
					}
					//命中策略后返回结果
					return result;
				}
				else {
					//这部分代码还有待优化
					result.setNextStrategyValue(strategyValue);
					result.setNeedPrice(shopContextInfo.setScale(strategyValue.getLimitValue()).subtract(originalPrice));
					result.setNeedQuantity(strategyValue.getLimitValue().intValue() - originalQuantity);
				}
			}
		}
		return result;
	}

}