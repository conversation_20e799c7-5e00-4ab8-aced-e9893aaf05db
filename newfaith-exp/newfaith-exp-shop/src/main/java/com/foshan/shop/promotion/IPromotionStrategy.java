package com.foshan.shop.promotion;

import java.util.Set;

import com.foshan.entity.shop.ProductEntity;

/**
 * 促销策略
 */
public interface IPromotionStrategy {

	/**
	 * 根据促销策略计算产品集合的优惠
	 * 
	 * @param products 产品实体Set集合
	 * @return 优惠金额  Map里面包含两个价格：orginalPrice，符合促销条件的购物车项或订单项的原价;benefitValue,优惠的金额
	 */
	public CaculatePromotionResult calculateCollectionPromotion(IPromotion promotion, Set<PromotionItem> items);

	/**
	 * 根据促销策略计算单个产品的优惠
	 * 
	 * @param product 产品实体
	 * @return 优惠金额   Map里面包含两个价格：orginalPrice，符合促销条件的购物车项或订单项的原价;benefitValue,优惠的金额
	 */
	public CaculatePromotionResult calculateProductPromotion(IPromotion promotion, ProductEntity product);

}
