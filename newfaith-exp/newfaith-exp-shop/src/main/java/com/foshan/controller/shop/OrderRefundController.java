package com.foshan.controller.shop;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.foshan.form.request.RefundNotifyReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.shop.request.OrderRefundReq;
import com.foshan.form.shop.response.refund.OrderRefundApplyRes;
import com.foshan.form.shop.response.refund.OrderRefundPageListRes;
import com.foshan.form.shop.response.refund.OrderRefundRes;
import com.foshan.form.shop.response.refund.OrderRefundResultRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "商城--订单退款模块")
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
public class OrderRefundController extends BaseShopController {
	@SuppressWarnings("unused")
	private final static IResponse REUEST_PARAMS_ERROR_RESPONSE = new GenericResponse(
			ResponseContext.RES_NULL_ERROR_CODE, ResponseContext.RES_NULL_ERROR_INFO);

	// 发起退款
	@ApiOperation(value = "发起退款(/store/order/orderRefund)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/store/order/orderRefund", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public OrderRefundApplyRes orderRefunds(@RequestBody OrderRefundReq req, HttpServletRequest request)throws Exception   {
		OrderRefundApplyRes res = null;
		if ((req.getOrderItemId() != null && req.getOrderItemId() != 0)
				&& (req.getCount() != null)) {
			// 订单项退还指定数量商品的金额
			res = (OrderRefundApplyRes)orderRefundService.refundForOrderItem(req.getOrderItemId(), req.getCount(), null,
					req.getReason());
		} else if ((req.getOrderItemId() != null && req.getOrderItemId() != 0)
				&& (req.getRefundAmount() != null)) {
			// 订单项退还指定金额
			res = (OrderRefundApplyRes)orderRefundService.refundForOrderItem(req.getOrderItemId(), req.getRefundAmount(), null,
					req.getReason());
		} else {
			// 退还整个订单的金额
			res = (OrderRefundApplyRes)orderRefundService.refundForOrder(req.getOrderId(), null, req.getReason());
		}
        return res;
    }

	// 查询退款结果
	@ApiOperation(value = "查询退款结果(/store/order/queryRefundResult)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/store/order/queryRefundResult", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public OrderRefundResultRes queryRefunds(@RequestBody OrderRefundReq req, HttpServletRequest request)throws Exception   {
		OrderRefundResultRes res = (OrderRefundResultRes)orderRefundService.queryRefund(req);
        return res;
    }

	// 重新提交退款请求
	@ApiOperation(value = "重新提交退款请求(/store/order/commitRefund)", httpMethod = "POST", notes = "商铺：退款申请id必填，平台管理员：退款申请id和storeId必填")
	@ResponseBody
	@RequestMapping(value = "/store/order/commitRefund", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public OrderRefundApplyRes commitRefund(@RequestBody OrderRefundReq req, HttpServletRequest request)throws Exception   {
		OrderRefundApplyRes res = (OrderRefundApplyRes)orderRefundService.commitRefund(req.getId(), req.getStoreId());
        return res;
    }

	// 查询退款列表
	@ApiOperation(value = "查询退款列表(/store/order/getOrderRefundList)", httpMethod = "POST", notes =  "商铺orderId选填,平台管理员orderId,storeId选 填")
	@ResponseBody
	@RequestMapping(value = "/store/order/getOrderRefundList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public OrderRefundPageListRes getOrderRefundList(@RequestBody OrderRefundReq req, HttpServletRequest request)throws Exception   {
		OrderRefundPageListRes res = (OrderRefundPageListRes)orderRefundService.getOrderRefundListPage(req.getRequestPage(), req.getPageSize(),
				req.getOrderId(), null);
        return res;
    }

	// 查询退款详情
	@ApiOperation(value = "查询退款详情(/store/order/getOrderRefundDetail)", httpMethod = "POST", notes =  "退款申请id必填")
	@ResponseBody
	@RequestMapping(value = "/store/order/getOrderRefundDetail", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public OrderRefundRes getOrderRefundDetail(@RequestBody OrderRefundReq req, HttpServletRequest request)throws Exception   {
		OrderRefundRes res = (OrderRefundRes)orderRefundService.getOrderRefundDetail(req.getId());
        return res;
    }
	
	// ******************************平台管理员部分接口************************************


	// 管理员查询退款列表
	//@RequestMapping(value = "/admin/order/getOrderRefundList", method = { RequestMethod.GET, RequestMethod.POST })
	@ApiOperation(value = "管理员查询退款列表(/admin/order/getOrderRefundList)", httpMethod = "POST", notes =  "退款申请id必填")
	@ResponseBody
	@RequestMapping(value = "/admin/order/getOrderRefundList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public OrderRefundPageListRes getRefundList(@RequestBody OrderRefundReq req, HttpServletRequest request)throws Exception   {
		OrderRefundPageListRes res = (OrderRefundPageListRes)orderRefundService.getOrderRefundListPage(req.getRequestPage(), req.getPageSize(),
				req.getOrderId(), null);
        return res;
    }


	// 管理员查询退款详情
	@ApiOperation(value = "管理员查询退款详情(/admin/order/getOrderRefundDetail)", httpMethod = "POST", notes =  "退款申请id必填")
	@ResponseBody
	@RequestMapping(value = "/admin/order/getOrderRefundDetail", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public OrderRefundRes getRefundDetail(@RequestBody OrderRefundReq req, HttpServletRequest request)throws Exception   {
		OrderRefundRes res = (OrderRefundRes)orderRefundService.getOrderRefundDetail(req.getId());
        return res;
    }

	/**
	 * 支付系统退款回调通知
	 */
	@ApiOperation(value = "支付系统退款回调通知(/shop/RefundNotify)", httpMethod = "POST", notes =  "在线退款结果回调通知对象")
	@ResponseBody
	@RequestMapping(value = "/shop/refundNotify", method = { RequestMethod.GET, RequestMethod.POST })
	public OrderRefundRes refundNotify(@RequestBody RefundNotifyReq req, HttpServletRequest request, HttpServletResponse response) throws Exception {
		OrderRefundRes res =(OrderRefundRes)orderRefundService.reciveRefundNotify(req);
		return res;
	}

}
