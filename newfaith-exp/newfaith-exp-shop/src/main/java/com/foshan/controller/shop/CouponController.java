package com.foshan.controller.shop;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.shop.request.ChooseCouponToUseReq;
import com.foshan.form.shop.request.CouponReq;
import com.foshan.form.shop.request.CouponSettingReq;
import com.foshan.form.shop.request.ProductReq;
import com.foshan.form.shop.response.coupon.GenerateCouponRes;
import com.foshan.form.shop.response.coupon.GetCanUseCouponListRes;
import com.foshan.form.shop.response.coupon.GetCouponInfoRes;
import com.foshan.form.shop.response.coupon.GetCouponListRes;
import com.foshan.form.shop.response.coupon.ReceiveCouponRes;
import com.foshan.form.shop.response.product.GetProductListRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "商城--优惠券模块")
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
public class CouponController extends BaseShopController {

	// 根据优惠券设置生成优惠券
	@ApiOperation(value = "根据优惠券设置生成优惠券(GenerateCoupon)", httpMethod = "POST", notes = "<p>1:必填字段：Id 优惠券设置Id，modelNum生成数量；")
	@ResponseBody
    @RequestMapping(value = "/generateCoupon", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenerateCouponRes generateCoupon(@RequestBody CouponSettingReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GenerateCouponRes res = (GenerateCouponRes) couponService.generateCoupon(req);
        return res;
    }

	// 获取优惠券详情
	@ApiOperation(value = "获取优惠券(GetCouponInfo)", httpMethod = "POST", notes = "<p>1:Id 优惠券id和giftRollCode不能同时为空；如果id不为空需要先登录商城查询，但只能查与自身相关的优惠券；如果通过giftRollCode查询，不需要登录就可以查询任何券的信息")
	@ResponseBody
    @RequestMapping(value = "/getCouponInfo", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCouponInfoRes getCouponInfo(@RequestBody CouponReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GetCouponInfoRes res = (GetCouponInfoRes) couponService.getCouponInfo(req);
        return res;
    }

	// 获取优惠券列表
	@ApiOperation(value = "获取优惠券列表(GetCouponList)", httpMethod = "POST", notes = "<p>1:优惠券号码giftRollCode模糊查询，领取会员的memberId，使用的订单orderId，优惠券设置couponSettingId，优惠券状态giftRollState 五个参数可以组合查询 ")
	@ResponseBody
    @RequestMapping(value = "/getCouponList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCouponListRes getCouponList(@RequestBody CouponReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GetCouponListRes res = (GetCouponListRes) couponService.getCouponList(req);
        return res;
    }

//	// 随机获取优惠券
//	@ApiOperation(value = "随机获取指定数量的优惠券用于展示(getCouponRandomList)", httpMethod = "POST", notes = "<p>1:Id 优惠券设置Id，count获取数量,如果实际优惠券已经不足该数量则返回实际数量 ；")
//	@ResponseBody
//	@RequestMapping(value = "/getCouponRandomList", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public GenerateCouponRes getCouponRandomList(@RequestBody CouponSettingReq req, HttpServletRequest request)
//			throws JsonProcessingException {
//		long start = System.currentTimeMillis();
//		logger.info("接收==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==请求数据："
//				+ mapper.writeValueAsString(req));
//		GenerateCouponRes res = (GenerateCouponRes) couponService.generateCoupon(req);
//		long end = System.currentTimeMillis();
//		logger.info(
//				"==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==处理时间:" + (end - start) + "毫秒!");
//		return res;
//	}

	// 会员领取优惠券
	@ApiOperation(value = "会员领取优惠券(ReceiveCoupon)", httpMethod = "POST", notes = "<p>1:Id 优惠券设置coponSettingId不能为空，会员登录商城领取memberId可以为空，其它情况memberId不能为空，领取数量receiveNum如果不传，默认为1")
	@ResponseBody
    @RequestMapping(value = "/receiveCoupon", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ReceiveCouponRes receiveCoupon(@RequestBody CouponReq req, HttpServletRequest request)throws JsonProcessingException  {
    	ReceiveCouponRes res = (ReceiveCouponRes) couponService.receiveCoupon(req);
        return res;
    }
	
	// 会员根据优惠券编码领取优惠券
	@ApiOperation(value = "会员领取优惠券(receiveCouponByCode)", httpMethod = "POST", notes = "<p>1:Code 优惠券编号giftRollCode不能为空，会员登录商城领取memberId可以为空，其它情况memberId不能为空，如系统派送；")
	@ResponseBody
    @RequestMapping(value = "/receiveCouponByCode", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ReceiveCouponRes receiveCouponByCode(@RequestBody CouponReq req, HttpServletRequest request)throws JsonProcessingException  {
    	ReceiveCouponRes res = (ReceiveCouponRes) couponService.receiveCouponByCode(req);
        return res;
    }

	// 会员获取当前购物车中可以使用的优惠券列表
	@ApiOperation(value = "会员获取当前购物车中可以使用的优惠券列表(GetCanUseCouponList)", httpMethod = "POST", notes = "<p>1:selectCouponIds 选择使用的优惠券Id数组，必填，如[44,22]")
	@ResponseBody
    @RequestMapping(value = "/getCanUseCouponList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCanUseCouponListRes getCanUseCouponList(@RequestBody ChooseCouponToUseReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GetCanUseCouponListRes res = (GetCanUseCouponListRes) couponService.getCanUseCouponList(req);
        return res;
    }
	
	// 获取商品详情中关联的优惠券列表
	@ApiOperation(value = "获取商品详情中关联的优惠券列表(GetCouponListByProductInfo)", httpMethod = "POST", notes = "<p>1:productId必填")
	@ResponseBody
    @RequestMapping(value = "/getCouponListByProductInfo", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCouponListRes getCouponListByProductInfo(@RequestBody ProductReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GetCouponListRes res = (GetCouponListRes) couponService.getCouponListByProductInfo(req);
        return res;
    }
	
	
	// 获取可关联优惠券设置的所有商品列表
	@ApiOperation(value = "获取参与优惠券活动的商品列表(GetCouponRelatedProduct)", httpMethod = "POST", notes = " 获取优惠券活动的商品列表，优惠券活动设置id必填")
	@ResponseBody
    @RequestMapping(value = "/getCouponRelatedProduct", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetProductListRes getCouponRelatedProduct(@RequestBody CouponSettingReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GetProductListRes res = (GetProductListRes) couponService.getCouponRelatedProduct(req);
        return res;
    }

	// 订制活动页面，根据优惠券设置获取优惠券信息 
	@ApiOperation(value = "定制优惠券领取活动页，获取优惠券活动的信息（仅在定制活动时使用）(GetCouponInfoByCouponSetting)", httpMethod = "POST", notes = "<p>1:优惠券设置的id必填 ")
	@ResponseBody
    @RequestMapping(value = "/getCouponInfoByCouponSetting", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCouponInfoRes getCouponInfoByCouponSetting(@RequestBody CouponSettingReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GetCouponInfoRes res = (GetCouponInfoRes) couponService.getCouponInfoByCouponSetting(req);
        return res;
    }
	
}
