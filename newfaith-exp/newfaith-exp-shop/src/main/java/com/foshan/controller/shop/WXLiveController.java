package com.foshan.controller.shop;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.request.BasePageRequest;
import com.foshan.form.shop.response.wxLive.GetliveListRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "商城--小程序直播")
@RestController
public class WXLiveController extends BaseShopController {

	// 获取小程序直播列表
	@ApiOperation(value = "获取小程序直播列表(getliveList)", httpMethod = "POST", notes = "获取小程序直播列表")
	@ResponseBody
	@RequestMapping(value = "/getliveList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetliveListRes getliveList(@RequestBody BasePageRequest req, HttpServletRequest request)
			throws JsonProcessingException {
		GetliveListRes res = (GetliveListRes) wXLiveService.getliveList(req);
		return res;
	}

}
