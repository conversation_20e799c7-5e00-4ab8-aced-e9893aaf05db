package com.foshan.controller.shop;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.shop.request.VerificaterLoginReq;
import com.foshan.form.shop.response.verificater.VerificaterLoginRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "商城--订单核销人员模块")
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
public class VerificaterController extends BaseShopController {

	// 订单核销人员登陆
	@ApiOperation(value = "订单核销人员登陆(verificaterLogin)", httpMethod = "POST", notes = "订单核销人员登录<p>1：userName和userPassword不能为空；")
	@ResponseBody
	@RequestMapping(value = "/verificaterLogin", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public VerificaterLoginRes verificaterLogin(@RequestBody VerificaterLoginReq req,HttpServletRequest request)throws JsonProcessingException   {
		VerificaterLoginRes res = (VerificaterLoginRes) verificaterService.login(req, request);
        return res;
    }
	

	// 直播间访问nginx smtp服务认证，临时时方案，以后要独立角色和用户结合直播间管理一起做。
	@ApiOperation(value = "直播间访问nginx smtp服务认证 (liveLogin)", httpMethod = "GET", notes = "分配给直播间的<p>1：userName和userPassword不能为空，userPassword为密码一次sm3结果；")
	@ResponseBody
	@RequestMapping(value = "/liveLogin", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public VerificaterLoginRes liveLogin(@RequestBody VerificaterLoginReq req,HttpServletRequest request)throws JsonProcessingException   {
		VerificaterLoginRes res = (VerificaterLoginRes) verificaterService.login(req, request);
        return res;
    }
}
