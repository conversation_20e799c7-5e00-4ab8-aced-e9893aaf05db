package com.foshan.controller.shop;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.shop.request.CompanyInvoiceInfoReq;
import com.foshan.form.shop.response.invoice.AddCompanyInvoiceInfoRes;
import com.foshan.form.shop.response.invoice.GetCompanyInvoiceInfoListRes;
import com.foshan.form.shop.response.invoice.GetCompanyInvoiceInfoRes;
import com.foshan.form.shop.response.invoice.ModifyCompanyInvoiceInfoRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
@Api(tags = "商城--企业发票信息模块")
@RestController
public class CompanyInvoiceInfoController extends BaseShopController {

	// 新增企业发票信息
	@ApiOperation(value = "新增企业发票信息(AddCompanyInvoiceInfo)", httpMethod = "POST", notes = "新增企业发票信息<p>1：invoiceType、、companyName、paytaxNo、registeredAddress、registeredTelephone、blank、blankAccount、auditState不能为空；")
	@ResponseBody
    @RequestMapping(value = "/addCompanyInvoiceInfo", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public AddCompanyInvoiceInfoRes addCompanyInvoiceInfo(@RequestBody CompanyInvoiceInfoReq req, HttpServletRequest request)throws JsonProcessingException  {
    	AddCompanyInvoiceInfoRes res = (AddCompanyInvoiceInfoRes) companyInvoiceInfoService.addCompanyInvoiceInfo(req);
        return res;
    }

	// 修改企业发票信息
	@ApiOperation(value = "修改企业发票信息(ModifyCompanyInvoiceInfo)", httpMethod = "POST", notes = "修改企业发票信息<p>1：Id不能为空；")
	@ResponseBody
    @RequestMapping(value = "/modifyCompanyInvoiceInfo", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ModifyCompanyInvoiceInfoRes modifyCompanyInvoiceInfo(@RequestBody CompanyInvoiceInfoReq req, HttpServletRequest request)throws JsonProcessingException  {
    	ModifyCompanyInvoiceInfoRes res = (ModifyCompanyInvoiceInfoRes) companyInvoiceInfoService.modifyCompanyInvoiceInfo(req);
        return res;
    }

	// 删除企业发票信息
	@ApiOperation(value = "删除企业发票信息(DeleteCompanyInvoiceInfo)", httpMethod = "POST", notes = "删除企业发票信息<p>1：Id不能为空；")
	@ResponseBody
    @RequestMapping(value = "/deleteCompanyInvoiceInfo", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse deleteCompanyInvoiceInfo(@RequestBody CompanyInvoiceInfoReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GenericResponse res = (GenericResponse) companyInvoiceInfoService.deleteCompanyInvoiceInfo(req);
        return res;
    }

	// 获取企业发票信息信息
	@ApiOperation(value = "获取企业发票信息信息(GetCompanyInvoiceInfo)", httpMethod = "POST", notes = "获取企业发票信息信息<p>1：Id不能为空；")
	@ResponseBody
    @RequestMapping(value = "/getCompanyInvoiceInfo", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCompanyInvoiceInfoRes getCompanyInvoiceInfo(@RequestBody CompanyInvoiceInfoReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GetCompanyInvoiceInfoRes res = (GetCompanyInvoiceInfoRes) companyInvoiceInfoService.getCompanyInvoiceInfo(req);
        return res;
    }

	// 获取企业发票信息列表
	@ApiOperation(value = "获取企业发票信息列表(getCompanyInvoiceInfoList)", httpMethod = "POST", notes = "获取企业发票信息列表")
	@ResponseBody
    @RequestMapping(value = "/getCompanyInvoiceInfoList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCompanyInvoiceInfoListRes getCompanyInvoiceInfoList(@RequestBody CompanyInvoiceInfoReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GetCompanyInvoiceInfoListRes res = (GetCompanyInvoiceInfoListRes) companyInvoiceInfoService.getCompanyInvoiceInfoList(req);
        return res;
    }

}
