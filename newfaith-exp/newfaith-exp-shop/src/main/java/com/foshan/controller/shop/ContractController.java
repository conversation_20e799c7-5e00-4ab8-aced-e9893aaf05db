package com.foshan.controller.shop;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.shop.request.ContractReq;
import com.foshan.form.shop.response.contract.GetContractDeductionDetailsListRes;
import com.foshan.form.shop.response.contract.GetContractInfoRes;
import com.foshan.form.shop.response.contract.GetContractListRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "商城--企业购合同模块")
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
public class ContractController extends BaseShopController {
	// 新增企业购合同
	@ApiOperation(value = "新增企业购合同(AddContract)", httpMethod = "POST", notes = "新增企业购合同<p>1:companyName不能为空；")
	@ResponseBody
    @RequestMapping(value = "/addContract", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse addContract(@RequestBody ContractReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GenericResponse res = (GenericResponse) contractService.addContract(req);
        return res;
    }

	// 修改企业购合同
	@ApiOperation(value = "修改企业购合同(ModifyContract)", httpMethod = "POST", notes = "修改企业购合同")
	@ResponseBody
    @RequestMapping(value = "/modifyContract", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse modifyContract(@RequestBody ContractReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GenericResponse res = (GenericResponse) contractService.modifyContract(req);
        return res;
    }

	// 删除企业购合同
	@ApiOperation(value = "删除企业购合同(DeleteContract)", httpMethod = "POST", notes = "删除企业购合同<p>1:courierId不能为空；")
	@ResponseBody
    @RequestMapping(value = "/deleteContract", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse deleteContract(@RequestBody ContractReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GenericResponse res = (GenericResponse) contractService.deleteContract(req);
        return res;
    }

	// 获取企业购合同信息
	@ApiOperation(value = "获取企业购合同信息(GetContractInfo)", httpMethod = "POST", notes = "获取企业购合同信息<p>1:courierId不能为空；")
	@ResponseBody
    @RequestMapping(value = "/getContractInfo", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetContractInfoRes getContractInfo(@RequestBody ContractReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GetContractInfoRes res = (GetContractInfoRes) contractService.getContractInfo(req);
        return res;
    }

	// 获取企业购合同列表
	@ApiOperation(value = "获取企业购合同列表(getContractList)", httpMethod = "POST", notes = "<p>1:companyId不能为空！ ")
	@ResponseBody
    @RequestMapping(value = "/getContractList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetContractListRes getContractList(@RequestBody ContractReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GetContractListRes res = (GetContractListRes) contractService.getContractList(req);
        return res;
    }
	
	// 获取合同额度扣减明细列表
	@ApiOperation(value = "获取合同额度扣减明细列表(getContractDeductionDetailsList)", httpMethod = "POST", notes = "<p>1:contractId不能为空！ ")
	@ResponseBody
    @RequestMapping(value = "/getContractDeductionDetailsList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetContractDeductionDetailsListRes getContractDeductionDetailsList(@RequestBody ContractReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GetContractDeductionDetailsListRes res = (GetContractDeductionDetailsListRes) contractService.getContractDeductionDetailsList(req);
        return res;
    }
}
