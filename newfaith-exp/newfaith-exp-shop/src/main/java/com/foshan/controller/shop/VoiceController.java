package com.foshan.controller.shop;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.shop.request.VoiceReq;
import com.foshan.form.shop.response.product.GetProductListRes;
import com.foshan.form.shop.response.voice.SearchBrandOrCategoryRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "商城--语音搜索模块")
@RestController
public class VoiceController extends BaseShopController {
	// 语音搜索按品牌或分类列出品牌或分类接口
	@ApiOperation(value = "语音搜索按品牌或分类列出品牌或分类接口(searchBrandOrCategory)", httpMethod = "POST", notes = "语音搜索按品牌或分类列出品牌或分类接口")
	@ResponseBody
	@RequestMapping(value = "/searchBrandOrCategory", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public SearchBrandOrCategoryRes searchBrandOrCategory(@RequestBody VoiceReq req,HttpServletRequest request)throws JsonProcessingException   {
		SearchBrandOrCategoryRes res = (SearchBrandOrCategoryRes) voiceService.searchBrandOrCategory(req);
        return res;
    }

	// 语音搜索产品接口
	@ApiOperation(value = "语音搜索产品接口(searchProduct)", httpMethod = "POST", notes = "语音搜索产品接口")
	@ResponseBody
	@RequestMapping(value = "/searchProduct", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetProductListRes searchProduct(@RequestBody VoiceReq req,HttpServletRequest request)throws JsonProcessingException   {
		GetProductListRes res = (GetProductListRes) voiceService.searchProduct(req);
        return res;
    }
}
