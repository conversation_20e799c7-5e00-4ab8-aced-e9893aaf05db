package com.foshan.controller.shop;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.shop.request.FinancialAccountingReq;
import com.foshan.form.shop.response.financialAccounting.GetFinancialAccountingListRes;
import com.foshan.form.shop.response.productGroup.AddProductGroupRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "商城--稽核系统模块")	
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
public class FinancialAccountingController extends BaseShopController {

/*	// 
	@RequestMapping(value = "/addFinancialAccounting", method = { RequestMethod.POST})
	public void addFinancialAccounting(String jsonData, HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		long start = System.currentTimeMillis();
		if (StringUtils.isEmpty(jsonData)) {
			BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()));
			jsonData = IOUtils.read(reader);
		}
		jsonData = URLDecoder.decode(jsonData, "UTF-8");
		logger.info("接收===>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<===【" + request.getMethod()
				+ "】===请求数据：" + jsonData);
		FinancialAccountingReq req = (FinancialAccountingReq) mapper.readValue(jsonData, FinancialAccountingReq.class);
		jsonOutByJackson(response, mapper, financialAccountingService.addFinancialAccounting(req));
		long end = System.currentTimeMillis();
		logger.info("===>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<===【" + request.getMethod()
				+ "】===请求处理时间:" + (end - start) + "毫秒!");
	}*/
	
	@ApiOperation(value = "新增稽核数据(addFinancialAccounting)", httpMethod = "POST", notes = "新增稽核数据")
	@ResponseBody
    @RequestMapping(value = "/addFinancialAccounting", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public AddProductGroupRes addFinancialAccounting(@RequestBody FinancialAccountingReq req, HttpServletRequest request)throws JsonProcessingException  {
    	AddProductGroupRes res = (AddProductGroupRes) financialAccountingService.addFinancialAccounting(req);
        return res;
    }

	
	// 
/*	@RequestMapping(value = "/uploadFtpFile", method = { RequestMethod.POST})
	public void uploadFtpFile(String jsonData, HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		long start = System.currentTimeMillis();
//		if (StringUtils.isEmpty(jsonData)) {
//			BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()));
//			jsonData = IOUtils.read(reader);
//		}
		//jsonData = URLDecoder.decode(jsonData, "UTF-8");
		logger.info("接收===>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<===【" + request.getMethod()
				+ "】===请求数据：" + jsonData);
	//	FinancialAccountingReq req = (FinancialAccountingReq) mapper.readValue(jsonData, FinancialAccountingReq.class);
		financialAccountingService.uploadFtpFile();
		long end = System.currentTimeMillis();
		logger.info("===>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<===【" + request.getMethod()
				+ "】===请求处理时间:" + (end - start) + "毫秒!");
	}*/
	
//	@ApiOperation(value = "上传稽核数据到ＦＴＰ(uploadFtpFile)", httpMethod = "POST", notes = "上传稽核数据到ＦＴＰ")
//	@ResponseBody
//	@RequestMapping(value = "/uploadFtpFile", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//	public void uploadFtpFile( HttpServletRequest request)
//			throws JsonProcessingException {
//		long start = System.currentTimeMillis();
//		logger.info("接收==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==请求数据：");
//		financialAccountingService.uploadFtpFile();
//		long end = System.currentTimeMillis();
//		logger.info(
//				"==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==处理时间:" + (end - start) + "毫秒!");
//		//return res;
//	}
	@ApiOperation(value = "上传稽核数据到ＦＴＰ(uploadFtpFile)", httpMethod = "POST", notes = "上传稽核数据到ＦＴＰ")
	@ResponseBody
    @RequestMapping(value = "/uploadFtpFile", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public void uploadFtpFile(HttpServletRequest request)throws JsonProcessingException  {
    	financialAccountingService.uploadFtpFile();
       // return res;
    }

	
	// 
/*	@RequestMapping(value = "/getFinancialAccountingList", method = { RequestMethod.POST})
	public void getFinancialAccountingList(String jsonData, HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		long start = System.currentTimeMillis();
		if (StringUtils.isEmpty(jsonData)) {
			BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()));
			jsonData = IOUtils.read(reader);
		}
		jsonData = URLDecoder.decode(jsonData, "UTF-8");
		logger.info("接收===>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<===【" + request.getMethod()
				+ "】===请求数据：" + jsonData);
		FinancialAccountingReq req = (FinancialAccountingReq) mapper.readValue(jsonData, FinancialAccountingReq.class);
		jsonOutByJackson(response, mapper, financialAccountingService.getFinancialAccountingList(req));
		long end = System.currentTimeMillis();
		logger.info("===>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<===【" + request.getMethod()
				+ "】===请求处理时间:" + (end - start) + "毫秒!");
	}*/
	
	@ApiOperation(value = "获取稽核列表数据(getFinancialAccountingList)", httpMethod = "POST", notes = "获取稽核列表数据")
	@ResponseBody
    @RequestMapping(value = "/getFinancialAccountingList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetFinancialAccountingListRes getFinancialAccountingList(@RequestBody FinancialAccountingReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GetFinancialAccountingListRes res = (GetFinancialAccountingListRes) financialAccountingService.getFinancialAccountingList(req);
        return res;
    }

}
