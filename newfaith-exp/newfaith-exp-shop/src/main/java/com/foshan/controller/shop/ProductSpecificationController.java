package com.foshan.controller.shop;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.shop.request.GetSpecificationListReq;
import com.foshan.form.shop.request.ProductSpecificationReq;
import com.foshan.form.shop.response.specification.AddProductSpecificationRes;
import com.foshan.form.shop.response.specification.GetProductSpecificationInfoRes;
import com.foshan.form.shop.response.specification.GetProductSpecificationListRes;
import com.foshan.form.shop.response.specification.GetSkuListBySpecificationRes;
import com.foshan.form.shop.response.specification.ModifyProductSpecificationRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "商城--产品规格模块")
@RestController
public class ProductSpecificationController extends BaseShopController {

	// 添加产品规格
	@ApiOperation(value = "新增产品规格(AddProductSpecification)", httpMethod = "POST", notes = "新增产品规格<p>1:brandId、categoryId、productName、specifications、deliveryRegionIdList不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addProductSpecification", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public AddProductSpecificationRes getProductPriceInfo(@RequestBody ProductSpecificationReq req,HttpServletRequest request)throws JsonProcessingException   {
		AddProductSpecificationRes res = (AddProductSpecificationRes)productSpecificationService.addProductSpecification(req, request);
        return res;
    }

	// 修改产品规格
	@ApiOperation(value = "修改产品规格(ModifyProductSpecification)", httpMethod = "POST", notes = "修改产品规格<p>1:specificationId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyProductSpecification", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ModifyProductSpecificationRes modifyProductSpecification(@RequestBody ProductSpecificationReq req,HttpServletRequest request)throws JsonProcessingException   {
		ModifyProductSpecificationRes res = (ModifyProductSpecificationRes)productSpecificationService.modifyProductSpecification(req, request);
        return res;
    }

	// 删除产品规格
	@ApiOperation(value = "删除产品规格(DeleteProductSpecification)", httpMethod = "POST", notes = "删除产品规格<p>1:specificationId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteProductSpecification", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse deleteProductSpecification(@RequestBody ProductSpecificationReq req,HttpServletRequest request)throws JsonProcessingException   {
		GenericResponse res = (GenericResponse)productSpecificationService.deleteProductSpecification(req, request);
        return res;
    }

	// 获取产品规格信息
	@ApiOperation(value = "获取产品规格信息(GetProductSpecificationInfo)", httpMethod = "POST", notes = "获取产品规格信息")
	@ResponseBody
	@RequestMapping(value = "/getProductSpecificationInfo", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetProductSpecificationInfoRes getProductSpecificationInfo(@RequestBody ProductSpecificationReq req,HttpServletRequest request)throws JsonProcessingException   {
		GetProductSpecificationInfoRes res = (GetProductSpecificationInfoRes)productSpecificationService.getProductSpecificationInfo(req);
        return res;
    }

	// 获取产品规格列表
	@ApiOperation(value = "获取产品规格列表(GetProductSpecificationList)", httpMethod = "POST", notes = "获取产品规格列表")
	@ResponseBody
	@RequestMapping(value = "/getProductSpecificationList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetProductSpecificationListRes getProductSpecificationList(@RequestBody GetSpecificationListReq req,HttpServletRequest request)throws JsonProcessingException   {
		GetProductSpecificationListRes res = (GetProductSpecificationListRes)productSpecificationService.getProductSpecificationList(req);
        return res;
    }

	// 获取SKU列表
	@ApiOperation(value = "获取SKU列表(GetSkuListBySpecification)", httpMethod = "POST", notes = "获取SKU列表<p>1:specificationId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getSkuListBySpecification", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetSkuListBySpecificationRes getSkuListBySpecification(@RequestBody ProductSpecificationReq req,HttpServletRequest request)throws JsonProcessingException   {
		GetSkuListBySpecificationRes res = (GetSkuListBySpecificationRes)productSpecificationService.getSkuListBySpecification(req);
        return res;
    }

	// 产品规格提交审核
	@ApiOperation(value = "产品规格提交审核(SubmitAuditProductSpecification)", httpMethod = "POST", notes = "产品规格提交审核<p>1:specificationIdList不能为空；")
	@ResponseBody
	@RequestMapping(value = "/submitAuditProductSpecification", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse submitAuditProductSpecification(@RequestBody ProductSpecificationReq req,HttpServletRequest request)throws JsonProcessingException   {
		GenericResponse res = (GenericResponse)productSpecificationService.submitAuditProductSpecification(req);
        return res;
    }

}
