package com.foshan.controller.shop;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.shop.request.ProductAppraiseReq;
import com.foshan.form.shop.response.productAppraiseItem.GetProductAppraiseItemListRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
@Api(tags = "商城--评价模块")
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
public class ProductAppraiseController extends BaseShopController {
	
	// 评价商品
	@ApiOperation(value = "评价商品(addProductAppraise)", httpMethod = "POST", notes = "评价商品")
	@ResponseBody
	@RequestMapping(value = "/addProductAppraise", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse addProductAppraise(@RequestBody ProductAppraiseReq req, HttpServletRequest request)throws Exception   {
		BaseResponse res = (BaseResponse)productAppraiseService.addProductAppraise(req);
        return res;
    }
	
	// 获取商品评价详情列表
	@ApiOperation(value = "获取商品评价详情列表(getProductAppraiseItemList)", httpMethod = "POST", notes = "获取商品评价详情列表")
	@ResponseBody
	@RequestMapping(value = "/getProductAppraiseItemList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetProductAppraiseItemListRes getProductAppraiseItemList(@RequestBody ProductAppraiseReq req, HttpServletRequest request)throws Exception   {
		GetProductAppraiseItemListRes res = (GetProductAppraiseItemListRes)productAppraiseService.getProductAppraiseItemList(req);
        return res;
    }
	
}
