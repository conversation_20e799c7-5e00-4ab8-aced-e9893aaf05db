package com.foshan.controller.shop;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.shop.request.AuthenticationReq;
import com.foshan.form.shop.response.authentication.VideoAuthorizationRes;
import com.foshan.form.shop.response.authentication.WhetherPassAuthenticationRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

	
@Api(tags = "商城--鉴权及授权")
@RestController
public class AuthenticationController extends BaseShopController {

	// 播放鉴权
    @ApiOperation(value = "播放鉴权(whetherPassAuthentication)", httpMethod = "POST", notes = "播放鉴权<p>1:memberId、productId和videoId不能为空；")
    @ResponseBody
    @RequestMapping(value = "/whetherPassAuthentication", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public WhetherPassAuthenticationRes whetherPassAuthentication(@RequestBody AuthenticationReq req, HttpServletRequest request)throws JsonProcessingException  {
    	WhetherPassAuthenticationRes res = (WhetherPassAuthenticationRes) authenticationService.whetherPassAuthentication(req);
        return res;
    }
	
	// 点播授权
    @ApiOperation(value = "点播授权(videoAuthorization)", httpMethod = "POST", notes = "点播授权<p>1:smartcardId、productId和videoId不能为空；")
    @ResponseBody
    @RequestMapping(value = "/videoAuthorization", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public VideoAuthorizationRes videoAuthorization(@RequestBody AuthenticationReq req, HttpServletRequest request)throws JsonProcessingException  {
    	VideoAuthorizationRes res = (VideoAuthorizationRes) authenticationService.videoAuthorization(req);
        return res;
    }
	
	
	// 获取点播授权
    @ApiOperation(value = "获取点播授权(getVideoAuthorization)", httpMethod = "POST", notes = "获取点播授权<p>1:smartcardId、productId和videoId不能为空；")
    @ResponseBody
    @RequestMapping(value = "/getVideoAuthorization", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public WhetherPassAuthenticationRes getVideoAuthorization(@RequestBody AuthenticationReq req, HttpServletRequest request)throws JsonProcessingException  {
    	WhetherPassAuthenticationRes res = (WhetherPassAuthenticationRes) authenticationService.getVideoAuthorization(req);
        return res;
    }
}
