/**   
* @Title: StorePluginConfigController.java 
* @Package com.foshan.controller.shop 
* @Description: TODO(用一句话描述该文件做什么) 
* <AUTHOR>
* @date 2019年3月29日 下午5:44:43 
* @version V1.0   
*/
package com.foshan.controller.shop;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.upload.UploadRes;
import com.foshan.form.shop.request.StorePluginReq;
import com.foshan.form.shop.request.UploadPaymentCertReq;
import com.foshan.form.shop.response.plugin.GetPaymentPluginsRes;
import com.foshan.form.shop.response.plugin.GetStorePluginRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName: StorePluginConfigController
 * @Description: TODO(商家平台管理支付插件接口)
 * <AUTHOR>
 * @date 2019年3月29日 下午5:44:43
 * 
 */
@Api(tags = "商城--商铺插件模块")
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
public class StorePluginConfigController extends BaseShopController {

//	/**
//	 * @Title: getCurentStoreId @Description: TODO(获取当前登录用户的商户ID) @param @return
//	 *         参数说明 @return Integer 返回类型 @throws
//	 */
//	private Integer getCurentStoreId() {
//		StoreEntity store = ShopAuthUtil.getStore();
//		if (store == null) {
//			return null;
//		}
//		return store.getId();
//	}
//
//	/**
//	 * @Title: isPlatformUser @Description: TODO(判断当前登录用户是否为平台管理员) @param @return
//	 *         参数说明 @return Integer 返回类型 @throws
//	 */
//	private boolean isPlatformUser() {
//		PlatformUserEntity result = null;
//
//		Subject curUser = SecurityUtils.getSubject();
//		PrincipalCollection principals = curUser.getPrincipals();
//		if (null != principals && !principals.isEmpty()) {
//			@SuppressWarnings("unchecked")
//			List<Object> principalList = principals.asList();
//			Object principal = (null != principalList.get(1) ? principalList.get(1) : null);
//
//			if (principal instanceof PlatformUserEntity) {
//				result = (PlatformUserEntity) principal;
//			}
//		}
//		return result != null;
//	}

	/**
	 * 获取商户支付插件配置详情
	 */
	@ApiOperation(value = "获取商户支付插件配置详情(/store/plugin/getStorePluginDetail)", httpMethod = "POST", notes = "id不能为空")
	@ResponseBody
	@RequestMapping(value = "/store/plugin/getStorePluginDetail", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetStorePluginRes getStorePluginDetail(@RequestBody StorePluginReq req,HttpServletRequest request)throws JsonProcessingException   {
		GetStorePluginRes res = (GetStorePluginRes) storePluginConfigService.getStorePluginDetail(req);
        return res;
    }
	
	/**
	 * 获取插件配置详情
	 */
	@ApiOperation(value = "获取插件配置详情(/store/plugin/getPluginDetail)", httpMethod = "POST", notes = "pluginId不能为空")
	@ResponseBody
	@RequestMapping(value = "/store/plugin/getPluginDetail", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetStorePluginRes getPluginDetail(@RequestBody StorePluginReq req,HttpServletRequest request)throws JsonProcessingException   {
		GetStorePluginRes res = (GetStorePluginRes) storePluginConfigService.getPaymentPluginDetail(req);
        return res;
    }


	/**
	 * 商户获取支付插件配件信息列表
	 */
	@ApiOperation(value = "获取插件配置详情(/store/plugin/getPaymentPlugins)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/store/plugin/getPaymentPlugins", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetPaymentPluginsRes getPaymentPlugins(HttpServletRequest request)throws JsonProcessingException   {
		GetPaymentPluginsRes res = (GetPaymentPluginsRes) storePluginConfigService.getPaymentPlugins(null);
        return res;
    }

	/**
	 * 
	 * @Title: addStorePaymentPluginConfig 
	 * @Description: TODO(商户配置支付参数) 
	 * @param jsonData = { "pluginId" : 1, "attr" :
	 *         "{\"charge\":\"0\",\"chargeType\":\"fixed\",\"appId\":\"wx7r5t6330771d2222\",\"mchId\":\"1490800003\",\"apiKey\":\"gdfsnb2015511917111118426Xinttwt\"}"} @param @param
	 *         request 
	 * @param response  
	 * @throws Exception 参数说明 
	 * @return
	 * void 返回类型 @throws
	 */
	@ApiOperation(value = "商户配置支付参数(/store/plugin/addStorePaymentPluginConfig)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/store/plugin/addStorePaymentPluginConfig", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse addStorePaymentPluginConfig(@RequestBody StorePluginReq req,HttpServletRequest request)throws JsonProcessingException   {
		BaseResponse res = (BaseResponse) storePluginConfigService.addStorePaymentPluginConfig(req);
        return res;
    }


	// 修改配置
	@ApiOperation(value = "修改配置(/store/plugin/modifyStorePaymentPluginConfig)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/store/plugin/modifyStorePaymentPluginConfig", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse modifyStorePaymentPluginConfig(@RequestBody StorePluginReq req,HttpServletRequest request)throws JsonProcessingException   {
		BaseResponse res = (BaseResponse) storePluginConfigService.updateStorePaymentPluginConfig(req);
        return res;
    }
	

//	/**
//	 *查询插件配置
//	 */
//	@ApiOperation(value = "查询插件配置(/store/plugin/getPluginConfig)", httpMethod = "POST", notes = "商铺插件id不能为空")
//	@ResponseBody
//	@RequestMapping(value = "/store/plugin/getPluginConfig", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//	public void getPluginConfig(@RequestBody StorePluginReq req, HttpServletRequest request,
//			HttpServletResponse response) throws Exception {
//
//		long start = System.currentTimeMillis();
//		logger.info("接收===>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<===【" + request.getMethod()
//				+ "】===请求数据：" +  mapper.writeValueAsString(req));
//
//		// 获取商户ID，验证商户是否登录
//		Integer storeId = null;
//		try {
//			storeId = getCurentStoreId();
//		} catch (Exception ex) {
//			storeId = null;
//		}
//
//		if (storeId == null) {
//			jsonOutByJackson(response, mapper,
//					new GenericResponse(ResponseContext.RES_PERM_UNLOGIN_CODE, ResponseContext.RES_PERM_UNLOGIN_INFO));
//		} else {
//			Response res;
//			req.setStoreId(storeId);
//			req.setAppoint(false);
//			res = storePluginConfigService.getPaymentPluginDetail(req);
//			jsonOutByJackson(response, mapper, res);
//		}
//		long end = System.currentTimeMillis();
//		logger.info("===>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<===【" + request.getMethod()
//				+ "】===请求处理时间:" + (end - start) + "毫秒!");
//	}
//	

	// 提交审核
	@ApiOperation(value = "修改配置(/store/plugin/commitPaymentPluginConfig)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/store/plugin/commitPaymentPluginConfig", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse commitPaymentPluginConfig(@RequestBody StorePluginReq req,HttpServletRequest request)throws JsonProcessingException   {
		BaseResponse res = (BaseResponse) storePluginConfigService.submitPaymentPluginConfig(req);
        return res;
    }

	/**
	 * 上传支付API证书，主要微信支付的退款及取消交易需要用到API证书支持 返回的字段中getUploadFile.FileName
	 * 字段就是需要上传文件的相对路径，需要保存在 真实路径为config.properties文件的shop.plugin.certFileRootDir +
	 * getUploadFile.FileName。
	 **/ 
	@ApiOperation(value = "上传支付API证书(/store/plugin/uploadCert)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/store/plugin/uploadCert", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public UploadRes uploadCertFile(@RequestBody UploadPaymentCertReq req,HttpServletRequest request)throws JsonProcessingException   {
		UploadRes res = (UploadRes) storePluginConfigService.uploadCertFile(request, req);
        return res;
    }

	// 审核**********平台管理员接口*************
	@ApiOperation(value = "平台管理员审核支付配置(/admin/plugin/auditStorePaymentPluginConfig)", httpMethod = "POST", notes = "id不能为空，auditStatus不能为空")
	@ResponseBody
	@RequestMapping(value = "/admin/plugin/auditStorePaymentPluginConfig", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse auditStorePaymentPluginConfig(@RequestBody StorePluginReq req,HttpServletRequest request)throws JsonProcessingException   {
		GenericResponse res = (GenericResponse) storePluginConfigService.auditStorePaymentPluginConfig(req);
        return res;
    }

	// 平台管理为商户指定支付配置**********平台管理员接口*************
	@ApiOperation(value = "平台管理为商户指定支付配置(/admin/plugin/appointStorePaymentPluginConfig)", httpMethod = "POST", notes = "storeId和pluginId不能为空")
	@ResponseBody
	@RequestMapping(value = "/admin/plugin/appointStorePaymentPluginConfig", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse appointStorePaymentPluginConfig(@RequestBody StorePluginReq req,HttpServletRequest request)throws JsonProcessingException   {
		GenericResponse res = (GenericResponse) storePluginConfigService.appointStorePaymentPluginConfig(req);
        return res;
    }

	/**
	 * 
	 * @Title: getPaymentPlugins
	 * @Description: TODO(平台管理员获取支付插件配置信息列表)
	 * @param request
	 * @param response
	 * @return { "ret": "0000", "retInfo": "操作成功！！！", "pluginList": [ { "pluginId":
	 *         "1", "storePluginId": null, "sn":
	 *         "com.foshan.plugin.payment.AlipayImmediatePaymentPlugin", "name":
	 *         "微信H5支付", "canConfig": true, "lastModifyTime": null, "auditStatus":
	 *         null, "auditHistory": null, "appoint": false }] }
	 * @throws IOException
	 * @throws JsonMappingException
	 * @throws JsonParseException
	 */
	@ApiOperation(value = "平台管理员获取支付插件配置信息列表(/admin/plugin/getStorePaymentPlugins)", httpMethod = "POST", notes = "storeId和pluginId不能为空")
	@ResponseBody
	@RequestMapping(value = "/admin/plugin/getStorePaymentPlugins", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetPaymentPluginsRes getPaymentStorePluginsByPlatform(@RequestBody StorePluginReq req,HttpServletRequest request)throws JsonProcessingException   {
		GetPaymentPluginsRes res = (GetPaymentPluginsRes) storePluginConfigService.getPaymentPlugins(req.getRequestPage(), req.getPageSize(),
				req.getStoreId(),req.getStoreName(), req.getPluginId(), req.getAuditStatus(),req.getRegionId());
        return res;
    }
	/**
	 * 商户获取支付插件配件信息列表
	 */
	@ApiOperation(value = "获取插件配置详情(/admin/plugin/getPaymentPlugins)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/admin/plugin/getPaymentPlugins", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetPaymentPluginsRes getPaymentPlugins(@RequestBody StorePluginReq req,HttpServletRequest request)throws JsonProcessingException   {
		GetPaymentPluginsRes res = (GetPaymentPluginsRes) storePluginConfigService.getPaymentPlugins(req);
        return res;
    }

	/**
	 * 
	 * @Title: getPaymentPlugins
	 * @Description: TODO(平台管理员获取支付插件配置信息详情)
	 * @param request
	 * @param response
	 * @return { "ret": "0000", "retInfo": "操作成功！！！", "pluginList": [ { "pluginId":
	 *         "1", "storePluginId": null, "sn":
	 *         "com.foshan.plugin.payment.AlipayImmediatePaymentPlugin", "name":
	 *         "微信H5支付", "canConfig": true, "lastModifyTime": null, "auditStatus":
	 *         null, "auditHistory": null, "appoint": false }] }
	 * @throws IOException
	 * @throws JsonMappingException
	 * @throws JsonParseException
	 */
	@ApiOperation(value = "平台管理员获取支付插件配置信息详情(/admin/plugin/getStorePluginDetail)", httpMethod = "POST", notes = "id不能为空,storeId不能为空")
	@ResponseBody
	@RequestMapping(value = "/admin/plugin/getStorePluginDetail", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetStorePluginRes getStorePluginDetailByPlatform(@RequestBody StorePluginReq req,HttpServletRequest request)throws JsonProcessingException   {
		GetStorePluginRes res = (GetStorePluginRes) storePluginConfigService.getStorePluginDetail(req);
        return res;
    }
	
	/**
	 * 平台管理员获取插件配置详情
	 */
	@ApiOperation(value = "平台管理员获取插件配置详情(/admin/plugin/getPluginDetail)", httpMethod = "POST", notes = "pluginId不能为空")
	@ResponseBody
	@RequestMapping(value = "/admin/plugin/getPluginDetail", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetStorePluginRes getPluginDetailByPlatform(@RequestBody StorePluginReq req,HttpServletRequest request)throws JsonProcessingException   {
		GetStorePluginRes res = (GetStorePluginRes) storePluginConfigService.getPaymentPluginDetail(req);
        return res;
    }
	

	// 平台管理员修改商户支付配置信息**********平台管理员接口*************
	@ApiOperation(value = "平台管理员修改商户支付配置信息(/admin/plugin/modifyStorePaymentPluginConfigByPlatform)", httpMethod = "POST", notes = "id不能为空")
	@ResponseBody
	@RequestMapping(value = "/admin/plugin/modifyStorePaymentPluginConfigByPlatform", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse modifyStorePaymentPluginConfigByPlatform(@RequestBody StorePluginReq req,HttpServletRequest request)throws JsonProcessingException   {
		GenericResponse res = (GenericResponse) storePluginConfigService.updateStorePaymentPluginConfigByPlatform(req);
        return res;
    }

	// 平台管理员删除商户支付配置信息**********平台管理员接口*************
		@ApiOperation(value = " 平台管理员删除商户支付配置信息(/admin/plugin/clearStorePaymentPluginConfigByPlatform)", httpMethod = "POST", notes = "id不能为空")
		@ResponseBody
		@RequestMapping(value = "/admin/plugin/clearStorePaymentPluginConfigByPlatform", method = {
	            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	    public GenericResponse deleteStorePaymentPluginConfigByPlatform(@RequestBody StorePluginReq req,HttpServletRequest request)throws JsonProcessingException   {
			GenericResponse res = (GenericResponse) storePluginConfigService.deleteStorePaymentPluginConfigByPlatform(req);
	        return res;
	    }

	/**
	 * 
	 * @Title: addStorePaymentPluginConfig 
	 * @Description: TODO(平台管理员配置支付参数) 
	 * @param jsonData = { "pluginId" : 1, "attr" :
	 *         "{\"charge\":\"0\",\"chargeType\":\"fixed\",\"appId\":\"wx7r5t6330771d2222\",\"mchId\":\"1490800003\",\"apiKey\":\"gdfsnb2015511917111118426Xinttwt\"}"} @param @param
	 *         request 
	 * @param response  
	 * @throws Exception 参数说明 
	 */
	@ApiOperation(value = "平台管理员配置支付参数(/admin/plugin/addStorePaymentPluginConfigByPlatform)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/admin/plugin/addStorePaymentPluginConfigByPlatform", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse addStorePaymentPluginConfigByPlatform(@RequestBody StorePluginReq req,HttpServletRequest request)throws JsonProcessingException   {
		BaseResponse res = (BaseResponse) storePluginConfigService.addStorePaymentPluginConfigByPlatform(req);
        return res;
    }
}
