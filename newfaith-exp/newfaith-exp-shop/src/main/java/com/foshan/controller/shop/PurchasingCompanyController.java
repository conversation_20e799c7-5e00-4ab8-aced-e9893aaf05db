package com.foshan.controller.shop;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.shop.request.MemberLoginReq;
import com.foshan.form.shop.request.PurchasingCompanyReq;
import com.foshan.form.shop.response.member.MemberLoginRes;
import com.foshan.form.shop.response.purchasingCompany.GetPurchasingCompanyInfoRes;
import com.foshan.form.shop.response.purchasingCompany.GetPurchasingCompanyListRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "商城--企业购公司模块")
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
public class PurchasingCompanyController extends BaseShopController {
	// 添加
	@ApiOperation(value = "新增企业购公司(AddPurchasingCompany)", httpMethod = "POST", notes = "新增企业购公司<p>1：brandName不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addPurchasingCompany", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse addPurchasingCompany(@RequestBody PurchasingCompanyReq req,HttpServletRequest request)throws JsonProcessingException   {
		GenericResponse res = (GenericResponse) purchasingCompanyService.addPurchasingCompany(req);
        return res;
    }

	// 修改
	@ApiOperation(value = "修改企业购公司(ModifyPurchasingCompany)", httpMethod = "POST", notes = "修改企业购公司<p>1：brandName不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyPurchasingCompany", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse modifyPurchasingCompany(@RequestBody PurchasingCompanyReq req,HttpServletRequest request)throws JsonProcessingException   {
		GenericResponse res = (GenericResponse) purchasingCompanyService.modifyPurchasingCompany(req);
        return res;
    }

	// 删除
	@ApiOperation(value = "删除企业购公司(DeletePurchasingCompany)", httpMethod = "POST", notes = "删除企业购公司<p>1：brandName不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deletePurchasingCompany", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse deletePurchasingCompany(@RequestBody PurchasingCompanyReq req,HttpServletRequest request)throws JsonProcessingException   {
		GenericResponse res = (GenericResponse) purchasingCompanyService.deletePurchasingCompany(req);
        return res;
    }

	// 获取列表数据
	@ApiOperation(value = "获取企业购公司列表(GetPurchasingCompanyList)", httpMethod = "POST", notes = "获取企业购公司列表<p>1：brandName不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getPurchasingCompanyList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetPurchasingCompanyListRes getPurchasingCompanyList(@RequestBody PurchasingCompanyReq req,HttpServletRequest request)throws JsonProcessingException   {
		GetPurchasingCompanyListRes res = (GetPurchasingCompanyListRes) purchasingCompanyService.getPurchasingCompanyList(req);
        return res;
    }

	// 获取详情
	@ApiOperation(value = "获取企业购公司详情(GetPurchasingCompanyInfo)", httpMethod = "POST", notes = "获取企业购公司详情<p>1：brandName不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getPurchasingCompanyInfo", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetPurchasingCompanyInfoRes getPurchasingCompanyInfo(@RequestBody PurchasingCompanyReq req,HttpServletRequest request)throws JsonProcessingException   {
		GetPurchasingCompanyInfoRes res = (GetPurchasingCompanyInfoRes) purchasingCompanyService.getPurchasingCompanyInfo(req);
        return res;
    }
	
	// 注册采购员
	@ApiOperation(value = "注册采购员(purchasingAgentRegist)", httpMethod = "POST", notes = "注册采购员<p>1：companyId、loginName不能为空；")
	@ResponseBody
	@RequestMapping(value = "/purchasingAgentRegist", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public MemberLoginRes purchasingAgentRegist(@RequestBody MemberLoginReq req,HttpServletRequest request)throws JsonProcessingException   {
		MemberLoginRes res = (MemberLoginRes) purchasingCompanyService.purchasingAgentRegist(req);
        return res;
    }
	
	// 删除采购员
	@ApiOperation(value = "删除采购员(deletePurchasingAgent)", httpMethod = "POST", notes = "删除采购员<p>1：memberId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deletePurchasingAgent", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse deletePurchasingAgent(@RequestBody MemberLoginReq req,HttpServletRequest request)throws JsonProcessingException   {
		GenericResponse res = (GenericResponse) purchasingCompanyService.deletePurchasingAgent(req);
        return res;
    }
}
