package com.foshan.controller.shop;

import java.io.File;
import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.foshan.entity.shop.ShopUserEntity;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.upload.UploadRes;
import com.foshan.form.shop.request.AddInvoiceBatchReq;
import com.foshan.form.shop.request.ProductOrderInvoiceReq;
import com.foshan.form.shop.response.invoice.AddInvoviceBatchRes;
import com.foshan.form.shop.response.invoice.ProductOrderInvoiceListRes;
import com.foshan.form.shop.response.invoice.ProductOrderInvoiceRes;
import com.foshan.util.ShopContextInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

//订单发票
@Api(tags = "商城--订单发票模块")
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
public class ProductOrderInvoiceController extends BaseShopController {
	private final static Logger logger = LoggerFactory.getLogger(ProductOrderInvoiceController.class);

	// 为订单添加电子发票
	@ApiOperation(value = "为订单添加电子发票(/store/invoice/addProductOrderInvoice)", httpMethod = "POST", notes = "为订单添加电子发票"
			+ "<p>1:needLocalInvoiceSystem=true时，会自动调用发票系统开具开票，needLocalInvoiceSystem=false 为第三方商铺上传发票，orderId为必填写段，needLocalInvoiceSystem、billType、invoiceType必为填项；")
	@ResponseBody
	@RequestMapping(value = "/store/invoice/addProductOrderInvoice", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse addProductOrderInvoice(@RequestBody ProductOrderInvoiceReq req,HttpServletRequest request)throws JsonProcessingException   {
		GenericResponse res = (GenericResponse)productOrderInvoiceService.addInvoice(req);
        return res;
    }

	// 修改订单电子发票
	@ApiOperation(value = "修改订单电子发票(/store/invoice/modifyProductOrderInvoice)", httpMethod = "POST", notes = "修改订单电子发票"
			+ "<p>1:needLocalInvoiceSystem=true时，会自动调用发票系统开具开票，needLocalInvoiceSystem=false 为第三方商铺上传发票，id为必填项；")
	@ResponseBody
	@RequestMapping(value = "/store/invoice/modifyProductOrderInvoice", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse modifyProductOrderInvoice(@RequestBody ProductOrderInvoiceReq req,HttpServletRequest request)throws JsonProcessingException   {
		GenericResponse res = (GenericResponse)productOrderInvoiceService.modifyInvoice(req);
        return res;
    }

	// 删除订单电子发票
	@ApiOperation(value = "删除订单电子发票(/store/invoice/deleteProductOrderInvoice)", httpMethod = "POST", notes = "删除订单电子发票"
			+ "<p>id为必填项；")
	@ResponseBody
	@RequestMapping(value = "/store/invoice/deleteProductOrderInvoice", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse deleteProductOrderInvoice(@RequestBody ProductOrderInvoiceReq req,HttpServletRequest request)throws JsonProcessingException   {
		GenericResponse res = (GenericResponse)productOrderInvoiceService.deleteInvoice(req);
        return res;
    }

	// 上传订单电子发票文件
	@ApiOperation(value = "上传订单电子发票文件(/store/invoice/uploadElectronicInvoiceFile)", httpMethod = "POST", notes = "上传订单电子发票文件"
			+ "上传电子发票PDF文件到系统，返回下载地址相对路径；")
	@RequestMapping(value = "/store/invoice/uploadElectronicInvoiceFile", method = {RequestMethod.POST } )
	public UploadRes uploadElectronicInvoiceFile(  HttpServletRequest request)
			throws JsonParseException, JsonMappingException, IOException {
		long start = System.currentTimeMillis();
		UploadRes res = null;

		logger.info("接收==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==" + request.getMethod()
				+ "请求数据：" );
		// ProductOrderInvoiceReq req = (ProductOrderInvoiceReq)
		// mapper.readValue(jsonData, ProductOrderInvoiceReq.class);

		// 获取商户ID，验证商户是否登录
		Subject curUser = SecurityUtils.getSubject();
		PrincipalCollection principals = curUser.getPrincipals();
		Object userObj = null;
		if (null != principals && !principals.isEmpty()) {
			@SuppressWarnings("unchecked")
			List<Object> principalList = principals.asList();
			userObj = (null != principalList.get(1) ? principalList.get(1) : null);
		}

		if (null != userObj) {
			if (userObj instanceof ShopUserEntity) {
				ShopUserEntity sotreUser = (ShopUserEntity) userObj;
				// 文件保存路径
				String virtualPath =  File.separator + "invoice" + File.separator + sotreUser.getStore().getId();
				String realDirPath = shopContextInfo.invoiceFileUploadPath + File.separator + sotreUser.getStore().getId() + File.separator;
				res = (UploadRes) uploadService.uploadFile(request, realDirPath, virtualPath, false, null, "png,jpg,bmp,pdf");

				//		+ File.separator + res.getUploadFile().getFileName();
				// res.getUploadFile().setFileName(res.getUploadFile().getFileName());
				// req.setDownloadUrl(saveFileName);
				// req.setNeedLocalInvoiceSystem(false);
				// productOrderInvoiceService.addInvoice(req);
				// jsonOutByJackson(response, mapper, res);

			} else {
				res = new UploadRes(ResponseContext.RES_PERM_UNLOGIN_CODE, ResponseContext.RES_PERM_UNLOGIN_INFO);
			}
		} else {
			res = new UploadRes(ResponseContext.RES_PERM_UNLOGIN_CODE, ResponseContext.RES_PERM_UNLOGIN_INFO);
		}
		long end = System.currentTimeMillis();
		logger.info(
				"==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==处理时间:" + (end - start) + "毫秒!");
		return res;
	}

	// 商户根据订单ID获取订单电子发票列表
	@ApiOperation(value = "商户根据订单ID获取订单电子发票列表(/store/invoice/getInvoiceListByOrderId)", httpMethod = "POST", notes = "商户根据订单ID获取订单电子发票列表"
			+ "orderId 必传字段；")
	@RequestMapping(value = "/store/invoice/getInvoiceListByOrderId", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ProductOrderInvoiceListRes getInvoiceList(@RequestBody ProductOrderInvoiceReq req,HttpServletRequest request)throws JsonProcessingException   {
		ProductOrderInvoiceListRes res = (ProductOrderInvoiceListRes)productOrderInvoiceService.getInvoiceListByOrderId(req);
        return res;
    }

	// 根据电子发票ID获取电子发票详情
	@ApiOperation(value = "根据电子发票ID获取电子发票详情(/store/invoice/getInvoiceDetail)", httpMethod = "POST", notes = "根据电子发票ID获取电子发票详情"
			+ "id 必传字段；")
	@RequestMapping(value = "/store/invoice/getInvoiceDetail", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ProductOrderInvoiceRes getInvoiceDetail(@RequestBody ProductOrderInvoiceReq req,HttpServletRequest request)throws JsonProcessingException   {
		ProductOrderInvoiceRes res = (ProductOrderInvoiceRes)productOrderInvoiceService.getInvoiceDetail(req);
        return res;
    }

	// 会员根据订单ID获取订单电子发票列表
	@ApiOperation(value = "会员根据订单ID获取订单电子发票列表(/store/invoice/getInvoiceListByOrderId)", httpMethod = "POST", notes = "会员根据订单ID获取订单电子发票列表"
			+ "id 必传字段；")
	@RequestMapping(value = "/member/invoice/getInvoiceListByOrderId", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ProductOrderInvoiceListRes getInvoiceListByOrderId(@RequestBody ProductOrderInvoiceReq req,HttpServletRequest request)throws JsonProcessingException   {
		ProductOrderInvoiceListRes res = (ProductOrderInvoiceListRes)productOrderInvoiceService.getInvoiceListByOrderId(req);
        return res;
    }
	
	// 重新下载电子发票
	@ApiOperation(value = "重新下载电子发票(/store/invoice/downloadInvoiceAgain)", httpMethod = "POST", notes = "重新下载电子发票"
			+ "<p>1:id必为填项；")
	@ResponseBody
	@RequestMapping(value = "/store/invoice/downloadInvoiceAgain", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse downloadInvoiceAgain(@RequestBody ProductOrderInvoiceReq req,HttpServletRequest request)throws JsonProcessingException   {
		GenericResponse res = (GenericResponse)productOrderInvoiceService.downloadInvoiceAgain(req);
        return res;
    }
	
	// 批量开发票
	@ApiOperation(value = "批量开发票(/store/invoice/addInvoicesBatch)", httpMethod = "POST", notes = "批量开发票"
			+ "<p>1:id必为填项；")
	@ResponseBody
	@RequestMapping(value = "/store/invoice/addInvoicesBatch", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public AddInvoviceBatchRes addInvoicesBatch(@RequestBody AddInvoiceBatchReq req,HttpServletRequest request)throws JsonProcessingException   {
		AddInvoviceBatchRes res = (AddInvoviceBatchRes)productOrderInvoiceService.addInvoicesBatch(req);
        return res;
    }
	
}
