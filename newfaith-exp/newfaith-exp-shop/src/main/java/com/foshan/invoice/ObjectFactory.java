package com.foshan.invoice;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _ElectroniceBatchQueryFPDM_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "FP_DM");
    private final static QName _ElectroniceBatchQueryFPHM_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "FP_HM");
    private final static QName _ElectroniceBatchQueryGHFMC_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "GHF_MC");
    private final static QName _ElectroniceBatchQueryGHFNSRSBH_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "GHF_NSRSBH");
    private final static QName _ElectroniceBatchQueryKPRQFrom_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "KPRQ_from");
    private final static QName _ElectroniceBatchQueryKPRQTo_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "KPRQ_to");
    private final static QName _ElectroniceBatchQueryXHFNSRSBH_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "XHF_NSRSBH");
    private final static QName _ElectroniceStockKPNSRSBH_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "KP_NSRSBH");
    private final static QName _ElectroniceStockSYFPFS_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "SYFPFS");
    private final static QName _ElectroniceStockReturnCode_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "returnCode");
    private final static QName _ElectroniceStockReturnMsg_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "returnMsg");
    private final static QName _ReturnBuyerListBuyerList_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "buyerList");
    private final static QName _ReturnBuyerListResponseCode_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "responseCode");
    private final static QName _ReturnBuyerListResponseMsg_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "responseMsg");
    private final static QName _ReturnElectroniceBatchListList_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "list");
    private final static QName _ReturnElectroniceCZDM_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "CZDM");
    private final static QName _ReturnElectroniceDDH_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "DDH");
    private final static QName _ReturnElectroniceEWM_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "EWM");
    private final static QName _ReturnElectroniceFPQQLSH_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "FPQQLSH");
    private final static QName _ReturnElectroniceFPZLDM_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "FPZL_DM");
    private final static QName _ReturnElectroniceFWM_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "FWM");
    private final static QName _ReturnElectroniceJYM_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "JYM");
    private final static QName _ReturnElectroniceKPLSH_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "KPLSH");
    private final static QName _ReturnElectroniceKPLX_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "KPLX");
    private final static QName _ReturnElectroniceKPRQ_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "KPRQ");
    private final static QName _ReturnElectronicePDFFILE_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "PDF_FILE");
    private final static QName _ReturnElectronicePDFURL_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "PDF_URL");
    private final static QName _ElectroniceDetailDW_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "DW");
    private final static QName _ElectroniceDetailGGXH_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "GGXH");
    private final static QName _ElectroniceDetailHSBZ_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "HSBZ");
    private final static QName _ElectroniceDetailLSLBS_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "LSLBS");
    private final static QName _ElectroniceDetailSL_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "SL");
    private final static QName _ElectroniceDetailSPBM_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "SPBM");
    private final static QName _ElectroniceDetailXMBM_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "XMBM");
    private final static QName _ElectroniceDetailXMMC_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "XMMC");
    private final static QName _ElectroniceDetailYHZCBS_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "YHZCBS");
    private final static QName _ElectroniceDetailZXBM_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "ZXBM");
    private final static QName _ElectroniceDetailZZSTSGL_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "ZZSTSGL");
    private final static QName _ElectroniceDetailPcguid_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "pcguid");
    private final static QName _ElectroniceInfoBMBBBH_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "BMB_BBH");
    private final static QName _ElectroniceInfoBZ_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "BZ");
    private final static QName _ElectroniceInfoCHYY_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "CHYY");
    private final static QName _ElectroniceInfoDKBZ_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "DKBZ");
    private final static QName _ElectroniceInfoDSPTBM_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "DSPTBM");
    private final static QName _ElectroniceInfoEMAIL_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "EMAIL");
    private final static QName _ElectroniceInfoFHR_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "FHR");
    private final static QName _ElectroniceInfoFPZT_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "FPZT");
    private final static QName _ElectroniceInfoFPMW_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "FP_MW");
    private final static QName _ElectroniceInfoGHFQYLX_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "GHFQYLX");
    private final static QName _ElectroniceInfoGHFDZ_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "GHF_DZ");
    private final static QName _ElectroniceInfoGHFEMAIL_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "GHF_EMAIL");
    private final static QName _ElectroniceInfoGHFGDDH_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "GHF_GDDH");
    private final static QName _ElectroniceInfoGHFSF_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "GHF_SF");
    private final static QName _ElectroniceInfoGHFSJ_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "GHF_SJ");
    private final static QName _ElectroniceInfoGHFYHZH_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "GHF_YHZH");
    private final static QName _ElectroniceInfoHYDM_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "HY_DM");
    private final static QName _ElectroniceInfoHYMC_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "HY_MC");
    private final static QName _ElectroniceInfoJQBH_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "JQBH");
    private final static QName _ElectroniceInfoKPR_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "KPR");
    private final static QName _ElectroniceInfoKPXM_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "KPXM");
    private final static QName _ElectroniceInfoKPNSRMC_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "KP_NSRMC");
    private final static QName _ElectroniceInfoNSRDZDAH_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "NSRDZDAH");
    private final static QName _ElectroniceInfoPYDM_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "PYDM");
    private final static QName _ElectroniceInfoSJ_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "SJ");
    private final static QName _ElectroniceInfoSKR_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "SKR");
    private final static QName _ElectroniceInfoSWJGDM_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "SWJG_DM");
    private final static QName _ElectroniceInfoTSCHBZ_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "TSCHBZ");
    private final static QName _ElectroniceInfoTSFS_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "TSFS");
    private final static QName _ElectroniceInfoXHFDH_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "XHF_DH");
    private final static QName _ElectroniceInfoXHFDZ_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "XHF_DZ");
    private final static QName _ElectroniceInfoXHFMC_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "XHF_MC");
    private final static QName _ElectroniceInfoXHFYHZH_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "XHF_YHZH");
    private final static QName _ElectroniceInfoYFPDM_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "YFP_DM");
    private final static QName _ElectroniceInfoYFPHM_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "YFP_HM");
    private final static QName _ElectroniceInfoAppId_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "appId");
    private final static QName _ElectroniceInfoAuthorizationCode_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "authorizationCode");
    private final static QName _ElectroniceInfoCodeType_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "codeType");
    private final static QName _ElectroniceInfoContent_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "content");
    private final static QName _ElectroniceInfoDataExchangeId_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "dataExchangeId");
    private final static QName _ElectroniceInfoDetails_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "details");
    private final static QName _ElectroniceInfoEncryptCode_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "encryptCode");
    private final static QName _ElectroniceInfoInterfaceCode_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "interfaceCode");
    private final static QName _ElectroniceInfoPassWord_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "passWord");
    private final static QName _ElectroniceInfoRequestCode_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "requestCode");
    private final static QName _ElectroniceInfoTerminalCode_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "terminalCode");
    private final static QName _ElectroniceInfoUserName_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "userName");
    private final static QName _ElectroniceInfoVersion_QNAME = new QName("http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", "version");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link ElectroniceInfo }
     * 
     */
    public ElectroniceInfo createElectroniceInfo() {
        return new ElectroniceInfo();
    }

    /**
     * Create an instance of {@link ArrayOfElectroniceDetail }
     * 
     */
    public ArrayOfElectroniceDetail createArrayOfElectroniceDetail() {
        return new ArrayOfElectroniceDetail();
    }

    /**
     * Create an instance of {@link ElectroniceDetail }
     * 
     */
    public ElectroniceDetail createElectroniceDetail() {
        return new ElectroniceDetail();
    }

    /**
     * Create an instance of {@link ReturnElectronice }
     * 
     */
    public ReturnElectronice createReturnElectronice() {
        return new ReturnElectronice();
    }

    /**
     * Create an instance of {@link ReturnElectroniceBatchList }
     * 
     */
    public ReturnElectroniceBatchList createReturnElectroniceBatchList() {
        return new ReturnElectroniceBatchList();
    }

    /**
     * Create an instance of {@link ArrayOfReturnElectronice }
     * 
     */
    public ArrayOfReturnElectronice createArrayOfReturnElectronice() {
        return new ArrayOfReturnElectronice();
    }

    /**
     * Create an instance of {@link ReturnBuyerList }
     * 
     */
    public ReturnBuyerList createReturnBuyerList() {
        return new ReturnBuyerList();
    }

    /**
     * Create an instance of {@link ElectroniceStock }
     * 
     */
    public ElectroniceStock createElectroniceStock() {
        return new ElectroniceStock();
    }

    /**
     * Create an instance of {@link ElectroniceBatchQuery }
     * 
     */
    public ElectroniceBatchQuery createElectroniceBatchQuery() {
        return new ElectroniceBatchQuery();
    }

    /**
     * Create an instance of {@link WechatInv }
     * 
     */
    public WechatInv createWechatInv() {
        return new WechatInv();
    }

    /**
     * Create an instance of {@link WechatInvResponse }
     * 
     */
    public WechatInvResponse createWechatInvResponse() {
        return new WechatInvResponse();
    }

    /**
     * Create an instance of {@link QueryEliDataByBZ }
     * 
     */
    public QueryEliDataByBZ createQueryEliDataByBZ() {
        return new QueryEliDataByBZ();
    }

    /**
     * Create an instance of {@link QueryEliDataByBZResponse }
     * 
     */
    public QueryEliDataByBZResponse createQueryEliDataByBZResponse() {
        return new QueryEliDataByBZResponse();
    }

    /**
     * Create an instance of {@link RePushFpInfo }
     * 
     */
    public RePushFpInfo createRePushFpInfo() {
        return new RePushFpInfo();
    }

    /**
     * Create an instance of {@link RePushFpInfoResponse }
     * 
     */
    public RePushFpInfoResponse createRePushFpInfoResponse() {
        return new RePushFpInfoResponse();
    }

    /**
     * Create an instance of {@link QueryEliData }
     * 
     */
    public QueryEliData createQueryEliData() {
        return new QueryEliData();
    }

    /**
     * Create an instance of {@link QueryEliDataResponse }
     * 
     */
    public QueryEliDataResponse createQueryEliDataResponse() {
        return new QueryEliDataResponse();
    }

    /**
     * Create an instance of {@link SendToInvEli }
     * 
     */
    public SendToInvEli createSendToInvEli() {
        return new SendToInvEli();
    }

    /**
     * Create an instance of {@link SendToInvEliResponse }
     * 
     */
    public SendToInvEliResponse createSendToInvEliResponse() {
        return new SendToInvEliResponse();
    }

    /**
     * Create an instance of {@link AutoSendByEmail }
     * 
     */
    public AutoSendByEmail createAutoSendByEmail() {
        return new AutoSendByEmail();
    }

    /**
     * Create an instance of {@link AutoSendByEmailResponse }
     * 
     */
    public AutoSendByEmailResponse createAutoSendByEmailResponse() {
        return new AutoSendByEmailResponse();
    }

    /**
     * Create an instance of {@link PdfToPng }
     * 
     */
    public PdfToPng createPdfToPng() {
        return new PdfToPng();
    }

    /**
     * Create an instance of {@link PdfToPngResponse }
     * 
     */
    public PdfToPngResponse createPdfToPngResponse() {
        return new PdfToPngResponse();
    }

    /**
     * Create an instance of {@link BuyerNameListByName }
     * 
     */
    public BuyerNameListByName createBuyerNameListByName() {
        return new BuyerNameListByName();
    }

    /**
     * Create an instance of {@link BuyerNameListByNameResponse }
     * 
     */
    public BuyerNameListByNameResponse createBuyerNameListByNameResponse() {
        return new BuyerNameListByNameResponse();
    }

    /**
     * Create an instance of {@link QueryEliStock }
     * 
     */
    public QueryEliStock createQueryEliStock() {
        return new QueryEliStock();
    }

    /**
     * Create an instance of {@link QueryEliStockResponse }
     * 
     */
    public QueryEliStockResponse createQueryEliStockResponse() {
        return new QueryEliStockResponse();
    }

    /**
     * Create an instance of {@link InvEli }
     * 
     */
    public InvEli createInvEli() {
        return new InvEli();
    }

    /**
     * Create an instance of {@link InvEliResponse }
     * 
     */
    public InvEliResponse createInvEliResponse() {
        return new InvEliResponse();
    }

    /**
     * Create an instance of {@link WechatInvByProG }
     * 
     */
    public WechatInvByProG createWechatInvByProG() {
        return new WechatInvByProG();
    }

    /**
     * Create an instance of {@link WechatInvByProGResponse }
     * 
     */
    public WechatInvByProGResponse createWechatInvByProGResponse() {
        return new WechatInvByProGResponse();
    }

    /**
     * Create an instance of {@link QueryEliBatchData }
     * 
     */
    public QueryEliBatchData createQueryEliBatchData() {
        return new QueryEliBatchData();
    }

    /**
     * Create an instance of {@link QueryEliBatchDataResponse }
     * 
     */
    public QueryEliBatchDataResponse createQueryEliBatchDataResponse() {
        return new QueryEliBatchDataResponse();
    }

    /**
     * Create an instance of {@link QueryDiskInfo }
     * 
     */
    public QueryDiskInfo createQueryDiskInfo() {
        return new QueryDiskInfo();
    }

    /**
     * Create an instance of {@link QueryDiskInfoResponse }
     * 
     */
    public QueryDiskInfoResponse createQueryDiskInfoResponse() {
        return new QueryDiskInfoResponse();
    }

    /**
     * Create an instance of {@link UpdateMailAndPhone }
     * 
     */
    public UpdateMailAndPhone createUpdateMailAndPhone() {
        return new UpdateMailAndPhone();
    }

    /**
     * Create an instance of {@link UpdateMailAndPhoneResponse }
     * 
     */
    public UpdateMailAndPhoneResponse createUpdateMailAndPhoneResponse() {
        return new UpdateMailAndPhoneResponse();
    }

    /**
     * Create an instance of {@link QueryEleDetails }
     * 
     */
    public QueryEleDetails createQueryEleDetails() {
        return new QueryEleDetails();
    }

    /**
     * Create an instance of {@link QueryEleDetailsResponse }
     * 
     */
    public QueryEleDetailsResponse createQueryEleDetailsResponse() {
        return new QueryEleDetailsResponse();
    }

    /**
     * Create an instance of {@link ArrayOfString }
     * 
     */
    public ArrayOfString createArrayOfString() {
        return new ArrayOfString();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "FP_DM", scope = ElectroniceBatchQuery.class)
    public JAXBElement<String> createElectroniceBatchQueryFPDM(String value) {
        return new JAXBElement<String>(_ElectroniceBatchQueryFPDM_QNAME, String.class, ElectroniceBatchQuery.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "FP_HM", scope = ElectroniceBatchQuery.class)
    public JAXBElement<String> createElectroniceBatchQueryFPHM(String value) {
        return new JAXBElement<String>(_ElectroniceBatchQueryFPHM_QNAME, String.class, ElectroniceBatchQuery.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "GHF_MC", scope = ElectroniceBatchQuery.class)
    public JAXBElement<String> createElectroniceBatchQueryGHFMC(String value) {
        return new JAXBElement<String>(_ElectroniceBatchQueryGHFMC_QNAME, String.class, ElectroniceBatchQuery.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "GHF_NSRSBH", scope = ElectroniceBatchQuery.class)
    public JAXBElement<String> createElectroniceBatchQueryGHFNSRSBH(String value) {
        return new JAXBElement<String>(_ElectroniceBatchQueryGHFNSRSBH_QNAME, String.class, ElectroniceBatchQuery.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "KPRQ_from", scope = ElectroniceBatchQuery.class)
    public JAXBElement<String> createElectroniceBatchQueryKPRQFrom(String value) {
        return new JAXBElement<String>(_ElectroniceBatchQueryKPRQFrom_QNAME, String.class, ElectroniceBatchQuery.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "KPRQ_to", scope = ElectroniceBatchQuery.class)
    public JAXBElement<String> createElectroniceBatchQueryKPRQTo(String value) {
        return new JAXBElement<String>(_ElectroniceBatchQueryKPRQTo_QNAME, String.class, ElectroniceBatchQuery.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "XHF_NSRSBH", scope = ElectroniceBatchQuery.class)
    public JAXBElement<String> createElectroniceBatchQueryXHFNSRSBH(String value) {
        return new JAXBElement<String>(_ElectroniceBatchQueryXHFNSRSBH_QNAME, String.class, ElectroniceBatchQuery.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "KP_NSRSBH", scope = ElectroniceStock.class)
    public JAXBElement<String> createElectroniceStockKPNSRSBH(String value) {
        return new JAXBElement<String>(_ElectroniceStockKPNSRSBH_QNAME, String.class, ElectroniceStock.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "SYFPFS", scope = ElectroniceStock.class)
    public JAXBElement<String> createElectroniceStockSYFPFS(String value) {
        return new JAXBElement<String>(_ElectroniceStockSYFPFS_QNAME, String.class, ElectroniceStock.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "returnCode", scope = ElectroniceStock.class)
    public JAXBElement<String> createElectroniceStockReturnCode(String value) {
        return new JAXBElement<String>(_ElectroniceStockReturnCode_QNAME, String.class, ElectroniceStock.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "returnMsg", scope = ElectroniceStock.class)
    public JAXBElement<String> createElectroniceStockReturnMsg(String value) {
        return new JAXBElement<String>(_ElectroniceStockReturnMsg_QNAME, String.class, ElectroniceStock.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ArrayOfString }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link ArrayOfString }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "buyerList", scope = ReturnBuyerList.class)
    public JAXBElement<ArrayOfString> createReturnBuyerListBuyerList(ArrayOfString value) {
        return new JAXBElement<ArrayOfString>(_ReturnBuyerListBuyerList_QNAME, ArrayOfString.class, ReturnBuyerList.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "responseCode", scope = ReturnBuyerList.class)
    public JAXBElement<String> createReturnBuyerListResponseCode(String value) {
        return new JAXBElement<String>(_ReturnBuyerListResponseCode_QNAME, String.class, ReturnBuyerList.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "responseMsg", scope = ReturnBuyerList.class)
    public JAXBElement<String> createReturnBuyerListResponseMsg(String value) {
        return new JAXBElement<String>(_ReturnBuyerListResponseMsg_QNAME, String.class, ReturnBuyerList.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ArrayOfReturnElectronice }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link ArrayOfReturnElectronice }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "list", scope = ReturnElectroniceBatchList.class)
    public JAXBElement<ArrayOfReturnElectronice> createReturnElectroniceBatchListList(ArrayOfReturnElectronice value) {
        return new JAXBElement<ArrayOfReturnElectronice>(_ReturnElectroniceBatchListList_QNAME, ArrayOfReturnElectronice.class, ReturnElectroniceBatchList.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "returnCode", scope = ReturnElectroniceBatchList.class)
    public JAXBElement<String> createReturnElectroniceBatchListReturnCode(String value) {
        return new JAXBElement<String>(_ElectroniceStockReturnCode_QNAME, String.class, ReturnElectroniceBatchList.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "returnMsg", scope = ReturnElectroniceBatchList.class)
    public JAXBElement<String> createReturnElectroniceBatchListReturnMsg(String value) {
        return new JAXBElement<String>(_ElectroniceStockReturnMsg_QNAME, String.class, ReturnElectroniceBatchList.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "CZDM", scope = ReturnElectronice.class)
    public JAXBElement<String> createReturnElectroniceCZDM(String value) {
        return new JAXBElement<String>(_ReturnElectroniceCZDM_QNAME, String.class, ReturnElectronice.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "DDH", scope = ReturnElectronice.class)
    public JAXBElement<String> createReturnElectroniceDDH(String value) {
        return new JAXBElement<String>(_ReturnElectroniceDDH_QNAME, String.class, ReturnElectronice.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "EWM", scope = ReturnElectronice.class)
    public JAXBElement<String> createReturnElectroniceEWM(String value) {
        return new JAXBElement<String>(_ReturnElectroniceEWM_QNAME, String.class, ReturnElectronice.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "FPQQLSH", scope = ReturnElectronice.class)
    public JAXBElement<String> createReturnElectroniceFPQQLSH(String value) {
        return new JAXBElement<String>(_ReturnElectroniceFPQQLSH_QNAME, String.class, ReturnElectronice.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "FPZL_DM", scope = ReturnElectronice.class)
    public JAXBElement<String> createReturnElectroniceFPZLDM(String value) {
        return new JAXBElement<String>(_ReturnElectroniceFPZLDM_QNAME, String.class, ReturnElectronice.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "FP_DM", scope = ReturnElectronice.class)
    public JAXBElement<String> createReturnElectroniceFPDM(String value) {
        return new JAXBElement<String>(_ElectroniceBatchQueryFPDM_QNAME, String.class, ReturnElectronice.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "FP_HM", scope = ReturnElectronice.class)
    public JAXBElement<String> createReturnElectroniceFPHM(String value) {
        return new JAXBElement<String>(_ElectroniceBatchQueryFPHM_QNAME, String.class, ReturnElectronice.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "FWM", scope = ReturnElectronice.class)
    public JAXBElement<String> createReturnElectroniceFWM(String value) {
        return new JAXBElement<String>(_ReturnElectroniceFWM_QNAME, String.class, ReturnElectronice.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "JYM", scope = ReturnElectronice.class)
    public JAXBElement<String> createReturnElectroniceJYM(String value) {
        return new JAXBElement<String>(_ReturnElectroniceJYM_QNAME, String.class, ReturnElectronice.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "KPLSH", scope = ReturnElectronice.class)
    public JAXBElement<String> createReturnElectroniceKPLSH(String value) {
        return new JAXBElement<String>(_ReturnElectroniceKPLSH_QNAME, String.class, ReturnElectronice.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "KPLX", scope = ReturnElectronice.class)
    public JAXBElement<String> createReturnElectroniceKPLX(String value) {
        return new JAXBElement<String>(_ReturnElectroniceKPLX_QNAME, String.class, ReturnElectronice.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "KPRQ", scope = ReturnElectronice.class)
    public JAXBElement<String> createReturnElectroniceKPRQ(String value) {
        return new JAXBElement<String>(_ReturnElectroniceKPRQ_QNAME, String.class, ReturnElectronice.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "PDF_FILE", scope = ReturnElectronice.class)
    public JAXBElement<String> createReturnElectronicePDFFILE(String value) {
        return new JAXBElement<String>(_ReturnElectronicePDFFILE_QNAME, String.class, ReturnElectronice.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "PDF_URL", scope = ReturnElectronice.class)
    public JAXBElement<String> createReturnElectronicePDFURL(String value) {
        return new JAXBElement<String>(_ReturnElectronicePDFURL_QNAME, String.class, ReturnElectronice.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "returnCode", scope = ReturnElectronice.class)
    public JAXBElement<String> createReturnElectroniceReturnCode(String value) {
        return new JAXBElement<String>(_ElectroniceStockReturnCode_QNAME, String.class, ReturnElectronice.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "returnMsg", scope = ReturnElectronice.class)
    public JAXBElement<String> createReturnElectroniceReturnMsg(String value) {
        return new JAXBElement<String>(_ElectroniceStockReturnMsg_QNAME, String.class, ReturnElectronice.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "DW", scope = ElectroniceDetail.class)
    public JAXBElement<String> createElectroniceDetailDW(String value) {
        return new JAXBElement<String>(_ElectroniceDetailDW_QNAME, String.class, ElectroniceDetail.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "GGXH", scope = ElectroniceDetail.class)
    public JAXBElement<String> createElectroniceDetailGGXH(String value) {
        return new JAXBElement<String>(_ElectroniceDetailGGXH_QNAME, String.class, ElectroniceDetail.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "HSBZ", scope = ElectroniceDetail.class)
    public JAXBElement<String> createElectroniceDetailHSBZ(String value) {
        return new JAXBElement<String>(_ElectroniceDetailHSBZ_QNAME, String.class, ElectroniceDetail.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "LSLBS", scope = ElectroniceDetail.class)
    public JAXBElement<String> createElectroniceDetailLSLBS(String value) {
        return new JAXBElement<String>(_ElectroniceDetailLSLBS_QNAME, String.class, ElectroniceDetail.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "SL", scope = ElectroniceDetail.class)
    public JAXBElement<String> createElectroniceDetailSL(String value) {
        return new JAXBElement<String>(_ElectroniceDetailSL_QNAME, String.class, ElectroniceDetail.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "SPBM", scope = ElectroniceDetail.class)
    public JAXBElement<String> createElectroniceDetailSPBM(String value) {
        return new JAXBElement<String>(_ElectroniceDetailSPBM_QNAME, String.class, ElectroniceDetail.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "XMBM", scope = ElectroniceDetail.class)
    public JAXBElement<String> createElectroniceDetailXMBM(String value) {
        return new JAXBElement<String>(_ElectroniceDetailXMBM_QNAME, String.class, ElectroniceDetail.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "XMMC", scope = ElectroniceDetail.class)
    public JAXBElement<String> createElectroniceDetailXMMC(String value) {
        return new JAXBElement<String>(_ElectroniceDetailXMMC_QNAME, String.class, ElectroniceDetail.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "YHZCBS", scope = ElectroniceDetail.class)
    public JAXBElement<String> createElectroniceDetailYHZCBS(String value) {
        return new JAXBElement<String>(_ElectroniceDetailYHZCBS_QNAME, String.class, ElectroniceDetail.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "ZXBM", scope = ElectroniceDetail.class)
    public JAXBElement<String> createElectroniceDetailZXBM(String value) {
        return new JAXBElement<String>(_ElectroniceDetailZXBM_QNAME, String.class, ElectroniceDetail.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "ZZSTSGL", scope = ElectroniceDetail.class)
    public JAXBElement<String> createElectroniceDetailZZSTSGL(String value) {
        return new JAXBElement<String>(_ElectroniceDetailZZSTSGL_QNAME, String.class, ElectroniceDetail.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "pcguid", scope = ElectroniceDetail.class)
    public JAXBElement<String> createElectroniceDetailPcguid(String value) {
        return new JAXBElement<String>(_ElectroniceDetailPcguid_QNAME, String.class, ElectroniceDetail.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "BMB_BBH", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoBMBBBH(String value) {
        return new JAXBElement<String>(_ElectroniceInfoBMBBBH_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "BZ", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoBZ(String value) {
        return new JAXBElement<String>(_ElectroniceInfoBZ_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "CHYY", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoCHYY(String value) {
        return new JAXBElement<String>(_ElectroniceInfoCHYY_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "CZDM", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoCZDM(String value) {
        return new JAXBElement<String>(_ReturnElectroniceCZDM_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "DDH", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoDDH(String value) {
        return new JAXBElement<String>(_ReturnElectroniceDDH_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "DKBZ", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoDKBZ(String value) {
        return new JAXBElement<String>(_ElectroniceInfoDKBZ_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "DSPTBM", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoDSPTBM(String value) {
        return new JAXBElement<String>(_ElectroniceInfoDSPTBM_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "EMAIL", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoEMAIL(String value) {
        return new JAXBElement<String>(_ElectroniceInfoEMAIL_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "EWM", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoEWM(String value) {
        return new JAXBElement<String>(_ReturnElectroniceEWM_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "FHR", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoFHR(String value) {
        return new JAXBElement<String>(_ElectroniceInfoFHR_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "FPQQLSH", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoFPQQLSH(String value) {
        return new JAXBElement<String>(_ReturnElectroniceFPQQLSH_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "FPZT", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoFPZT(String value) {
        return new JAXBElement<String>(_ElectroniceInfoFPZT_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "FP_DM", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoFPDM(String value) {
        return new JAXBElement<String>(_ElectroniceBatchQueryFPDM_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "FP_HM", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoFPHM(String value) {
        return new JAXBElement<String>(_ElectroniceBatchQueryFPHM_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "FP_MW", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoFPMW(String value) {
        return new JAXBElement<String>(_ElectroniceInfoFPMW_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "GHFQYLX", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoGHFQYLX(String value) {
        return new JAXBElement<String>(_ElectroniceInfoGHFQYLX_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "GHF_DZ", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoGHFDZ(String value) {
        return new JAXBElement<String>(_ElectroniceInfoGHFDZ_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "GHF_EMAIL", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoGHFEMAIL(String value) {
        return new JAXBElement<String>(_ElectroniceInfoGHFEMAIL_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "GHF_GDDH", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoGHFGDDH(String value) {
        return new JAXBElement<String>(_ElectroniceInfoGHFGDDH_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "GHF_MC", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoGHFMC(String value) {
        return new JAXBElement<String>(_ElectroniceBatchQueryGHFMC_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "GHF_NSRSBH", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoGHFNSRSBH(String value) {
        return new JAXBElement<String>(_ElectroniceBatchQueryGHFNSRSBH_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "GHF_SF", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoGHFSF(String value) {
        return new JAXBElement<String>(_ElectroniceInfoGHFSF_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "GHF_SJ", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoGHFSJ(String value) {
        return new JAXBElement<String>(_ElectroniceInfoGHFSJ_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "GHF_YHZH", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoGHFYHZH(String value) {
        return new JAXBElement<String>(_ElectroniceInfoGHFYHZH_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "HY_DM", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoHYDM(String value) {
        return new JAXBElement<String>(_ElectroniceInfoHYDM_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "HY_MC", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoHYMC(String value) {
        return new JAXBElement<String>(_ElectroniceInfoHYMC_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "JQBH", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoJQBH(String value) {
        return new JAXBElement<String>(_ElectroniceInfoJQBH_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "JYM", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoJYM(String value) {
        return new JAXBElement<String>(_ReturnElectroniceJYM_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "KPLX", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoKPLX(String value) {
        return new JAXBElement<String>(_ReturnElectroniceKPLX_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "KPR", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoKPR(String value) {
        return new JAXBElement<String>(_ElectroniceInfoKPR_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "KPRQ", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoKPRQ(String value) {
        return new JAXBElement<String>(_ReturnElectroniceKPRQ_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "KPXM", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoKPXM(String value) {
        return new JAXBElement<String>(_ElectroniceInfoKPXM_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "KP_NSRMC", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoKPNSRMC(String value) {
        return new JAXBElement<String>(_ElectroniceInfoKPNSRMC_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "KP_NSRSBH", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoKPNSRSBH(String value) {
        return new JAXBElement<String>(_ElectroniceStockKPNSRSBH_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "NSRDZDAH", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoNSRDZDAH(String value) {
        return new JAXBElement<String>(_ElectroniceInfoNSRDZDAH_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "PYDM", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoPYDM(String value) {
        return new JAXBElement<String>(_ElectroniceInfoPYDM_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "SJ", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoSJ(String value) {
        return new JAXBElement<String>(_ElectroniceInfoSJ_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "SKR", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoSKR(String value) {
        return new JAXBElement<String>(_ElectroniceInfoSKR_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "SWJG_DM", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoSWJGDM(String value) {
        return new JAXBElement<String>(_ElectroniceInfoSWJGDM_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "TSCHBZ", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoTSCHBZ(String value) {
        return new JAXBElement<String>(_ElectroniceInfoTSCHBZ_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "TSFS", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoTSFS(String value) {
        return new JAXBElement<String>(_ElectroniceInfoTSFS_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "XHF_DH", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoXHFDH(String value) {
        return new JAXBElement<String>(_ElectroniceInfoXHFDH_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "XHF_DZ", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoXHFDZ(String value) {
        return new JAXBElement<String>(_ElectroniceInfoXHFDZ_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "XHF_MC", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoXHFMC(String value) {
        return new JAXBElement<String>(_ElectroniceInfoXHFMC_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "XHF_NSRSBH", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoXHFNSRSBH(String value) {
        return new JAXBElement<String>(_ElectroniceBatchQueryXHFNSRSBH_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "XHF_YHZH", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoXHFYHZH(String value) {
        return new JAXBElement<String>(_ElectroniceInfoXHFYHZH_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "YFP_DM", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoYFPDM(String value) {
        return new JAXBElement<String>(_ElectroniceInfoYFPDM_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "YFP_HM", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoYFPHM(String value) {
        return new JAXBElement<String>(_ElectroniceInfoYFPHM_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "appId", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoAppId(String value) {
        return new JAXBElement<String>(_ElectroniceInfoAppId_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "authorizationCode", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoAuthorizationCode(String value) {
        return new JAXBElement<String>(_ElectroniceInfoAuthorizationCode_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "codeType", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoCodeType(String value) {
        return new JAXBElement<String>(_ElectroniceInfoCodeType_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "content", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoContent(String value) {
        return new JAXBElement<String>(_ElectroniceInfoContent_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "dataExchangeId", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoDataExchangeId(String value) {
        return new JAXBElement<String>(_ElectroniceInfoDataExchangeId_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ArrayOfElectroniceDetail }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link ArrayOfElectroniceDetail }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "details", scope = ElectroniceInfo.class)
    public JAXBElement<ArrayOfElectroniceDetail> createElectroniceInfoDetails(ArrayOfElectroniceDetail value) {
        return new JAXBElement<ArrayOfElectroniceDetail>(_ElectroniceInfoDetails_QNAME, ArrayOfElectroniceDetail.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "encryptCode", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoEncryptCode(String value) {
        return new JAXBElement<String>(_ElectroniceInfoEncryptCode_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "interfaceCode", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoInterfaceCode(String value) {
        return new JAXBElement<String>(_ElectroniceInfoInterfaceCode_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "passWord", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoPassWord(String value) {
        return new JAXBElement<String>(_ElectroniceInfoPassWord_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "requestCode", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoRequestCode(String value) {
        return new JAXBElement<String>(_ElectroniceInfoRequestCode_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "responseCode", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoResponseCode(String value) {
        return new JAXBElement<String>(_ReturnBuyerListResponseCode_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "terminalCode", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoTerminalCode(String value) {
        return new JAXBElement<String>(_ElectroniceInfoTerminalCode_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "userName", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoUserName(String value) {
        return new JAXBElement<String>(_ElectroniceInfoUserName_QNAME, String.class, ElectroniceInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com", name = "version", scope = ElectroniceInfo.class)
    public JAXBElement<String> createElectroniceInfoVersion(String value) {
        return new JAXBElement<String>(_ElectroniceInfoVersion_QNAME, String.class, ElectroniceInfo.class, value);
    }

}
