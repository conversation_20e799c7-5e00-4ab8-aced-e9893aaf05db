package com.foshan.dao.shop.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.Query;
import org.hibernate.Session;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Repository;

import com.foshan.dao.generic.HibernateDao;
import com.foshan.dao.generic.Page;
import com.foshan.dao.shop.IStorePluginConfigDao;
import com.foshan.entity.shop.StorePluginConfigEntity;


@Repository("storePluginConfigDao")
public class StorePluginConfigDaoImpl extends HibernateDao<StorePluginConfigEntity, Integer>
		implements IStorePluginConfigDao {

	/*
	 * Title: getStorePluginConfigList Description:
	 * 
	 * @param storeId
	 * 
	 * @param pluginId
	 * 
	 * @param type
	 * 
	 * @return
	 * 
	 * @see
	 * com.foshan.dao.shop.StorePluginConfigDao#getStorePluginConfigList(java.lang.
	 * Long, java.lang.Long, java.lang.Integer)
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<StorePluginConfigEntity> getStorePluginConfigList(Integer storeId, Integer pluginId, Integer type) {
		StringBuilder hql = new StringBuilder(
				"Select s from StorePluginConfigEntity s where s.state = 1 and s.plugin.status = 1 and s.plugin.canVisibleForSeller = true");
		if (storeId != null) {
			hql.append(" and s.store.id = " + storeId.intValue());
		} 
		if (pluginId != null) {
			hql.append(" and s.plugin.id = " + pluginId.intValue());
		}
		if (type != null) {
			hql.append(" and s.plugin.pluginType = " + type.intValue());
		}
		Session session = getSession();
		Query query = session.createQuery(hql.toString());
		List<StorePluginConfigEntity> result = (List<StorePluginConfigEntity>) query.list();

		return result;
	}

	/*
	 * Title: getPaymentPluginsByStoreId Description:
	 * 
	 * @param string
	 * 
	 * @param id
	 * 
	 * @see
	 * com.foshan.dao.shop.StorePluginConfigDao#getPaymentPluginsByStoreId(java.lang
	 * .String, java.lang.Integer)
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<StorePluginConfigEntity> getPaymentPluginsByStoreId(int id) {
		StringBuilder hql = new StringBuilder(
				"Select s from StorePluginConfigEntity s where s.state = 1 and s.store.id = " + id);

		Session session = getSession();
		Query query = session.createQuery(hql.toString());
		List<StorePluginConfigEntity> result = (List<StorePluginConfigEntity>) query.list();
		return result;

	}

	/*
	* Title: saveEntity
	*Description: 
	* @param entity 
	* @see com.foshan.dao.shop.StorePluginConfigDao#saveEntity(com.foshan.entity.shop.StorePluginConfigEntity) 
	*/
	@Override
	@CacheEvict(value = "store_payment_plugin",  key="#entity.store.id")
	public void saveEntity(StorePluginConfigEntity entity) {
		save(entity);
	}

	/*
	* Title: updateEntity
	*Description: 
	* @param entity 
	* @see com.foshan.dao.shop.StorePluginConfigDao#updateEntity(com.foshan.entity.shop.StorePluginConfigEntity) 
	*/
	@Override
	@CacheEvict(value = "store_payment_plugin", key="#entity.store.id")
	public void updateEntity(StorePluginConfigEntity entity) {
		update(entity);
	}
	
	/*
	* Title: updateEntity
	*Description: 
	* @param entity 
	* @see com.foshan.dao.shop.StorePluginConfigDao#updateEntity(com.foshan.entity.shop.StorePluginConfigEntity) 
	*/
	@Override
	@CacheEvict(value = "store_payment_plugin", key="#entity.store.id")
	public void deleteEntity(StorePluginConfigEntity entity) {
		delete(entity);
	}

	/*
	* Title: Evict
	*Description: 
	* @param entity 
	* @see com.foshan.dao.shop.StorePluginConfigDao#Evict(com.foshan.entity.shop.StorePluginConfigEntity) 
	*/
	@Override
	public void Evict(StorePluginConfigEntity entity) {
		// TODO Auto-generated method stub
		getSession().evict(entity);
	}

	@Override
	public void getPaymentPlugins(Page<StorePluginConfigEntity> page, Integer storeId, String storeName,Integer pluginId,
			Integer auditStatus) {
		// TODO Auto-generated method stub
		Map<String,Object> params =  new HashMap<String,Object>();
		StringBuilder sbHql =  new StringBuilder("select sp from StorePluginConfigEntity sp inner join sp.store s inner join sp.plugin p where sp.state = 1 and p.status = 1");
		if(storeId != null) {
			sbHql.append(" and sp.store.id = :storeId");
			params.put("storeId", storeId);
		}
		if(StringUtils.isNotBlank(storeName)) {
			sbHql.append(" and sp.store.storeName like :storeName");
			params.put("storeName", "%" + storeName + "%");
		}
		if(pluginId != null) {
			sbHql.append(" and sp.plugin.id = :pluginId");
			params.put("pluginId", pluginId);
		}
		if(auditStatus != null) {
			sbHql.append(" and sp.auditStatus = :auditStatus");
			params.put("auditStatus", auditStatus);
		}
		
		queryPage(page, sbHql.toString(), params);
		
	}

	@Override
	public StorePluginConfigEntity getStorePluginByMachId(String machId, String pluginSn) {
		// TODO Auto-generated method stub
		String hql =  "select sp from StorePluginConfigEntity sp inner join sp.plugin p where sp.state = 1 and p.status =1 and sp.validAttr like ? and p.sn = ?";
	
		return getUniqueByHql(hql, "%"+ machId +"%" , pluginSn);
		
	}

	@Override
	public void getPaymentPlugins(Page<StorePluginConfigEntity> page, Integer storeId, String storeName,
			Integer pluginId, Integer auditStatus, String startRegionCode, String endRegionCode) {
		// TODO Auto-generated method stub
				Map<String,Object> params =  new HashMap<String,Object>();
				StringBuilder sbHql =  new StringBuilder("select sp from StorePluginConfigEntity sp inner join sp.store s inner join sp.plugin p where sp.state = 1 and p.status = 1");
				if(StringUtils.isNotBlank(startRegionCode) && StringUtils.isNotBlank(endRegionCode)) {
					sbHql.append(" and sp.store.region.regionCode >= " + startRegionCode + " and sp.store.region.regionCode <= " + endRegionCode);
				}
				if(storeId != null) {
					sbHql.append(" and sp.store.id = :storeId");
					params.put("storeId", storeId);
				}
				if(StringUtils.isNotBlank(storeName)) {
					sbHql.append(" and sp.store.storeName like :storeName");
					params.put("storeName", "%" + storeName + "%");
				}
				if(pluginId != null) {
					sbHql.append(" and sp.plugin.id = :pluginId");
					params.put("pluginId", pluginId);
				}
				if(auditStatus != null) {
					sbHql.append(" and sp.auditStatus = :auditStatus");
					params.put("auditStatus", auditStatus);
				}
				
				queryPage(page, sbHql.toString(), params);
	}

	

}
