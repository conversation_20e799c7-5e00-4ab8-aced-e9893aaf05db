package com.foshan.entity.shop;

import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_product_order_trace")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class ProductOrderTraceEntity extends Shop {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1346708230725309316L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "int(1) comment '订单轨迹状态 0--创建 1--买家付款完成 2--商家已发货 3--买家已签收 4--订单退款 5--订单已拆分'")
	private Integer orderFlowState;
	@Column(columnDefinition = "varchar(1000) comment '订单轨迹内容'")
	private String orderFlowContent;
	@Column(columnDefinition = "varchar(10) comment '轨迹发起人'")
	private String traceOwner;
	@ManyToOne(targetEntity = ProductOrderEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "orderId", referencedColumnName = "id", nullable = true)
	private ProductOrderEntity productOrder;

	public ProductOrderTraceEntity(Timestamp createTime, Timestamp lastModifyTime, Integer version, Integer state) {
		super(createTime, lastModifyTime, version, state);
		// TODO Auto-generated constructor stub
	}

	public ProductOrderTraceEntity(Integer orderFlowState, String orderFlowContent, String traceOwner,ProductOrderEntity productOrder) {
		super();
		this.orderFlowState = orderFlowState;
		this.orderFlowContent = orderFlowContent;
		this.traceOwner = traceOwner;
		this.productOrder = productOrder;
	}

	
}
