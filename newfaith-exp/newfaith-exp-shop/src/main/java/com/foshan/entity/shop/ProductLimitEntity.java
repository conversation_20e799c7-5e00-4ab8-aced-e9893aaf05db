package com.foshan.entity.shop;

import java.sql.Timestamp;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_product_limit", uniqueConstraints = {
		@UniqueConstraint(columnNames = { "storeId","specificationId", "productId", "limitType", "limitS"
				+ "tate", "state" }) })
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class ProductLimitEntity extends Shop {

	/**
	 * 产品限购策略
	 */
	private static final long serialVersionUID = 2470107909536073860L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "int(11)  comment '产品限购数量'")
	private Integer limitAmount;
	@Column(columnDefinition = "int(11)  comment '产品限购库存'")
	private Integer inventoryAmount;
	@Column(columnDefinition = "Timestamp comment '限购开始时间'")
	private Timestamp startTime;
	@Column(columnDefinition = "Timestamp comment '限购结束时间'")
	private Timestamp endTime;
	@Column(columnDefinition = "int(1) default 0 comment '产品限购策略类型  0：只限制库存；1：只限制购买数量；2：限购时间内限制库存；3：限购时间内限制数量；4：限购时间内限制数量及库存'")
	private Integer limitType;
	@Column(columnDefinition = "int(1) default 1 comment '产品限购策略状态  0：无效   1：有效'")
	private Integer limitState;
	@Column(columnDefinition = "int(1) default 0 comment '产品限购策略有效范围  0：单产品   1：同规格下其它单品  2：同规格下所有产品'")
	private Integer limitScope;
	@Column(columnDefinition = "int(11)  comment '产品的规格Id'")
	private Integer specificationId;
	@Column(columnDefinition = "int(11)  comment '产品所属商铺Id'")
	private Integer storeId;
	@ManyToOne(targetEntity = ProductEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "productId", referencedColumnName = "id", nullable = true)
	private ProductEntity product;

	public ProductLimitEntity(Timestamp createTime, Timestamp lastModifyTime, Integer version, Integer state) {
		super(createTime, lastModifyTime, version, state);
		// TODO Auto-generated constructor stub
	}


}
