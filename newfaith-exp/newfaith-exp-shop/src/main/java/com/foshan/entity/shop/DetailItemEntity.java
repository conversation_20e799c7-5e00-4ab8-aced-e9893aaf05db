package com.foshan.entity.shop;


import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_detail_item")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class DetailItemEntity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1465444874832490185L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
/*	@Column(columnDefinition = "int(1)  comment '发票行性质：0－正常行；1－折扣行；2－被折扣行'")
	private Integer fphxz;*/
	@Column(columnDefinition = "varchar(20)  comment '商品分类编码'")
	//private String spbm;
	private String commodityClassificationCode;
	@Column(columnDefinition = "varchar(20)  comment '项目编码'")
	//private String xmbm;
	private String projectCode;
	@Column(columnDefinition = "varchar(20)  comment '自行编码'")
	//private String zxbm;
	private String selfCoding;
	@Column(columnDefinition = "int(1)  comment '优惠政策标识:0-不使用；1－使用'")
	//private String yhzcbs;
	private String preferentialPolicyLabel;
	@Column(columnDefinition = "int(1)  comment '零税率标识:0：出口零税，1-免税；2－不征收；3－普通零税率'")
	//private Integer lslbs;
	private String zeroTariffMark;
	@Column(columnDefinition = "varchar(500)  comment '增值税特殊管理'")
	//private String zzstsgl;
	private String vatSpecial;
	@Column(columnDefinition = "varchar(90)  comment '项目名称'")
	//private String xmmc;
	private String projectName;
	@Column(columnDefinition = "varchar(20)  comment '计量单位'")
	//private String dw;
	private String projectUnit;
	@Column(columnDefinition = "varchar(256)  comment '规格型号'")
	//private String ggxh;
	private String specifications;
	@Column(columnDefinition = "varchar(20)  comment '项目数量'")
	//private String xmsl;
	private String projectNumber;
	@Column(columnDefinition = "varchar(1)  comment '含税标志'")
	//private String hsbz;
	private String taxInclusive;
	@Column(columnDefinition = "varchar(20)  comment '项目单价'")
	//private String xmdj;
	private String unitPrice;
	@Column(columnDefinition = "varchar(12)  comment '项目金额'")
	//private String xmje;
	private String projectAmount;
	@Column(columnDefinition = "varchar(20)  comment '税率'")
	//private String sl;
	private String taxRate;
	@Column(columnDefinition = "varchar(12)  comment '税额'")
	//private String se;
	private String amountOfTax;
	@Column(columnDefinition = "varchar(20)  comment '扣除额'")
	//private String kce;
	private String deduction;
/*	@Column(columnDefinition = "varchar(200)  comment '备用字段1'")
	private String by1;
	@Column(columnDefinition = "varchar(20)  comment '备用字段2'")
	private String by2;
	@Column(columnDefinition = "varchar(20)  comment '备用字段3'")
	private String by3;*/
	@Column(columnDefinition = "varchar(50)  comment '订单项ID'")
	private Integer orderItemId;
	
	@ManyToOne(targetEntity = InvoiceEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "invoiceId", referencedColumnName = "id", nullable = true)
	private InvoiceEntity invoice;
	
	
	
}
