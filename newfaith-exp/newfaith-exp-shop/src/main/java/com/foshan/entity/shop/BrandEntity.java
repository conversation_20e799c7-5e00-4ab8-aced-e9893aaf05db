package com.foshan.entity.shop;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AssetEntity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_brand")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class BrandEntity extends Shop {

	/**
	 * 品牌
	 */
	private static final long serialVersionUID = -7360631519357213804L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer Id;
	@Column(columnDefinition = "int(11) comment '排序值'")
	private Integer orders;
	@Column(columnDefinition = "varchar(5000) comment '品牌说明'")
	private String introduction;
//	@Column(columnDefinition = "int(11) comment 'logo图片地址'")
//	private String logoId;
	@Column(columnDefinition = "varchar(30) comment '品牌名称'")
	private String brandName;
	@Column(columnDefinition = "varchar(64) comment '名称首字母缩略'")
	private String brandAcronym;
	@Column(columnDefinition = "varchar(100) comment '关键字'")
	private String brandKeywords;
	@Column(columnDefinition = "int(1) comment '品牌类别'")
	private Integer brandType;
	@Column(columnDefinition = "varchar(200) comment '品牌网址'")
	private String brandURL;
	@ManyToOne(targetEntity = AssetEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "logoId", referencedColumnName = "id", nullable = true)
	private AssetEntity logo;
	@OneToMany(targetEntity = ProductSpecificationEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "brandId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<ProductSpecificationEntity> productSpecificationList = new ArrayList<ProductSpecificationEntity>();


	public BrandEntity(Timestamp createTime, Timestamp lastModifyTime, Integer version, Integer state) {
		super(createTime, lastModifyTime, version, state);
		// TODO Auto-generated constructor stub
	}

	public BrandEntity(Integer orders, String introduction, AssetEntity logo, String brandName, String brandAcronym,
			String brandKeywords, Integer brandType, String brandURL) {
		super();
		this.orders = orders;
		this.introduction = introduction;
		this.logo = logo;
		this.brandName = brandName;
		this.brandAcronym = brandAcronym;
		this.brandKeywords = brandKeywords;
		this.brandType = brandType;
		this.brandURL = brandURL;
	}

	
}
