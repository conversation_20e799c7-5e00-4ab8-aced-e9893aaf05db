package com.foshan.entity.shop;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_member_invoice_info")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class MemberInvoiceInfoEntity extends Shop {

	/**
	 * 会员地址
	 */
	private static final long serialVersionUID = 4149149474405968042L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '开单位发票时，单位名称'")
	private String companyName;
	@Column(columnDefinition = "varchar(64) comment '开单位发票时，单位的纳税识别号'")
	private String paytaxNo;
	@Column(columnDefinition = "varchar(254) comment '注册地址电话'")
	private String registerPhone;
	@Column(columnDefinition = "varchar(64) comment '注册地址'")
	private String registerAddress;
	@Column(columnDefinition = "varchar(64) comment '开户银行'")
	private String bankName;
	@Column(columnDefinition = "varchar(64) comment '银行帐号'")
	private String bankAccount;
	@Column(columnDefinition = "int(1) default 0 comment '发票抬头 0-个人 1--单位'")
	private Integer invoiceTitle;
	@Column(columnDefinition = "int(1) default 0 comment '发票类型 0-普通发票 1--增值税专用发票'")
	private Integer invoiceType;
	@Column(columnDefinition = "int(1) comment '是否默认地址 0--否 1--是'")
	private Integer isDefault;
	@ManyToOne(targetEntity = MemberEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
	private MemberEntity member;

	public MemberInvoiceInfoEntity(String companyName, String paytaxNo, String registerPhone, String registerAddress,
			String bankName,String bankAccount,Integer invoiceTitle,Integer invoiceType,MemberEntity member,Integer isDefault) {
		super();
		this.companyName = companyName;
		this.paytaxNo = paytaxNo;
		this.registerPhone = registerPhone;
		this.isDefault = isDefault;
		this.registerAddress =registerAddress;
		this.bankName = bankName;
		this.bankAccount = bankAccount;
		this.invoiceType = invoiceType;
		this.invoiceTitle = invoiceTitle;
		this.member = member;
	}
	

	

}
