package com.foshan.entity.shop;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AddressEntity;
import com.foshan.entity.RegionEntity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_pickup_point")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class PickupPointEntity extends Shop {

	/**
	 * 自提点
	 */
	private static final long serialVersionUID = 7526985085577348953L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(100) comment '自提点名称'")
	private String pointName;
	@Column(columnDefinition = "varchar(200) comment '自提点地址'")
	private String pointAddress;
	@Column(columnDefinition = "varchar(100) comment '自提点联系电话'")
	private String pointPhone;
	@Column(columnDefinition = "varchar(100) comment '自提点服务时间'")
	private String pointServiceTime;
	@Column(columnDefinition = "varchar(512) comment '自提点服务说明'")
	private String pointServiceMemo;
	@Column(columnDefinition = "varchar(50) comment '自提点定位'")
	private String pointLocation;
	@Column(columnDefinition = "varchar(100) comment '自提点地图截图路径'")
	private String pointMapImage;
	@ManyToMany(targetEntity = StoreEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_store_pickup", joinColumns = @JoinColumn(name = "pickId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "storeId", referencedColumnName = "id"))
	@JsonIgnore
	private List<StoreEntity> storeList = new ArrayList<StoreEntity>();
	@ManyToOne(targetEntity = AddressEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "addressId", referencedColumnName = "id", nullable = true)
	private AddressEntity address;
	@ManyToOne(targetEntity = RegionEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "regionId", referencedColumnName = "id", nullable = true)
	private RegionEntity region;


	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PickupPointEntity other = (PickupPointEntity) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}


	

}
