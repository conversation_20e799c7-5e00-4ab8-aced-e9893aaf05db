package com.foshan.entity.shop;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;


//import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.shop.promotion.IPromotionContext;
import com.foshan.shop.promotion.PromotionType;
import com.foshan.shop.promotion.StrategyValue;
import com.foshan.util.ShopContextInfo;
import com.foshan.util.SpringHandler;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 促销活动
 */
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_shop_promotion")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class PromotionEntity extends Shop implements Comparable<PromotionEntity>, IPromotionContext {

	private static final long serialVersionUID = -7156592006948655606L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	/**
	 * 活动的名称
	 */
	@Column(columnDefinition = "varchar(128)  comment '名称'")
	private String name;
	/**
	 * 活动的标题
	 */
	@Column(columnDefinition = "varchar(128)  comment '标题'")
	private String title;
	/**
	 * 活动的图片
	 */
	@Column(columnDefinition = "varchar(255)  comment '图片'")
	private String image;
	/**
	 * 活动的详情
	 */
	@Column(columnDefinition = "varchar(3000)  comment '详情'")
	private String detail;
	/**
	 * 活动地址
	 */
	@Column(columnDefinition = "varchar(255)  comment '活动地址'")
	private String url;
	/**
	 * 起始日期
	 */
	@Column(columnDefinition = "Timestamp  comment '开始时间'")
	private Timestamp startTime;

	/**
	 * 结束日期
	 */
	@Column(columnDefinition = "Timestamp  comment '结束日期'")
	private Timestamp endTime;

	/**
	 * 促销类型
	 */
	@Column(columnDefinition = "int(8) comment '促销类型:1 满额减，2 满额折，3 满额包邮，4 满数量包邮，5 N元任拣'")
	private PromotionType promotionType;

	/**
	 * 优先级
	 */
	@Column(columnDefinition = "int(8) default 0 comment '优先级，当订单满足多个促销优惠时，购物车优先选择优先级值大的优惠'")
	private Integer priority;

	/**
	 * 促销策略json字符串
	 */
	@Column(columnDefinition = "varchar(512) comment '促销策略json字符串'")
	private String strategyValue;

	/**
	 * 是否包邮
	 */
	@Column(columnDefinition = "bit(1) default false comment '是否包邮，true参加促销活动的商品免邮费， false参加促销的商品不包邮'")
	private Boolean isFreeShipping;

	/**
	 * 生效状态：0未生效，1生效，2关闭
	 */
	@Column(columnDefinition = "int(8) default 0 comment '生效状态：0未生效，1生效，2关闭'")
	private Integer promotionStatus;
	/**
	 * 活动关联的产品
	 */

	@JsonIgnore
	@ManyToMany(targetEntity = ProductEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_product_promotion", joinColumns = @JoinColumn(name = "promotionId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "productId", referencedColumnName = "id"))
	private List<ProductEntity> productList = new ArrayList<ProductEntity>();

	/**
	 * 活动的商铺
	 */
	@JsonIgnore
	@ManyToOne(targetEntity = StoreEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "storeId", referencedColumnName = "id", nullable = true)
	private StoreEntity store;

	/**
	 * 获取促销活动优先级，当订单出多个促销优惠时，优先计算优先级值大的优惠，预留字段
	 * 
	 * @return 优先级值
	 */
	public Integer getPriority() {
		return priority;
	}

	/**
	 * 设置促销活动优先级，当订单出多个促销优惠时，优先计算优先级值大的优惠，预留字段
	 * 
	 * @return
	 */
	public void setPriority(Integer priority) {
		this.priority = priority;
	}

	/**
	 * 生效状态：0未生效，1生效，2关闭
	 */
	public Integer getPromotionStatus() {
		return promotionStatus;
	}

	/**
	 * 生效状态：0未生效，1生效，2关闭
	 */
	public void setPromotionStatus(Integer status) {
		this.promotionStatus = status;
	}

	/**
	 * 判断是否已开始
	 * 
	 * @return 是否已开始
	 */
	public boolean isBegun() {
		return getStartTime() == null || !getStartTime().after(new Date());
	}

	/**
	 * 判断是否已结束
	 * 
	 * @return 是否已结束
	 */
	public boolean isEnded() {
		return getEndTime() != null && !getEndTime().after(new Date());
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (!(obj instanceof PromotionEntity))
			return false;
		PromotionEntity other = (PromotionEntity) obj;
		if (id == null) {
			if (other.getId() != null)
				return false;
		} else if (!id.equals(other.getId()))
			return false;
		return true;
	}

	@Override
	public int compareTo(PromotionEntity other) { // 使用限制值比较
		Integer otherPriority = Integer.valueOf(0);
		Integer thisPriority = priority != null ? priority : Integer.valueOf(0);

		if (other != null && other.getPriority() != null) {
			otherPriority = other.getPriority();
		}
		if (otherPriority.intValue() == thisPriority.intValue()) {
			return other.getId().compareTo(id);
		} else {
			return otherPriority.compareTo(thisPriority); // 倒序
		}
	}

	@Override
	public String toString() {
		return "{\"id\":" + id + ",\"name\":\"" + name + "\"}";
	}

	/**
	 * 
	 *  商品详情中优惠提示 
	 * @return String  如：满100元减5元,满200元减20元 
	 */
	public String getTips() {
		List<StrategyValue> promotionStrategyValueList = getPromotionStrategyValueList();
		if (promotionStrategyValueList.size() > 0) {
			ShopContextInfo shopContextInfo = (ShopContextInfo) SpringHandler.getBean("shopContextInfo");
			ArrayList<String> tags = new ArrayList<String>();
			for (StrategyValue strategyValue : promotionStrategyValueList) {
				switch (promotionType.toString()) {
					case "reduceByAmount":
						if (strategyValue.getLimitValue().compareTo(BigDecimal.ZERO) == 0) {
							tags.add("直減" + strategyValue.getBenefitValue() + "元");
						} else {
							tags.add("满" + strategyValue.getLimitValue() + "元減" + strategyValue.getBenefitValue() + "元");
						}
						break;
					case "discountByAmount":
						if (strategyValue.getLimitValue().compareTo(BigDecimal.ZERO) == 0) {
							tags.add("打" + formatDiscountString(strategyValue.getBenefitValue()) + "折");
						} else {
							tags.add("满" + strategyValue.getLimitValue() + "元打"
									+ formatDiscountString(strategyValue.getBenefitValue()) + "折");
						}
						break;
					case "freeShippingByAmount":
						tags.add(strategyValue.getLimitValue().compareTo(BigDecimal.ZERO) == 0 ? "包邮"
								: ("满" + strategyValue.getLimitValue() + "元包邮"));
						break;
					case "reduceByQuantity":
	
						tags.add((strategyValue.getLimitValue().compareTo(BigDecimal.ZERO) == 0 ? ""
								: ("满" + strategyValue.getLimitValue() + "件")) + "减" + strategyValue.getBenefitValue()
								+ "元");
						break;
					case "discountByQuantity":
						tags.add((strategyValue.getLimitValue().compareTo(BigDecimal.ZERO) == 0 ? ""
								: ("满" + strategyValue.getLimitValue() + "件")) + "打"
								+ formatDiscountString(strategyValue.getBenefitValue()) + "折");
						break;
					case "freeShippingByQuantity":
						tags.add(strategyValue.getLimitValue().compareTo(BigDecimal.ZERO) == 0 ? "包邮"
								: ("满" + strategyValue.getLimitValue() + "件包邮"));
						break;
					case "chooseQuantityByFixdPrice":
						tags.add(shopContextInfo.setScale(strategyValue.getBenefitValue()).toString() + "元任拣"
								+ strategyValue.getLimitValue() + "件");
						break;
				}
			}
			return join(tags, ",");
		}
		return null;
	}

	/**
	 * 获取优惠策略值的集合
	 * 
	 * @return 返回优惠策略值的集合
	 */
	@SuppressWarnings("unchecked")
	public List<StrategyValue> getPromotionStrategyValueList() {

		ArrayList<StrategyValue> strategyValueResultList = new ArrayList<StrategyValue>();
		ObjectMapper mapper = new ObjectMapper();
		// 此处转换出来的结果并不是ArrayList<StrategyValue>, 暂时还未想到好的办法，后续继续优化
		ArrayList<StrategyValue> strategyValueList=null;
		try {
			strategyValueList = mapper.readValue(this.strategyValue, new ArrayList<StrategyValue>().getClass());
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		mapper.setSerializationInclusion(Include.NON_NULL); 
		for (int i = 0; i < strategyValueList.size(); i++) {
			StrategyValue strategyValue = null;
			try {
				strategyValue = mapper.readValue(mapper.writeValueAsString(strategyValueList.get(i)),StrategyValue.class);
			} catch (IOException e) {
				e.printStackTrace();
			}
			strategyValueResultList.add(strategyValue);
		}

		return strategyValueResultList;
	}

	/**
	 *  打折提示语，如打8.00折，8.0折转换成打8折
	 * @param discount
	 * @return 8.0折转换成打8折
	 */
	private static String formatDiscountString(BigDecimal discount) {
		BigDecimal hun = new BigDecimal(100);
		if (discount != null) {
			BigDecimal b3 = hun.multiply(discount);
			if (b3 != null) {
				String result = "";
				String s1 = b3.setScale(0, RoundingMode.CEILING) + "";
				String s2 = s1.substring(s1.length() - 1, s1.length());
				if ("0".equals(s2)) {
					result = s1.substring(0, s1.length() - 1);
				} else {
					result = s1;
				}
				return result;
			}
		}
		return null;
	}

	public static String join(Collection<String> coll, String split) {
		if (coll.isEmpty()) return "";

		StringBuilder sb = new StringBuilder();
		boolean isFirst = true;
		for (String s : coll) {
			if (isFirst) isFirst = false;
			else sb.append(split);
			sb.append(s);
		}
		return sb.toString();
	}
}
