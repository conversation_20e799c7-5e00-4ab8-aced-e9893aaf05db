package com.foshan.entity.shop;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AccountEntity;
import com.foshan.entity.RoleEntity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@DiscriminatorValue("M")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class MemberEntity extends AccountEntity {

	/**
	 * 会员
	 */
	private static final long serialVersionUID = 7020361743841368852L;

	@OneToMany(targetEntity = MemberAddressEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<MemberAddressEntity> addressList = new ArrayList<MemberAddressEntity>();
	@OneToOne(targetEntity = CartEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "cartId", referencedColumnName = "id", nullable = true)
	private CartEntity cart;
	@OneToMany(targetEntity = ProductOrderEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<ProductOrderEntity> productOrderList = new ArrayList<ProductOrderEntity>();
	@ManyToMany(targetEntity = RoleEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_member_role", joinColumns = @JoinColumn(name = "memberId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "roleId", referencedColumnName = "id"))
	private List<RoleEntity> roleList = new ArrayList<RoleEntity>();
	
	@ManyToMany(targetEntity = ProductEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_product_favorite", joinColumns = @JoinColumn(name = "memberId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "productId", referencedColumnName = "id"))
	private List<ProductEntity> productList = new ArrayList<ProductEntity>();
	
	@OneToMany(targetEntity = CouponEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CouponEntity> couponList = new ArrayList<CouponEntity>();

	@ManyToOne(targetEntity = PurchasingCompanyEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "commpayId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private PurchasingCompanyEntity purchasingCompany;

	@Column(columnDefinition = "varchar(20)  comment 'Boss客户ID'")
	private String customId;

	@Column(columnDefinition = "varchar(10)  comment 'Boss客户地区ID'")
	private String areaId;

	@Column(columnDefinition = "varchar(200)  comment '绑定BOSS发展人信息'")
	private String boundDeveloper;

	@Column(columnDefinition = "Timestamp  comment '绑定BOSS发展人时间'")
	private Timestamp boundTime;

//	public MemberEntity() {
//		super();
//		// TODO Auto-generated constructor stub
//	}

	public MemberEntity(String userCode, String registName, String loginName, String nickName, String password,
			Integer sex, String birthday, String homePhone, String officePhone, String mobile, Integer phoneVerifyState,
			String mail, Integer mailVerifyState, String qq, String weixin, Integer state, String photo,
			String headImage, Timestamp registTime) {
		super(userCode, registName, loginName, nickName, password, sex, birthday, homePhone, officePhone, mobile,
				phoneVerifyState, mail, mailVerifyState, qq, weixin, state, photo, headImage, registTime);
		// TODO Auto-generated constructor stub
	}

	public MemberEntity(List<MemberAddressEntity> addressList, CartEntity cart,
			List<ProductOrderEntity> productOrderList) {
		super();
		this.addressList = addressList;
		this.cart = cart;
		this.productOrderList = productOrderList;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		MemberEntity other = (MemberEntity) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}


}
