package com.foshan.entity.shop;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.PluginEntity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** 
* @ClassName: PluginConfig 
* @Description: TODO(商户支付插件管理配置) 
* <AUTHOR>
* @date 2019年3月11日 下午3:03:38 
*  
*/
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_shop_store_plugin",indexes={@Index(name="storeId_pluginId_index",columnList="storeId,pluginId")})
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class StorePluginConfigEntity extends Shop {
	

	private static final long serialVersionUID = -5324501796381562823L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private int id;
	@Column(columnDefinition = "bit(1) comment '是否为平台为商家指定配置'",nullable = false)
	private Boolean isAppoint;
	@Column(columnDefinition = "int(1) comment '审核状态：0未提交审核、1、审核中、2审核通过、3审核不通过 '",nullable = false)
	private int auditStatus;
	@Column(columnDefinition = "varchar(3000) comment '审核历史记录'")
	private String auditHistory;
	@Column(columnDefinition = "varchar(3000) comment '未审核通过的插件特征属性值的json字符串'")
	private String attr;
	@Column(columnDefinition = "varchar(3000) comment '已经审核通过的插件特征属性值的json字符串'")
	private String validAttr;
	@ManyToOne(targetEntity = StoreEntity.class)
	@JoinColumn(name = "storeId", referencedColumnName = "id", nullable = true, updatable = false)
	private StoreEntity store;
	@ManyToOne(targetEntity = PluginEntity.class)
	@JoinColumn(name = "pluginId", referencedColumnName = "id", nullable = true, updatable = false)
	private PluginEntity plugin;
	
	
	public StorePluginConfigEntity(int id, Boolean isAppoint, int auditStatus, String auditHistory, String attr,
			StoreEntity store, PluginEntity plugin) {
		super();
		this.id = id;
		this.isAppoint = isAppoint;
		this.auditStatus = auditStatus;
		this.auditHistory = auditHistory;
		this.attr = attr;
	
		this.store = store;
		this.plugin = plugin;
	}

	public Boolean isAppoint() {
		return isAppoint;
	}	
	public void setAppoint(Boolean isAppoint) {
		this.isAppoint = isAppoint;
	}
}
