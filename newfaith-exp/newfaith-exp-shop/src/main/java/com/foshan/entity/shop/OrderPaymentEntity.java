package com.foshan.entity.shop;

import java.math.BigDecimal;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 
* @ClassName: OrderPaymentEntity 
* @Description: TODO(订单支付类) 
* <AUTHOR>
* @date 2019年3月13日 下午4:24:22 
*
 */
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_shop_order_payment",uniqueConstraints ={@UniqueConstraint(columnNames="sn")})
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class OrderPaymentEntity extends Shop {

	private static final long serialVersionUID = -8277270744125256249L;

	/**
	 * 方式
	 */
	public enum Method {

		/**
		 * 在线支付
		 */
		online,
		/**
		 * 货到付款
		 */
		offline,

		/**
		 * 积分付款
		 */
		points

	}
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private int id;

	/**
	 * 编号
	 */
	@Column(columnDefinition = "varchar(32) comment '订单支付编号'",nullable = false, updatable = false)
	private String sn;

	/**
	 * 方式
	 */
	@Column(columnDefinition = "int(8) comment '订单支付方式'",nullable = false, updatable = false)
	private OrderPaymentEntity.Method method;

	/**
	 *  支付插件SN
	 */
	@Column(columnDefinition = "varchar(255) comment '支付插件SN'", nullable = true, updatable = true)
	private String paymentPlunginSn;
	
	/**
	 * 第三方支付系统支付订单号,支付成功后支付平台回调通知报文中含该值
	 */
	@Column(columnDefinition = "varchar(64) comment '支付系统支付订单号'",nullable = true, updatable = true)
	private String outTradeNo;
	
	/**
	 * 订单线上支付对应的支付会话SN
	 */
	@Column(columnDefinition = "varchar(64) comment '订单支付对应的SN'",nullable = true, updatable = true)
	private String paymentSessionSn;
	
	/**
	 * 支付方式
	 */
	@Column(columnDefinition = "varchar(200) comment '订单支付方式'", updatable = false)
	private String paymentMethod;

	/**
	 * 收款银行
	 */
	@Column(columnDefinition = "varchar(200) comment '收款银行'", updatable = false)
	private String bank;

	/**
	 * 收款账号
	 */
	@Column(columnDefinition = "varchar(200) comment '收款账号'", updatable = false)
	private String account;

	/**
	 * 付款金额
	 */
	@Column(columnDefinition = "decimal(10,2) comment '付款金额'",nullable = false, updatable = false)
	private BigDecimal amount;

	/**
	 * 支付手续费
	 */
	@Column(columnDefinition = "decimal(10,2) comment '支付手续费'",nullable = false, updatable = false)
	private BigDecimal fee;

	/**
	 * 付款人
	 */
	@Column(columnDefinition = "varchar(200) comment '付款人'", updatable = false)
	private String payer;

	/**
	 * 备注
	 */
	@Column(columnDefinition = "varchar(200) comment '备注'", updatable = false)
	private String memo;

	/**
	 * 订单
	 */
	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "orderId", nullable = false, updatable = false)
	private ProductOrderEntity order;
	
	/**
	 * 父支付记录
	 */
	@ManyToOne(targetEntity = OrderPaymentEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentPaymentId", referencedColumnName = "id", nullable = true)
	private OrderPaymentEntity parentOrderPayment;
	
	

	
	/**
	 * 设置方式
	 * 
	 * @param method
	 *            方式
	 */
	public void setMethod(OrderPaymentEntity.Method method) {
		this.method = method;
	}

	
	/**
	 * 获取付款金额
	 * 
	 * @return 付款金额
	 */
	public BigDecimal getAmount() {
		return amount == null ? BigDecimal.ZERO : amount;
	}

	/**
	 * 设置付款金额
	 * 
	 * @param amount
	 *            付款金额
	 */
	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	/**
	 * 获取支付手续费
	 * 
	 * @return 支付手续费
	 */
	public BigDecimal getFee() {
		return fee == null ? BigDecimal.ZERO : fee;
	}


	/**
	 * 获取有效金额
	 * 
	 * @return 有效金额
	 */
	@Transient
	public BigDecimal getEffectiveAmount() {
		
		BigDecimal effectiveAmount = getAmount().subtract(getFee());
		return effectiveAmount.compareTo(BigDecimal.ZERO) >= 0 ? effectiveAmount : BigDecimal.ZERO;
	}
	

}