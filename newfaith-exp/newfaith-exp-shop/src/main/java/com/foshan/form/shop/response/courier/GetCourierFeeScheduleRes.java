package com.foshan.form.shop.response.courier;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.shop.CourierFeeScheduleForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取运费模板返回对象(GetCourierFeeScheduleRes)")
@JsonInclude(Include.NON_NULL)
public class GetCourierFeeScheduleRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 9070516987597677303L;
	@ApiModelProperty(value = "运费模板返回对象")
	private  CourierFeeScheduleForm  courierFeeScheduleForm;

	public GetCourierFeeScheduleRes(CourierFeeScheduleForm courierFeeScheduleForm) {
		super();
		this.courierFeeScheduleForm = courierFeeScheduleForm;
	}
	
}
