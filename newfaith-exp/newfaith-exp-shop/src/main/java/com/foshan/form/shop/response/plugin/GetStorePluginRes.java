package com.foshan.form.shop.response.plugin;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.shop.PickupForm;
import com.foshan.form.shop.StorePaymentPluginListForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** 
* @ClassName: package-info 
* @Description: TODO(获取支付信息的返回报文) 
* <AUTHOR>
* @date 2019年3月12日 下午10:42:25 
*  
*/
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class GetStorePluginRes extends BaseResponse {
	
	private static final long serialVersionUID = 7766600875090845238L;
	private StorePaymentPluginListForm StorePaymentPlugin;
	
	
	public GetStorePluginRes(StorePaymentPluginListForm storePaymentPlugin) {
		super();
		StorePaymentPlugin = storePaymentPlugin;
	}

	public GetStorePluginRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}
	
	
	
}
