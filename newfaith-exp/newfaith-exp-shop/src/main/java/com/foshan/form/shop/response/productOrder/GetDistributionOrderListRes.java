package com.foshan.form.shop.response.productOrder;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BasePageResponse;
import com.foshan.form.shop.ProductOrderForm;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class GetDistributionOrderListRes extends BasePageResponse{

	/**
	 * 
	 */
	private static final long serialVersionUID = -4815473236508637856L;
	private List<ProductOrderForm> productOrderList = new ArrayList<ProductOrderForm>();
	private BigDecimal paidAmount = BigDecimal.ZERO;
	private BigDecimal notPayAmount = BigDecimal.ZERO;
	private BigDecimal refundAmount = BigDecimal.ZERO;
	private BigDecimal totalAmount = BigDecimal.ZERO;
	public GetDistributionOrderListRes(int currentPage, int pageSize, int total, int totalResult) {
		super(currentPage, pageSize, total, totalResult);
		// TODO Auto-generated constructor stub
	}
	public GetDistributionOrderListRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}
	public GetDistributionOrderListRes(List<ProductOrderForm> productOrderList) {
		super();
		this.productOrderList = productOrderList;
	}
	
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	
	
}
