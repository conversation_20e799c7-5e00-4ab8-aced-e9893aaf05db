package com.foshan.form.shop;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "产品价格对象(ProductPriceForm)")
@JsonInclude(Include.NON_NULL)
public class ProductPriceForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7381776961945520241L;
	@ApiModelProperty(value = "价格Id", example = "1")
	private Integer priceId;
	@ApiModelProperty(value = "产品Id", example = "1")
	private Integer productId;
	@ApiModelProperty(value = "栏目Id", example = "1")
	private Integer columnId;

	@ApiModelProperty(value = "积分池Id", example = "1")
	private Integer pointsPoolId;
	@ApiModelProperty(value = "规格Id", example = "1")
	private Integer specificationId;
	@ApiModelProperty(value = "sku属性索引")
	private String specificationIndex;
	@ApiModelProperty(value = "基础价格", example = "1")
	private BigDecimal productPrice;
	@ApiModelProperty(value = "销售价格", example = "1")
	private BigDecimal salesPrice;
	@ApiModelProperty(value = "活动价格", example = "1")
	private BigDecimal promotionPrice;
	@ApiModelProperty(value = "价格生效时间",example="2019-01-01 00:00:00")
	private String enableTime;
	@ApiModelProperty(value = "价格失效时间",example="2019-01-01 00:00:00")
	private String disableTime;
	@ApiModelProperty(value = "价格类别 0-普通销售价格 1--活动价格", example = "1")
	private Integer priceType;
	@ApiModelProperty(value = "价格单位")
	private String priceUnit;
	@ApiModelProperty(value = "是否为当前有效价格")
	private Boolean isVailPrice;
	@ApiModelProperty(value = "价格状态 0--无效 1--有效", example = "1")
	private Integer state;
	@ApiModelProperty(value = "企业商品基础销售价格", example = "1")
	private BigDecimal enterpriseProductPrice;
	@ApiModelProperty(value = "是否默认,0非默认价格，1为默认价格")
	private Boolean isDefault;
	
	public ProductPriceForm(Integer priceId, Integer productId, Integer columnId, Integer specificationId,
			String specificationIndex, BigDecimal productPrice, BigDecimal salesPrice, BigDecimal promotionPrice,
			String enableTime, String disableTime, Integer priceType, String priceUnit, Boolean isDefault , Boolean isVailPrice,Integer state) {
		super();
		this.priceId = priceId;
		this.productId = productId;
		this.columnId = columnId;
		this.specificationId = specificationId;
		this.specificationIndex = specificationIndex;
		this.productPrice = productPrice;
		this.salesPrice = salesPrice;
		this.promotionPrice = promotionPrice;
		this.enableTime = enableTime;
		this.disableTime = disableTime;
		this.priceType = priceType;
		this.priceUnit = priceUnit;
		this.state = state;
		this.isDefault = isDefault;
		this.isVailPrice = isVailPrice;
	}


	@Override
	public String toString() {
		return "ProductPriceForm [priceId=" + priceId + ", productId=" + productId + ", columnId=" + columnId
				+ ", specificationId=" + specificationId + ", specificationIndex=" + specificationIndex
				+ ", productPrice=" + productPrice + ", salesPrice=" + salesPrice + ", promotionPrice=" + promotionPrice
				+ ", enableTime=" + enableTime + ", disableTime=" + disableTime + ", priceType=" + priceType
				+ ", priceUnit=" + priceUnit + ", isVailPrice=" + isVailPrice + ", state=" + state
				+ ", enterpriseProductPrice=" + enterpriseProductPrice + ", isDefault=" + isDefault + "]";
	}

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
