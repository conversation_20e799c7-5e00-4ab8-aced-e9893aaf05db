package com.foshan.form.shop;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.AssetForm;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
@ApiModel(value = "合同对象(CouponForm)")
public class ContractForm implements IForm {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -9035097118332004560L;
	@ApiModelProperty(value = "Integer comment '合同Id'", example = "1")
	private Integer contractId;
	@ApiModelProperty(value = "合同名称")
	private String contractName;
	@ApiModelProperty(value = "合同编码号")
	private String contractCode;
	@ApiModelProperty(value = "合同有效期的开始时间")
	private String startTime;
	@ApiModelProperty(value = "合同有效期的结束时间")
	private String endTime;
	@ApiModelProperty(value = "合同额度")
	private String contractAccount;
	@ApiModelProperty(value = "剩余额度")
	private String overplusLimit;
	@ApiModelProperty(value = "消费额度的方式: 0 恢复额度、1 多次扣除额度不恢复", example = "1")
	private Integer consumeType;
	@ApiModelProperty(value = "附件", example = "1")
	private AssetForm enclosure;
	@ApiModelProperty(value = "公司")
	private PurchasingCompanyForm purchasingCompanyForm;

	public ContractForm(Integer contractId, String contractName,String contractCode,String startTime,String endTime,String contractAccount,String overplusLimit,Integer consumeType) {
		this.contractId = contractId; 
		this.contractName = contractName;
		this.contractCode = contractCode;
		this.startTime = startTime;
		this.endTime = endTime;
		this.contractAccount = contractAccount;
		this.overplusLimit = overplusLimit;
		this.consumeType = consumeType;
	}
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}


}
