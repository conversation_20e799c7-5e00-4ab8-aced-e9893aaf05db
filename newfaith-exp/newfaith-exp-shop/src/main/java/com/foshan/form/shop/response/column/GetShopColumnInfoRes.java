package com.foshan.form.shop.response.column;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.column.GetColumnInfoRes;
import com.foshan.form.shop.StoreForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取商城栏目返回对象(GetShopColumnInfoRes)")
@JsonInclude(Include.NON_NULL) 
public class GetShopColumnInfoRes extends GetColumnInfoRes {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7023307587691682351L;
	@ApiModelProperty(value = "商铺列表")
	private List<StoreForm> storeList = new ArrayList<StoreForm>();

	public GetShopColumnInfoRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}


	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
