package com.foshan.form.shop.response.sellerInformation;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class ModifySellerInformationRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -884852642805667849L;
	private Integer  sellerInformationId;

	public ModifySellerInformationRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	
	
}
