package com.foshan.form.shop.response.member;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.EmployeeInfoForm;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "会员登录返回对象(MemberLoginRes)")
@JsonInclude(Include.NON_NULL)
public class MemberLoginRes extends BaseResponse{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -3685317499363264149L;
	@ApiModelProperty(value = "会员Id",example="1")
	private Integer memberId;
	@ApiModelProperty(value = "会员电话")
	private String phone;
	@ApiModelProperty(value = "微信")
	private String weixin;
	@ApiModelProperty(value = "CA卡号")
	private String smartcardId;
	@ApiModelProperty(value = "帐号")
	private String loginName;
	@ApiModelProperty(value = "码是否为空标志：0-非空，1空",example="1")
	private Integer isEmptyPassword;//密码是否为空标志：0-非空，1空
//	@ApiModelProperty(value = "企业微信用户id")
//	private String wxEnterpriceMiniprogramUserid;
//	@ApiModelProperty(value = "企业微信用户名")
//	private String wxEnterpriceMiniprogramUsername;
	@ApiModelProperty(value = "企业微信用户信息")
	private EmployeeInfoForm employeeInfo;
	@ApiModelProperty(value = "区域码")
	private String regionCode;
	@ApiModelProperty(value = "公司ID",example="1")
	private Integer companyId;
	

	public MemberLoginRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}
	
}
