package com.foshan.form.shop.response.column;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.shop.ProductGroupForm;
import com.foshan.form.shop.ShopColumnForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取商城根栏目返回对象(GetServiceShopRootColumnRes)")
@JsonInclude(Include.NON_NULL)
public class GetServiceShopRootColumnRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5767428881575360007L;
	@ApiModelProperty(value = "业务Id",example="1")
	private Integer serviceId;
	@ApiModelProperty(value = "业务根栏目Id",example="1")
	private Integer serviceColumnId;
	@ApiModelProperty(value = "栏目列表",example="1")
	private List<ShopColumnForm> subColumnList = new ArrayList<ShopColumnForm>();
	@ApiModelProperty(value = "产品包列表",example="1")
	private List<ProductGroupForm> productGroupList = new ArrayList<ProductGroupForm>();

	public GetServiceShopRootColumnRes(String ret, String retInfo) {
		super(ret, retInfo);
	}

	public GetServiceShopRootColumnRes(Integer serviceId, Integer serviceColumnId,
			List<ShopColumnForm> subColumnList, List<ProductGroupForm> productGroupList) {
		super();
		this.serviceId = serviceId;
		this.serviceColumnId = serviceColumnId;
		this.subColumnList = subColumnList;
		this.productGroupList = productGroupList;
	}


	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
