package com.foshan.form.shop;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.AddressForm;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="运费模板明细(CourierFeeScheduleItemFrom)")
@JsonInclude(Include.NON_NULL)
public class CourierFeeScheduleItemForm implements IForm {

	private static final long serialVersionUID = -1408510473326506113L;

	@ApiModelProperty(value = "id", example="1")
	private Integer id;
	@ApiModelProperty(value = "首重，单位：公斤" ,example="1.2")
	private Double firstHeavy;
	@ApiModelProperty(value = "首重金额，单位：元", example="1.00")
	private BigDecimal firstHeavyCost;
	@ApiModelProperty(value = "续重，单位：公斤", example="1.2")
	private Double continueHeavy;
	@ApiModelProperty(value = "续重金额，单位：元", example="1.00")
	private BigDecimal continueHeavyCost;
	@ApiModelProperty(value = "配送区域列表，如果不在该列表的区域将采用默认首重和续重参数计算",example="1.00")
	private List<AddressForm> toAddressList = new ArrayList<AddressForm>();
	@ApiModelProperty(value = "配送地址Id字符串，多个用英文,隔开，如'1,2,3' ")
	private String toAddressIds;
	@ApiModelProperty(value = "配送区域说明")
	private String toRegionInfo;
	@ApiModelProperty(value = "该字段仅店铺级别的模板有效：免费条件限制值类型 0--订单价格  1--商品数量 ")
	private Integer limitType;
	@ApiModelProperty(value = "该字段仅店铺级别的模板有效：满足免邮的限制值，如3元，4件 ")
	private Double limitValue;
	@ApiModelProperty(value = "该字段仅店铺级别的模板有效：满足条件限制条件的订单运费 ")
	private BigDecimal logisticsCost;
	@ApiModelProperty(value = "该字段仅店铺级别的模板有效：商铺运费模板邮免策略：0--满LimitValue（元/件）包邮，不满则LogisticsCost元运费;  1--每张订单固定LogisticsCost运费",example="true")
	private Integer caculateStrategy;
	@ApiModelProperty(value = "所属的运费模板", example="com.foshan.form.CourierFeeScheduleForm")
    private CourierFeeScheduleForm courierFeeSchedule;
	@ApiModelProperty(value = "最后更新时间",example = "yyyy-MM-dd HH:mm:ss")
	private String lastModifyTime;

	public CourierFeeScheduleItemForm(Integer id, Double firstHeavy, BigDecimal firstHeavyCost, Double continueHeavy,
			BigDecimal continueHeavyCost, List<AddressForm> toAddressList, String toAddressIds, String toRegionInfo,
			Integer limitType, Double limitValue, BigDecimal logisticsCost, Integer caculateStrategy,
			CourierFeeScheduleForm courierFeeSchedule, String lastModifyTime) {
		super();
		this.id = id;
		this.firstHeavy = firstHeavy;
		this.firstHeavyCost = firstHeavyCost;
		this.continueHeavy = continueHeavy;
		this.continueHeavyCost = continueHeavyCost;
		this.toAddressList = toAddressList;
		this.toAddressIds = toAddressIds;
		this.toRegionInfo = toRegionInfo;
		this.limitType = limitType;
		this.limitValue = limitValue;
		this.logisticsCost = logisticsCost;
		this.caculateStrategy = caculateStrategy;
		this.courierFeeSchedule = courierFeeSchedule;
		this.lastModifyTime = lastModifyTime;
	}


	@Override
	public String toString() {
		return "CourierFeeScheduleItemForm [id=" + id + ", firstHeavy=" + firstHeavy + ", firstHeavyCost="
				+ firstHeavyCost + ", continueHeavy=" + continueHeavy + ", continueHeavyCost=" + continueHeavyCost
				+ ", toAddressList=" + toAddressList + ", toAddressIds=" + toAddressIds + ", toRegionInfo="
				+ toRegionInfo + ", limitType=" + limitType + ", limitValue=" + limitValue + ", logisticsCost="
				+ logisticsCost + ", caculateStrategy=" + caculateStrategy + ", courierFeeSchedule="
				+ courierFeeSchedule + ", lastModifyTime=" + lastModifyTime + "]";
	}

	@Override
	public int compareTo(Object arg0) {
		// TODO Auto-generated method stub
		return 0;
	}

}
