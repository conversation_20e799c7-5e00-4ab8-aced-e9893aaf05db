package com.foshan.form.shop.response.column;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.column.ModifyColumnRes;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "修改商城栏目返回对象(ModifyShopColumnRes)")
@JsonInclude(Include.NON_NULL)
public class ModifyShopColumnRes extends ModifyColumnRes {

	/**
	 * 
	 */
	private static final long serialVersionUID = -608906523018219007L;
	

	public ModifyShopColumnRes(Integer columnId, String columnCode, String columnName, Integer columnState,
			Integer parentColumnId) {
		super(columnId, columnCode, columnName, columnState, parentColumnId);
		// TODO Auto-generated constructor stub
	}

	public ModifyShopColumnRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
