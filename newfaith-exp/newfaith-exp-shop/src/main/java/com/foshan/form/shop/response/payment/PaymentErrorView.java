package com.foshan.form.shop.response.payment;

import java.io.PrintWriter;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.servlet.View;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** 
* @ClassName: PaymentErrorView 
* @Description: TODO(支付过程发生异常视图) 
* <AUTHOR>
* @date 2019年8月12日 上午23:06:42 
*  
*/
@Getter
@Setter
@NoArgsConstructor
public class PaymentErrorView implements View {
	
	//错误码
	private  String errorCode = "" ;
	//错误信息
	private  String errorMsg ="" ;
	//重定向URL
	private String  redirectUrl = "";
	
	public PaymentErrorView(String  errorCode,String errorMsg,String redirectUrl) {
		super();
		this.errorCode = errorCode;
		this.errorMsg = errorMsg;
		this.redirectUrl = redirectUrl;
	}

	@Override
	public String getContentType() {
		 return "text/html;charset=utf-8";
	}

	@Override
	public void render(Map<String, ?> model, HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		PrintWriter out = response.getWriter();
		StringBuilder sb = new StringBuilder("");
		sb.append("<!DOCTYPE html>\r\n" + 
				"<html lang=\"zh-cn\">\r\n" + 
				"<head>\r\n" + 
				"    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\"/>\r\n" + 
				"    <meta http-equiv=\"Pragma\" content=\"no-cache\">\r\n" + 
				"    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no\"/>\r\n" + 
				"    <meta content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0\" name=\"viewport\"/>\r\n" + 
				"    <meta name=\"format-detection\" content=\"telephone=no,date=no,address=no,email=no,url=no\"/>\r\n" + 
				"    <meta name=\"apple-mobile-web-app-capable\" content=\"yes\"/>\r\n" + 
				"    <meta name=\"apple-mobile-web-app-status-bar-style\" content=\"black\"/>\r\n" + 
				"    <meta name=\"screen-orientation\" content=\"portrait\"/>\r\n" + 
				"    <meta name=\"x5-orientation\" content=\"portrait\"/>\r\n" + 
				"    <meta name=\"title\" content=\"错误页\"/>\r\n" + 
				"    <meta name=\"copyright\" content=\"广东省广播电视网络股份有限公司佛山分公司\"/>\r\n" + 
				"    <meta name=\"author\" content=\"新业务发展部\"/>\r\n" + 
				"    <title>错误</title>\r\n" + 
				"    <link type=\"favicon\" rel=\"shortcut icon\" href=\"images/common/favicon.ico\"/>	\r\n" + 
				"</head>\r\n" + 
				"<body style=\"background-image: url(../../../../mobileShop/images/page/register/back.jpg);background-repeat: no-repeat;background-position: top center;background-size: 100% auto;\">\r\n" + 

				"<div style=\"position: absolute;top: 80vw;width:100%;padding: 0 20px;\">\r\n" + 
				"<div class=\"errorCode\"><span style=\"line-height:40px; font-size: 18px;color:#ff5500;\">错误码：</span>"+ this.errorCode +"</div>\r\n" + 
				" <div class=\"errorMsg\"><span style=\"line-height:40px; font-size: 18px;color:#ff5500;\">错误信息：</span>"+this.errorMsg +"</div>\r\n" + 
				" <div class=\"errorData\" style=\"margin-bottom:10vw;\"></div>\r\n" + 
				" <div class=\"register-btns\">\r\n" + 
				" <button style=\"border: 1px solid #ff5500; color:#fff; background-color: #ff5500; border-radius: 5px; height: 40px;line-height: 40px;width: 84%; font-size: 18px; padding: 0;\" id=\"register_btn\" onclick=\"javascript:window.location.href = '"+this.redirectUrl+"'\">确认</button>\r\n" + 
				" </div>\r\n" + 
				" </div>\r\n" + 
				"</script>\r\n" + 
				"</body>\r\n" + 
				"</html>\r\n" + 
				"");
		
		out.print(sb.toString());

		out.flush();
		out.close();
	}

}

