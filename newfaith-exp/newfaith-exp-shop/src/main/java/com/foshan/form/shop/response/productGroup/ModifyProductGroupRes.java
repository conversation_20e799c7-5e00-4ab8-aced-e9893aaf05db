package com.foshan.form.shop.response.productGroup;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class ModifyProductGroupRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2556562499251558899L;
	private Integer storeId;
	private Integer columnId;
	private Integer productGroupId;
	private String productGroupName;
	private Integer productGroupType;
	private Integer orders;
	private Integer assetId;


	public ModifyProductGroupRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	public ModifyProductGroupRes(Integer storeId, Integer columnId, Integer productGroupId, String productGroupName,
			Integer productGroupType, Integer orders) {
		super();
		this.storeId = storeId;
		this.columnId = columnId;
		this.productGroupId = productGroupId;
		this.productGroupName = productGroupName;
		this.productGroupType = productGroupType;
		this.orders = orders;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
