package com.foshan.form.shop.response.courier;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.shop.CourierCompanyForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取物流公司返回对象(GetCourierCompanyInfoRes)")
@JsonInclude(Include.NON_NULL)
public class GetCourierCompanyInfoRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 9070516987597677303L;
	@ApiModelProperty(value = "物流公司对象")
	private CourierCompanyForm companyForm;


	public GetCourierCompanyInfoRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	public GetCourierCompanyInfoRes(CourierCompanyForm companyForm) {
		super();
		this.companyForm = companyForm;
	}


	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
