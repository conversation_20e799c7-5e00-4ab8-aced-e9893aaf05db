package com.foshan.form.shop.response.productOrder;

import java.util.HashMap;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取订单状态分组数据(GetProductOrderCountByOrderStateRes)")
@JsonInclude(Include.NON_NULL)
public class GetProductOrderCountByOrderStateRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -4520147561414691176L;
	@ApiModelProperty(value = "分组数据")
	private HashMap<String,String> stateCount;


	public GetProductOrderCountByOrderStateRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	
}
