package com.foshan.form.shop;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(value = "演唱会电子门票对象(LiveTicketForm)")
public class LiveTicketsForm implements IForm {


    /**
	 * 
	 */
	private static final long serialVersionUID = -9035353091154036456L;
	@ApiModelProperty(value = "票根码")
    private String code;
    @ApiModelProperty(value = "演唱会名称")
    private String liveName;
    @ApiModelProperty(value = "卡号", example = "1")
    private String keyNo;
    @ApiModelProperty(value = "手机号")
    private String phone;
    @ApiModelProperty(value = "直播场次标识,取值范围1000-9999")
    private String liveCode;
    @ApiModelProperty(value = "票根码周期类型，日为“D”，月为“M”")
    private String freeCycle;
    @ApiModelProperty(value = "流水号")
    private String orderId;

    public LiveTicketsForm(String code, String liveName, String keyNo, String phone, String liveCode, String freeCycle, String orderId) {
        this.code = code;
        this.liveName = liveName;
        this.keyNo = keyNo;
        this.phone = phone;
        this.liveCode = liveCode;
        this.freeCycle = freeCycle;
        this.orderId = orderId;
    }


    @Override
    public String toString() {
        return "LiveTicketsForm{" +
                "code='" + code + '\'' +
                ", keyNo='" + keyNo + '\'' +
                ", phone='" + phone + '\'' +
                ", liveCode=" + liveCode +
                ", freeCycle='" + freeCycle + '\'' +
                ", orderId='" + orderId + '\'' +
                '}';
    }

    @Override
    public int compareTo(Object o) {
        return 0;
    }
}
