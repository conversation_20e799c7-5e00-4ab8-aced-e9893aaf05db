package com.foshan.form.shop;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="SKU参数对象(Parameters)")
@JsonInclude(Include.NON_NULL)
public class Parameters implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = 9042664173319952602L;
	@ApiModelProperty(value = "参数名称")
	private String name;
	@ApiModelProperty(value = "是否可搜索 0--不可搜索  1--可搜索",example="1")
	private Integer isSearch;
	@ApiModelProperty(value = "是否公用属性 0--sku属性 1--公用属性",example="1")
	private Integer global;
	@ApiModelProperty(value = "属性待选值")
	private String[] options;
	@ApiModelProperty(value = "是否数值型参数 0--不是 1--是",example="1")
	private Integer isNumber;
	@ApiModelProperty(value = "属性单位")
	private String unitName;
	@ApiModelProperty(value = "属性值")
	private String value;


	
	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
