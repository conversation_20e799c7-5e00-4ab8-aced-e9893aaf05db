package com.foshan.form.shop;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "产品规格属性对象(Specifications)")
@JsonInclude(Include.NON_NULL)
public class Specifications implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3951515668830380219L;
	@ApiModelProperty(value = "属性分组名称")
	private String group;
	@ApiModelProperty(value = "属性列表")
	private List<Parameters> parameters = new ArrayList<Parameters>();


	public Specifications(String group, List<Parameters> parameters) {
		super();
		this.group = group;
		this.parameters = parameters;
	}


	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
