package com.foshan.form.shop.response.coupon;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BasePageResponse;
import com.foshan.form.shop.CouponForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(Include.NON_NULL)
@ApiModel(value = "获取优惠券列表返回对象(GetCouponListRes)")
public class GetCouponListRes extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4154241408456717733L;
	@ApiModelProperty(value = "优惠券列表对象",dataType="List<CouponForm>")
	List<CouponForm> couponFormList = new ArrayList<CouponForm>();
	@ApiModelProperty(value = "商品对应的可以领取优惠券的优惠提示",dataType="List<CouponForm>")
    String couponTips;
	
	
	public List<CouponForm> getCouponList() {
		return couponFormList;
	}

	public void setCouponList(List<CouponForm> couponFormList) {
		this.couponFormList = couponFormList;

	}

	public GetCouponListRes() {
		super();
	}

	public GetCouponListRes(String ret, String retInfo) {
		super(ret, retInfo);

	}

	public String getCouponTips() {
		return couponTips;
	}

	public void setCouponTips(String couponTips) {
		this.couponTips = couponTips;
	}
	

}
