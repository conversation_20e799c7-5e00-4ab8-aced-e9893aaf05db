package com.foshan.form.shop.response.statistical;

import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BasePageResponse;
import com.foshan.form.shop.SalesProductForm;
import com.foshan.form.shop.StoreOrderStatisticalForm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class GetHomePageRes extends BasePageResponse{

	/**
	 * 
	 */
	private static final long serialVersionUID = -8372230571272291421L;
	private String orderTotalAmount="0";
	private int orderCount=0;
	private Integer productCount=0;
	private StoreOrderStatisticalForm sosf;
	private List<SalesProductForm> salesProductFormList = new ArrayList<SalesProductForm>();
	private Integer orderAllCount=0;
	@ApiModelProperty(value = "退款数", example = "1")
	private Integer refundment=0;
	@ApiModelProperty(value = "退货数", example = "1")
	private Integer returnGoods=0;
	
	public GetHomePageRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}
	public GetHomePageRes(String orderTotalAmount,Integer orderCount,Integer productCount,StoreOrderStatisticalForm sosf,List<SalesProductForm> salesProductFormList) {
		this.orderTotalAmount=orderTotalAmount;
		this.orderCount = orderCount;
		this.productCount = productCount;
		this.sosf = sosf;
		this.salesProductFormList= salesProductFormList;
	}
	
}
