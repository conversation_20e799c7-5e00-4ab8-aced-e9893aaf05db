package com.foshan.form.shop;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "订单轨迹对象(ProductOrderForm)")
@JsonInclude(Include.NON_NULL)
public class ProductOrderTraceForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7634439652435126236L;
	@ApiModelProperty(value = "订单Id", example = "1")
	private Integer productOrderId;
	@ApiModelProperty(value = "订单轨迹状态 0--创建 1--买家付款完成 2--商家已发货 3--买家已签收 4--订单退款 5--订单已拆分", example = "1")
	private Integer productOrderTraceState;
	@ApiModelProperty(value = "订单轨迹内容")
	private String traceContent;
	@ApiModelProperty(value = "轨迹发起人")
	private String traceOwner;
	@ApiModelProperty(value = "轨迹记录时间",example="2019-01-01 00:00:00")
	private String traceTime;

	public ProductOrderTraceForm(Integer productOrderId, Integer productOrderTraceState, String traceContent,
			String traceOwner,String traceTime) {
		super();
		this.productOrderId = productOrderId;
		this.productOrderTraceState = productOrderTraceState;
		this.traceContent = traceContent;
		this.traceOwner = traceOwner;
		this.traceTime = traceTime;
	}


	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
