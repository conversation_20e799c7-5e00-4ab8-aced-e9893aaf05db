package com.foshan.form.shop.response.plugin;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BasePageResponse;
import com.foshan.form.shop.PickupForm;
import com.foshan.form.shop.StorePaymentPluginListForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** 
* @ClassName: package-info 
* @Description: TODO(获取商户配置支付信息的返回报文) 
* <AUTHOR>
* @date 2019年3月12日 下午10:42:25 
*  
*/
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class GetPaymentPluginsRes extends BasePageResponse {
	
	private static final long serialVersionUID = 7766600875090845238L;
	
	List<StorePaymentPluginListForm> pluginList =  new ArrayList<StorePaymentPluginListForm>();

	
	public void addPaymentPlugin(StorePaymentPluginListForm form) {
		this.pluginList.add(form);
	}
	

	public GetPaymentPluginsRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}
	
	
}
