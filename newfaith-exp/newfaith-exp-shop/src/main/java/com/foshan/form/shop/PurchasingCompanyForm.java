package com.foshan.form.shop;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.AssetForm;
import com.foshan.form.IForm;
import com.foshan.form.RegionForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class PurchasingCompanyForm implements IForm {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 4941895500566130310L;
	@ApiModelProperty(value = "企业购公司Id", example = "1")
	private Integer purchasingCompanyId;
	@ApiModelProperty(value = "活动的名称")
	private String name;
	@ApiModelProperty(value = "组织代号")
	private String code;
	@ApiModelProperty(value = "组织地址")
	private String address;
	@ApiModelProperty(value = "组织联系电话")
	private String tel;
	@ApiModelProperty(value = "组织联系人")
	private String contacts; ;
	@ApiModelProperty(value = "组织邮箱")
	private String email;
	@ApiModelProperty(value = "logoId", example = "1")
	private AssetForm logoForm;
	@ApiModelProperty(value = "下级区域列表")
	private List<RegionForm> regionList = new ArrayList<RegionForm>();
	
	public PurchasingCompanyForm(Integer purchasingCompanyId,String name,String code,String address,String tel,String contacts,String email) {
		this.purchasingCompanyId = purchasingCompanyId;
		this.name = name;
		this.code = code;
		this.address = address;
		this.tel = tel;
		this.contacts = contacts;
		this.email = email;
	}
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
	

}
