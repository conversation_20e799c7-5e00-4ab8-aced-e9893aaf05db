package com.foshan.form.shop.response.productLimit;

import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BasePageResponse;
import com.foshan.form.shop.ProductLimitForm;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取产品限购策略列表返回对象(GetProductLimitListRes)")
@JsonInclude(Include.NON_NULL)
public class GetProductLimitListRes extends BasePageResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -3650310145449341467L;
	private List<ProductLimitForm> productLimitList = new ArrayList<>();


	public GetProductLimitListRes(int currentPage, int pageSize, int total, int totalResult) {
		super(currentPage, pageSize, total, totalResult);
		// TODO Auto-generated constructor stub
	}

	public GetProductLimitListRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
