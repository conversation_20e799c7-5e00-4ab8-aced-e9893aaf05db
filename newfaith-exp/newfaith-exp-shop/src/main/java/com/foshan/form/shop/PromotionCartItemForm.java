package com.foshan.form.shop;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class PromotionCartItemForm implements IForm{

	/**
	 * 
	 */
	private static final long serialVersionUID = -3733963682616493254L;
	private Integer id;
	/**
	 * 活动的名称
	 */
	private String name;
	
	/**
	 * 活动的标题
	 */
	private String promotionTips;
	/**
	 * 活动的图片
	 */
	private String image;
	/**
	 * 活动地址
	 */
	private String url;
	/**
	 * 促销类型
	 */
	private String promotionType;
	
	/**
	 * 是否包邮, true参加促销活动的商品免邮费， false参加促销的商品不包邮
	 */
	private Boolean isFreeShipping;

	/**
	 *  关联的产品ID
	 */
	private List<CartItemForm> cartItemList;
	
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (!(obj instanceof PromotionCartItemForm))
			return false;
		PromotionCartItemForm other = (PromotionCartItemForm) obj;
		if (id == null) {
			if (other.getId() != null)
				return false;
		} else if (!id.equals(other.getId()))
			return false;
		return true;
	}


	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
