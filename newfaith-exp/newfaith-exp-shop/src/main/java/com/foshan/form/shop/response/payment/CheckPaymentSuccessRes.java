
/** 
* @ClassName: package-info 
* @Description: TODO(检查订单支付是否成功的返回报文) 
* <AUTHOR>
* @date 2019年3月18日 上午10:26:59 
*  
*/
package com.foshan.form.shop.response.payment;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class CheckPaymentSuccessRes extends BaseResponse {
	
	private static final long serialVersionUID = 7766600875090845238L;
	
	private String shopPaymentRecordCode;
	private boolean checkResult;

	public boolean isCheckResult() {
		return checkResult;
	}

	public CheckPaymentSuccessRes(String shopPaymentRecordCode, boolean checkResult) {
		super();
		this.shopPaymentRecordCode = shopPaymentRecordCode;
		this.checkResult = checkResult;
	}
	
	public CheckPaymentSuccessRes(String ret, String retInfo) {
		super(ret, retInfo);
	}
	
    
	
	
	
	
}
