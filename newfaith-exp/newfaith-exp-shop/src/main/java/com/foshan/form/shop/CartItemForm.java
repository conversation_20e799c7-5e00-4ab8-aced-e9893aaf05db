package com.foshan.form.shop;

import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@ApiModel(value="购物车项对象(CartItemForm)")
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class CartItemForm implements IForm {

	private static final long serialVersionUID = 1285353692107863755L;
	@ApiModelProperty(value = "购物车项Id",example="1")
	private Integer cartItemId;
	@ApiModelProperty(value = "产品数量",example="1")
	private Integer number;
	@ApiModelProperty(value = "栏目Id",example="1")
	private Integer columnId;
	@ApiModelProperty(value = "金额",example="1")
	private BigDecimal amount;
	@ApiModelProperty(value = "购物车产品")
	private ProductForm productForm;
	@ApiModelProperty(value = "是否选择 0--没有选择 1--选择",example="1")
	private Integer selection;
	@ApiModelProperty(value = "活动")
	private PromotionForm selectedPromotion;
   

	public CartItemForm(Integer cartItemId, Integer number, Integer columnId, BigDecimal amount,
			ProductForm productForm, Integer selection, PromotionForm selectedPromotion ) {
		super();
		this.cartItemId = cartItemId;
		this.number = number;
		this.columnId = columnId;
		this.amount = amount;
		this.productForm = productForm;
		this.selection = selection;
		this.selectedPromotion = selectedPromotion;
	}

	

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return ((CartItemForm)o).getCartItemId().compareTo(cartItemId);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (!(obj instanceof CartItemForm))
			return false;
		CartItemForm other = (CartItemForm) obj;
		if (cartItemId == null) {
			if (other.getCartItemId() != null)
				return false;
		} else if (!getCartItemId().equals(other.getCartItemId()))
			return false;
		return true;
	}
	
}
