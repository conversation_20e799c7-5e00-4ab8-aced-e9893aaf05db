package com.foshan.form.shop.response.authentication;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "点播鉴权对象(WhetherPassAuthenticationRes)")
@JsonInclude(Include.NON_NULL)
public class WhetherPassAuthenticationRes extends BasePageResponse{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -4375445567271497173L;
	@ApiModelProperty(value = "是否通过;0 通过;1过期;2未购买; " ,example="1")
	private Integer whetherPass;
	@ApiModelProperty(value = "播放有郊期")
	private String playingTime;

	public WhetherPassAuthenticationRes(int currentPage, int pageSize, int total, int totalResult) {
		super(currentPage, pageSize, total, totalResult);
	}
	public WhetherPassAuthenticationRes(String ret, String retInfo) {
		super(ret, retInfo);
	}
	public WhetherPassAuthenticationRes(Integer whetherPass,String playingTime) {
		super();
		this.whetherPass = whetherPass;
		this.playingTime = playingTime;
	}

}
