package com.foshan.form.shop.response.shopUser;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.PermissionForm;
import com.foshan.form.RoleForm;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.shop.StoreForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "商铺管理员登录返回对象(LoginRes)")
@JsonInclude(Include.NON_NULL)
public class ShopLoginRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2512448641823106261L;
	@ApiModelProperty(value = "商铺管理员Id", example = "1")
	private Integer userId;
	@ApiModelProperty(value = "管理员名称")
	private String userName;
	@ApiModelProperty(value = "管理员编号")
	private String userCode;
	@ApiModelProperty(value = "角色列表")
	private List<RoleForm> roleList;
	@ApiModelProperty(value = "店铺")
	private StoreForm store;
	@ApiModelProperty(value = "管理员姓名")
	private String name;
	@ApiModelProperty(value = "权限列表")
	private List<PermissionForm> permissionList;


	public void setPermissionList(List<PermissionForm> permissionList) {
		this.permissionList = permissionList;
	}

}
