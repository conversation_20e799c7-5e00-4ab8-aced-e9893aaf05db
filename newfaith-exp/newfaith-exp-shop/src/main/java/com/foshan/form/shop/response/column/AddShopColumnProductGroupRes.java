package com.foshan.form.shop.response.column;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.shop.ProductGroupForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "新增栏目产品包返回对象(AddShopColumnProductGroupRes)")
@JsonInclude(Include.NON_NULL) 
public class AddShopColumnProductGroupRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3650503121233502959L;
	@ApiModelProperty(value = "栏目Id",example="1")
	private Integer columnId;
	@ApiModelProperty(value = "产品包列表")
	private List<ProductGroupForm> productGroupList = new ArrayList<ProductGroupForm>();

	public AddShopColumnProductGroupRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	public AddShopColumnProductGroupRes(Integer columnId, List<ProductGroupForm> productGroupList) {
		super();
		this.columnId = columnId;
		this.productGroupList = productGroupList;
	}


	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
