package com.foshan.form.shop;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class PromotionCalculateDetailForm implements IForm {

	private static final long serialVersionUID = 1285353692107863755L;

	// 总优惠金额
	private BigDecimal totalDiscountAmount;
	// 命中的优惠方案
	private List<PromotionResultForm> promotionResultList = new ArrayList<PromotionResultForm>();;

	public BigDecimal getTotalDiscountAmount() {
		return totalDiscountAmount;
	}

	public void setTotalDiscountAmount(BigDecimal totalDiscountAmount) {
		this.totalDiscountAmount = totalDiscountAmount;
	}

	public List<PromotionResultForm> getPromotionResultList() {
		return promotionResultList;
	}

	public void setPromotionResultList(List<PromotionResultForm> promotionResultList) {
		this.promotionResultList = promotionResultList;
	}


	@Override
	public int compareTo(Object arg0) {
		// TODO Auto-generated method stub
		return 0;
	}

	public PromotionCalculateDetailForm(BigDecimal totalDiscountAmount, List<PromotionResultForm> promotionResultList) {
		super();
		this.totalDiscountAmount = totalDiscountAmount;
		this.promotionResultList = promotionResultList;
	}


	

	
}
