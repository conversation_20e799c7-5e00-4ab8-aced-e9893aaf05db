package com.foshan.form.shop;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 企业发票信息Form
 * <AUTHOR>
 *
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "企业发票信息(CompanyInvoiceInfoForm)")
@JsonInclude(Include.NON_NULL)
public class CompanyInvoiceInfoForm implements IForm{

	/**
	 * 
	 */
	private static final long serialVersionUID = -1026498287319588886L;
	@ApiModelProperty(value = "企业发票信息Id", example = "1")
	private Integer id;
	@ApiModelProperty(value = "发票类型:0-增值发票；1－其他", example = "1")
	private Integer invoiceType;
	@ApiModelProperty(value = "抬头名称")
	private String companyName;
	@ApiModelProperty(value = "纳税识别号")
	private String paytaxNo;
	@ApiModelProperty(value = "注册地址")
	private String registeredAddress;
	@ApiModelProperty(value = "注册电话")
	private String registeredTelephone;
	@ApiModelProperty(value = "注册银行")
	private String blank;
	@ApiModelProperty(value = "银行账行")
	private String blankAccount;
	@ApiModelProperty(value = "审核状态：0未提交、1提交审核、3审核通过、4审核不通过", example = "1")
	private Integer auditState;
	@ApiModelProperty(value = "创建时间")
	private String createTime;
	@ApiModelProperty(value = "最后修改时间")
	private String lastModifyTime;
	
	
	public CompanyInvoiceInfoForm(Integer id, Integer invoiceType, String companyName, String paytaxNo,
			String registeredAddress, String registeredTelephone, String blank, String blankAccount,
			Integer auditState) {
		super();
		this.id = id;
		this.invoiceType = invoiceType;
		this.companyName = companyName;
		this.paytaxNo = paytaxNo;
		this.registeredAddress = registeredAddress;
		this.registeredTelephone = registeredTelephone;
		this.blank = blank;
		this.blankAccount = blankAccount;
		this.auditState = auditState;
	}

	/* (non-Javadoc)
	 * @see java.lang.Comparable#compareTo(java.lang.Object)
	 */
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}


	
}
