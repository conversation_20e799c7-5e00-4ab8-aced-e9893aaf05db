package com.foshan.form.shop.response.specification;

import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BasePageResponse;
import com.foshan.form.shop.ProductSpecificationForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class GetProductSpecificationListRes extends BasePageResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 6684446826757566917L;
	private List<ProductSpecificationForm> specificationList = new ArrayList<ProductSpecificationForm>();


	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
