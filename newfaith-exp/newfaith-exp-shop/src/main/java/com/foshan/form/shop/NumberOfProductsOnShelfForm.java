package com.foshan.form.shop;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class NumberOfProductsOnShelfForm implements IForm{
	
		/**
	 * 
	 */
	private static final long serialVersionUID = 7418971313817070251L;
		private String  storeName;
		private Long totality;
	

		public NumberOfProductsOnShelfForm(String storeName,  Long totality) {
			super();
			//this.memberId = memberId;
			this.storeName = storeName;
			this.totality = totality;
		}

		
		@Override
		public int compareTo(Object o) {
			// TODO Auto-generated method stub
			return 0;
		}
}