package com.foshan.form.shop;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class GoodsForm implements IForm {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 2521521138981832614L;
	@ApiModelProperty(value = "商品名")
	private String productName;
	@ApiModelProperty(value = "商品图片")
	private String coverImg;
	@ApiModelProperty(value = "商品详情页的小程序路径")
	private String url;
	@ApiModelProperty(value = "价格")
	private String price;
	@ApiModelProperty(value = "价格2")
	private String price2;
	@ApiModelProperty(value = "1：一口价，2：价格区间，3：显示折扣价；1：一口价，只有price；2：价格区间，price字段为左边界，price2字段为右边界。3：折扣价，price字段为原价，price2字段为现价" , example = "1")
	private Integer priceType;

	public GoodsForm(String productName,String coverImg,String url,String price,String price2,Integer priceType) {
		this.productName = productName;
		this.coverImg = coverImg;
		this.url = url;
		this.price = price;
		this.price2 = price2;
		this.priceType = priceType;
	}
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}


}
