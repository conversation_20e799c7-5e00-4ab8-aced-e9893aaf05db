package com.foshan.form.shop.request;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "产品限购策略请求对象(ProductLimitReq)")
public class ProductLimitReq extends BasePageRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = 9076199879175448661L;
	@ApiModelProperty(value = "限购产品所属商铺Id", example = "1")
	private Integer storeId;
	@ApiModelProperty(value = "产品限购策略Id", example = "1")
	private Integer productLimitId;
	@ApiModelProperty(value = "限购产品规格Id", example = "1")
	private Integer specificationId;
	@ApiModelProperty(value = "限购产品Id", example = "1")
	private Integer productId;
	@ApiModelProperty(value = "产品限购数量", example = "1")
	private Integer limitAmount;
	@ApiModelProperty(value = "产品限购库存", example = "1")
	private Integer inventoryAmount;
	@ApiModelProperty(value = "策略开始时间")
	private String startTime;
	@ApiModelProperty(value = "策略结束时间")
	private String endTime;
	@ApiModelProperty(value = "策略类型  0：只限制库存；1：只限制购买数量；2：限购时间内限制库存；3：限购时间内限制数量；4：限购时间内限制数量及库存", example = "0")
	private Integer limitType;
	@ApiModelProperty(value = "策略状态  0:无效  1:有效", example = "1")
	private Integer limitState;
	@ApiModelProperty(value = "产品限购策略有效范围  0：单产品   1：同规格下其它单品  2：同规格下所有产品", example = "0")
	private Integer limitScope;
	@ApiModelProperty(value = "限购产品id列表，逗号分隔", example = "1,2")
	private String productIds;
	@ApiModelProperty(value = "限购产品策略id列表，逗号分隔", example = "1,2")
	private String productLimitIds;

}
