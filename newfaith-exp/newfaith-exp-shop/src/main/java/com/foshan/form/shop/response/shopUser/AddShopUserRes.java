package com.foshan.form.shop.response.shopUser;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AddShopUserRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 273813116572002160L;
	private Integer userId;
	private String userCode;
	private String userName;
}
