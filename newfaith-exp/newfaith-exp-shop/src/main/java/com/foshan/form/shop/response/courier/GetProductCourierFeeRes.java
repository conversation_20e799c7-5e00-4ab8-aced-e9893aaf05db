package com.foshan.form.shop.response.courier;

import java.math.BigDecimal;

import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取物流费返回对象(GetProductCourierFeeRes)")
public class GetProductCourierFeeRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -7658794113366795342L;
	@ApiModelProperty(value = "物流费",example="1")
	private BigDecimal logisticsCost;

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
