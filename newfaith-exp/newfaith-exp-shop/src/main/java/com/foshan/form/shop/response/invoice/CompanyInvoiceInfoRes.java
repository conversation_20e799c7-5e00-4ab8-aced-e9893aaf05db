package com.foshan.form.shop.response.invoice;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.shop.CompanyInvoiceInfoForm;
import com.foshan.form.shop.ExpressPackageForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "企业发票信息请求返回对象(CompanyInvoiceInfoRes)")
@JsonInclude(Include.NON_NULL)
public class CompanyInvoiceInfoRes {
	@ApiModelProperty(value = "CompanyInvoiceInfoForm  企业发票对象")
	private CompanyInvoiceInfoForm companyInvoiceInfoForm;

	public CompanyInvoiceInfoRes(CompanyInvoiceInfoForm companyInvoiceInfoForm) {
		super();
		this.companyInvoiceInfoForm = companyInvoiceInfoForm;
	}
	
}
