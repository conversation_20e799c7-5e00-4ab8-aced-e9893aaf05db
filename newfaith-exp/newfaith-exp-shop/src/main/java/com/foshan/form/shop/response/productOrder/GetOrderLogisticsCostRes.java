package com.foshan.form.shop.response.productOrder;

import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class GetOrderLogisticsCostRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 3525168623806659877L;
	private Integer productOrderId;
	private BigDecimal logisticsCost;

	public GetOrderLogisticsCostRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
