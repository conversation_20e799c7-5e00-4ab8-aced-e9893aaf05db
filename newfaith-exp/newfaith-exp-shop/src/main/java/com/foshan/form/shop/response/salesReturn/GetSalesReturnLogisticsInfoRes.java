package com.foshan.form.shop.response.salesReturn;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BasePageResponse;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class GetSalesReturnLogisticsInfoRes extends BasePageResponse{

	/**
	 * 
	 */
	private static final long serialVersionUID = 6622965900016374709L;
    
	private Integer salesReturnId;
    private String logisticsInfo;
	public GetSalesReturnLogisticsInfoRes(Integer salesReturnId, String logisticsInfo) {
		super();
		this.salesReturnId = salesReturnId;
		this.logisticsInfo = logisticsInfo;
	}

	
}
