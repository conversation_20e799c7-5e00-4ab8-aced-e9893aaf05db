package com.foshan.form.shop;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import com.foshan.shop.promotion.StrategyValue;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class PromotionResultForm implements IForm {


	
	private static final long serialVersionUID = 1285353692107863755L;
	// 促销活动实例
		private PromotionForm  promotion;
		// 优惠金额
		private BigDecimal discountAmount;
		// 符合该促销活动的购物车项
		private List<CartItemForm> cartItemList;
		// 符合该促销活动的购订单项
		private List<ProductForm> orderItemList;
		
		private StrategyValue strategyValue;
		
		private String promotionTips;


	@Override
	public int compareTo(Object arg0) {
		// TODO Auto-generated method stub
		return 0;
	}
	
	


}
