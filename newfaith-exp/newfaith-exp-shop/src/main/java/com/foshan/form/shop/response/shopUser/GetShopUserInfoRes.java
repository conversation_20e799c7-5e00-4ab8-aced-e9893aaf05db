package com.foshan.form.shop.response.shopUser;

import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.RegionForm;
import com.foshan.form.RoleForm;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取商铺管理员信息返回对象(GetShopUserInfoRes)")
@JsonInclude(Include.NON_NULL)
public class GetShopUserInfoRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -2032343488866980932L;
	@ApiModelProperty(value = "用户id", example = "1")
	private Integer userId;
	@ApiModelProperty(value = "用户代码")
	private String userCode;
	@ApiModelProperty(value = "登陆用户名")
	private String userName;
	@ApiModelProperty(value = "姓名")
	private String name;
	@ApiModelProperty(value = "电话号码")
	private String phone;
	@ApiModelProperty(value = "角色对象列表")
	private List<RoleForm> roleList = new ArrayList<>();
	@ApiModelProperty(value = "商铺名称，如果为商铺管理员用户，该字段为对应商铺的名称")
	private String storeName;
	@ApiModelProperty(value = "商铺名称，如果为商铺管理员用户，该字段为对应商铺的ID")
	private Integer storeId;
	@ApiModelProperty(value = "是否自营 0--否 1--自营", example = "1")
	private Integer isSelfSupport;
	@ApiModelProperty(value = "经营模式 0--面向普通消费者 1--面向企业购 2--面向积分商城；用,隔开")
	private String businessModel;
	@ApiModelProperty(value = "最后修改密码时间")
	private String lastPwdModifyTime;
	@ApiModelProperty(value = "区域")
	private List<RegionForm> regionList = new ArrayList<>();

}
