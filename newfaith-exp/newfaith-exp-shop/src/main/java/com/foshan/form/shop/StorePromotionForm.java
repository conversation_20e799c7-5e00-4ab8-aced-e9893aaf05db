package com.foshan.form.shop;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class StorePromotionForm implements IForm {

	private static final long serialVersionUID = -6926591090363253260L;
	private Integer storeId;
	private String storeName;
	private String logisticsTips = "";
	private List<String> logisticsTipsList = new ArrayList<String>();
	// 不含任何优惠活动或邮的商品
	private List<CartItemForm> cartItemList = new ArrayList<CartItemForm>();
	private Integer isSelfSupport;
	private Integer regionId;
	@JsonIgnoreProperties
	private Comparator<PromotionCartItemForm> promotionCartItemFormComparator = new Comparator<PromotionCartItemForm>() {
		@Override
		public int compare(PromotionCartItemForm o1, PromotionCartItemForm o2) {
			// TODO Auto-generated method stub
			return o2.getId().compareTo(o1.getId());
		}
	};
	private Set<PromotionCartItemForm> promotionList = new TreeSet<PromotionCartItemForm>(
			promotionCartItemFormComparator);
	private List<CouponForm> receivedCouponList = new ArrayList<CouponForm>();
	private List<CouponForm> canReceiveCouponList = new ArrayList<CouponForm>();

	

	public StorePromotionForm(Integer storeId, String storeName, String logisticsTips, Integer isSelfSupport,
			Integer regionId, Set<PromotionCartItemForm> promotionList) {
		super();
		this.storeId = storeId;
		this.storeName = storeName;
		this.logisticsTips = logisticsTips;
		this.isSelfSupport = isSelfSupport;
		this.regionId = regionId;
		this.promotionList = promotionList;
		
	}

	public StorePromotionForm(Integer storeId, String storeName, Integer isSelfSupport, Integer regionId) {
		super();
		this.storeId = storeId;
		this.storeName = storeName;
		this.isSelfSupport = isSelfSupport;
		this.regionId = regionId;
	}

	@Override
	public int compareTo(Object arg0) {
		// TODO Auto-generated method stub
		return 0;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (!(obj instanceof StorePromotionForm))
			return false;
		StorePromotionForm other = (StorePromotionForm) obj;
		if (storeId == null) {
			if (other.getStoreId() != null)
				return false;
		} else if (!storeId.equals(other.getStoreId()))
			return false;
		return true;
	}

}
