package com.foshan.form.shop;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.ColumnForm;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="商城栏目对象(ShopColumnForm)")
@JsonInclude(Include.NON_NULL)
public class ShopColumnForm extends ColumnForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3661530198002040215L;
	@ApiModelProperty(value = "产品包列表")
	private List<ProductGroupForm> productGroupList = new ArrayList<ProductGroupForm>();
	@ApiModelProperty(value = "产品列表")
	private List<ProductForm> productList = new ArrayList<ProductForm>();
	@ApiModelProperty(value = "商铺列表")
	private List<StoreForm> storeList = new ArrayList<StoreForm>();

	public ShopColumnForm(Integer columnId, Integer serviceId, Integer parentColumnId, String columnCode,
			String columnName, Integer columnType, Integer columnLevel, String columnInfo, Integer columnState,
			Integer commendFlag, String mappingFolderId, String mappingFolderName, Integer mappingSystem,
			Integer orderNumber, Integer targetType, Integer isGlobal, Integer state, Boolean isOperation) {
		super(columnId, serviceId, parentColumnId, columnCode, columnName, columnType, columnLevel, columnInfo,
				columnState, commendFlag, mappingFolderId, mappingFolderName, mappingSystem, orderNumber, targetType,
				isGlobal, state, isOperation);
		// TODO Auto-generated constructor stub
	}

	public ShopColumnForm(Integer columnId, String columnCode, String columnName, String columnPath, Integer targetType,
			Integer isGlobal, Integer state, Boolean isOperation) {
		super(columnId, columnCode, columnName, columnPath, targetType, isGlobal, state, isOperation);
		// TODO Auto-generated constructor stub
	}

	public ShopColumnForm(List<ProductGroupForm> productGroupList) {
		super();
		this.productGroupList = productGroupList;
	}



	@Override
	public int compareTo(Object o) {

		return 0;
	}

}
