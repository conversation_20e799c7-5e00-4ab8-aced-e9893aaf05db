package com.foshan.form.shop;

import java.util.ArrayList;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.EmployeeInfoForm;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
@ApiModel(value = "会员对象(MemberForm)")
public class MemberForm implements IForm {

	private static final long serialVersionUID = -3777990232268530586L;
	@ApiModelProperty(value = "会员ID", example = "1")
	private Integer memberId;
	@ApiModelProperty(value = "昵称")
	private String nickName;
	@ApiModelProperty(value = "email")
	private String email;
	@ApiModelProperty(value = "家庭电话")
	private String homePhone;
	@ApiModelProperty(value = "办公电话")
	private String officePhone;
	@ApiModelProperty(value = "手机号码")
	private String phone;
	@ApiModelProperty(value = "性别：0-女，1-男", example = "1")
	private Integer sex;
	@ApiModelProperty(value = "CA卡号")
	private String smartcardId;
	@ApiModelProperty(value = "注册名")
	private String registName;
	@ApiModelProperty(value = "是否默认电视客户端（最后一次登陆的账号）：null/0-否，1-是", example = "1")
	private Integer isDefaultTvClient;
	@ApiModelProperty(value = "头像")
	private String headImage;
	@ApiModelProperty(value = "企业微信用户信息")
	private EmployeeInfoForm employeeInfo;
	@ApiModelProperty(value = "企业购公司信息")
	private PurchasingCompanyForm purchasingCompanyForm;
	@ApiModelProperty(value = "区域码")
	private String regionCode;
	@ApiModelProperty(value = "小程序帐号")
	private String miniProgramOpenId;
	@ApiModelProperty(value = "微信通讯录部门")
	private String wxEnterpriceDepartment;
	@ApiModelProperty(value = "信通讯录姓名")
	private String wxEnterpriceName;
	@ApiModelProperty(value = "微信通讯录岗位")
	private String wxEnterpricePosition;
	@ApiModelProperty(value = "微信通讯录部门名称")
	private String wxEnterpriceDepartmentName;
	@ApiModelProperty(value = "生日")
	private String birthday;
	@ApiModelProperty(value = "邮箱")
	private String loginName;
	@ApiModelProperty(value = "邮箱验证状态 0--未验证 1--验证", example = "1")
	private Integer mailVerifyState;
	@ApiModelProperty(value = "电话验证状态 0--未验证 1--验证", example = "1")
	private Integer phoneVerifyState;
	@ApiModelProperty(value = "照片")
	private String photo;
	@ApiModelProperty(value = "qq")
	private String qq;
	@ApiModelProperty(value = "用户编码")
	private String userCode;
	@ApiModelProperty(value = "微信昵称")
	private String weixin;
	@ApiModelProperty(value = "兑奖营业厅")
	private String exchangeHall;
	@ApiModelProperty(value = "兑奖区域")
	private String exchangeRegion;
	@ApiModelProperty(value = "家庭住址")
	private String homeAddress;
	@ApiModelProperty(value = "身份证号码")
	private String idCard;
	@ApiModelProperty(value = "IP地址")
	private String ip;
	@ApiModelProperty(value = "MAC地址")
	private String mac;
	@ApiModelProperty(value = "公司地址")
	private String officeAddress;
	@ApiModelProperty(value = "预约时间")
	private String orderTime;
	@ApiModelProperty(value = "电视号")
	private String tvNo;
	@ApiModelProperty(value = "用户真实姓名")
	private String userName;
	@ApiModelProperty(value = "微信头像")
	private String weixinAvatar;
	@ApiModelProperty(value = "微信帐号")
	private String weixinOpenId;
	@ApiModelProperty(value = "是否智能卡主（首次绑定），1-是，0-否", example = "1")
	private Integer isSmartcardMaster;
	@ApiModelProperty(value = "企业微信小程序userid")
	private String wxEnterpriceMiniProgramUserid;
	@ApiModelProperty(value = "微信通讯录Email")
	private String wxEnterpriceEmail;
	@ApiModelProperty(value = "微信通讯录手机")
	private String wxEnterpriceMobile;
	@ApiModelProperty(value = "注册时间")
	private String registTime;
	@ApiModelProperty(value = "状态 0--无效 1--有效", example = "1")
	private Integer userState;
	@ApiModelProperty(value = "账号类型：0个人账号，1企业账号", example = "1")
	private Integer type;
	@ApiModelProperty(value = "注册推荐人信息")
	private String registeredDeveloper;
	@ApiModelProperty(value = "BOSS绑定推荐人信息")
	private String boundDeveloper;
	@ApiModelProperty(value = "BOSS绑定客户Id")
	private String custId;
//	@ApiModelProperty(value = "用户总积分", example = "0")
//	private Integer totalPoints;
	@ApiModelProperty(value = "用户积分级别")
	private String pointsLevelName;
	@ApiModelProperty(value = "最后修改密码时间")
	private String lastPwdModifyTime;
	@ApiModelProperty(value = "积分详情")
	private ArrayList pointsInfo;
	

	public MemberForm(Integer memberId, String nickName, String email, String homePhone, String officePhone,
			String phone, String homeAddress, Integer sex) {
		super();
		this.nickName = nickName;
		this.email = email;
		this.homePhone = homePhone;
		this.officePhone = officePhone;
		this.phone = phone;
		this.sex = sex;
	}


	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

	

	
}
