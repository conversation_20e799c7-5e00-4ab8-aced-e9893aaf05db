package com.foshan.form.shop.response.productGroup;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.shop.ShopColumnForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class GetProductGroupUpShelfColumnlistRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6820663915872994763L;
	private Integer productGroupId;
	private String productGroupName;
	private Integer productGroupType;
	private List<ShopColumnForm> columnList = new ArrayList<ShopColumnForm>();


	public GetProductGroupUpShelfColumnlistRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	public GetProductGroupUpShelfColumnlistRes(Integer productGroupId, String productGroupName,
			Integer productGroupType, List<ShopColumnForm> columnList) {
		super();
		this.productGroupId = productGroupId;
		this.productGroupName = productGroupName;
		this.productGroupType = productGroupType;
		this.columnList = columnList;
	}

	
	public static long getSerialversionuid() {
		return serialVersionUID;
	}


}
