package com.foshan.form.shop.response.coupon;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.shop.CouponSettingForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 
* @ClassName: GetCouponSettingRes 
* @Description: 获取优惠券设置返回报文
* <AUTHOR>
* @date 2020年3月30日 下午3:31:15 
*
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取优惠券设置详情返回对象(GetCouponSettingRes)")
@JsonInclude(Include.NON_NULL)
public class GetCouponSettingRes extends GenericResponse {
	
	private static final long serialVersionUID = 6094155415258458869L;
	@ApiModelProperty(value = "优惠券设置对象",dataType = "CouponSettingForm")
	CouponSettingForm couponSettingForm;

	public GetCouponSettingRes(CouponSettingForm couponSettingForm) {
		super();
		this.couponSettingForm = couponSettingForm;
	}


	public GetCouponSettingRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}
	
	
	
}
