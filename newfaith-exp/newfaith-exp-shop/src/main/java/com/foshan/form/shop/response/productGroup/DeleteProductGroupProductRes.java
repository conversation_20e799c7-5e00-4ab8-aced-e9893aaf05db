package com.foshan.form.shop.response.productGroup;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BasePageResponse;
import com.foshan.form.shop.ProductForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class DeleteProductGroupProductRes extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7233712917869890736L;
	private List<ProductForm> productList = new ArrayList<ProductForm>();

	public DeleteProductGroupProductRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	public DeleteProductGroupProductRes(List<ProductForm> productList) {
		super();
		this.productList = productList;
	}


	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
