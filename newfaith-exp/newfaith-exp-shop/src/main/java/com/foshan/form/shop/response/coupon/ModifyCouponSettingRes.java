package com.foshan.form.shop.response.coupon;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
@JsonInclude(Include.NON_NULL)
@ApiModel(value = "修改优惠券结果返回对象(ModifyCouponSettingRes)")
public class ModifyCouponSettingRes extends GenericResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1476624262198367421L;
	@ApiModelProperty(value = "优惠券设置Id",example="1")
	private Integer couponSettingId;

	public Integer setCouponSettingId() {
		return couponSettingId;
	}

	public void getCouponSettingId(Integer id) {
		couponSettingId = id;
	}
}
