package com.foshan.form.shop;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class ProductAppraiseForm implements IForm{

	private static final long serialVersionUID = 5038608853156285910L;
	private Integer appraiseId; 
	private String appraiseNumber;
	private String appraiseScore;
	private String downTimes;
	private String upTimes;
	private String mediumTimes;
	
	public ProductAppraiseForm(Integer appraiseId, String appraiseNumber, String appraiseScore, String downTimes,
			String upTimes,String mediumTimes) {
		super();
		this.appraiseId = appraiseId;
		this.appraiseNumber = appraiseNumber;
		this.appraiseScore = appraiseScore;
		this.downTimes = downTimes;
		this.upTimes = upTimes;
		this.mediumTimes = mediumTimes;
	}
	
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

	
}
