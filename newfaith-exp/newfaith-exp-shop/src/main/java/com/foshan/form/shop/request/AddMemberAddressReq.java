package com.foshan.form.shop.request;

import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="新增会员地址请求对象(AddMemberAddressReq)")
public class AddMemberAddressReq extends BasePageRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6266868255892574535L;
	@ApiModelProperty(value = "会员Id",example="1")
	private Integer memberId;
	@ApiModelProperty(value = "会员地址Id",example="1")
	private Integer memberAddressId;
	@ApiModelProperty(value = "是否默认地址 0--否 1--是",example="1")
	private Integer isDefault;
	@ApiModelProperty(value = "收货人")
	private String receiver;
	@ApiModelProperty(value = "收货地址")
	private String receiverAddress;
	@ApiModelProperty(value = "收货电话")
	private String receiverPhone;
	@ApiModelProperty(value = "固定电话")
	private String receiverTel;
	@ApiModelProperty(value = "收货人邮箱")
	private String email;
	@ApiModelProperty(value = "标准地址Id",example="1")
	private Integer addressId;
	@ApiModelProperty(value = "区域Id",example="1")
	private Integer regionId;
	@ApiModelProperty(value = "小区Id",example="1")
	private Integer districtId;
	@ApiModelProperty(value = "不传或传1,0为商圈的区域",example="1")
	private Integer districtIdIsNull;
	

	public AddMemberAddressReq(Integer memberId, Integer memberAddressId, Integer isDefault, String receiver,
			String receiverAddress, String receiverPhone, Integer addressId) {
		super();
		this.memberId = memberId;
		this.memberAddressId = memberAddressId;
		this.isDefault = isDefault;
		this.receiver = receiver;
		this.receiverAddress = receiverAddress;
		this.receiverPhone = receiverPhone;
		this.addressId = addressId;
	}
	
	public AddMemberAddressReq(Integer memberId, Integer memberAddressId, Integer isDefault, String receiver,
			String receiverAddress, String receiverPhone, String receiverTel, String email, Integer addressId,
			Integer regionId) {
		super();
		this.memberId = memberId;
		this.memberAddressId = memberAddressId;
		this.isDefault = isDefault;
		this.receiver = receiver;
		this.receiverAddress = receiverAddress;
		this.receiverPhone = receiverPhone;
		this.receiverTel = receiverTel;
		this.email = email;
		this.addressId = addressId;
		this.regionId = regionId;
	}


}
