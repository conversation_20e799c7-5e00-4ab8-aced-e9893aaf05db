package com.foshan.form.shop.response.product;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.shop.ProductSpecificationForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AddProductRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6821428977006770732L;
	private ProductSpecificationForm productSpecificationForm;

	public AddProductRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	public AddProductRes(ProductSpecificationForm productSpecificationForm) {
		super();
		this.productSpecificationForm = productSpecificationForm;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
