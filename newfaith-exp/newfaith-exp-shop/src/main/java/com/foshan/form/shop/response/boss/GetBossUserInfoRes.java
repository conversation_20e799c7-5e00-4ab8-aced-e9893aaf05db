package com.foshan.form.shop.response.boss;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取BOSS用户信息对象(GetBossUserInfoRes)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetBossUserInfoRes extends BaseResponse {

    /**
	 * 
	 */
	private static final long serialVersionUID = -3698233997343005085L;
	@ApiModelProperty(value = "BOSS用户详情",example="1")
    private ArrayList resultList;

    public GetBossUserInfoRes(ArrayList resultList) {
        this.resultList = resultList;
    }

    public GetBossUserInfoRes(String ret, String retInfo, ArrayList resultList) {
        super(ret, retInfo);
        this.resultList = resultList;
    }

}
