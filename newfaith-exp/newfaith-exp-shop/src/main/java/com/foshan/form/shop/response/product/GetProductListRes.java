package com.foshan.form.shop.response.product;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BasePageResponse;
import com.foshan.form.shop.ProductForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class GetProductListRes extends BasePageResponse {
	
	private static final long serialVersionUID = 7766600875090845238L;
	private List<ProductForm> productList = new ArrayList<ProductForm>();


	public GetProductListRes(int currentPage, int pageSize, int total, int totalResult) {
		super(currentPage, pageSize, total, totalResult);
		// TODO 自动生成的构造函数存根
	}

	public GetProductListRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO 自动生成的构造函数存根
	}


	@Override
	public String toString() {
		return "GetProductListRes [productList=" + productList + "]";
	}
	
}
