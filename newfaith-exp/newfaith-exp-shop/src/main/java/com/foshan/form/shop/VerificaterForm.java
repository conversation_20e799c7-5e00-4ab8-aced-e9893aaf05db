package com.foshan.form.shop;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "核销员对象(VerificaterForm)")
@JsonInclude(Include.NON_NULL)
public class VerificaterForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6230317213603388645L;
	@ApiModelProperty(value = "用户Id", example = "1")
	private Integer verificaterId;
	@ApiModelProperty(value = "用户编号")
	private String userCode;
	@ApiModelProperty(value = "登录名")
	private String userName;
	@ApiModelProperty(value = "用户姓名")
	private String name;
	@ApiModelProperty(value = "电话")
	private String phone;
	// private String userPassword;


	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

	

}
