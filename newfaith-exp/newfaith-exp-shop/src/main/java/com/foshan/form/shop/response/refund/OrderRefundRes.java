/**   
 * Copyright © 2019 gcable foshan Info. Tech Ltd. All rights reserved.
 * 
 * @Package: com.foshan.form.shop.response.refund 
 * @author: pgq   
 * @date: 2019年4月13日 下午9:09:21 
 */
package com.foshan.form.shop.response.refund;

import com.foshan.form.response.BaseResponse;
import com.foshan.form.shop.OrderRefundForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 *   订单退款详情
 */
@Getter
@Setter
@NoArgsConstructor
public class OrderRefundRes extends BaseResponse {

	private static final long serialVersionUID = 134902223103819795L;

	OrderRefundForm orderRefundForm;

}
