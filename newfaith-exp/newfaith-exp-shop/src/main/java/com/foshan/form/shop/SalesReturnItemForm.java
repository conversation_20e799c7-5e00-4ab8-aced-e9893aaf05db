package com.foshan.form.shop;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class SalesReturnItemForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = 3590545082677682173L;
	@ApiModelProperty(value = "产品ID" , example = "1")
	private Integer productId;
	@ApiModelProperty(value = "产品名称" )
	private String productName;
	@ApiModelProperty(value = "产品编号" )
	private String productCode;
	@ApiModelProperty(value = "产品详情" )
	private String productInfo;
	@ApiModelProperty(value = "订购数量" , example = "1")
	private Integer orderNumber;
	@ApiModelProperty(value = "产品图ID" , example = "1")
	private Integer productImageId;
	@ApiModelProperty(value = "产品图地址" )
	private String productImageUrl;
	@ApiModelProperty(value = "产品手机图ID" , example = "1")
	private Integer productPhoneImageId;
	@ApiModelProperty(value = "产品手机图地址" )
	private String productPhoneImageUrl;
	@ApiModelProperty(value = "销售价格" )
	private String salesPrice;
	@ApiModelProperty(value = "订单项ID" , example = "1")
	private Integer ordetItemId;
	@ApiModelProperty(value = "订单项订购金额" )
	private String orderItemAmount;
	@ApiModelProperty(value = "订单项已退款金额" )
	private String refundedAmount;
	@ApiModelProperty(value = "商铺名称" )
	private String storeName;
	@ApiModelProperty(value = "退货数量" , example = "1")
	private Integer returnedQuantity;
	@ApiModelProperty(value = "调整金额" )
	private String adjustAmount;
	@ApiModelProperty(value = "ID" , example = "1")
	private Integer salesReturnOrderItemId;
	@ApiModelProperty(value = "sku属性列表" )
	private String skuSpecification;
	@ApiModelProperty(value = "售后数量" , example = "1")
	private Integer afterSalesAmount;
	@ApiModelProperty(value = "优惠券活动优惠金额" )
	private String couponDiscountAmount;
	@ApiModelProperty(value = "促销活动优惠金额" )
	private String discountAmount;
	@ApiModelProperty(value = "运费分摊金额" )
	private String logisticsAmount;
	@ApiModelProperty(value = "平均单个商品最大可退金额" )
	private String averageAmount;

	public SalesReturnItemForm(Integer productId,String productName,String productInfo,Integer orderNumber,
			Integer productImageId,String productImageUrl,Integer productPhoneImageId,String productPhoneImageUrl,
			String salesPrice,Integer ordetItemId,String orderItemAmount,
			String refundedAmount,String storeName,Integer returnedQuantity,String adjustAmount,Integer salesReturnOrderItemId,
			String skuSpecification,Integer afterSalesAmount,String productCode, 
			String couponDiscountAmount,String discountAmount,String logisticsAmount,String averageAmount) {
		super();
		this.productId = productId;
		this.productName = productName;
		this.productInfo = productInfo;
		this.orderNumber = orderNumber;
		this.productImageId = productImageId;
		this.productImageUrl = productImageUrl;
		this.productPhoneImageId = productPhoneImageId;
		this.productPhoneImageUrl = productPhoneImageUrl;
		this.salesPrice = salesPrice;
		this.ordetItemId = ordetItemId;
		this.orderItemAmount = orderItemAmount;
		this.refundedAmount = refundedAmount;
		this.storeName = storeName;
		this.returnedQuantity = returnedQuantity;
		this.adjustAmount = adjustAmount;
		this.salesReturnOrderItemId = salesReturnOrderItemId;
		this.skuSpecification = skuSpecification;
		this.afterSalesAmount = afterSalesAmount;
		this.productCode = productCode;
		this.couponDiscountAmount = couponDiscountAmount;
		this.discountAmount = discountAmount;
		this.logisticsAmount = logisticsAmount;
		this.averageAmount = averageAmount;
	}
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
	
}
