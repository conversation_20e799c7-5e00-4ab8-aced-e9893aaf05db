package com.foshan.form.shop.response.ExcelImport;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class OrderExcelImportRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2678660445223893546L;

	public OrderExcelImportRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

}
