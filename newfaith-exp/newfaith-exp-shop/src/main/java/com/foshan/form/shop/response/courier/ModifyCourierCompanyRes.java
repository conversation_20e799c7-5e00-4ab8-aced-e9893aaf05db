package com.foshan.form.shop.response.courier;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.shop.CourierCompanyForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "修改物流公司返回对象(ModifyCourierCompanyRes)")
@JsonInclude(Include.NON_NULL)
public class ModifyCourierCompanyRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4813755744673909279L;
	@ApiModelProperty(value = "物流公司对象")
	private CourierCompanyForm companyForm;

	public ModifyCourierCompanyRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	public ModifyCourierCompanyRes(CourierCompanyForm companyForm) {
		super();
		this.companyForm = companyForm;
	}


	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
