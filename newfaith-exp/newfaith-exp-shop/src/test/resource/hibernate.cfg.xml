<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-configuration PUBLIC "-//Hibernate/Hibernate Configuration DTD 3.0//EN"
                                         "http://www.hibernate.org/dtd/hibernate-configuration-3.0.dtd">
<hibernate-configuration>
	<session-factory>
		<property name="hibernate.dialect">org.hibernate.dialect.MySQL57InnoDBDialect</property>
		<property name="driverClassName">org.mariadb.jdbc.Driver</property>
		<property name="url">***************************************************************************************</property>
		<property name="username">root</property>
		<property name="password">welcome123</property>
		<property name="hibernate.hbm2ddl.auto">none</property>
		<property name="hibernate.cache.region.factory_class">org.hibernate.cache.ehcache.EhCacheRegionFactory</property>
		<property name="hibernate.connection.provider_class">com.alibaba.druid.support.hibernate.DruidConnectionProvider</property>
		<!-- 配置初始化大小、最小、最大 -->
		<property name="initialSize">5</property>
		<property name="minIdle">5</property>
		<property name="maxActive">100</property>
		<!-- 配置获取连接等待超时的时间 -->
		<property name="maxWait">60000</property>
		<!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
		<property name="timeBetweenEvictionRunsMillis">60000</property>
		<!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
		<property name="minEvictableIdleTimeMillis">300000</property>
		<!-- Enable Hibernate's automatic session context management -->
		<property name="current_session_context_class">thread</property>
		<property name="hibernate.show_sql">true</property>
		<property name="hibernate.use_sql_comments">true</property>


		<!-- <mapping class="com.foshan.entity.AccountEntity" />
		<mapping class="com.foshan.entity.AccountInfoEntity" />
		<mapping class="com.foshan.entity.AssetEntity" />
		<mapping class="com.foshan.entity.AssetSpecEntity" />
		<mapping class="com.foshan.entity.AddressEntity"/>
		<mapping class="com.foshan.entity.ColumnEntity" />
		<mapping class="com.foshan.entity.RegionEntity" />
		<mapping class="com.foshan.entity.ServiceEntity" />
		<mapping class="com.foshan.entity.VisitHistoryEntity" />
		<mapping class="com.foshan.entity.VisitRealEntity" />
		<mapping class="com.foshan.entity.LuckyDrawEntity" />
		<mapping class="com.foshan.entity.LuckyPrizeEntity" />
		<mapping class="com.foshan.entity.LuckyRuleEntity" />
		<mapping class="com.foshan.entity.QuestionCategoryEntity" />
		<mapping class="com.foshan.entity.QuestionColumnEntity" />
		<mapping class="com.foshan.entity.QuestionPlanEntity" />
		<mapping class="com.foshan.entity.QuestionStroreEntity" />
		<mapping class="com.foshan.entity.VoteColumnEntity" />
		<mapping class="com.foshan.entity.VoteContestantEntity" />
		<mapping class="com.foshan.entity.VoteGroupEntity" />
		<mapping class="com.foshan.entity.VotePlanEntity" />
		<mapping class="com.foshan.entity.VoteResultEntity" /> -->
		<mapping class="com.foshan.entity.PlatformUserEntity" />
		<mapping class="com.foshan.entity.RoleEntity" />
		<mapping class="com.foshan.entity.PermissionEntity" />
		<mapping class="com.foshan.entity.RegionEntity" />
		<mapping class="com.foshan.entity.ServiceEntity" />
		<mapping class="com.foshan.entity.ColumnEntity" />
		<mapping class="com.foshan.entity.AddressEntity" />
		<mapping class="com.foshan.util.passwordsm3.MemberEntity" />
		<mapping class="com.foshan.util.passwordsm3.ShopUserEntity" />
		
	</session-factory>
</hibernate-configuration>
