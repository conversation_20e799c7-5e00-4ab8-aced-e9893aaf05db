package com.foshan.form.cultureCloud.request;


import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="活动室请求(CultureCloudRoomBookReq)")
public class CultureCloudRoomBookReq extends BasePageRequest {


    private static final long serialVersionUID = -7162224930893542611L;
    @ApiModelProperty(name = "活动室id")
    private Integer roomId;

    private Integer bookId;

    private String curDate;

    private String openPeriod;

    private Integer timeSort;

    private Integer dayOfWeek;

    private Integer bookStatus;

    private String tuserId;

    private String userId;

    private String userName;

    private String userTel;

    private Date createTime;

    private String orderNo;

    private Date updateTime;

    private Date curDateBegin;

    private Date curDateEnd;

    private Integer curDateOperator;
    private String times;

    private String sysId;
    private String sysNo;
    private String bookIds;

    //团体名称 add by cj 2015-12-10
    private String tuserName;

    private String bookStatuStr;

    private String roomAllocationId;

    private Integer relateType;

    private String relateId;

    private String relateEventId;

    private Date openStartTime;

    private Date openEndTime;

    private Integer sortType;

    private Integer hasRelateInfo;

    private String startDate;

    private Integer changedStatus;
}
