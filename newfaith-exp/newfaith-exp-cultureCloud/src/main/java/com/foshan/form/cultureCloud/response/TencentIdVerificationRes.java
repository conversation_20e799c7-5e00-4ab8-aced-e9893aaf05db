package com.foshan.form.cultureCloud.response;

import com.foshan.form.response.GenericResponse;

/**
 * 腾讯云实名认证响应类
 * 用于身份信息认证（二要素核验）结果返回
 */
public class TencentIdVerificationRes extends GenericResponse {

    /**
     * 认证结果码
     * 收费结果码：
     * 0: 姓名和身份证号一致
     * -1: 姓名和身份证号不一致
     * 不收费结果码：
     * -2: 非法身份证号（长度、校验位等不正确）
     * -3: 非法姓名（长度、格式等不正确）
     * -4: 证件库服务异常
     * -5: 证件库中无此身份证记录
     * -6: 权威比对系统升级中，请稍后再试
     * -7: 认证次数超过当日限制
     */
    private String result;

    /**
     * 业务结果描述
     */
    private String description;

    /**
     * 腾讯云请求ID
     */
    private String requestId;

    /**
     * 是否验证成功
     */
    private boolean verified;

    public TencentIdVerificationRes() {
        super();
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
        // 根据结果码设置验证状态
        this.verified = "0".equals(result);
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public boolean isVerified() {
        return verified;
    }

    public void setVerified(boolean verified) {
        this.verified = verified;
    }

    /**
     * 获取结果码对应的中文描述
     * @return 结果描述
     */
    public String getResultDescription() {
        if (result == null) {
            return "未知结果";
        }
        
        switch (result) {
            case "0":
                return "姓名和身份证号一致";
            case "-1":
                return "姓名和身份证号不一致";
            case "-2":
                return "非法身份证号（长度、校验位等不正确）";
            case "-3":
                return "非法姓名（长度、格式等不正确）";
            case "-4":
                return "证件库服务异常";
            case "-5":
                return "证件库中无此身份证记录";
            case "-6":
                return "权威比对系统升级中，请稍后再试";
            case "-7":
                return "认证次数超过当日限制";
            default:
                return "未知结果码：" + result;
        }
    }

    /**
     * 判断是否为收费结果
     * @return true表示收费，false表示不收费
     */
    public boolean isChargeable() {
        return "0".equals(result) || "-1".equals(result);
    }

    @Override
    public String toString() {
        return "TencentIdVerificationRes{" +
                "result='" + result + '\'' +
                ", description='" + description + '\'' +
                ", requestId='" + requestId + '\'' +
                ", verified=" + verified +
                '}';
    }
}
