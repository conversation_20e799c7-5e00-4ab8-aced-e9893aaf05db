package com.foshan.form.cultureCloud;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="活动对象(CultureCloudActivityDetailForm)")
@JsonInclude(Include.NON_NULL)
public  class CultureCloudActivityDetailForm extends CultureCloudActivityForm{

	private static final long serialVersionUID = 4200113536124435681L;
	/**
	 *
	 */
	private Integer activityIsCollect;

	private String activityFunName;

	private Integer collectNum;

	private Integer activityAbleCount;
	private Integer activitySupplementType;
	private String activityEventIds;
	private String activityEventimes;
	private String status;
	private String timeQuantum;
	private String eventCounts;
	private String eventPrices;
	private String spikeDifferences;
	private Integer activityIsPast;
	private Integer singleEvent;
	private Integer customizeMethod;
	private Integer activityDateNums;

	private Date cancelTime;
	private Date cancelEndTime;
	private Integer writtenOffTime;
	private Integer beforeStartMinute;
	private Integer afterStartMinute;
	private Integer detailTimeLimitMinutes;
	private Integer timeLimitOfPickingUpTickets;

	private String shareUrl;

	private Integer activityIsWant;

	private String activityNotice;

	private String activityTips;

	private String integralStatus;

	private String[]assnSub;

	private String tagName;

//	private List<CmsTagSubVO> subList;

	private int browseCount;

	private int shareCount;

	private int likeCount;

	private int collectionCount;

	private Date serverTime;
	
    @ApiModelProperty(value = "关键字")
    private String keywords;
	@ApiModelProperty(value = "标签")
	private List<CultureCloudTagForm> tagList = new ArrayList<>();
	@ApiModelProperty(value = "类型")
	private List<CultureCloudTypeForm> typeList = new ArrayList<>();
	@ApiModelProperty(value = "艺术类型")
	private List<CultureCloudTypeForm> artTypeList = new ArrayList<>();
	@ApiModelProperty(value = "部门")
	private CultureCloudDepartmentForm department;

//	List<CmsResourceAttachment> attachmentList;

	//活动预订冲突开关
	private Integer activityConflict;

	private String venueName;



	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}


}
