package com.foshan.form.cultureCloud.response.cultureCloudTag;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.cultureCloud.CultureCloudTagForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="文化活动(GetCultureCloudTagListReq)")
public class GetCultureCloudTagListRes extends BasePageResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "文化活动列表")
	private List<CultureCloudTagForm> cultureCloudTagList = new ArrayList<CultureCloudTagForm>(); 

}
