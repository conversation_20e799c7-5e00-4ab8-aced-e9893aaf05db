package com.foshan.form.cultureCloud.request;

import com.foshan.form.request.GenericRequest;

/**
 * 腾讯云实名认证请求类
 * 用于身份信息认证（二要素核验）
 */
public class TencentIdVerificationReq extends GenericRequest {

    /**
     * 身份证号
     * 必填参数
     */
    private String idCard;

    /**
     * 姓名
     * 必填参数
     */
    private String name;

    /**
     * 敏感数据加密信息（可选）
     * 对传入信息（姓名、身份证号）有加密需求的用户可使用此参数
     */
    private String encryption;

    public TencentIdVerificationReq() {
        super();
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEncryption() {
        return encryption;
    }

    public void setEncryption(String encryption) {
        this.encryption = encryption;
    }

    @Override
    public String toString() {
        return "TencentIdVerificationReq{" +
                "idCard='" + idCard + '\'' +
                ", name='" + name + '\'' +
                ", encryption='" + encryption + '\'' +
                '}';
    }
}
