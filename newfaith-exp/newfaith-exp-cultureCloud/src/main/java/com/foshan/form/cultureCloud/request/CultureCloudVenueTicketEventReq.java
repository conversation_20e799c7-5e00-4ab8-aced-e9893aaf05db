package com.foshan.form.cultureCloud.request;

import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="活动场次请求(CultureCloudVenueTicketEventReq)")
public class CultureCloudVenueTicketEventReq extends BasePageRequest {

    private static final long serialVersionUID = -103481183501904689L;
    private Integer venueId;
    private Integer userId;
    private Integer venueTicketEventId;
    private String ticketEventStart;
    private String ticketEventEnd;
    private Integer maxPerson;
    private String timePeriod;
    private Integer openStatus;
}
