package com.foshan.form.cultureCloud;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="线下培训表单(CultureCloudOfflineTrainingForm)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class  CultureCloudOfflineTrainingForm implements IForm {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "培训标题")
    private String trainTitle;

    @ApiModelProperty(value = "标签列表")
    private List<CultureCloudTagForm> tagList = new ArrayList<>();

    @ApiModelProperty(value = "类型")
    private List<CultureCloudTypeForm> typeList = new ArrayList<>();

    @ApiModelProperty(value = "标签")
    private String tags;

    @ApiModelProperty(value = "培训图片URL")
    private String trainImgUrl;

    @ApiModelProperty(value = "培训介绍")
    private String trainIntroduce;

    @ApiModelProperty(value = "培训状态 0-下架 1-上架")
    private Integer trainStatus;

    @ApiModelProperty(value = "是否删除 0-删除 1-正常")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private String  createTime;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "省份")
    private String trainProvince;

    @ApiModelProperty(value = "城市")
    private String trainCity;

    @ApiModelProperty(value = "区域")
    private String trainArea;

    @ApiModelProperty(value = "位置")
    private String trainLocation;

    @ApiModelProperty(value = "镇")
    private String trainTown;

    @ApiModelProperty(value = "村")
    private String trainVillage;

    @ApiModelProperty(value = "详细地址")
    private String trainAddress;

    @ApiModelProperty(value = "场馆类型")
    private String venueType;

    @ApiModelProperty(value = "场馆ID")
    private String venueId;

    @ApiModelProperty(value = "培训类型")
    private String trainType;

    @ApiModelProperty(value = "培训标签")
    private String trainTag;

    @ApiModelProperty(value = "录取方式 1-先到先得，2人工录取，3随机录取，4面试后录取")
    private Integer admissionType;

    @ApiModelProperty(value = "最大人数")
    private Integer maxPeople;

    @ApiModelProperty(value = "培训场次 1-单场 2-多场")
    private Integer trainField;

    @ApiModelProperty(value = "经度")
    private Double lon;

    @ApiModelProperty(value = "纬度")
    private Double lat;

    @ApiModelProperty(value = "面试时间")
    private String interviewTime;

    @ApiModelProperty(value = "面试地址")
    private String interviewAddress;

    @ApiModelProperty(value = "温馨提示")
    private String reminder;

    @ApiModelProperty(value = "咨询电话")
    private String consultingPhone;

    @ApiModelProperty(value = "联系方式")
    private String contactInformation;

    @ApiModelProperty(value = "报名要求")
    private String registrationRequirements;

    @ApiModelProperty(value = "课程介绍")
    private String courseIntroduction;

    @ApiModelProperty(value = "教师介绍")
    private String teachersIntroduction;

    @ApiModelProperty(value = "报名开始时间")
    private String registrationStartTime;

    @ApiModelProperty(value = "报名结束时间")
    private String registrationEndTime;

    @ApiModelProperty(value = "培训开始时间")
    private String trainStartTime;

    @ApiModelProperty(value = "培训结束时间")
    private String trainEndTime;

    @ApiModelProperty(value = "更新人")
    private String updateUser;

    @ApiModelProperty(value = "更新时间")
    private String updateTime;

    @ApiModelProperty(value = "课程类型(新春班/春季班/暑期班/秋季班)")
    private String courseType;

    @ApiModelProperty(value = "报名次数")
    private Integer registrationCount;

    @ApiModelProperty(value = "是否需要签到 0：否 1：是")
    private Integer sign;

    @ApiModelProperty(value = "所需积分")
    private Integer integralNeeded;

    @ApiModelProperty(value = "审核状态 0：待审核 1：审核不通过 2：审核通过")
    private Integer checkStatus;

    @ApiModelProperty(value = "最小年龄")
    private Integer minAge;

    @ApiModelProperty(value = "最大年龄")
    private Integer maxAge;

    @ApiModelProperty(value = "附件")
    private Integer enclosure;

    @ApiModelProperty(value = "性别限制 0-不限 1-仅男 2-仅女")
    private Integer genderRestriction;

    @ApiModelProperty(value = "是否发放证书 0-否 1-是")
    private Integer hasIssuingCertificate;

    @ApiModelProperty(value = "是否可以请假 0-否 1-是")
    private Integer isAskLeave;

    @ApiModelProperty(value = "是否需要填写身份证号 0-否 1-是")
    private Integer isCardNo;

    @ApiModelProperty(value = "关键词")
    private String keywords;

    @ApiModelProperty(value = "所属平台")
    private String website;

    @ApiModelProperty(value = "h5链接")
    private String mobileUrl;

    @ApiModelProperty(value = "pc链接")
    private String pcUrl;

    @ApiModelProperty(value = "发布来源")
    private String publishSource;

    @ApiModelProperty(value = "是否实名报名 0-否 1-是")
    private Integer realNameSystem;

    @ApiModelProperty(value = "退订截止时间")
    private String refundTime;

    @ApiModelProperty(value = "视频")
    private String resourceVideoUrl;

    @ApiModelProperty(value = "课程表添加方式 1-手动添加 2-批量导入 3-固定场次")
    private Integer scheduleImportType;

    @ApiModelProperty(value = "是否自定义退订截止时间 1-默认 2-自定义")
    private Integer setRefundWay;

    @ApiModelProperty(value = "主办方单位")
    private String sponsorUnit;

    @ApiModelProperty(value = "培训周期")
    private String trainPeriod;

    @ApiModelProperty(value = "活动室ID")
    private String venueRoomId;

    @ApiModelProperty(value = "培训周期(1,2,3,4,5,6,7)")
    private String weekStr;

    @ApiModelProperty(value = "标签ID列表，多个以逗号分隔")
    private String tagIdList;

    @ApiModelProperty(value = "类型ID列表，多个以逗号分隔")
    private String typeIdList;

    @ApiModelProperty(value = "资源图片URL")
    private String resourceImgUrl;

    @ApiModelProperty(value = "联系人")
    private String linkman;

    private List<Map<String,String>> fieldList = new ArrayList<>();

    @Override
    public int compareTo(Object o) {
        return 0;
    }
}