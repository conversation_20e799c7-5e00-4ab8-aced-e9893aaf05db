package com.foshan.form.cultureCloud;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.entity.cultureCloud.CultureCloudVenueSeatEntity;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="座位模板(CultureCloudVenueSeatTemplateForm)")
@JsonInclude(Include.NON_NULL)
public  class CultureCloudVenueSeatTemplateForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = -487593623302458767L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer venueSeatTemplateId;
	@ApiModelProperty(value = "模板名称")
	private String templateName;
	@ApiModelProperty(value = "剩余数量",example="1")
	private Integer validCount;
	@ApiModelProperty(value = "座位排数",example="1")
	private Integer seatRow;
	@ApiModelProperty(value = "座位列数",example="1")
	private Integer seatColumn;
	@ApiModelProperty(value = "创建用户")
	private String templateCreateUser;
	@ApiModelProperty(value = "更新用户")
	private String templateUpdateUser;
	@ApiModelProperty(value = "模板描述")
	private String templateDesc;
	@ApiModelProperty(value = "二楼座位排数",example="1")
	private Integer secondFloorRow;
	@ApiModelProperty(value = "座位")
	private Map<String, Map<Integer,List<CultureCloudVenueSeatForm>>> venueSeatMap = new HashMap<>();
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
