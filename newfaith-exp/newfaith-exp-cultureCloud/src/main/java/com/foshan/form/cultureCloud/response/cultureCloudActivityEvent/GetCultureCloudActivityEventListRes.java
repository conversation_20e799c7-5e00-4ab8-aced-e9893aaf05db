package com.foshan.form.cultureCloud.response.cultureCloudActivityEvent;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.cultureCloud.CultureCloudActivityEventForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取活动列表对象(GetCultureCloudActivityEventListRes)")
@JsonInclude(Include.NON_NULL)
public class GetCultureCloudActivityEventListRes extends BasePageResponse {
	private static final long serialVersionUID = 7975109531889994057L;
	/**
	 * 
	 */

	@ApiModelProperty(value = "活动对象")
	private CultureCloudActivityEventForm activity;

	@ApiModelProperty(value = "活动场次列表对象")
	private List<CultureCloudActivityEventForm> eventList = new ArrayList<>();

}
