package com.foshan.form.cultureCloud.request;

import javax.persistence.Column;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="文化活动(CultureCloudSeatAccountReq)")
public  class CultureCloudSeatAccountReq extends BasePageRequest {


	/**
	 * 
	 */
	private static final long serialVersionUID = 1564169174163891581L;
	@ApiModelProperty(value = "订座ID",example="1")
	private Integer seatAccountId;
	@ApiModelProperty(value = "订座ID",example="1")
	private String seatAccountIdList;
	@ApiModelProperty(value = "模板ID",example="1")
	private Integer venueSeatTemplateId;
	@ApiModelProperty(value = "模板名称")
	private String templateName;
	@ApiModelProperty(value = "活动ID",example="1")
	private Integer activityId;
	@ApiModelProperty(value = "座位ID",example="1")
	private Integer venueSeatId;
	@ApiModelProperty(value = "座位区域'")
	private String seatArea;
	@ApiModelProperty(value = "座位排数'",example="1")
	private Integer seatRow;
	@ApiModelProperty(value = "座位列数'",example="1")
	private Integer seatColumn;
	@ApiModelProperty(value = "座位编号'")
	private String seatCode;
	@ApiModelProperty(value = "座位状态 1-正常 2-待修 3-不存在 4-vip 5-普通已预订 6-vip已预订 7-预定 8-取消预定'",example="1")
	private Integer seatStatus;
	@ApiModelProperty(value = "座位状态 1-正常 2-待修 3-不存在 4-vip 5-普通已预订 6-vip已预订'")
	private String seatStatusList;
	
	
}
