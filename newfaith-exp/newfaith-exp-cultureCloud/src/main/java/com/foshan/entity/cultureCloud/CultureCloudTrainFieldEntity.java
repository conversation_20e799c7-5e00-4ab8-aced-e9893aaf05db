package com.foshan.entity.cultureCloud;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_culture_cloud_train_field")
@org.hibernate.annotations.Table(appliesTo = "t_culture_cloud_train_field", comment="培训场次")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CultureCloudTrainFieldEntity extends CultureCloud {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(columnDefinition = "int(11) comment '主键ID'")
    private Integer id;

    @Column(columnDefinition = "varchar(128) comment '场次时间描述'")
    private String fieldTimeStr;

    @Column(columnDefinition = "datetime comment '场次开始时间'")
    private Date fieldStartTime;

    @Column(columnDefinition = "datetime comment '场次结束时间'")
    private Date fieldEndTime;

    @Column(columnDefinition = "datetime comment '场次更新时间'")
    private Date fieldUpdateTime;

    @Column(columnDefinition = "varchar(64) comment '场次更新人'")
    private String fieldUpdateUser;

    @Column(columnDefinition = "varchar(128) comment '场次标题'")
    private String fieldTitle;

    @Column(columnDefinition = "int(1) comment '场次状态 1-正常'")
    private Integer fieldState;

    @ManyToOne(targetEntity = CultureCloudOfflineTrainingEntity.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "trainId", referencedColumnName = "id", insertable = false, updatable = false)
    @JsonIgnore
    private CultureCloudOfflineTrainingEntity train;
}
