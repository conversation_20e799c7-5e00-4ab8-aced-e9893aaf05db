package com.foshan.entity.cultureCloud;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_culture_cloud_activity_extend_info")
@org.hibernate.annotations.Table(appliesTo = "t_culture_cloud_activity_extend_info",comment="文化活动场次")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CultureCloudActivityExtendInfoEntity extends CultureCloud{


    private static final long serialVersionUID = 256337213319198290L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private String activityExtendInfoUUID;
    private String activityId;
    private String activityUUID;
    private String activityTicketOrderNotice;
    private String activityTicketEntranceExplanation;
    private String activityTicketSafetyNotice;
    private Integer activityCommentSwitch;
    private String activitySpecialNotice;
    private Integer activitySpecialNoticeSecs;
    private Integer purchasableDuringShow;
    private Integer timeLimitOfPickingUpTickets;
    private Integer detailTimeLimitMinutes;
    private String branchDeptId;
    private String branchDeptShopPath;
    private Integer needSign;
    private String shopProvince;
}
