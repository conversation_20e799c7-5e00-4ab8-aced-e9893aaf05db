package com.foshan.entity.cultureCloud.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.cultureCloud.CultureCloudActivityOrderDetailForm;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CheckConflictVO {

    private String shopPath;
    private String shopProvince;
    private String userIds;
    private String userId;
    private String identityCard;
    private String fromModule;
//    private CmsShop mainShop;
    private String mainShopUserIds;
    private Date startTime;
    private Date endTime;
    private String activityId;
    private List<CultureCloudActivityOrderDetailForm> activityOrderDetailList;
    private String activityEventIds;
    private String activityOrderName;
    private String trainId;
}
