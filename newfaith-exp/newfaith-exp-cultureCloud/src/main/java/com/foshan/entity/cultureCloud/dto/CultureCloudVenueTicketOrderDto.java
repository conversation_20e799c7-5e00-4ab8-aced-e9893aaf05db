package com.foshan.entity.cultureCloud.dto;

import com.foshan.form.cultureCloud.CultureCloudVenueTicketOrderForm;
import lombok.Getter;
import lombok.Setter;

import java.math.BigInteger;
import java.util.Date;

@Getter
@Setter
public class CultureCloudVenueTicketOrderDto extends CultureCloudVenueTicketOrderForm {


    private String venueName;

    private String venueIconUrl;

    private Date eventDate;

    private Date cancelEndTime;

    private Boolean isOverCancelTime;

    private String timePeriod;

    private String userName;

    private Integer venueCommentSwitch;

    private Integer venueTicketCredentialMode;


    private String venueTicketEventId;
    private String userId;
    private String orderSerialNumber;
    private String orderPersonName;
    private String orderPersonLinknum;
    private String orderCode;
    private Integer orderVotes;
    private Integer orderStatus;
    private Integer isDeleted;
    private Integer allowCancel;
    private Integer cancelEndHour;
    private Integer cancelEndBeforeHour;
    private Integer cancelBeforeOrEnd;
    private Integer reorderNum;
    private Date createTime;
    private Date updateTime;
    private String updateBackUser;

    private String createTimeStr;

    private BigInteger orderNum;
    private String updateBackUserName;
    private Integer canCancel;
    private String curDateStart;
    private String curDateEnd;
    private String realInformation;
}
