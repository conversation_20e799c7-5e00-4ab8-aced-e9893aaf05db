package com.foshan.entity.cultureCloud;


import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.DepartmentEntity;
import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.RegionEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
@DiscriminatorValue("CC")
public class CultureCloudUserEntity extends PlatformUserEntity{
	/**
	 * 
	 */
	private static final long serialVersionUID = -628503235673440200L;
	
	@Column(columnDefinition = "varchar(100) comment '身份证号码'")
	private String idCard;
	@Column(columnDefinition = "varchar(10) comment '生日'")
	protected String birthday;
	@Column(columnDefinition = "varchar(64) comment '邮箱'")
	protected String email;
	
	@ManyToOne(targetEntity = DepartmentEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "departmentId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private DepartmentEntity department;

}
