package com.foshan.entity.cultureCloud;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_culture_cloud_sensitive_words")
@org.hibernate.annotations.Table(appliesTo = "t_culture_cloud_sensitive_words",comment="敏感字") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CultureCloudSensitiveWordsEntity extends CultureCloud {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -3609213972649541024L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(32) comment '创建人'")
	private String createUser;
	@Column(columnDefinition = "int(11) comment '更新人'")
	private String updateUser;
	@Column(columnDefinition = "varchar(32) comment '敏感词'")
	private String sensitiveWord;
	@Column(columnDefinition = "int(1) comment '使用状态：0--未使用 1--使用'")
	private Integer useState;
	

}
