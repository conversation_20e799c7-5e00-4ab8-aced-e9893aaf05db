package com.foshan.entity.cultureCloud;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AddressEntity;
import com.foshan.entity.IEntityBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_culture_cloud_sys_user_analyse")
@org.hibernate.annotations.Table(appliesTo = "t_culture_cloud_sys_user_analyse",comment="用户分析表")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CultureCloudSysUserAnalyseEntity implements IEntityBean {
	private static final long serialVersionUID = 6940390317043162623L;
	/**
	 * 
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	private String userId;
	private String tagId;
	private Integer visitScore;
	private Integer collectScore;
	private Integer orderScore;
	private String signId;


}
