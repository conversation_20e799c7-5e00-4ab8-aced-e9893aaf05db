package com.foshan.entity.cultureCloud;

import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@MappedSuperclass
public abstract class CultureCloud implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1867092932540522876L;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '时间'")
	protected Timestamp createTime;
	@Column(columnDefinition = "Timestamp  comment '最后修改时间'")
	protected Timestamp lastModifyTime;
	@Column(columnDefinition = "int(1) default 1 comment '状态 0--无效数据  1--有效数据'")
	protected Integer state;

}
