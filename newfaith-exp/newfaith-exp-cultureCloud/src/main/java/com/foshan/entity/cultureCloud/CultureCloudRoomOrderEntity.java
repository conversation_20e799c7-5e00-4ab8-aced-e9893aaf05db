package com.foshan.entity.cultureCloud;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.UserEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_culture_cloud_room_order")
@org.hibernate.annotations.Table(appliesTo = "t_culture_cloud_room_order",comment="活动室")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CultureCloudRoomOrderEntity extends CultureCloud {
	private static final long serialVersionUID = 5584490719165157747L;
	//活动室订单
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	private String roomOrderUUID;
	private String userName;
	private String userTel;
	private String tuserId;
	private String orderNo;
	private String roomId;
	private Integer bookStatus;
	private String bookId;
	private String validCode;
	private Date orderCreateTime;
	private Date orderUpdateTime;
	private String orderUpdateUser;
	private String sysNo;
	private String sysId;
	private String sysUserId;
	private String purpose;
	private Integer checkStatus;
	private String tuserName;
	private String checkReason;
	private String reservationMethod;
	private Integer orderAttendNum;
	private String applicationUrl;
	/**
	 *是否允许退订（1：是，2：否）
	 */
	private Integer allowCancel;
	/**
	 *退订截止时间场次开始前小时数量
	 */
	private Integer cancelEndHour;
	private boolean overCancelTime;
	private Date cancelEndTime;
	private String userIdNo;
	private String city;
	private String area;
	private String curDates;
	private String dictName;
	private Integer commentNums;
	private String roomOpenTime;
	private String venueName;
	private String venueCity;
	private String venueArea;
	private String venueAddress;
	@Column(columnDefinition = "varchar(128) comment '活动室名称'")
	private String roomName;
	private String tuserTeamName;
	@Column(columnDefinition = "int(1) comment '是否免费1：免费 2：收费 3：支付'")
	private Integer roomIsFree;
	@Column(columnDefinition = "varchar(256) comment '活动室图片URL'")
	private String roomPicUrl;
	private String openPeriod;
	private  String roomNo;
	@Column(columnDefinition = "varchar(64) comment '面积大小说明'")
	private  String roomArea;
	@Column(columnDefinition = "int(11) comment '容纳人数'")
	private  String roomCapacity;
	private String orderNum;
	private Date curDate;
	@Column(columnDefinition = "varchar(256) comment '收费标准'")
	private String roomFee;
	private String sysUserName;
	private Integer dayOfWeek;
	private Integer userType;
	private Integer tuserIsDisplay;
	private Integer ifNeedTuser;
	private String urgentOrderReason;
	private String roomAllocationId;
	/**
	 * 计划id
	 */
	private String planId;
	/**
	 * 开锁码
	 */
	private String unlockCode;

	private Integer useType;
	/**
	 * 取票次数
	 */
	private Integer printTicketTimes;
	/**
	 * 机器码
	 */
	private String machineCode;

	private String roomOrderId;
	private String venueUUID;


	/**
	 * 下单用户
	 */
	@OneToOne(targetEntity = CultureCloudMemberEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
	private CultureCloudMemberEntity member;



	@ManyToOne(targetEntity = CultureCloudVenueEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "venueId", referencedColumnName = "id", nullable = true)
	private CultureCloudVenueEntity venue;









}
