package com.foshan.entity.cultureCloud;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_culture_cloud_activity")
@org.hibernate.annotations.Table(appliesTo = "t_culture_cloud_activity",comment="文化活动") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CultureCloudActivityEntity extends CultureCloud {
    //活动表
	private static final long serialVersionUID = 8487008155728342198L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '活动uuid'")
	private String activityUUID;
	@Column(columnDefinition = "varchar(200) comment '活动名称'")
	private String activityName;
	@Column(columnDefinition = "varchar(256) comment '活动图标URL'")
	private String activityIconUrl;
	@Column(columnDefinition = "longtext comment '活动描述'")
	private String activityMemo;
	@Column(columnDefinition = "double comment '活动经度'")
	private BigDecimal activityLon;
	@Column(columnDefinition = "double comment '活动纬度'")
	private BigDecimal activityLat;
	@Column(columnDefinition = "varchar(128) comment '活动所在省'")
	private String activityProvince;
	@Column(columnDefinition = "varchar(128) comment '活动所在市'")
	private String activityCity;
	@Column(columnDefinition = "varchar(128) comment '活动所在区'")
	private String activityArea;
	@Column(columnDefinition = "varchar(128) comment '活动所在街道'")
	private String activityTown;
	@Column(columnDefinition = "varchar(128) comment '活动所在村居'")
	private String activityVillage;
	@Column(columnDefinition = "varchar(256) comment '活动所在详细地址'")
	private String activityAddress;
	@Column(columnDefinition = "varchar(128) comment '活动开放时间'")
	private String activityTime;
	@Column(columnDefinition = "varchar(256) comment '活动类型'")
	private String activityType;
	@Column(columnDefinition = "int(1) comment '自建活动编码 0省级自建活动(非自建活动),1市级自建活动,2区级自建活动,3街道级自建活动,4村居级自建活动'")
	private Integer createActivityCode;
	@Column(columnDefinition = "varchar(64) comment '活动联系人'")
	private String activityLiaison;
	@Column(columnDefinition = "varchar(256) comment '活动web网站url'")
	private String activityWebSite;
	@Column(columnDefinition = "varchar(128) comment '活动联系邮箱'")
	private String activityMail;
	@Column(columnDefinition = "varchar(64) comment '活动联系电话'")
	private String activityTel;
	@Column(columnDefinition = "varchar(32) comment '活动联系手机号码'")
	private String activityMobile;
	@Column(columnDefinition = "int(1) comment '是否免费1：免费 2：收费 3：支付'")
	private Integer activityIsFree;
    //对应原来的ACTIVITY_PRICE
	@Column(columnDefinition = "varchar(256) comment '收费说明'")  
	private String activityPaymentDesc;
	//对应原来的ACTIVITY_PAY_PRICE
	private BigDecimal activityPayPrice;
	@Column(columnDefinition = "varchar(128) comment '活动门票价格'")
	private String activityPrice;
	@Column(columnDefinition = "int(1) comment '是否删除 1-未删除 2-删除 3-下架'")
	private Integer activityIsDel;
	@Column(columnDefinition = "int(1) comment '状态 0-未审核 1-草稿 2-已审核 3-个人发布活动待审核 5-回收站 6-已发布 7-个人审核未通过 8-Y码活动'")
	private Integer activityState;
	//对应原来ACTIVITY_SUPPLEMENT_TYPE
	@Column(columnDefinition = "int(1) comment '活动预订类型 1.不可预订 2.直接前往 3.电话预约'")
	private Integer activityReservationType;
	@Column(columnDefinition = "int(1) comment '活动是否预定 1：否 2：是'")
	private Integer activityIsReservation;
	@Column(columnDefinition = "int(11) comment '可预订人数'")
	private Integer activityReservationCount;
	@Column(columnDefinition = "varchar(256) comment '活动开始时间'")
	private String activityStartTime;
	@Column(columnDefinition = "varchar(256) comment '活动结束时间'")
	private String activityEndTime;
	@Column(columnDefinition = "longtext comment '购票须知'")
	private String activityNotice;
	@Column(columnDefinition = "text comment '活动详情'")
	private String activityContent;
	@Column(columnDefinition = "varchar(256) comment '了平台跳转链接'")
	private String activitySysUrl;
	@Column(columnDefinition = "varchar(2) comment '是否在线售票 Y-是 N-否'")
	private String activitySalesOnline;
	@Column(columnDefinition = "int(11) comment '每个场次的放票数量'")
	private Integer eventCount;
	//对应原来EVENT_COUNT
	@Column(columnDefinition = "int(11) comment '每个场次的放票数量'")
	private Integer activityEventCode;
	@Column(columnDefinition = "int(1) comment '补充活动类型 1.不可预订 2.直接前往 3.电话预约'")
	private Integer activitySupplementType;
	@Column(columnDefinition = "Timestamp comment '活动推荐时间'")
	private Timestamp activityRecommendTime;
	@Column(columnDefinition = "Timestamp comment '活动信息创建时间'")
	private Timestamp activityCreateTime;
	@Column(columnDefinition = "Timestamp comment '活动信息更新时间'")
	private Timestamp activityUpdateTime;
	@Column(columnDefinition = "varchar(5) comment '活动信息更新用户'")
	private String activityUpdateUser;
	@Column(columnDefinition = "Timestamp comment '活动信息发布时间'")
	private Timestamp publicTime;
	@Column(columnDefinition = "Timestamp comment '活动信息release时间'")
	private Timestamp activityReleaseTime;
	@Column(columnDefinition = "varchar(128) comment '主办单位'")
	private String activityHost;
	@Column(columnDefinition = "varchar(128) comment '承办单位'")
	private String activityOrganizer;
	@Column(columnDefinition = "varchar(255) comment '协办单位'")
	private String activityCoorganizer;
	@Column(columnDefinition = "varchar(128) comment '演出单位'")
	private String activityPerformed;
	@Column(columnDefinition = "varchar(256) comment '友情提示'")
	private String activityPrompt;
	@Column(columnDefinition = "varchar(128) comment '主讲人'")
	private String activitySpeaker;
	@Column(columnDefinition = "text comment '活动附件'")
	private String activityAttachment;
	@Column(columnDefinition = "int(1) comment '是否为个人活动 0为否 1 是 空也为否'")
	private Integer activityPersonal;
	@Column(columnDefinition = "varchar(2) comment '账号订票设置（Y默认,N自定义）'")
	private String activityTicketSettings;
	@Column(columnDefinition = "int(11) comment '订票限制次数'")
	private Integer activityTicketNumber;
	@Column(columnDefinition = "int(11) comment '订票张数'")
	private Integer activityTicketCount;
	@Column(columnDefinition = "Timestamp comment '发布时间'")
	private Timestamp activityPublicTime;
	@Column(columnDefinition = "int(1) comment '是否需要买票时添加身份证号 0：不需要  1：需要，一证多票 2：需要，一证一票'")
	private Integer activityIdentityCard;
	@Column(columnDefinition = "varchar(2) comment '活动是否推荐：N-否 Y-是'")
	private String activityRecommend;
	@Column(columnDefinition = "int(1) comment '短信模板类型（0：取票码入场；1：纸质票入场；2：入场凭证入场；3：实名认证入场；4：拼团入场）'")
	private Integer activitySmsType;
	@Column(columnDefinition = "varchar(32) comment '活动标签：1.文广体系 2.独立商家 3.其他'")
	private String activityDeptLabel;
	@Column(columnDefinition = "varchar(256) comment '收费备注'")
	private String activityPriceNotice;
	@Column(columnDefinition = "int(11) comment '活动排序'")
	private Integer activitySort;
	@Column(columnDefinition = "int(1) comment '是否置顶(1:是;2:否)'")
	private Integer activityIsTop;
	@Column(columnDefinition = "Timestamp comment '置顶时间'")
	private Timestamp activityTopTime;
	@Column(columnDefinition = "text comment '活动剩余票数'")
	private String availableCount;
	@Column(columnDefinition = "varchar(256) comment '场次'")
	private String eventTime;
	@Column(columnDefinition = "varchar(256) comment '日期'")
	private String eventDate;
	@Column(columnDefinition = "varchar(256) comment '权限信息--部门标示'")
	private String activityDept;
	@Column(columnDefinition = "varchar(256) comment '关键词'")
	private String keyword;

	private Integer customizeMethod;

	private Date cancelTime;
	private Date cancelEndTime;
	private Integer writtenOffTime;
	private Integer beforeStartMinute;
	private Integer afterStartMinute;
	private Integer detailTimeLimitMinutes;
	private Integer timeLimitOfPickingUpTickets;

	private String seatIds;

	private String spikeTimes;

	private String endTimePoint;

	private Short orderPayStatus;

	private String activityOrderId;

	private String showOffline;
	@Column(columnDefinition = "int(1) comment '是否是秒杀   0：非秒杀  1:秒杀'")
	private Integer spikeType;

	private String orderPrice;
	@Column(columnDefinition = "int(1) comment '是否是单场次活动 0：非单场次 1：单场次'")
	private Integer singleEvent;
	@Column(columnDefinition = "int(1) comment '活动是否详情 1：是 2：否'")
	private Integer activityIsDetails;
	@Column(columnDefinition = "int(1) comment '活动是否推荐：1-否 2-是'")
	private Integer activityIsRecommend;
	@Column(columnDefinition = "varchar(16) comment '艺术类型'")
	private String activityArtType;

	private String resourceImgUrl;

	private String resourceVideoUrl;

	private String activityProfile;

	@Column(columnDefinition = "Timestamp comment '活动报名开始时间（非秒杀）'")
	private Timestamp signStartTime;


	@Column(columnDefinition = "Timestamp comment '活动报名结束时间（非秒杀）'")
	private Timestamp signEndTime;

	/**
	 * 固定电话
	 */
	private  String fixedTel;

	/*活动地点*/
	private String activitySite;
	//活动时间具体描述
	private String activityTimeDes;
	private String minAge;

	private String maxAge;
	//来源
	private String activityPublishFrom;

	/**
	 * 活动场次具体时间
	 **/
	private String eventDateTimes;
	

	@ManyToMany(targetEntity = CultureCloudVenueEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_activity_venue", joinColumns = @JoinColumn(name = "activityId", referencedColumnName = "id"), inverseJoinColumns =
	@JoinColumn(name = "venueId", referencedColumnName = "id"))
	@JsonIgnore
	private List<CultureCloudVenueEntity> venueList = new ArrayList<CultureCloudVenueEntity>();

	@OneToMany(targetEntity = CultureCloudActivityEventEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "eventId", referencedColumnName = "id", nullable = true, foreignKey = @ForeignKey(name = "none", value =
			ConstraintMode.NO_CONSTRAINT))
	@JsonIgnore
	private List<CultureCloudActivityEventEntity> eventList = new ArrayList<>();
	
	@ManyToOne(targetEntity = CultureCloudVenueSeatTemplateEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "seatTemplateId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CultureCloudVenueSeatTemplateEntity venueSeatTemplate;
	
	@OneToMany(targetEntity = CultureCloudVenueSeatEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "activityId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CultureCloudVenueSeatEntity> seatList = new ArrayList<CultureCloudVenueSeatEntity>();
	
	
	@ManyToMany(targetEntity = CultureCloudTagEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_culture_cloud_activity_tag", joinColumns = @JoinColumn(name = "activityId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "tagId", referencedColumnName = "id"))
	@JsonIgnore
	private List<CultureCloudTagEntity> tagList = new ArrayList<CultureCloudTagEntity>();
	
	@ManyToOne(targetEntity = CultureCloudDepartmentEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "departmentId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CultureCloudDepartmentEntity department;

	@Column(columnDefinition = "int(1) default 0 comment '是否需要同步到文化广东：0-不需要，1-需要'")
	private Integer needSync = 0;

	@Column(columnDefinition = "int(1) default 0 comment '是否已同步到文化广东：0-未同步，1-已同步 2-已修改未同步 3-已修改'")
	private Integer hasSynced = 0;
}
