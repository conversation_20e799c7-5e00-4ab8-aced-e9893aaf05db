package com.foshan.entity.cultureCloud;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_culture_cloud_activity_seat")
@org.hibernate.annotations.Table(appliesTo = "t_culture_cloud_activity_seat",comment="活动座位信息")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CultureCloudActivitySeatEntity extends CultureCloud {
	//活动座位信息表
	private static final long serialVersionUID = -1763140213686059242L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(24) comment 'seatUUID'")
	private String seatId;
	@Column(columnDefinition = "varchar(32) comment '座位区域'")
	private String seatArea;
	@Column(columnDefinition = "int(2) comment '座位排数'")
	private Integer seatRow;
	@Column(columnDefinition = "int(2) comment '座位列数'")
	private Integer seatColumn;
	@Column(columnDefinition = "varchar(32) comment '座位编号'")
	private String seatCode;
	@Column(columnDefinition = "int(1) comment '座位状态 1-正常 2-待修 3-不存在 4-vip'")
	private Integer seatStatus;
	@Column(columnDefinition = "Timestamp comment '创建时间'")
	private Date seatCreateTime;
	@Column(columnDefinition = "varchar(32) comment '创建用户'")
	private String seatCreateUser;
	@Column(columnDefinition = "Timestamp comment '更新时间'")
	private Date seatUpdateTime;
	@Column(columnDefinition = "varchar(32) comment '更新用户'")
	private String seatUpdateUser;
	@Column(columnDefinition = "varchar(32) comment '场馆模板ID'")
	private String templateId;
	@Column(columnDefinition = "varchar(32) comment '座位模板ID'")
	private String seatVal;
	//总票数
	private Integer totalTicket;

	private Integer seatIsSold;

	@ManyToOne(targetEntity = CultureCloudActivityEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "activityId", referencedColumnName = "id", nullable = true)
	private CultureCloudActivityEntity activity;

	@ManyToOne(targetEntity = CultureCloudActivityEventEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "eventId", referencedColumnName = "id", nullable = true)
	private CultureCloudActivityEventEntity event;
}
