package com.foshan.entity.cultureCloud.dto;

import lombok.Getter;
import lombok.Setter;

import java.sql.Date;
import java.util.List;

@Getter
@Setter
public class CultureCloudVenueTicketEventDto {
    private Integer hasOrderCount;	//当前用户已经预定场数
    private Integer id;
    private Integer venueTicketEventId;
    private Integer venueId;
    private String eventDate;
    private Integer eventWeekday;
    private String timePeriod;
    private Integer maxPerson;
    private Integer availableCount;
    private Integer openStatus;
    private String createTime;
    private String updateTime;
    private String updateUser;
    private List<CultureCloudVenueTicketEventTimePeriodDto> timePeriodList;
}
