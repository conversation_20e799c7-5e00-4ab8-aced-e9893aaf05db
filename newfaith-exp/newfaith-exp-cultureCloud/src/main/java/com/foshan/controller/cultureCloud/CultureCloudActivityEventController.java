package com.foshan.controller.cultureCloud;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.cultureCloud.request.ActivityReq;
import com.foshan.form.cultureCloud.response.cultureCloudActivityEvent.GetCultureCloudActivityEventListRes;
import com.foshan.form.cultureCloud.response.cultureCloudActivityEvent.QueryActivityEventListRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "活动场次模块")
@RestController
public class CultureCloudActivityEventController extends BaseCultureCloudController{


    // 获取活动场次列表
    @ApiOperation(value = "获取活动场次列表(getActivityEventList)", httpMethod = "POST", notes = "获取活动场次列表；")
    @ResponseBody
    @RequestMapping(value = "/getActivityEventList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCultureCloudActivityEventListRes getActivityEventList(@RequestBody ActivityReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GetCultureCloudActivityEventListRes res = (GetCultureCloudActivityEventListRes) cultureCloudActivityEventService.getActivityEventList(req);
        return res;
    }


    // 获取活动场次列表
    @ApiOperation(value = "app用户端活动场次列表(queryActivityEventList)", httpMethod = "POST", notes = "app用户端活动场次列表；")
    @ResponseBody
    @RequestMapping(value = "/queryActivityEventList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public QueryActivityEventListRes queryActivityEventList(@RequestBody ActivityReq req, HttpServletRequest request)
            throws JsonProcessingException {
        QueryActivityEventListRes res = (QueryActivityEventListRes) cultureCloudActivityEventService.queryActivityEventList(req);
        return res;
    }


}
