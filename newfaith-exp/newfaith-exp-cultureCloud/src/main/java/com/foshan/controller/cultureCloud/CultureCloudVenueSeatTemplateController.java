package com.foshan.controller.cultureCloud;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.cultureCloud.request.CultureCloudVenueSeatTemplateReq;
import com.foshan.form.cultureCloud.response.cultureCloudVenueSeatTemplate.AddVenueSeatTemplateRes;
import com.foshan.form.cultureCloud.response.cultureCloudVenueSeatTemplate.GetVenueSeatTemplateInfoRes;
import com.foshan.form.cultureCloud.response.cultureCloudVenueSeatTemplate.GetVenueSeatTemplateListRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "座位模板")
@RestController
public class CultureCloudVenueSeatTemplateController extends BaseCultureCloudController{


    // 新增座位模板
    @ApiOperation(value = "新增座位模板(addVenueSeatTemplate)", httpMethod = "POST", notes = "新增座位模板；")
    @ResponseBody
    @RequestMapping(value = "/addVenueSeatTemplate", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public AddVenueSeatTemplateRes addVenueSeatTemplate(@RequestBody CultureCloudVenueSeatTemplateReq req, HttpServletRequest request)
            throws JsonProcessingException {
    	AddVenueSeatTemplateRes res = (AddVenueSeatTemplateRes) cultureCloudVenueSeatTemplateService.addVenueSeatTemplate(req);
        return res;
    }
    
    // 修改座位模板
    @ApiOperation(value = "修改座位模板(modifyVenueSeatTemplate)", httpMethod = "POST", notes = "修改座位模板；")
    @ResponseBody
    @RequestMapping(value = "/modifyVenueSeatTemplate", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public AddVenueSeatTemplateRes modifyVenueSeatTemplate(@RequestBody CultureCloudVenueSeatTemplateReq req, HttpServletRequest request)
            throws JsonProcessingException {
    	AddVenueSeatTemplateRes res = (AddVenueSeatTemplateRes) cultureCloudVenueSeatTemplateService.modifyVenueSeatTemplate(req);
        return res;
    }
    
    // 删除座位模板
    @ApiOperation(value = "删除座位模板(deleteVenueSeatTemplate)", httpMethod = "POST", notes = "删除座位模板；")
    @ResponseBody
    @RequestMapping(value = "/deleteVenueSeatTemplate", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse deleteVenueSeatTemplate(@RequestBody CultureCloudVenueSeatTemplateReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GenericResponse res = (GenericResponse) cultureCloudVenueSeatTemplateService.deleteVenueSeatTemplate(req);
        return res;
    }
    
    // 获取座位模板详情
    @ApiOperation(value = "获取座位模板详情(getVenueSeatTemplateInfo)", httpMethod = "POST", notes = "获取座位模板详情；")
    @ResponseBody
    @RequestMapping(value = "/getVenueSeatTemplateInfo", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetVenueSeatTemplateInfoRes getVenueSeatTemplateInfo(@RequestBody CultureCloudVenueSeatTemplateReq req, HttpServletRequest request)
            throws JsonProcessingException {
    	GetVenueSeatTemplateInfoRes res = (GetVenueSeatTemplateInfoRes) cultureCloudVenueSeatTemplateService.getVenueSeatTemplateInfo(req);
        return res;
    }
    
    // 获取座位模板列表
    @ApiOperation(value = "获取座位模板列表(getVenueSeatTemplateList)", httpMethod = "POST", notes = "获取座位模板列表；")
    @ResponseBody
    @RequestMapping(value = "/getVenueSeatTemplateList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetVenueSeatTemplateListRes getVenueSeatTemplateList(@RequestBody CultureCloudVenueSeatTemplateReq req, HttpServletRequest request)
            throws JsonProcessingException {
    	GetVenueSeatTemplateListRes res = (GetVenueSeatTemplateListRes) cultureCloudVenueSeatTemplateService.getVenueSeatTemplateList(req);
        return res;
    }

}
