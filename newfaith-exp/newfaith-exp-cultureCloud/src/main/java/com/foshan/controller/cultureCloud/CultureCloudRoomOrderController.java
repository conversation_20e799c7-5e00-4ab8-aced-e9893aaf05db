package com.foshan.controller.cultureCloud;

import com.foshan.form.cultureCloud.request.CultureCloudRoomOrderReq;

import com.foshan.form.cultureCloud.request.CultureCloudWebRoomOrderReq;
import com.foshan.form.cultureCloud.response.cultureCloudRoomOrder.GetCultureCloudRoomOrderListRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "活动室订单模块")
@RestController
public class CultureCloudRoomOrderController extends BaseCultureCloudController{


    // 获取活动室待审核订单列表 后台用
    @ApiOperation(value = "获取活动室待审核订单列表(roomOrderCheckIndex)", httpMethod = "POST", notes = "获取活动室待审核订单列表；")
    @ResponseBody
    @RequestMapping(value = "/roomOrderCheckIndex", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCultureCloudRoomOrderListRes roomOrderCheckIndex(@RequestBody CultureCloudRoomOrderReq req, HttpServletRequest request){
        GetCultureCloudRoomOrderListRes res = (GetCultureCloudRoomOrderListRes) cultureCloudRoomOrderService.roomOrderCheckIndex(req);
        return res;
    }

    // 活动室订单信息（当前未过期订单）后台用
    @ApiOperation(value = "活动室订单信息（当前未过期订单）后台用(roomOrderIndex)", httpMethod = "POST", notes = "活动室订单信息（当前未过期订单）后台用；")
    @ResponseBody
    @RequestMapping(value = "/roomOrderIndex", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCultureCloudRoomOrderListRes roomOrderIndex(@RequestBody CultureCloudRoomOrderReq req, HttpServletRequest request){
        GetCultureCloudRoomOrderListRes res = (GetCultureCloudRoomOrderListRes) cultureCloudRoomOrderService.roomOrderIndex(req);
        return res;
    }

    // 用户活动室历史订单信息（过期订单，即历史订单）后台用
    @ApiOperation(value = "用户活动室历史订单信息（过期订单，即历史订单）后台用(roomOrderHistoryIndex)", httpMethod = "POST", notes = "用户活动室历史订单信息（过期订单，即历史订单）后台用；")
    @ResponseBody
    @RequestMapping(value = "/roomOrderHistoryIndex", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCultureCloudRoomOrderListRes roomOrderHistoryIndex(@RequestBody CultureCloudRoomOrderReq req, HttpServletRequest request){
        GetCultureCloudRoomOrderListRes res = (GetCultureCloudRoomOrderListRes) cultureCloudRoomOrderService.roomOrderHistoryIndex(req);
        return res;
    }

    // PC个人中心，用户场馆订单 前端用
    @ApiOperation(value = "PC个人中心，用户场馆订单 前端用(webUserRoomOrder)", httpMethod = "POST", notes = "PC个人中心，用户场馆订单 前端用；")
    @ResponseBody
    @RequestMapping(value = "/webUserRoomOrder", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCultureCloudRoomOrderListRes webUserRoomOrder(@RequestBody CultureCloudWebRoomOrderReq req, HttpServletRequest request){
        GetCultureCloudRoomOrderListRes res = (GetCultureCloudRoomOrderListRes) cultureCloudRoomOrderService.webUserRoomOrder(req);
        return res;
    }

    // PC个人中心，用户活动室待审核订单 前端用
    @ApiOperation(value = "PC个人中心，用户活动室待审核订单 前端用(webUserCheckRoomOrder)", httpMethod = "POST", notes = "PC个人中心，用户活动室待审核订单 前端用；")
    @ResponseBody
    @RequestMapping(value = "/webUserCheckRoomOrder", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCultureCloudRoomOrderListRes webUserCheckRoomOrder(@RequestBody CultureCloudRoomOrderReq req, HttpServletRequest request){
        GetCultureCloudRoomOrderListRes res = (GetCultureCloudRoomOrderListRes) cultureCloudRoomOrderService.webUserCheckRoomOrder(req);
        return res;
    }

    // 用户取消活动室订单 前端用
    @ApiOperation(value = "用户取消活动室订单 前端用(cancelRoomOrder)", httpMethod = "POST", notes = "用户取消活动室订单 前端用；")
    @ResponseBody
    @RequestMapping(value = "/cancelRoomOrder", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse cancelRoomOrder(@RequestBody CultureCloudRoomOrderReq req, HttpServletRequest request){
        GenericResponse res = (GenericResponse) cultureCloudRoomOrderService.cancelRoomOrder(req);
        return res;
    }


    // 审核活动室订单 后端用
    @ApiOperation(value = "审核活动室订单 后端用(checkRoomOrder)", httpMethod = "POST", notes = "审核活动室订单 后端用；")
    @ResponseBody
    @RequestMapping(value = "/checkRoomOrder", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse checkRoomOrder(@RequestBody CultureCloudRoomOrderReq req, HttpServletRequest request){
        GenericResponse res = (GenericResponse) cultureCloudRoomOrderService.checkRoomOrder(req);
        return res;
    }
}
