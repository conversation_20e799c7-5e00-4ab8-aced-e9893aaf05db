package com.foshan.controller.cultureCloud;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.cultureCloud.request.CultureCloudSensitiveWordsReq;
import com.foshan.form.cultureCloud.response.cultureCloudSensitiveWords.GetCultureCloudSensitiveWordsInfoRes;
import com.foshan.form.cultureCloud.response.cultureCloudSensitiveWords.GetCultureCloudSensitiveWordsListRes;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api(tags = "敏感字模块")
@RestController
public class CultureCloudSensitiveWordsController extends BaseCultureCloudController {

	// 获取敏感字列表
	@ApiOperation(value = "获取敏感字列表(getCultureCloudSensitiveWordsList)", httpMethod = "POST", notes = "获取敏感字列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCultureCloudSensitiveWordsList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCultureCloudSensitiveWordsListRes getCultureCloudSensitiveWordsList(@RequestBody CultureCloudSensitiveWordsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCultureCloudSensitiveWordsListRes res = (GetCultureCloudSensitiveWordsListRes) cultureCloudSensitiveWordsService.getCultureCloudSensitiveWordsList(req);
		return res;
	}
	
	// 新增敏感字列表
	@ApiOperation(value = "新增敏感字列表(addCultureCloudSensitiveWords)", httpMethod = "POST", notes = "新增敏感字列表，sensitiveWord不能这空；")
	@ResponseBody
	@RequestMapping(value = "/addCultureCloudSensitiveWords", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addCultureCloudSensitiveWords(@RequestBody CultureCloudSensitiveWordsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) cultureCloudSensitiveWordsService.addCultureCloudSensitiveWords(req);
		return res;
	}
	
	
	// 修改敏感字列表
	@ApiOperation(value = "修改敏感字列表(modifyCultureCloudSensitiveWords)", httpMethod = "POST", notes = "修改敏感字列表，sensitiveWordsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCultureCloudSensitiveWords", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyCultureCloudSensitiveWords(@RequestBody CultureCloudSensitiveWordsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) cultureCloudSensitiveWordsService.modifyCultureCloudSensitiveWords(req);
		return res;
	}
	
	
	// 删除敏感字列表
	@ApiOperation(value = "删除敏感字列表(deleteCultureCloudSensitiveWords)", httpMethod = "POST", notes = "删除敏感字列表，sensitiveWordsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCultureCloudSensitiveWords", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCultureCloudSensitiveWords(@RequestBody CultureCloudSensitiveWordsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) cultureCloudSensitiveWordsService.deleteCultureCloudSensitiveWords(req);
		return res;
	}
	
	
	// 获取敏感字详情
	@ApiOperation(value = "获取敏感字详情(getCultureCloudSensitiveWordsInfo)", httpMethod = "POST", notes = "获取敏感字详情，sensitiveWordsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCultureCloudSensitiveWordsInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCultureCloudSensitiveWordsInfoRes getCultureCloudSensitiveWordsInfo(@RequestBody CultureCloudSensitiveWordsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCultureCloudSensitiveWordsInfoRes res = (GetCultureCloudSensitiveWordsInfoRes) cultureCloudSensitiveWordsService.getCultureCloudSensitiveWordsInfo(req);
		return res;
	}
	
	@PostConstruct
	public void initSensitiveWords() {
		log.info("---开始装载敏感词数据");
		cultureCloudSensitiveWordsService.initSensitiveWords();
		log.info("装载敏感词数据结束");
	}
	
}
