package com.foshan.controller.cultureCloud;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.cultureCloud.request.CultureCloudVenueTicketOrderDtoReq;
import com.foshan.form.cultureCloud.request.CultureCloudVenueTicketOrderReq;

import com.foshan.form.cultureCloud.response.cultureCloudVenueTicketOrder.GetCultureCloudVenueOrderInfoRes;
import com.foshan.form.cultureCloud.response.cultureCloudVenueTicketOrder.GetCultureCloudVenueOrderListRes;
import com.foshan.form.cultureCloud.response.cultureCloudVenueTicketOrder.GetCultureCloudVenueTicketOrderInfoRes;
import com.foshan.form.cultureCloud.response.cultureCloudVenueTicketOrder.GetCultureCloudVenueTicketOrderListRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "场馆预约订单模块")
@RestController
public class CultureCloudVenueTicketOrderController extends BaseCultureCloudController{



    // 场馆订单列表-后台用
    @ApiOperation(value = "场馆订单列表-后台用(venueTicketOrderList)", httpMethod = "POST", notes = "场馆订单列表-后台用；")
    @ResponseBody
    @RequestMapping(value = "/venueTicketOrderList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCultureCloudVenueTicketOrderListRes venueTicketOrderList(@RequestBody CultureCloudVenueTicketOrderDtoReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GetCultureCloudVenueTicketOrderListRes res =
                (GetCultureCloudVenueTicketOrderListRes) cultureCloudVenueTicketOrderService.venueTicketOrderList(req);
         return res;
    }

    // 预约场馆订单列表
    @ApiOperation(value = "预约场馆订单列表(getVenueTicketOrderList)", httpMethod = "POST", notes = "预约场馆订单列表；")
    @ResponseBody
    @RequestMapping(value = "/getVenueTicketOrderList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCultureCloudVenueTicketOrderListRes getVenueTicketOrderList(@RequestBody CultureCloudVenueTicketOrderReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GetCultureCloudVenueTicketOrderListRes res =
                (GetCultureCloudVenueTicketOrderListRes) cultureCloudVenueTicketOrderService.getVenueTicketOrderList(req);
        return res;
    }


    // 预约场馆订单详情
    @ApiOperation(value = "预约场馆订单详情(getVenueTicketOrderInfo)", httpMethod = "POST", notes = "预约场馆订单详情；")
    @ResponseBody
    @RequestMapping(value = "/getVenueTicketOrderInfo", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCultureCloudVenueTicketOrderInfoRes getVenueTicketOrderInfo(@RequestBody CultureCloudVenueTicketOrderReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GetCultureCloudVenueTicketOrderInfoRes res =
                (GetCultureCloudVenueTicketOrderInfoRes) cultureCloudVenueTicketOrderService.getVenueTicketOrderInfo(req);
        return res;
    }

    // 改签预约场馆订单
    @ApiOperation(value = "改签预约场馆订单(changeVenueTicketOrder)", httpMethod = "POST", notes = "改签预约场馆订单；")
    @ResponseBody
    @RequestMapping(value = "/changeVenueTicketOrder", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse changeVenueTicketOrder(@RequestBody CultureCloudVenueTicketOrderReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GenericResponse res =
                (GenericResponse) cultureCloudVenueTicketOrderService.changeVenueTicketOrder(req);
        return res;
    }

    // 取消预约场馆订单
    @ApiOperation(value = "取消预约场馆订单(cancelVenueTicketOrder)", httpMethod = "POST", notes = "取消预约场馆订单；")
    @ResponseBody
    @RequestMapping(value = "/cancelVenueTicketOrder", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse cancelVenueTicketOrder(@RequestBody CultureCloudVenueTicketOrderReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GenericResponse res =
                (GenericResponse) cultureCloudVenueTicketOrderService.cancelVenueTicketOrder(req);
        return res;
    }

    // 核销预约场馆订单
    @ApiOperation(value = "核销预约场馆订单(verifyVenueTicketOrder)", httpMethod = "POST", notes = "核销预约场馆订单；")
    @ResponseBody
    @RequestMapping(value = "/verifyVenueTicketOrder", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse verifyVenueTicketOrder(@RequestBody CultureCloudVenueTicketOrderReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GenericResponse res =
                (GenericResponse) cultureCloudVenueTicketOrderService.verifyVenueTicketOrder(req);
        return res;
    }

    // 待审核订单列表（待审核未过期）
    @ApiOperation(value = "待审核订单列表（待审核未过期）(getVenueOrderCheck)", httpMethod = "POST", notes = "待审核订单列表（待审核未过期）；")
    @ResponseBody
    @RequestMapping(value = "/getVenueOrderCheck", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCultureCloudVenueOrderListRes getVenueOrderCheck(@RequestBody CultureCloudVenueTicketOrderReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GetCultureCloudVenueOrderListRes res =
                (GetCultureCloudVenueOrderListRes) cultureCloudVenueTicketOrderService.getVenueOrderCheck(req);
        return res;
    }

    // 当前订单列表（审核通过未过期）
    @ApiOperation(value = "当前订单列表（审核通过未过期）(venueOrderIndex)", httpMethod = "POST", notes = "当前订单列表（审核通过未过期）；")
    @ResponseBody
    @RequestMapping(value = "/venueOrderIndex", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCultureCloudVenueOrderListRes venueOrderIndex(@RequestBody CultureCloudVenueTicketOrderReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GetCultureCloudVenueOrderListRes res =
                (GetCultureCloudVenueOrderListRes) cultureCloudVenueTicketOrderService.venueOrderIndex(req);
        return res;
    }

    // 历史订单列表（已过期，审核不通过，取消）
    @ApiOperation(value = "历史订单列表（已过期，审核不通过，取消）(venueOrderHistoryIndex)", httpMethod = "POST", notes = "历史订单列表（已过期，审核不通过，取消）；")
    @ResponseBody
    @RequestMapping(value = "/venueOrderHistoryIndex", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCultureCloudVenueOrderListRes venueOrderHistoryIndex(@RequestBody CultureCloudVenueTicketOrderReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GetCultureCloudVenueOrderListRes res =
                (GetCultureCloudVenueOrderListRes) cultureCloudVenueTicketOrderService.venueOrderHistoryIndex(req);
        return res;
    }

    // 订单详情页
    @ApiOperation(value = "订单详情页(venueOrderDetail)", httpMethod = "POST", notes = "订单详情页；")
    @ResponseBody
    @RequestMapping(value = "/venueOrderDetail", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCultureCloudVenueOrderInfoRes venueOrderDetail(@RequestBody CultureCloudVenueTicketOrderReq req, HttpServletRequest request)
    {
        GetCultureCloudVenueOrderInfoRes res =
                (GetCultureCloudVenueOrderInfoRes) cultureCloudVenueTicketOrderService.venueOrderDetail(req);
        return res;
    }

}
