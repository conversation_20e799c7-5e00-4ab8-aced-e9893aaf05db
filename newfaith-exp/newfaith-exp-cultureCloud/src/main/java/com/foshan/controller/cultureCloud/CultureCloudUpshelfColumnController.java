package com.foshan.controller.cultureCloud;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.cultureCloud.request.CultureCloudUpshelfColumnReq;
import com.foshan.form.request.UpshelfColumnReq;
import com.foshan.form.response.upshelfColumn.AddUpshelfColumnRes;
import com.foshan.form.response.upshelfColumn.DeleteUpshelfColumnRes;
import com.foshan.form.response.upshelfColumn.GetUpshelfColumnInfoRes;
import com.foshan.form.response.upshelfColumn.GetUpshelfColumnListRes;
import com.foshan.form.response.upshelfColumn.ModifyUpshelfColumnRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "文化云上架模块")
@RestController
public class CultureCloudUpshelfColumnController extends BaseCultureCloudController {

	// 获取产品类别列表
	@ApiOperation(value = "获取栏目上架列表(getCultureCloudUpshelfColumnList)", httpMethod = "POST", notes = "获取栏目上架列表")
	@ResponseBody
	@RequestMapping(value = "/getCultureCloudUpshelfColumnList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetUpshelfColumnListRes getCultureCloudUpshelfColumnList(@RequestBody CultureCloudUpshelfColumnReq req, HttpServletRequest request) throws JsonProcessingException {

		GetUpshelfColumnListRes res = (GetUpshelfColumnListRes) cultureCloudUpshelfColumnService.getCultureCloudUpshelfColumnList(req);
		return res;
	}
	
	// 获取产品信息
	@ApiOperation(value = "获取栏目上架详情信息(getCultureCloudUpshelfColumnInfo)", httpMethod = "POST", notes = "获取栏目上架详情信息(<p>1：specificationId和productId不能同时为空；")
	@ResponseBody
	@RequestMapping(value = "/getCultureCloudUpshelfColumnInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetUpshelfColumnInfoRes getCultureCloudUpshelfColumnInfo(@RequestBody CultureCloudUpshelfColumnReq req, HttpServletRequest request) throws JsonProcessingException {

		GetUpshelfColumnInfoRes res = (GetUpshelfColumnInfoRes) cultureCloudUpshelfColumnService.getCultureCloudUpshelfColumnInfo(req);
		return res;
	}
	
	// 修改栏目上架媒资
	@ApiOperation(value = "修改栏目上架媒资(modifyCultureCloudUpshelfColumn)", httpMethod = "POST", notes = "修改栏目上架媒资(<p>1：UpshelfColumnId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCultureCloudUpshelfColumn", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyUpshelfColumnRes modifyCultureCloudUpshelfColumn(@RequestBody CultureCloudUpshelfColumnReq req, HttpServletRequest request) throws JsonProcessingException {

		ModifyUpshelfColumnRes res = (ModifyUpshelfColumnRes) cultureCloudUpshelfColumnService.modifyCultureCloudUpshelfColumn(req);
		return res;
	}
	
	// 新增栏目上架资源
	@ApiOperation(value = "新增栏目上架资源(addCultureCloudUpshelfColumn)", httpMethod = "POST", notes = "新增栏目上架资源(<p>1：")
	@ResponseBody
	@RequestMapping(value = "/addCultureCloudUpshelfColumn", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddUpshelfColumnRes addCultureCloudUpshelfColumn(@RequestBody CultureCloudUpshelfColumnReq req, HttpServletRequest request) throws JsonProcessingException {

		AddUpshelfColumnRes res = (AddUpshelfColumnRes) cultureCloudUpshelfColumnService.addCultureCloudUpshelfColumn(req);
		return res;
	}
	
	
	// 删除栏目上架资源
	@ApiOperation(value = "删除栏目上架资源(deleteCultureCloudUpshelfColumn)", httpMethod = "POST", notes = "删除栏目上架资源(<p>1：UpshelfColumnId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCultureCloudUpshelfColumn", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DeleteUpshelfColumnRes deleteCultureCloudUpshelfColumn(@RequestBody CultureCloudUpshelfColumnReq req, HttpServletRequest request) throws JsonProcessingException {

		DeleteUpshelfColumnRes res = (DeleteUpshelfColumnRes) cultureCloudUpshelfColumnService.deleteCultureCloudUpshelfColumn(req);
		return res;
	}
	
	
	// 修改排序
	@ApiOperation(value = "修改排序(setCultureCloudUpshelfColumnOrderNumber)", httpMethod = "POST", notes = "修改排序(<p>1：UpshelfColumnId和OrderNumber不能同时为空；")
	@ResponseBody
	@RequestMapping(value = "/setCultureCloudUpshelfColumnOrderNumber", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyUpshelfColumnRes setCultureCloudUpshelfColumnOrderNumber(@RequestBody CultureCloudUpshelfColumnReq req, HttpServletRequest request) throws JsonProcessingException {

		ModifyUpshelfColumnRes res = (ModifyUpshelfColumnRes) cultureCloudUpshelfColumnService.setCultureCloudUpshelfColumnOrderNumber(req);
		return res;
	}
	
	// 修改上架时间
	@ApiOperation(value = "修改上架时间(setCultureCloudUpshelfColumnUpshelfTime)", httpMethod = "POST", notes = "修改上架时间(<p>1：UpshelfColumnId和OrderNumber不能同时为空；")
	@ResponseBody
	@RequestMapping(value = "/setCultureCloudUpshelfColumnUpshelfTime", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyUpshelfColumnRes setCultureCloudUpshelfColumnUpshelfTime(@RequestBody CultureCloudUpshelfColumnReq req, HttpServletRequest request) throws JsonProcessingException {

		ModifyUpshelfColumnRes res = (ModifyUpshelfColumnRes) cultureCloudUpshelfColumnService.setCultureCloudUpshelfColumnUpshelfTime(req);
		return res;
	}
	
	

}
