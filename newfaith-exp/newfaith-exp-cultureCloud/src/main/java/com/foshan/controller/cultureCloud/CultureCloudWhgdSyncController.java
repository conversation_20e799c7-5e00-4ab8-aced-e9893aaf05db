package com.foshan.controller.cultureCloud;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.cultureCloud.request.WhgdCancelOrderReq;
import com.foshan.form.cultureCloud.request.WhgdSyncReq;
import com.foshan.form.response.IResponse;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 文化广东同步控制器
 * 用于同步信息到文化广东平台
 */
@Api(tags = "文化广东同步模块")
@RestController
public class CultureCloudWhgdSyncController extends BaseCultureCloudController {

    /**
     * 同步活动信息到文化广东
     */
    @ApiOperation(value = "同步活动信息(syncActivityToWhgd)", httpMethod = "POST", notes = "同步活动信息到文化广东平台")
    @ResponseBody
    @RequestMapping(value = "/syncActivityToWhgd", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public IResponse syncActivityToWhgd(@RequestBody WhgdSyncReq req, HttpServletRequest request)
            throws JsonProcessingException {
        return cultureCloudWhgdSyncService.syncActivityToWhgd(req);
    }

    /**
     * 同步场馆信息到文化广东
     */
    @ApiOperation(value = "同步场馆信息(syncRoomToWhgd)", httpMethod = "POST", notes = "同步场馆信息到文化广东平台")
    @ResponseBody
    @RequestMapping(value = "/syncRoomToWhgd", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public IResponse syncRoomToWhgd(@RequestBody WhgdSyncReq req, HttpServletRequest request)
            throws JsonProcessingException {
        return cultureCloudWhgdSyncService.syncRoomToWhgd(req);
    }

    /**
     * 同步培训信息到文化广东
     */
    @ApiOperation(value = "同步培训信息(syncTrainingToWhgd)", httpMethod = "POST", notes = "同步培训信息到文化广东平台")
    @ResponseBody
    @RequestMapping(value = "/syncTrainingToWhgd", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public IResponse syncTrainingToWhgd(@RequestBody WhgdSyncReq req, HttpServletRequest request)
            throws JsonProcessingException {
        return cultureCloudWhgdSyncService.syncTrainingToWhgd(req);
    }
    
    /**
     * 手动触发全部同步
     */
    @ApiOperation(value = "全部同步(syncAllToWhgd)", httpMethod = "POST", notes = "同步所有信息到文化广东平台")
    @ResponseBody
    @RequestMapping(value = "/syncAllToWhgd", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public IResponse syncAllToWhgd(@RequestBody WhgdSyncReq req, HttpServletRequest request)
            throws JsonProcessingException {
        // 同步活动
        IResponse activityRes = cultureCloudWhgdSyncService.syncActivityToWhgd(req);
        
        // 同步场馆
        IResponse venueRes = cultureCloudWhgdSyncService.syncRoomToWhgd(req);
        
        // 同步培训
        IResponse trainingRes = cultureCloudWhgdSyncService.syncTrainingToWhgd(req);
        
        // 返回活动同步结果
        return activityRes;
    }

    /**
     * 同步培训信息到文化广东
     */
    @ApiOperation(value = "文化广东取消活动订单(cancelWhgdActivityOrder)", httpMethod = "POST", notes = "文化广东取消活动订单")
    @ResponseBody
    @RequestMapping(value = "/cancelWhgdActivityOrder", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public String syncTrainingToWhgd(@RequestBody WhgdCancelOrderReq req, HttpServletRequest request)
            throws JsonProcessingException {
        return cultureCloudWhgdSyncService.cancelWhgdActivityOrder(req);
    }
}