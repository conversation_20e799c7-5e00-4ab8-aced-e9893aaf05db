package com.foshan.controller.cultureCloud;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.cultureCloud.request.CultureCloudSeatAccountReq;
import com.foshan.form.cultureCloud.request.CultureCloudTagReq;
import com.foshan.form.cultureCloud.response.cultureCloudSeatAccount.GetSeatAccountListRes;
import com.foshan.form.cultureCloud.response.cultureCloudTag.GetCultureCloudTagListRes;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "会员订座模块")
@RestController
public class CultureCloudSeatAccountController extends BaseCultureCloudController {

	// 获取会员订座列表
	@ApiOperation(value = "获取会员订座列表(getSeatAccountList)", httpMethod = "POST", notes = "获取会员订座列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getSeatAccountList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetSeatAccountListRes getSeatAccountList(@RequestBody CultureCloudSeatAccountReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetSeatAccountListRes res = (GetSeatAccountListRes) cultureCloudSeatAccountService.getSeatAccountList(req);
		return res;
	}
	
	// 订座或取消订座
	@ApiOperation(value = "订座或取消订座(modifySeatAccount)", httpMethod = "POST", notes = "订座或取消订座，seatAccountIdList和seatStatus不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifySeatAccount", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifySeatAccount(@RequestBody CultureCloudSeatAccountReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) cultureCloudSeatAccountService.modifySeatAccount(req);
		return res;
	}

}
