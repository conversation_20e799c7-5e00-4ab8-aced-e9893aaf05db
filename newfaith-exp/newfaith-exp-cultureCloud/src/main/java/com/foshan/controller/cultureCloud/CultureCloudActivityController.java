package com.foshan.controller.cultureCloud;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.cultureCloud.request.ActivityReq;
import com.foshan.form.cultureCloud.response.cultureCloudActivity.GetCultureCloudActivityInfoRes;
import com.foshan.form.cultureCloud.response.cultureCloudActivity.GetCultureCloudActivityListRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.text.ParseException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Api(tags = "活动模块")
@RestController
public class CultureCloudActivityController extends BaseCultureCloudController{


    // 获取活动列表
    @ApiOperation(value = "获取活动列表(getCultureCloudActivityList)", httpMethod = "POST", notes = "获取活动列表；")
    @ResponseBody
    @RequestMapping(value = "/getCultureCloudActivityList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCultureCloudActivityListRes getCultureCloudActivityList(@RequestBody ActivityReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GetCultureCloudActivityListRes res = (GetCultureCloudActivityListRes) cultureCloudActivityService.getCultureCloudActivityList(req);
        return res;
    }

    
    // 新增活动
    @ApiOperation(value = "新增活动(addCultureCloudActivity)", httpMethod = "POST", notes = "新增活动；")
    @ResponseBody
    @RequestMapping(value = "/addCultureCloudActivity", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse addCultureCloudActivity(@RequestBody ActivityReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GenericResponse res = (GenericResponse) cultureCloudActivityService.addActivity(req);
        return res;
    }

    // 复制活动
    @ApiOperation(value = "复制活动(copyActivity)", httpMethod = "POST", notes = "复制活动；")
    @ResponseBody
    @RequestMapping(value = "/copyActivity", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse copyActivity(@RequestBody ActivityReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GenericResponse res = (GenericResponse) cultureCloudActivityService.copyActivity(req);
        return res;
    }

    // 获取活动详情
    @ApiOperation(value = "获取活动详情(getCultureCloudActivityInfo)", httpMethod = "POST", notes = "获取活动详情；")
    @ResponseBody
    @RequestMapping(value = "/getCultureCloudActivityInfo", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCultureCloudActivityInfoRes getCultureCloudActivityInfo(@RequestBody ActivityReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GetCultureCloudActivityInfoRes res = (GetCultureCloudActivityInfoRes) cultureCloudActivityService.getCultureCloudActivityInfo(req);
        return res;
    }

    // 修改活动
    @ApiOperation(value = "修改活动(modifyCultureCloudActivity)", httpMethod = "POST", notes = "修改活动；")
    @ResponseBody
    @RequestMapping(value = "/modifyCultureCloudActivity", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse modifyCultureCloudActivity(@RequestBody ActivityReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GenericResponse res = (GenericResponse) cultureCloudActivityService.modifyCultureCloudActivity(req);
        return res;
    }

    // 修改活动排序
    @ApiOperation(value = "修改活动排序(modifyCultureCloudActivitySort)", httpMethod = "POST", notes = "修改活动排序；")
    @ResponseBody
    @RequestMapping(value = "/modifyCultureCloudActivitySort", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse modifyCultureCloudActivitySort(@RequestBody ActivityReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GenericResponse res = (GenericResponse) cultureCloudActivityService.modifyCultureCloudActivitySort(req);
        return res;
    }

    // 发布活动
    @ApiOperation(value = "发布活动(publishActivity)", httpMethod = "POST", notes = "发布活动；")
    @ResponseBody
    @RequestMapping(value = "/publishActivity", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse publishActivity(@RequestBody ActivityReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GenericResponse res = (GenericResponse) cultureCloudActivityService.publishActivity(req);
        return res;
    }

    // 删除活动
    @ApiOperation(value = "删除活动(deleteActivity)", httpMethod = "POST", notes = "删除活动；")
    @ResponseBody
    @RequestMapping(value = "/deleteActivity", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse deleteActivity(@RequestBody ActivityReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GenericResponse res = (GenericResponse) cultureCloudActivityService.deleteActivity(req);
        return res;
    }

    // 将回收站的活动还原至草稿箱
    @ApiOperation(value = "将回收站的活动还原至草稿箱(returnActivity)", httpMethod = "POST", notes = "将回收站的活动还原至草稿箱；")
    @ResponseBody
    @RequestMapping(value = "/returnActivity", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse returnActivity(@RequestBody ActivityReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GenericResponse res = (GenericResponse) cultureCloudActivityService.returnActivity(req);
        return res;
    }

	// 导出活动列表
	@ApiOperation(value = "导出活动列表(exportCultureCloudActivityList)", httpMethod = "POST", notes = "导出活动列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/exportCultureCloudActivityList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportCultureCloudActivityList(@RequestBody ActivityReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		cultureCloudActivityService.exportCultureCloudActivityList(req,response);
	}

    // 新增活动
    @ApiOperation(value = "修改活动同步文化广东状态(modifySyncWhgdStatus)", httpMethod = "POST", notes = "修改活动同步文化广东状态；")
    @ResponseBody
    @RequestMapping(value = "/modifySyncWhgdStatus", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse modifySyncWhgdStatus(@RequestBody ActivityReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GenericResponse res = (GenericResponse) cultureCloudActivityService.modifySyncWhgdStatus(req);
        return res;
    }
}
