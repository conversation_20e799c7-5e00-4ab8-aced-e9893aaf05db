package com.foshan.controller.cultureCloud;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.cultureCloud.request.CultureCloudTypeReq;
import com.foshan.form.cultureCloud.response.cultureCloudType.AddCultureCloudTypeRes;
import com.foshan.form.cultureCloud.response.cultureCloudType.GetCultureCloudTypeInfoRes;
import com.foshan.form.cultureCloud.response.cultureCloudType.GetCultureCloudTypeListRes;
import com.foshan.form.cultureCloud.response.cultureCloudType.ModifyCultureCloudTypeRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "类型模块")
@RestController
public class CultureCloudTypeController extends BaseCultureCloudController {

	// 获取类型列表
	@ApiOperation(value = "获取类型列表(getCultureCloudTypeList)", httpMethod = "POST", notes = "获取类型列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCultureCloudTypeList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCultureCloudTypeListRes getCommunityEventCategoryItemsList(@RequestBody CultureCloudTypeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCultureCloudTypeListRes res = (GetCultureCloudTypeListRes) cultureCloudTypeService.getCultureCloudTypeList(req);
		return res;
	}
	
	// 新增类型
	@ApiOperation(value = "新增类型(addCultureCloudType)", httpMethod = "POST", notes = "新增类型")
	@ResponseBody
	@RequestMapping(value = "/addCultureCloudType", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCultureCloudTypeRes addCultureCloudType(@RequestBody CultureCloudTypeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCultureCloudTypeRes res = (AddCultureCloudTypeRes) cultureCloudTypeService.addCultureCloudType(req);
		return res;
	}
	
	// 修改类型
	@ApiOperation(value = "修改类型(modifyCultureCloudType)", httpMethod = "POST", notes = "修改类型，cultureCloudTypeId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCultureCloudType", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCultureCloudTypeRes modifyCultureCloudType(@RequestBody CultureCloudTypeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCultureCloudTypeRes res = (ModifyCultureCloudTypeRes) cultureCloudTypeService.modifyCultureCloudType(req);
		return res;
	}
	
	// 删除类型
	@ApiOperation(value = "删除事件类型项目(deleteCultureCloudType)", httpMethod = "POST", notes = "删除类型，cultureCloudTypeId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCultureCloudType", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCultureCloudType(@RequestBody CultureCloudTypeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) cultureCloudTypeService.deleteCultureCloudType(req);
		return res;
	}
	
	// 获取类型详情
	@ApiOperation(value = "获取类型详情(getCultureCloudTypeInfo)", httpMethod = "POST", notes = "获取类型详情，cultureCloudTypeId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCultureCloudTypeInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCultureCloudTypeInfoRes getCultureCloudTypeInfo(@RequestBody CultureCloudTypeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCultureCloudTypeInfoRes res = (GetCultureCloudTypeInfoRes) cultureCloudTypeService.getCultureCloudTypeInfo(req);
		return res;
	}
   
}
