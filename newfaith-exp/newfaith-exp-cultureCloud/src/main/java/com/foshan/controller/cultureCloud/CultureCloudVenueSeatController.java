package com.foshan.controller.cultureCloud;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.cultureCloud.request.CultureCloudVenueSeatReq;
import com.foshan.form.cultureCloud.response.cultureCloudVenueSeat.GetVenueSeatByActivityListRes;
import com.foshan.form.cultureCloud.response.cultureCloudVenueSeat.GetVenueSeatListRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "场馆座位模块")
@RestController
public class CultureCloudVenueSeatController extends BaseCultureCloudController{
    // 获取座位列表
    @ApiOperation(value = "获取座位列表(getVenueSeatList)", httpMethod = "POST", notes = "获取座位列表（手机端）；")
    @ResponseBody
    @RequestMapping(value = "/getVenueSeatList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetVenueSeatListRes getVenueSeatList(@RequestBody CultureCloudVenueSeatReq req, HttpServletRequest request)
            throws JsonProcessingException {
    	GetVenueSeatListRes res = (GetVenueSeatListRes) cultureCloudVenueSeatService.getVenueSeatList(req);
        return res;
    }
    
    
    // 选座
    @ApiOperation(value = "选座(modifyVenueSeat)", httpMethod = "POST", notes = "选座；")
    @ResponseBody
    @RequestMapping(value = "/modifyVenueSeat", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetVenueSeatListRes modifyVenueSeat(@RequestBody CultureCloudVenueSeatReq req, HttpServletRequest request)
            throws JsonProcessingException {
    	GetVenueSeatListRes res = (GetVenueSeatListRes) cultureCloudVenueSeatService.modifyVenueSeat(req);
        return res;
    }
    
    // 根据活动获取座位列表
    @ApiOperation(value = "根据活动获取座位列表(getVenueSeatByActivityList)", httpMethod = "POST", notes = "根据活动获取座位列表（后端）；")
    @ResponseBody
    @RequestMapping(value = "/getVenueSeatByActivityList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetVenueSeatByActivityListRes getVenueSeatByActivityList(@RequestBody CultureCloudVenueSeatReq req, HttpServletRequest request)
            throws JsonProcessingException {
    	GetVenueSeatByActivityListRes res = (GetVenueSeatByActivityListRes) cultureCloudVenueSeatService.getVenueSeatByActivityList(req);
        return res;
    }
}
