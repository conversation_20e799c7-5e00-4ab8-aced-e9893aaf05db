package com.foshan.service.cultureCloud;

import com.foshan.entity.cultureCloud.CultureCloudVenueExtendInfoEntity;
import com.foshan.entity.cultureCloud.CultureCloudVenueTicketTemplateEntity;
import com.foshan.form.cultureCloud.request.CultureCloudVenueReq;
import com.foshan.form.cultureCloud.request.CultureCloudVenueTicketReq;
import com.foshan.form.response.IResponse;

import java.util.Date;
import java.util.List;

public interface ICultureCloudVenueService {
    IResponse addCultureCloudVenue(CultureCloudVenueReq req);

    IResponse modifyCultureCloudVenue(CultureCloudVenueReq req);

    IResponse getCultureCloudVenueList(CultureCloudVenueReq req);

    IResponse getCultureCloudVenueInfo(CultureCloudVenueReq req);

    IResponse deleteCultureCloudVenue(CultureCloudVenueReq req);

    IResponse publishCultureCloudVenue(CultureCloudVenueReq req);

    IResponse backCultureCloudVenue(CultureCloudVenueReq req);

    IResponse deleteRecycleCultureCloudVenue(CultureCloudVenueReq req);

    IResponse updateCultureCloudSortValue(CultureCloudVenueReq req);

    IResponse auditCultureCloudVenue(CultureCloudVenueReq req);

    IResponse upshelfCultureCloudVenue(CultureCloudVenueReq req);

    IResponse getVenueTicketAndTemplate(CultureCloudVenueTicketReq req);

    int initAddtionalVenueTicketEvent(Date startDate, Date endDate, CultureCloudVenueExtendInfoEntity extendInfo, List<CultureCloudVenueTicketTemplateEntity> templateList);
}
