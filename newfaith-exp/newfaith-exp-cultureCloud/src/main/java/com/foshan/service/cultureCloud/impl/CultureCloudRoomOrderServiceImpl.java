package com.foshan.service.cultureCloud.impl;


import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.cultureCloud.CultureCloudMemberEntity;
import com.foshan.entity.cultureCloud.CultureCloudRoomBookEntity;
import com.foshan.entity.cultureCloud.CultureCloudRoomOrderEntity;
import com.foshan.form.cultureCloud.CultureCloudRoomOrderForm;

import com.foshan.form.cultureCloud.request.CultureCloudRoomOrderReq;
import com.foshan.form.cultureCloud.request.CultureCloudWebRoomOrderReq;
import com.foshan.form.cultureCloud.response.cultureCloudRoomOrder.GetCultureCloudRoomOrderListRes;

import com.foshan.form.request.BasePageRequest;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.cultureCloud.ICultureCloudRoomOrderService;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.query.Query;
import org.hibernate.transform.Transformers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;


@Transactional
@Service("cultureCloudRoomOrderService")
public class CultureCloudRoomOrderServiceImpl extends GenericCultureCloudService implements ICultureCloudRoomOrderService {

    private final static Logger logger = LoggerFactory.getLogger(CultureCloudRoomOrderServiceImpl.class);


    /**
     * 待审核订单列表（待审核未过期）后台用
     *
     * @param req
     * @return
     */
    @Override
    public IResponse roomOrderCheckIndex(CultureCloudRoomOrderReq req) {
        GetCultureCloudRoomOrderListRes res = new GetCultureCloudRoomOrderListRes();
        try {


            Object userObj = getPrincipal(true);
            PlatformUserEntity user = null;
            if (null == userObj) {
                res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
                res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
                return res;
            } else if (userObj instanceof PlatformUserEntity) {
                user = (PlatformUserEntity) userObj;
            } else {
                res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
                res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
                return res;
            }

//            //在云店中，存在上下架
//            CmsShop shop = (CmsShop) session.getAttribute("shop");
//            String shopPath = null;
//            if (shop != null && StringUtils.isNotBlank(shop.getShopId())){
//                shopPath = shop.getShopPath();
//            }

            res = queryRoomOrderCheck(user, req);


//            model.addObject("roomOrderList", roomOrderList);
//            model.addObject("venueName", venueName);
//            model.addObject("phoneNum", phoneNum);
//            model.addObject("userType", userType);
//            model.addObject("tuserIsDisplay", tuserIsDisplay);
//            model.addObject("curDateStart", curDateStart);
//            model.addObject("curDateEnd", curDateEnd);
//            model.addObject("orderCreateTimeStart", orderCreateTimeStart);
//            model.addObject("orderCreateTimeEnd", orderCreateTimeEnd);
//            model.addObject("roomName", roomName);
//            model.addObject("useType", useType);
//
//            model.addObject("page", page);
//            model.addObject("shopProvince", staticServer.getCityInfo().split(",")[3]);

//            model.setViewName("admin/roomOrder/roomCheckOrder");

        } catch (Exception e) {
            logger.error("roomOrderCheckIndex error {}", e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
        }
        return res;
    }


    /**
     * 显示或搜索用户活动室订单信息（当前未过期订单）后台用
     *
     * @param req
     * @return
     */
    @Override
    public IResponse roomOrderIndex(CultureCloudRoomOrderReq req) {
        GetCultureCloudRoomOrderListRes res = new GetCultureCloudRoomOrderListRes();
        try {


            Object userObj = getPrincipal(true);
            PlatformUserEntity user = null;
            if (null == userObj) {
                res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
                res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
                return res;
            } else if (userObj instanceof PlatformUserEntity) {
                user = (PlatformUserEntity) userObj;
            } else {
                res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
                res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
                return res;
            }

//            //在云店中，存在上下架
//            CmsShop shop = (CmsShop) session.getAttribute("shop");
//            String shopPath = null;
//            if (shop != null && StringUtils.isNotBlank(shop.getShopId())){
//                shopPath = shop.getShopPath();
//            }

            res = queryRoomOrder(user, req);

        } catch (Exception e) {
            logger.error("roomOrderCheckIndex error {}", e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
        }
        return res;
    }

    /**
     * 显示或搜索用户活动室历史订单信息（过期订单，即历史订单）后台用
     *
     * @param req
     * @return
     */
    @Override
    public IResponse roomOrderHistoryIndex(CultureCloudRoomOrderReq req) {
        GetCultureCloudRoomOrderListRes res = new GetCultureCloudRoomOrderListRes();
        try {


            Object userObj = getPrincipal(true);
            PlatformUserEntity user = null;
            if (null == userObj) {
                res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
                res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
                return res;
            } else if (userObj instanceof PlatformUserEntity) {
                user = (PlatformUserEntity) userObj;
            } else {
                res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
                res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
                return res;
            }

//            //在云店中，存在上下架
//            CmsShop shop = (CmsShop) session.getAttribute("shop");
//            String shopPath = null;
//            if (shop != null && StringUtils.isNotBlank(shop.getShopId())){
//                shopPath = shop.getShopPath();
//            }

            res = queryRoomOrderHistory(user, req);


        } catch (Exception e) {
            logger.error("roomOrderCheckIndex error {}", e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
        }
        return res;
    }




    public GetCultureCloudRoomOrderListRes queryRoomOrderCheck(PlatformUserEntity user, CultureCloudRoomOrderReq req) {

        GetCultureCloudRoomOrderListRes res = new GetCultureCloudRoomOrderListRes();


        StringBuffer sql = new StringBuffer("SELECT cro.orderNo,cro.purpose, cro.id, cv.venueName," +
                " cv.venueAddress, cro.orderCreateTime, cr.roomName, cr.roomFee, cr.roomIsFree," +
                " cr.id as roomId, cro.validCode, cr.roomPicUrl, " +
//                "ctu.TUSER_NAME as tuser_team_name," +
                " cro.venueId, rb.openPeriod AS openPeriod," +
                " rb.curDate AS curDates, rb.dayOfWeek AS dayOfWeek, cro.bookStatus, cro.checkStatus," +
                " user.passAuthentication, user.id as userId, " +
//                "ctu.TUSER_IS_DISPLAY, cro.TUSER_ID, cro.TUSER_NAME, cro.IF_NEED_TUSER," +
                " cr.useType, cro.reservationMethod, ifnull(user.nickName,cro.userName) userName," +
//                " ifnull(ctu.user_telephone,user.user_telephone) userTel" +
                "  user.phone as userTel" +
//                ", ctu.TUSER_TAG" +
                " FROM  t_culture_cloud_room_order cro LEFT JOIN  t_culture_cloud_venue cv ON cro.venueId = cv.id " +
//                "LEFT JOIN  t_culture_cloud_team_user ctu ON cro.TUSER_ID = ctu.TUSER_ID " +
//                "LEFT JOIN  t_culture_cloud_comment c ON cro.VENUE_ID = cro.COMMENT_RK_ID " +
                "LEFT JOIN  t_culture_cloud_room cr ON cr.id = cro.roomId " +
                "LEFT JOIN  t_culture_cloud_room_book rb ON cro.bookId = rb.id " +
                "LEFT JOIN  t_account user ON user.id = cro.memberId " +
                "WHERE cro.bookStatus =0 and CONCAT(rb.curDate,' ',left(rb.openPeriod,5)) >= DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i')");

        sql.append(StringUtils.isNotEmpty(req.getRoomName()) ? "AND cr.roomName like '%" + req.getRoomName() + "%'" : "")
                .append(StringUtils.isNotEmpty(req.getPhoneNum()) ? "cro.userTel == '" + req.getPhoneNum() + "'" : "")
                .append(StringUtils.isNotEmpty(req.getVenueName()) ?
                        "AND cv.venueName like '%" + req.getVenueName() + "%'" : "")
                .append(null != req.getUserType() ? "AND user.passAuthentication == " + req.getUserType()
                        : "")
                .append(StringUtils.isNotEmpty(req.getCurDateStart()) ? "AND rb.curDate >= '" + req.getCurDateStart() + "'"
                        : "")
                .append(StringUtils.isNotEmpty(req.getCurDateEnd()) ? "AND rb.curDate <= '" + req.getCurDateEnd() + "'"
                        : "")
                .append(StringUtils.isNotEmpty(req.getOrderCreateTimeStart()) ?
                        " AND DATE_FORMAT(cro.orderCreateTime, '%Y-%m-%d') >= '" + req.getOrderCreateTimeStart() + "'"
                        : "")
                .append(StringUtils.isNotEmpty(req.getOrderCreateTimeEnd()) ?
                        " AND DATE_FORMAT(cro.orderCreateTime, '%Y-%m-%d') <= '" + req.getOrderCreateTimeEnd() + "'"
                        : "")
                .append(null != req.getUseType() ? " AND cr.useType == " + req.getUseType() : "");
        sql.append(" group by cro.id order by cro.orderUpdateTime desc");

        Query query =
                cultureCloudRoomOrderDao.createSQLQuery(sql.toString()).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        Integer totalResult = query.getResultList().size();
        query.setMaxResults(null != req.getPageSize() ? req.getPageSize() : 10);
        query.setFirstResult((null != req.getRequestPage() ? req.getRequestPage() - 1 : 1 - 1) * (null != req.getPageSize() ? req.getPageSize() :
                10));

        List<CultureCloudRoomOrderForm> resultList = new ArrayList<>();
        query.getResultList().forEach(o -> {
            CultureCloudRoomOrderForm dto = new CultureCloudRoomOrderForm();
            try {
                copyProperties(dto, o);
                Map obj = (Map) o;
                dto.setRoomOrderId(obj.get("id").toString());
                resultList.add(dto);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        res.setRoomOrderList(resultList);
        res.setTotalResult(totalResult);
        res.setPageSize(req.getPageSize());
        res.setCurrentPage(req.getRequestPage());
        res.setTotal((totalResult + req.getPageSize() - 1) / req.getPageSize());
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

        return res;
    }


    public GetCultureCloudRoomOrderListRes queryRoomOrder(PlatformUserEntity user, CultureCloudRoomOrderReq req) {

        GetCultureCloudRoomOrderListRes res = new GetCultureCloudRoomOrderListRes();


        StringBuffer sql = new StringBuffer("SELECT cro.orderNo, cro.id, cv.venueName," +
                " cv.venueAddress, cro.orderCreateTime, cr.roomName, cr.roomFee, cr.roomIsFree," +
                " cr.id as roomId, cro.validCode, cr.roomPicUrl, " +
//                "ctu.TUSER_NAME as tuser_team_name," +
                " cro.venueId, rb.openPeriod AS openPeriod," +
                " rb.curDate AS curDates, rb.dayOfWeek AS dayOfWeek, cro.bookStatus, cro.checkStatus," +
                " user.passAuthentication, user.id as userId, " +
//                "ctu.TUSER_IS_DISPLAY, cro.TUSER_ID, cro.TUSER_NAME, cro.IF_NEED_TUSER," +
                " cr.useType, cro.reservationMethod, ifnull(user.nickName,cro.userName) userName," +
//                " ifnull(ctu.user_telephone,user.user_telephone) userTel" +
                "  user.phone as userTel" +
//                ", ctu.TUSER_TAG" +
                " FROM  t_culture_cloud_room_order cro LEFT JOIN  t_culture_cloud_venue cv ON cro.venueId = cv.id " +
//                "LEFT JOIN  t_culture_cloud_team_user ctu ON cro.TUSER_ID = ctu.TUSER_ID " +
//                "LEFT JOIN  t_culture_cloud_comment c ON cro.VENUE_ID = c.COMMENT_RK_ID " +
                "LEFT JOIN  t_culture_cloud_room cr ON cr.id = cro.roomId " +
                "LEFT JOIN  t_culture_cloud_room_book rb ON cro.bookId = rb.id " +
                "LEFT JOIN  t_account user ON user.id = cro.memberId " +
                "WHERE cro.bookStatus =1 and CONCAT(rb.curDate,' ',left(rb.openPeriod,5)) >= DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i')");

        sql.append(StringUtils.isNotEmpty(req.getRoomName()) ? "AND cr.roomName like '%" + req.getRoomName() + "%'" : "")
                .append(StringUtils.isNotEmpty(req.getPhoneNum()) ? "cro.userTel == '" + req.getPhoneNum() + "'" : "")
                .append(StringUtils.isNotEmpty(req.getVenueName()) ?
                        "AND cv.venueName like '%" + req.getVenueName() + "%'" : "")
                .append(null != req.getUserType() ? "AND user.passAuthentication == " + req.getUserType()
                        : "")
                .append(StringUtils.isNotEmpty(req.getCurDateStart()) ? "AND rb.curDate >= '" + req.getCurDateStart() + "'"
                        : "")
                .append(StringUtils.isNotEmpty(req.getCurDateEnd()) ? "AND rb.curDate <= '" + req.getCurDateEnd() + "'"
                        : "")
                .append(StringUtils.isNotEmpty(req.getOrderCreateTimeStart()) ?
                        " AND DATE_FORMAT(cro.orderCreateTime, '%Y-%m-%d') >= '" + req.getOrderCreateTimeStart() + "'"
                        : "")
                .append(StringUtils.isNotEmpty(req.getOrderCreateTimeEnd()) ?
                        " AND DATE_FORMAT(cro.orderCreateTime, '%Y-%m-%d') <= '" + req.getOrderCreateTimeEnd() + "'"
                        : "")
                .append(null != req.getUseType() ? " AND cr.useType == " + req.getUseType() : "");
        sql.append(" group by cro.id order by cro.orderUpdateTime desc");

        Query query =
                cultureCloudRoomOrderDao.createSQLQuery(sql.toString()).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        Integer totalResult = query.getResultList().size();
        query.setMaxResults(null != req.getPageSize() ? req.getPageSize() : 10);
        query.setFirstResult((null != req.getRequestPage() ? req.getRequestPage() - 1 : 1 - 1) * (null != req.getPageSize() ? req.getPageSize() :
                10));

        List<CultureCloudRoomOrderForm> resultList = new ArrayList<>();
        query.getResultList().forEach(o -> {
            CultureCloudRoomOrderForm dto = new CultureCloudRoomOrderForm();
            try {
                copyProperties(dto, o);
                Map obj = (Map) o;
                dto.setRoomOrderId(obj.get("id").toString());
                resultList.add(dto);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        res.setRoomOrderList(resultList);
        res.setTotalResult(totalResult);
        res.setPageSize(req.getPageSize());
        res.setCurrentPage(req.getRequestPage());
        res.setTotal((totalResult + req.getPageSize() - 1) / req.getPageSize());
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

        return res;
    }


    public GetCultureCloudRoomOrderListRes queryRoomOrderHistory(PlatformUserEntity user, CultureCloudRoomOrderReq req) {

        GetCultureCloudRoomOrderListRes res = new GetCultureCloudRoomOrderListRes();


        StringBuffer sql = new StringBuffer("SELECT cro.orderNo, cro.id, cv.venueName," +
                " cv.venueAddress, cro.orderCreateTime, cr.roomName, cr.roomFee, cr.roomIsFree," +
                " cr.id as roomId, cro.validCode, cr.roomPicUrl, " +
//                "ctu.TUSER_NAME as tuser_team_name," +
                " cro.venueId, rb.openPeriod AS openPeriod," +
                " rb.curDate AS curDates, rb.dayOfWeek AS dayOfWeek, cro.bookStatus, cro.checkStatus," +
                " user.passAuthentication, user.id as userId, " +
//                "ctu.TUSER_IS_DISPLAY, cro.TUSER_ID, cro.TUSER_NAME, cro.IF_NEED_TUSER," +
                " cr.useType, cro.reservationMethod, ifnull(user.nickName,cro.userName) userName," +
//                " ifnull(ctu.user_telephone,user.user_telephone) userTel" +
                "  user.phone as userTel" +
//                ", ctu.TUSER_TAG" +
                " FROM  t_culture_cloud_room_order cro LEFT JOIN  t_culture_cloud_venue cv ON cro.venueId = cv.id " +
//                "LEFT JOIN  t_culture_cloud_team_user ctu ON cro.TUSER_ID = ctu.TUSER_ID " +
//                "LEFT JOIN  t_culture_cloud_comment c ON cro.VENUE_ID = c.COMMENT_RK_ID " +
                "LEFT JOIN  t_culture_cloud_room cr ON cr.id = cro.roomId " +
                "LEFT JOIN  t_culture_cloud_room_book rb ON cro.bookId = rb.id " +
                "LEFT JOIN  t_account user ON user.id = cro.memberId " +
                "WHERE cro.bookStatus in (2,3,4,5,6) and CONCAT(rb.curDate,' ',left(rb.openPeriod,5)) < DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i')");

        sql.append(StringUtils.isNotEmpty(req.getRoomName()) ? "AND cr.roomName like '%" + req.getRoomName() + "%'" : "")
                .append(StringUtils.isNotEmpty(req.getPhoneNum()) ? "cro.userTel == '" + req.getPhoneNum() + "'" : "")
                .append(StringUtils.isNotEmpty(req.getVenueName()) ?
                        "AND cv.venueName like '%" + req.getVenueName() + "%'" : "")
                .append(null != req.getUserType() ? "AND user.passAuthentication == " + req.getUserType()
                        : "")
                .append(StringUtils.isNotEmpty(req.getCurDateStart()) ? "AND rb.curDate >= '" + req.getCurDateStart() + "'"
                        : "")
                .append(StringUtils.isNotEmpty(req.getCurDateEnd()) ? "AND rb.curDate <= '" + req.getCurDateEnd() + "'"
                        : "")
                .append(StringUtils.isNotEmpty(req.getOrderCreateTimeStart()) ?
                        " AND DATE_FORMAT(cro.orderCreateTime, '%Y-%m-%d') >= '" + req.getOrderCreateTimeStart() + "'"
                        : "")
                .append(StringUtils.isNotEmpty(req.getOrderCreateTimeEnd()) ?
                        " AND DATE_FORMAT(cro.orderCreateTime, '%Y-%m-%d') <= '" + req.getOrderCreateTimeEnd() + "'"
                        : "")
                .append(null != req.getUseType() ? " AND cr.useType == " + req.getUseType() : "");
        sql.append(" group by cro.id order by cro.orderUpdateTime desc");

        Query query =
                cultureCloudRoomOrderDao.createSQLQuery(sql.toString()).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        Integer totalResult = query.getResultList().size();
        query.setMaxResults(null != req.getPageSize() ? req.getPageSize() : 10);
        query.setFirstResult((null != req.getRequestPage() ? req.getRequestPage() - 1 : 1 - 1) * (null != req.getPageSize() ? req.getPageSize() :
                10));

        List<CultureCloudRoomOrderForm> resultList = new ArrayList<>();
        query.getResultList().forEach(o -> {
            CultureCloudRoomOrderForm dto = new CultureCloudRoomOrderForm();
            try {
                copyProperties(dto, o);
                Map obj = (Map) o;
                dto.setRoomOrderId(obj.get("id").toString());
                resultList.add(dto);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        res.setRoomOrderList(resultList);
        res.setTotalResult(totalResult);
        res.setPageSize(req.getPageSize());
        res.setCurrentPage(req.getRequestPage());
        res.setTotal((totalResult + req.getPageSize() - 1) / req.getPageSize());
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

        return res;
    }

    /**
     * PC个人中心，用户场馆订单
     * @param req
     * @return
     * @throws Exception
     */
    @Override
    public IResponse webUserRoomOrder(CultureCloudWebRoomOrderReq req){
        GetCultureCloudRoomOrderListRes res = new GetCultureCloudRoomOrderListRes();

        try {
            Object userObj = getPrincipal(true);
            CultureCloudMemberEntity user = null;
            if (null == userObj) {
                res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
                res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
                return res;
            } else if (userObj instanceof CultureCloudMemberEntity) {
                user = (CultureCloudMemberEntity) userObj;
            } else {
                res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
                res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
                return res;
            }

            if (null != user) {
//                Pagination pagination = new Pagination();
//                pagination.setRows(pageNum);
//                pagination.setPage(page);
//                List<CultureCloudRoomOrderForm> cmsRoomOrders =  queryRoomOrderListService(user, req,"shopPath");
                res =  queryRoomOrderListService(user, req,"shopPath",null != req.getIsHistory()?req.getIsHistory():false);
//                for (CultureCloudRoomOrderForm order : cmsRoomOrders){
//                    if (order.getRoomPicUrl().indexOf("http") == -1){
//                        order.setRoomPicUrl(staticServer.getStaticServerUrl()+order.getRoomPicUrl());
//                    }
//                }
//                JSONObject jsonObj = new JSONObject();
//                jsonObj.put("status", 200);
//                jsonObj.put("data", cmsRoomOrders);
//                jsonObj.put("page", pagination);
//                json = jsonObj.toJSONString();
            } else {
                res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
                res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
                return res;
//                json = JSONResponse.commonResultFormat(500, "用户id缺失", null);
            }
        } catch (Exception e) {
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
//            json = JSONResponse.toAppResultFormat(500, e.getMessage());
            logger.info("query webUserRoomOrder error" + e.getMessage());
            e.printStackTrace();
            return res;
        }
//        response.setContentType("text/html;charset=UTF-8");
//        response.getWriter().print(json);
//        response.getWriter().flush();
//        response.getWriter().close();
        return res;
    }

    /**
     * 前端显示我的场馆中的列表
     * @return
     */
    public GetCultureCloudRoomOrderListRes queryRoomOrderListService(CultureCloudMemberEntity user,BasePageRequest req,
                                                                     String shopPath,Boolean isHistory) {
        GetCultureCloudRoomOrderListRes res = new GetCultureCloudRoomOrderListRes();

//        Map<String, Object> map = new HashMap<String, Object>();
//
//        map.put("userId",userId);
//
//        CmsTerminalUser terminalUser = userMapper.queryTerminalUserById(userId);
//        if(StringUtils.isNotBlank(terminalUser.getUserInternetway())){
//            Map<String, Object> paramMap = new HashMap<String, Object>();
//            paramMap.put("userInternetway", terminalUser.getUserInternetway());
//            paramMap.put("userIsDisable", 1);
//            paramMap.put("notEqualUserId", terminalUser.getUserId());
//            List<CmsTerminalUser> list = userMapper.queryTerminalUserByCondition(paramMap);
//            if(list.size() > 0){
//                CmsTerminalUser mergeUser = list.get(0);
//                map.put("mergeUserId", mergeUser.getUserId());
//            }
//        }
//
//        if (StringUtils.isNotBlank(shopPath)){
//            String userIds = cmsShopService.getUserIdsByShopPath(shopPath);
//            map.put("userIds", userIds);
//            map.put("shopPath", shopPath);
//        }

//        //网页分页
//        if (page != null && page.getFirstResult() != null && page.getRows() != null) {
//            map.put("firstResult", page.getFirstResult());
//            map.put("rows", page.getRows());
//            int total = cmsRoomOrderMapper.queryRoomOrderListCount(map);
//            page.setTotal(total);
//        }
//        //app分页
//        if (pageApp != null && pageApp.getFirstResult() != null && pageApp.getRows() != null) {
//            map.put("firstResult", pageApp.getFirstResult());
//            map.put("rows", pageApp.getRows());
//        }
        StringBuffer sql = new StringBuffer();
        if (isHistory){
            sql = new StringBuffer("SELECT c.id as cid,c.memberId, c.tuserId, c.orderNo, c.userTel," +
                    " c.validCode, 4 as bookStatus, DATE_FORMAT(c.orderCreateTime, '%Y-%m-%d %H:%i:%s') AS orderCreateTime, r.id, c.venueId+0, c" +
                    ".roomOrderId," +
                    " DATE_FORMAT(c.orderUpdateTime, '%Y-%m-%d %H:%i:%s') as orderUpdateTime, c.checkStatus," +
//                " c.ifNeedTuser, t.tuserName as tuser_team_name,t.T_CREATE_TIME," +
                    " r.roomName, r.roomIsFree, v.venueCity, v.venueArea, v.venueAddress," +
                    " v.venueName, r.roomNo, r.roomPicUrl, rb.openPeriod as openPeriod," +
                    " rb.curDate AS curDates" +
                    " FROM  t_culture_cloud_room_order c" +
//                " LEFT JOIN  t_culture_cloud_team_user t on c.TUSER_ID = t.TUSER_ID" +
                    " LEFT JOIN  t_culture_cloud_venue v on c.venueId = v.id" +
                    " LEFT JOIN  t_culture_cloud_room r on c.roomId = r.id" +
                    " LEFT JOIN  t_culture_cloud_room_book rb on c.bookId = rb.id" +
                    " where 1=1 AND c.bookStatus = 1 AND NOW() > CONCAT(rb.curDate,' ',left(rb.openPeriod,5))" +
                    " and c.memberId = " + user.getId() +
                    " union ALL SELECT c.userId, c.tuserId, c.orderNo, c.userTel, c.validCode, c.bookStatus, c.checkStatus," +
                    " DATE_FORMAT(c.orderCreateTime, '%Y-%m-%d %H:%i:%s') AS orderCreateTime," +
                    " c.roomId, c.venueId+0, c.roomOrderId, c.orderUpdateTime," +
//                    " t.tuserName as tuser_team_name, t.T_CREATE_TIME," +
                    " r.roomName, r.roomIsFree, v.venueCity, v.venueArea, v.venueAddress, v.venueName, r.roomNo, r.roomPicUrl, rb" +
                    ".openPeriod as openPeriod, rb.curDate as curDate FROM  t_culture_cloud_room_order c" +
//                    " LEFT JOIN  t_culture_cloud_team_user t on c.TUSER_ID = t.TUSER_ID" +
                    " LEFT JOIN  t_culture_cloud_venue v on c.venueId = v.id" +
                    " LEFT JOIN  t_culture_cloud_room r on c.roomId = r.id" +
                    " LEFT JOIN  t_culture_cloud_room_book rb on c.bookId = rb.id where 1=1 AND c.bookStatus in (2,3,5,6) and c.memberId = "+user.getId()+
                    " order by orderCreateTime desc");
        }else {
             sql = new StringBuffer("SELECT c.id as cid,c.memberId, c.tuserId, c.orderNo, c.userTel," +
                    " c.validCode, c.bookStatus, c.orderCreateTime, r.id, c.venueId+0, roomOrderId," +
                    " DATE_FORMAT(c.orderUpdateTime, '%Y-%m-%d %H:%i:%s') as orderUpdateTime," +
//                " c.checkStatus, c.ifNeedTuser, t.tuserName as tuser_team_name,t.T_CREATE_TIME," +
                    " r.roomName, r.roomIsFree, v.venueCity, v.venueArea, v.venueAddress," +
                    " v.venueName, r.roomNo, r.roomPicUrl, r.roomFee, rb.openPeriod as openPeriod," +
                    " rb.curDate AS curDates" +
                    " FROM  t_culture_cloud_room_order c" +
//                " LEFT JOIN  t_culture_cloud_team_user t on c.TUSER_ID = t.TUSER_ID" +
                    " LEFT JOIN  t_culture_cloud_venue v on c.venueId = v.id" +
                    " LEFT JOIN  t_culture_cloud_room r on c.roomId = r.id" +
                    " LEFT JOIN  t_culture_cloud_room_book rb on c.bookId = rb.id" +
                    " where 1=1 AND c.bookStatus = 1 AND NOW() < CONCAT(rb.curDate,' ',left(rb.openPeriod,5))" +
                    " and c.memberId = " + user.getId() + " order by c.orderCreateTime desc");

        }

        Query query =
                cultureCloudRoomOrderDao.createSQLQuery(sql.toString()).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        Integer totalResult = query.getResultList().size();
        query.setMaxResults(null != req.getPageSize() ? req.getPageSize() : 10);
        query.setFirstResult((null != req.getRequestPage() ? req.getRequestPage() - 1 : 1 - 1) * (null != req.getPageSize() ? req.getPageSize() :
                10));

        List<CultureCloudRoomOrderForm> cmsRoomOrders = new ArrayList<>();
        query.getResultList().forEach(o -> {
            CultureCloudRoomOrderForm dto = new CultureCloudRoomOrderForm();
            try {
                copyProperties(dto, o);
                Map obj = (Map) o;
                dto.setRoomOrderId(obj.get("cid").toString());
                cmsRoomOrders.add(dto);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });


//        List<CultureCloudRoomOrderEntity> cmsRoomOrders = cmsRoomOrderMapper.queryRoomOrderList(map);
        List<CultureCloudRoomOrderForm> resultList = new ArrayList<>();
        if (isHistory){
            resultList = cmsRoomOrders;
        }else {
            for (CultureCloudRoomOrderForm cmsRoomOrder : cmsRoomOrders) {
                CultureCloudRoomOrderForm cms = new CultureCloudRoomOrderForm();
                BeanUtils.copyProperties(cmsRoomOrder, cms);
                Integer allowCancel = cmsRoomOrder.getAllowCancel();
                Integer cancelEndHour = cmsRoomOrder.getCancelEndHour();
                Calendar calendar = Calendar.getInstance();
                if (allowCancel != null && allowCancel == 1) {
                    String cancelTime = new SimpleDateFormat("yyyy-MM-dd").format(cmsRoomOrder.getCurDate()) + "-" + cmsRoomOrder.getOpenPeriod().split("-")[0];
                    Date parse = null;
                    try {
                        parse = new SimpleDateFormat("yyyy-MM-dd-HH:mm").parse(cancelTime);
                        calendar.setTime(parse);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    if (cmsRoomOrder.getCancelEndHour() != null && cmsRoomOrder.getCancelEndHour() != 0) {
                        calendar.add(Calendar.HOUR, -cmsRoomOrder.getCancelEndHour());
                        cms.setCancelEndTime(calendar.getTime());
                    }
                }
                if (cms.getCancelEndTime() != null) {
                    cms.setOverCancelTime(cms.getCancelEndTime().before(new Date()));
                } else {
                    cms.setOverCancelTime(false);
                }
                resultList.add(cms);
            }
        }
        res.setRoomOrderList(resultList);
        res.setTotalResult(totalResult);
        res.setPageSize(req.getPageSize());
        res.setCurrentPage(req.getRequestPage());
        res.setTotal((totalResult + req.getPageSize() - 1) / req.getPageSize());
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

        return res;
    }

    /**
     * 前端显示我的场馆中的列表
     * @return
     */
    @Override
    public GetCultureCloudRoomOrderListRes webUserCheckRoomOrder(CultureCloudRoomOrderReq req) {
        GetCultureCloudRoomOrderListRes res = new GetCultureCloudRoomOrderListRes();
        Object userObj = getPrincipal(true);
        CultureCloudMemberEntity user = null;
        if (null == userObj) {
            res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
            return res;
        } else if (userObj instanceof CultureCloudMemberEntity) {
            user = (CultureCloudMemberEntity) userObj;
        } else {
            res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
            return res;
        }
        StringBuffer sql = new StringBuffer();

            sql = new StringBuffer("SELECT cro.id as cid, cro.orderNo,cro.roomOrderId,v.venueName,v.venueAddress, DATE_FORMAT(cro" +
                    ".orderCreateTime, '%Y-%m-%d %H:%i:%s') AS orderCreateTime, cro.userTel," +
                    " cro.validCode, cro.bookStatus, r.id, cro.venueId+0, " +
                    " DATE_FORMAT(cro.orderUpdateTime, '%Y-%m-%d %H:%i:%s') as orderUpdateTime, cro.checkStatus," +
//                " cro.ifNeedTuser, t.tuserName as tuser_team_name,t.T_CREATE_TIME," +
                    " r.roomName, r.roomIsFree, v.venueCity, v.venueArea," +
                    " r.roomNo, r.roomPicUrl, rb.openPeriod as openPeriod," +
                    " rb.curDate AS curDates" +
                    " FROM  t_culture_cloud_room_order cro" +
//                " LEFT JOIN  t_culture_cloud_team_user t on cro.TUSER_ID = t.TUSER_ID" +
                    " LEFT JOIN  t_culture_cloud_venue v on cro.venueId = v.id" +
                    " LEFT JOIN  t_culture_cloud_room r on cro.roomId = r.id" +
                    " LEFT JOIN  t_culture_cloud_room_book rb on cro.bookId = rb.id" +
                    " where cro.bookStatus = 0 AND NOW() <= CONCAT(rb.curDate,' ',left(rb.openPeriod,5))" +
                    " and cro.memberId = " + user.getId());
        sql.append(StringUtils.isNotEmpty(req.getRoomName()) ? "AND cr.roomName like '%" + req.getRoomName() + "%'" : "");

        sql.append(" group by cro.id order by cro.orderUpdateTime desc");

        Query query =
                cultureCloudRoomOrderDao.createSQLQuery(sql.toString()).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        Integer totalResult = query.getResultList().size();
        query.setMaxResults(null != req.getPageSize() ? req.getPageSize() : 10);
        query.setFirstResult((null != req.getRequestPage() ? req.getRequestPage() - 1 : 1 - 1) * (null != req.getPageSize() ? req.getPageSize() :
                10));

        List<CultureCloudRoomOrderForm> cmsRoomOrders = new ArrayList<>();
        query.getResultList().forEach(o -> {
            CultureCloudRoomOrderForm dto = new CultureCloudRoomOrderForm();
            try {
                copyProperties(dto, o);
                Map obj = (Map) o;
                dto.setRoomOrderId(obj.get("cid").toString());
                cmsRoomOrders.add(dto);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });


//        List<CultureCloudRoomOrderEntity> cmsRoomOrders = cmsRoomOrderMapper.queryRoomOrderList(map);
        List<CultureCloudRoomOrderForm> resultList = new ArrayList<>();

            resultList = cmsRoomOrders;

        res.setRoomOrderList(resultList);
        res.setTotalResult(totalResult);
        res.setPageSize(req.getPageSize());
        res.setCurrentPage(req.getRequestPage());
        res.setTotal((totalResult + req.getPageSize() - 1) / req.getPageSize());
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

        return res;
    }


    /**
     * 用户取消活动室订单
     *
     * @param req
     * @return
     * @throws Exception
     */
    @Override
    public IResponse cancelRoomOrder(CultureCloudRoomOrderReq req) {
        GenericResponse res = new GenericResponse();
        try {
            if (null != req.getRoomOrderId()) {

                Object userObj = getPrincipal(true);
                CultureCloudMemberEntity user = null;
                if (null != userObj && userObj instanceof CultureCloudMemberEntity) {
                    user = (CultureCloudMemberEntity) userObj;
                }else if(null != userObj && userObj instanceof PlatformUserEntity){

                }else
                {
                    res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
                    res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
                    return res;
                }

                    res = (GenericResponse) cancelRoomOrder(req.getRoomOrderId(), "");

            } else {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
                return res;
            }
        } catch (Exception e) {
            e.printStackTrace();
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
            return res;

        }
        return res;
    }

    /**
     * 取消单个活动室订单
     * @param roomOrderId
     * @return
     */
    public IResponse cancelRoomOrder(Integer roomOrderId,String shopPath){
        GenericResponse res = new GenericResponse();

        Object userObj = getPrincipal(true);
        CultureCloudMemberEntity user = null;


        try {
            CultureCloudRoomOrderEntity cmsRoomOrder = cultureCloudRoomOrderDao.get(roomOrderId);

            String userName = "";
            if (null != userObj && userObj instanceof CultureCloudMemberEntity) {
                user = (CultureCloudMemberEntity) userObj;
                if (cmsRoomOrder.getMember().getId() != user.getId()){
                    res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
                    res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
                    return res;
                }
                userName= user.getUserName();
            }else if (null != userObj && userObj instanceof PlatformUserEntity){
                userName= ((PlatformUserEntity) userObj).getUserName();
            }

            if(null != cmsRoomOrder  && cmsRoomOrder.getBookStatus()<2){
                //状态为2代表取消
                cmsRoomOrder.setBookStatus(2);
                cmsRoomOrder.setOrderUpdateTime(new Date());
                cmsRoomOrder.setOrderUpdateUser(userName);
                //更改活动室订单状态
                cultureCloudRoomOrderDao.update(cmsRoomOrder);

//                    result = true;


                //获取活动室预定信息并修改预定状态
                CultureCloudRoomBookEntity cmsRoomBook = cultureCloudRoomBookDao.get(Integer.parseInt(cmsRoomOrder.getBookId()));

//                CmsActivityRoomVO cmsRoom= activityRoomMapper.queryCmsActivityRoomById(cmsRoomOrder.getRoomId());

//                CmsVenueVO cmsVenue= venueMapper.queryVenueById(cmsRoom.getRoomVenueId());

//                if(cmsRoomBook.getTuserId()!=null)

//                    if(cmsRoomBook.getTuserId().equals(cmsRoomOrder.getTuserId())
//                            &&cmsRoomBook.getUserId().equals(cmsRoomOrder.getMember().getId())&&cmsRoomBook.getBookStatus()==2)
                    if(cmsRoomBook.getUserId().equals(cmsRoomOrder.getMember().getId().toString())&&cmsRoomBook.getBookStatus()==2)
                    {
                        //状态为1代表可选
                        cmsRoomBook.setBookStatus(1);
                        cmsRoomBook.setUpdateTime(new Timestamp(new Date().getTime()));
                        cultureCloudRoomBookDao.update(cmsRoomBook);

                        //&& cancelResult.equals(Constant.RESULT_STR_SUCCESS)
                            res.setRet(ResponseContext.RES_SUCCESS_CODE);
                            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);


                        //场馆短信发送 先注释
//                        Map<String,Object> map = new HashMap<String, Object>();
//                        map.put("userName",cmsRoomOrder.getUserName());
//                        map.put("venue",cmsVenue.getVenueName());
//                        map.put("activity",cmsRoom.getRoomName());
//                        map.put("time", DateUtils.formatDate(cmsRoomBook.getCurDate())+","+cmsRoomBook.getOpenPeriod());
//                        //发送短信--阿里大鱼
//                        smsService.sendMsg(cmsRoomOrder.getUserTel(), map, Constant.SMS_CANCEL_ROOM_ORDER_CODE, "文化云","取消场馆订单发送失败",shopPath);
//                        //发送站内信消息
//                        userMessageService.sendSystemMessage(Constant.MESSAGE_TYPE_CANCEL_ORDER_ROOM_CANCEL, map, cmsRoomOrder.getUserId(), cmsRoomOrder.getRoomOrderId());
                    }

                //修改Redis活动室预定状态
                //String cancelResult = cacheService.cancelRoomBook(cmsRoomBook);

                //当文化系统取消成功以后，刷新子系统的取消功能 先注释
//                this.webServiceRoomOrderService.cancelOrder(cmsRoomOrder);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            }else{
                res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
                res.setRetInfo("无需取消");
            }
        } catch (Exception e) {
            logger.error("取消活动室订单出错!",e);
        }
        return res;
    }

//    /**
//     * 预订单审核通过
//     *
//     * @param roomOrderId
//     * @return
//     */
//    @RequestMapping("/preCheckPass")
//    @ResponseBody
//    public int preCheckPass(@RequestParam String roomOrderId){
//
//        SysUser sysUser = (SysUser)session.getAttribute("user");
//        if(sysUser != null&&StringUtils.isNotBlank(sysUser.getUserId())){
//
//            CmsRoomOrder order = cmsRoomOrderMapper.queryCmsRoomOrderById(roomOrderId);
//
//            //该场次已被长期预约是否继续审核
//            int count = cmsRoomAllocationMapper.selectPlanByBookId(order.getBookId());
//
//            return count;
//        }
//
//        return 0;
//    }


    /**
     * 订单审核通过
     *
     * @param req
     * @return
     */
    @Override
    public IResponse checkRoomOrder(CultureCloudRoomOrderReq req){
        GenericResponse res = new GenericResponse();

        if(null==req.getCheckStatus()&&StringUtils.isEmpty(req.getCheckReason())){
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
            return res;
        }

        Object userObj = getPrincipal(true);
        PlatformUserEntity user = null;
        if (null == userObj) {
            res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
            return res;
        } else if (userObj instanceof PlatformUserEntity) {
            user = (PlatformUserEntity) userObj;
        } else {
            res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
            return res;
        }

        CultureCloudRoomOrderEntity order = cultureCloudRoomOrderDao.get(Integer.valueOf(req.getRoomOrderId()));
        if (null != order){

            try {

                CultureCloudRoomBookEntity cmsRoomBook = cultureCloudRoomBookDao.get(Integer.parseInt(order.getBookId()));

                //查询该订单所在场次是否已有审核通过的订单
                List<CultureCloudRoomOrderEntity> count = cultureCloudRoomOrderDao.getListByHql("select a from CultureCloudRoomOrderEntity a where a.bookStatus =1 and a" +
                        ".checkStatus = 1 and a.bookId='"+order.getBookId()+"'");
//			if(cmsRoomBook.getBookStatus()!=1)
                if(count.size() > 0)
                {
                    // 场次不可选
                    res.setRet(ResponseContext.RES_PERM_TOPLIMIT_CODE);
                    res.setRetInfo(ResponseContext.RES_PERM_TOPLIMIT_INFO+"已有已审核未使用订单");
                    return res;
                }

                String roomBookId=order.getBookId();

                // 该时间的所有订单
//                List<CultureCloudRoomOrderEntity> allCmsRoomOrderList= cmsRoomOrderMapper.queryRoomOrderListByBookId(roomBookId);
                List<CultureCloudRoomOrderEntity> allCmsRoomOrderList = cultureCloudRoomOrderDao.getListByHql("select a from " +
                        "CultureCloudRoomOrderEntity a where a.bookId='"+order.getBookId()+"'");

                for (CultureCloudRoomOrderEntity cmsRoomOrder : allCmsRoomOrderList) {

                    // 当前订单 审核通过
                    if(cmsRoomOrder.getId().equals(req.getRoomOrderId()))
                    {
                        // 审核状态 0.待审核 1.审核通过 2.审核未通过
                        cmsRoomOrder.setCheckStatus(1);
                        // 状态预定成功
                        cmsRoomOrder.setBookStatus(1);

                        cmsRoomOrder.setOrderUpdateTime(new Date());
                        cmsRoomOrder.setOrderUpdateUser(user.getId().toString());
                        cultureCloudRoomOrderDao.update(cmsRoomOrder);


//                        if(StringUtils.isNotBlank(cmsRoomOrder.getRoomAllocationId())){
//                            CmsRoomAllocation cmsRoomAllocation = new CmsRoomAllocation();
//                            cmsRoomAllocation.setRoomAllocationId(cmsRoomOrder.getRoomAllocationId());
//                            cmsRoomAllocation.setGenerateOrder(2);
//                            cmsRoomAllocationMapper.update(cmsRoomAllocation);
//                        }

                        cmsRoomBook.setUserId(cmsRoomOrder.getMember().getId().toString());
                        cmsRoomBook.setTuserId(cmsRoomOrder.getTuserId());
                        cmsRoomBook.setUserName(cmsRoomOrder.getUserName());
                        cmsRoomBook.setUserTel(cmsRoomOrder.getUserTel());
                        // 已选
                        cmsRoomBook.setBookStatus(2);
                        cmsRoomBook.setUpdateTime(new Timestamp(new Date().getTime()));
                        cultureCloudRoomBookDao.update(cmsRoomBook);
//                        int r=cmsRoomBookMapper.editCmsRoomBook(cmsRoomBook);

                        //发短信 先注释
//                        if(r>0){
//
//                            CmsUserOperatorLog log=CmsUserOperatorLog.createInstance(null, cmsRoomOrder.getRoomOrderId(), null, sysUser.getUserId(), CmsUserOperatorLog.USER_TYPE_ADMIN, UserOperationEnum.CHECK_PASS);
//
//                            cmsUserOperatorLogMapper.insert(log);
//
//                            // 发送成功短信
//                            this.selectPhoneByRoomOrderId(roomOrderId);
//                        }


                    }
                    else
                    {
                        //状态为2代表取消 其他订单都取消
                        cmsRoomOrder.setBookStatus(2);
                        cmsRoomOrder.setOrderUpdateTime(new Date());
                        cmsRoomOrder.setOrderUpdateUser(user.getId().toString());

                        // 审核状态 0.待审核 1.审核通过 2.审核未通过
                        cmsRoomOrder.setCheckStatus(2);
                        // 状态预定成功
                        cmsRoomOrder.setBookStatus(2);
                        cultureCloudRoomOrderDao.update(cmsRoomOrder);
//                        int r=cmsRoomOrderMapper.editCmsRoomOrder(cmsRoomOrder);

//                        if(StringUtils.isNotBlank(cmsRoomOrder.getRoomAllocationId())){
//                            CmsRoomAllocation cmsRoomAllocation = new CmsRoomAllocation();
//                            cmsRoomAllocation.setRoomAllocationId(cmsRoomOrder.getRoomAllocationId());
//                            cmsRoomAllocation.setGenerateOrder(3);
//                            cmsRoomAllocationMapper.update(cmsRoomAllocation);
//                        }

                        //取消其他订单发短信和取消 先注释
//                        if(r>0)
//                        {
//
//                            CmsUserOperatorLog log=CmsUserOperatorLog.createInstance(null, cmsRoomOrder.getRoomOrderId(), null, sysUser.getUserId(), CmsUserOperatorLog.USER_TYPE_ADMIN, UserOperationEnum.CANCEL);
//                            cmsUserOperatorLogMapper.insert(log);
//
//                            //发送短信--阿里大鱼
//                            /*SmsUtil.cancelVenueOrderSms(cmsRoomOrder.getUserTel(), map);*/
//                            //场馆短信发送
//                            HashMap<String, Object> map = new HashMap();
//                            CmsActivityRoom cmsActivityRoom = cmsActivityRoomService.queryCmsActivityRoomById(cmsRoomOrder.getRoomId());
//                            map.put("roomName",cmsActivityRoom.getRoomName() + "("+ DateUtil.dateToString(cmsRoomBook.getCurDate(),"yyyy-MM-dd") +" "+cmsRoomBook.getOpenPeriod()+")");
//                            smsService.sendMsg(cmsRoomOrder.getUserTel(), map, Constant.SMS_CHECK_NO_PASS_ROOM_ORDER_CODE, "文化云","取消活动室订单短信发送失败");
//
//                            //当文化系统取消成功以后，刷新子系统的取消功能
//                            this.cmsApiRoomOrderService.cancelOrder(cmsRoomOrder);
//                        }


                    }
                }
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
                return res;

            } catch (Exception e) {
                e.printStackTrace();
                res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
                return res;
            }

        }else{
            res.setRet(ResponseContext.RES_DATA_NULL_CODE);
            res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
            return res;
        }
    }

}
