package com.foshan.service.cultureCloud;

import com.foshan.form.cultureCloud.request.CultureCloudUserReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.user.AddUserRes;
import com.foshan.form.response.user.DeleteUserRes;
import com.foshan.form.response.user.GetUserInfoRes;
import com.foshan.form.response.user.GetUserListRes;
import com.foshan.form.response.user.ModifyUserRes;

public interface ICultureCloudUserService {
	public AddUserRes addCultureCloudUser(CultureCloudUserReq req);
	public IResponse getCultureCloudUserInfo(CultureCloudUserReq req);
	public IResponse getCultureCloudUserList(CultureCloudUserReq req);
	public DeleteUserRes deleteCultureCloudUser(CultureCloudUserReq req);
	public ModifyUserRes modifyCultureCloudUser(CultureCloudUserReq req) ;
	public GenericResponse setUserState(CultureCloudUserReq req) ;

}
