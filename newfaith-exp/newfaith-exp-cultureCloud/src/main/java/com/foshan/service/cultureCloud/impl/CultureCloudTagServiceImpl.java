package com.foshan.service.cultureCloud.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.foshan.entity.cultureCloud.CultureCloudTagEntity;
import com.foshan.service.cultureCloud.ICultureCloudTagService;
import org.springframework.transaction.annotation.Transactional;
import com.foshan.form.cultureCloud.request.CultureCloudTagReq;
import com.foshan.form.cultureCloud.response.cultureCloudTag.AddCultureCloudTagRes;
import com.foshan.form.cultureCloud.response.cultureCloudTag.ModifyCultureCloudTagRes;
import com.foshan.form.cultureCloud.response.cultureCloudTag.GetCultureCloudTagInfoRes;
import com.foshan.form.cultureCloud.response.cultureCloudTag.GetCultureCloudTagListRes;
import com.foshan.form.cultureCloud.CultureCloudTagForm;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.IResponse;
import com.foshan.dao.generic.Page;

@Transactional
@Service("cultureCloudTagService")
public class CultureCloudTagServiceImpl extends GenericCultureCloudService implements ICultureCloudTagService{

	@Override
	public IResponse getCultureCloudTagList(CultureCloudTagReq req) {
		GetCultureCloudTagListRes res = new GetCultureCloudTagListRes();
		Page<CultureCloudTagEntity> page = new Page<CultureCloudTagEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CultureCloudTagEntity a where 1=1 ");
		hql.append(null !=req.getCategory() ? " and a.category="+req.getCategory():"")
			.append(StringUtils.isNoneEmpty(req.getTagName()) ? " and a.tagName like'%"+req.getTagName()+"%'" : "")
			.append(null !=req.getDisplayType() ? " and a.displayType="+req.getDisplayType():"");
		hql.append(" ORDER BY a.id desc");
		page = cultureCloudTagDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CultureCloudTagForm cultureCloudTagForm = new CultureCloudTagForm();
			cultureCloudTagForm.setCultureCloudTagId(o.getId());
            cultureCloudTagForm.setCategory(o.getCategory());
            cultureCloudTagForm.setTagName(o.getTagName());
            cultureCloudTagForm.setDisplayType(o.getDisplayType());
			res.getCultureCloudTagList().add(cultureCloudTagForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public IResponse addCultureCloudTag(CultureCloudTagReq req) {
		AddCultureCloudTagRes res = new AddCultureCloudTagRes();
		//if () {
			CultureCloudTagEntity cultureCloudTag = new CultureCloudTagEntity();
			
            cultureCloudTag.setCategory(req.getCategory());
            cultureCloudTag.setTagName(req.getTagName());
            cultureCloudTag.setDisplayType(req.getDisplayType());
			cultureCloudTagDao.save(cultureCloudTag);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		} else {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//		}
		return res;
	}

	@Override
	public IResponse modifyCultureCloudTag(CultureCloudTagReq req) {
		ModifyCultureCloudTagRes res = new ModifyCultureCloudTagRes();
		if (null!=req.getCultureCloudTagId() ) {
			CultureCloudTagEntity cultureCloudTag = cultureCloudTagDao.get(req.getCultureCloudTagId()) ;
			if(null != cultureCloudTag){
                cultureCloudTag.setCategory(req.getCategory());
                cultureCloudTag.setTagName(req.getTagName());
                cultureCloudTag.setDisplayType(req.getDisplayType());
				res.setCultureCloudTagId(cultureCloudTag.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse deleteCultureCloudTag(CultureCloudTagReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCultureCloudTagId()) {
		CultureCloudTagEntity cultureCloudTag = cultureCloudTagDao.get(req.getCultureCloudTagId());
			if (null != cultureCloudTag) {
				cultureCloudTag.setActivityList(null);
				cultureCloudTag.setAssetList(null);
				cultureCloudTag.setRoomList(null);
				cultureCloudTag.setVenueList(null);
				cultureCloudTagDao.deleteById(req.getCultureCloudTagId());
				//cultureCloudTag.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCultureCloudTagInfo(CultureCloudTagReq req) {
		GetCultureCloudTagInfoRes res = new GetCultureCloudTagInfoRes();
		if (null != req.getCultureCloudTagId()) {
			CultureCloudTagEntity cultureCloudTag = cultureCloudTagDao.get(req.getCultureCloudTagId());
			if (null != cultureCloudTag) {
				CultureCloudTagForm cultureCloudTagForm = new CultureCloudTagForm();
				cultureCloudTagForm.setCultureCloudTagId(cultureCloudTag.getId());
                cultureCloudTagForm.setCategory(cultureCloudTag.getCategory());
                cultureCloudTagForm.setTagName(cultureCloudTag.getTagName());
                cultureCloudTagForm.setDisplayType(cultureCloudTag.getDisplayType());
				res.setCultureCloudTagForm(cultureCloudTagForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}