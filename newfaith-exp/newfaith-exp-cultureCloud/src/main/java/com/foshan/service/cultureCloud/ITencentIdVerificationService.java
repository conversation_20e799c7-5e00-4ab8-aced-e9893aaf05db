package com.foshan.service.cultureCloud;

import com.foshan.form.cultureCloud.request.TencentIdVerificationReq;
import com.foshan.form.cultureCloud.response.TencentIdVerificationRes;
import com.foshan.form.response.IResponse;

/**
 * 腾讯云实名认证服务接口
 * 提供身份信息认证（二要素核验）功能
 */
public interface ITencentIdVerificationService {

    /**
     * 身份信息认证（二要素核验）
     * 传入姓名和身份证号，校验两者的真实性和一致性
     * 
     * @param req 实名认证请求参数
     * @return 认证结果
     */
    IResponse verifyIdentity(TencentIdVerificationReq req);

    /**
     * 批量身份信息认证
     * 支持批量验证多个身份信息
     * 
     * @param reqList 批量实名认证请求参数列表
     * @return 批量认证结果
     */
    IResponse batchVerifyIdentity(java.util.List<TencentIdVerificationReq> reqList);

    /**
     * 检查身份证号格式是否正确
     * 
     * @param idCard 身份证号
     * @return true表示格式正确，false表示格式错误
     */
    boolean isValidIdCard(String idCard);

    /**
     * 检查姓名格式是否正确
     * 
     * @param name 姓名
     * @return true表示格式正确，false表示格式错误
     */
    boolean isValidName(String name);
}
