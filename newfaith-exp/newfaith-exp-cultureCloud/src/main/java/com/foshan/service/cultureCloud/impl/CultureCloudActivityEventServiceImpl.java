package com.foshan.service.cultureCloud.impl;

import com.alibaba.fastjson.JSONObject;
import com.foshan.entity.UserEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.cultureCloud.CultureCloudActivityEntity;
import com.foshan.entity.cultureCloud.CultureCloudActivityEventEntity;
import com.foshan.form.cultureCloud.CultureCloudActivityEventForm;
import com.foshan.form.cultureCloud.request.ActivityReq;
import com.foshan.form.cultureCloud.request.CultureCloudActivityEventReq;
import com.foshan.form.cultureCloud.response.cultureCloudActivityEvent.GetCultureCloudActivityEventListRes;
import com.foshan.form.cultureCloud.response.cultureCloudActivityEvent.QueryActivityEventListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.cultureCloud.ICultureCloudActivityEventService;
import com.foshan.service.cultureCloud.ICultureCloudActivityOrderService;
import com.foshan.util.cultureCloud.TimeCompareUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

@Transactional
@Service("cultureCloudActivityEventService")
public class CultureCloudActivityEventServiceImpl extends GenericCultureCloudService implements ICultureCloudActivityEventService {

    private final static Logger logger = LoggerFactory.getLogger(CultureCloudActivityEventServiceImpl.class);

    @Resource(name = "cultureCloudActivityOrderService")
    protected ICultureCloudActivityOrderService cultureCloudActivityOrderService;


    @Override
    public IResponse addActivityEvent(CultureCloudActivityEventEntity activityEvent) {
        return null;
    }

    @Override
    public IResponse getActivityEventList(ActivityReq req){
        GetCultureCloudActivityEventListRes res = new GetCultureCloudActivityEventListRes();

        try {
            Object userObj = getPrincipal(true);
            UserEntity user = null != userObj?(UserEntity) userObj:null;

            // 在云店中 先注释
//            CmsShop shop = (CmsShop) session.getAttribute("shop");
//            String shopPath = null;
//            if (shop != null && StringUtils.isNotBlank(shop.getShopId())) {
//                shopPath = shop.getShopPath();
//            }
            CultureCloudActivityEntity activity = cultureCloudActivityDao.get(req.getActivityId());
            List<CultureCloudActivityEventEntity> activityEventList = activity.getEventList();

            ArrayList<CultureCloudActivityEventForm> eventFormList = new ArrayList<>();
            for (CultureCloudActivityEventEntity activityEvent : activityEventList) {

                CultureCloudActivityEventForm eventForm = new CultureCloudActivityEventForm();
                eventForm.setEventId(activityEvent.getId().toString());
                if (activityEvent.getSingleEvent().equals(1)) {
                    if (StringUtils.isNotBlank(activityEvent.getEventEndDate())) {
                        if (activityEvent.getEventEndDate().equals(activityEvent.getEventDate())) {
                            eventForm.setEventDate(activityEvent.getEventDate());
                        } else {
                            eventForm.setEventDate(activityEvent.getEventDate() + "至" + activityEvent.getEventEndDate());
                        }
                    }
                } else {
                    eventForm.setEventDate(activityEvent.getEventDate());
                }
                eventForm.setEventTime(activityEvent.getEventTime());
                //需要秒杀的活动显示秒杀时间，否则显示报名时间
                String strDateFormat = "yyyy-MM-dd HH:mm";
                SimpleDateFormat sdf = new SimpleDateFormat(strDateFormat);
                if (activity.getSpikeType().equals(1)) {
                    if (activityEvent.getSpikeTime() != null && activityEvent.getSpikeEndTime() != null) {
                        eventForm.setSpikeOrSignTime(sdf.format(activityEvent.getSpikeTime()) + "~" + sdf.format(activityEvent.getSpikeEndTime()));
                    } else {
                        eventForm.setSpikeOrSignTime("暂无数据");
                    }
                } else if (activity.getSpikeType().equals(0)) {
                    //否则显示报名时间
                    if (activity.getSignStartTime() != null && activity.getSignEndTime() != null) {
                        eventForm.setSpikeOrSignTime(sdf.format(activity.getSignStartTime()) + "~" + sdf.format(activity.getSignEndTime()));
                    } else {
                        eventForm.setSpikeOrSignTime("暂无数据");
                    }
                }
                eventForm.setOrderCount(activityEvent.getOrderCount());
                //此处暂时用availableCount表示预定票数而非剩余票数
                int ticketCount = cultureCloudActivityOrderService.queryEventTicketCount(activityEvent.getEventUUID(), user, "");
                eventForm.setAvailableCount(ticketCount);
                //场次状态 判断与当前的状态
                String eventTime = activityEvent.getEventTime();
                String eventStartTime = eventTime.split("-")[0];
                String eventEndTime = eventTime.split("-")[1];
                Date startDate = sdf.parse(activityEvent.getEventDate() + " " + eventStartTime);
                Date endDate = sdf.parse(activityEvent.getEventEndDate() + " " + eventEndTime);

                Date now = new Date();
                //已取消
                if (activityEvent.getAvailableCount().equals(0)) {
                    eventForm.setEventStatus(3);
                } else {
                    if (now.compareTo(startDate) < 0) {
                        //未开始
                        eventForm.setEventStatus(0);
                    } else if (now.compareTo(startDate) > 0 && now.compareTo(endDate) < 0) {
                        //已开始
                        eventForm.setEventStatus(1);
                    } else if (now.compareTo(endDate) > 0) {
                        //已结束
                        eventForm.setEventStatus(2);
                    }
                }

                eventFormList.add(eventForm);
            }
            res.setEventList(eventFormList);
//            res.setActivity(activity);
        } catch (Exception e) {
            logger.error("error {}", e);
        }
        return res;


    }


    /**
     * why3.5 app活动场次列表
     *
     * @param req
     * @return
     */
    @Override
    public IResponse queryActivityEventList(ActivityReq req) {
        QueryActivityEventListRes res = new QueryActivityEventListRes();

        List<Map<String, Object>> listMap = new ArrayList<Map<String, Object>>();
        List<CultureCloudActivityEventEntity> list = cultureCloudActivityEventDao.getListByHql("from CultureCloudActivityEventEntity where state = "+ EntityContext.RECORD_STATE_VALID +
                " and activity" +
                ".id = "+ req.getActivityId()+(StringUtils.isNotEmpty(req.getEventDate())? " and eventDate >= '"+req.getEventDate()+"'":"")
                +(StringUtils.isNotEmpty(req.getEventEndDate())? " and eventDate <= '"+req.getEventEndDate()+"'":""));
        CultureCloudActivityEntity cmsActivity = cultureCloudActivityDao.get(req.getActivityId());
        try {
            if (CollectionUtils.isNotEmpty(list)) {
                for (CultureCloudActivityEventEntity cultureCloudActivityEventEntity : list) {
                    Map<String, Object> mapActivityEvent = new HashMap<String, Object>();
                    mapActivityEvent.put("eventId", cultureCloudActivityEventEntity.getId());
                    mapActivityEvent.put("eventDate", cultureCloudActivityEventEntity.getEventDate() != null ? cultureCloudActivityEventEntity.getEventDate() : "");
                    mapActivityEvent.put("eventEndDate", cultureCloudActivityEventEntity.getEventEndDate() != null ? cultureCloudActivityEventEntity.getEventEndDate() : cultureCloudActivityEventEntity.getEventDate());
                    mapActivityEvent.put("eventTime", cultureCloudActivityEventEntity.getEventTime() != null ? cultureCloudActivityEventEntity.getEventTime() : "");
                    mapActivityEvent.put("availableCount", cultureCloudActivityEventEntity.getAvailableCount() != null ? cultureCloudActivityEventEntity.getAvailableCount() : 0);
                    mapActivityEvent.put("singleEvent", cultureCloudActivityEventEntity.getSingleEvent() != null ? cultureCloudActivityEventEntity.getSingleEvent() : "");
                    mapActivityEvent.put("spikeType", cultureCloudActivityEventEntity.getSpikeType() != null ? cultureCloudActivityEventEntity.getSpikeType() : "");
                    mapActivityEvent.put("spikeTime", cultureCloudActivityEventEntity.getSpikeTime() != null ? cultureCloudActivityEventEntity.getSpikeTime().getTime() / 1000 : "");
                    mapActivityEvent.put("spikeEndTime", cultureCloudActivityEventEntity.getSpikeEndTime() != null ? cultureCloudActivityEventEntity.getSpikeEndTime().getTime() / 1000 : "");
                    mapActivityEvent.put("orderPrice", cultureCloudActivityEventEntity.getOrderPrice() != null ? cultureCloudActivityEventEntity.getOrderPrice() : "");
                    mapActivityEvent.put("orderCount", cultureCloudActivityEventEntity.getOrderCount() != null ? cultureCloudActivityEventEntity.getOrderCount() : 0);
                    long spikeDifference = 0;
                    if (cultureCloudActivityEventEntity.getSpikeTime() != null) {
                        spikeDifference = (cultureCloudActivityEventEntity.getSpikeTime().getTime() - new Date().getTime()) / 1000;
                    }
                    spikeDifference = spikeDifference >= 0 ? spikeDifference : 0;
                    mapActivityEvent.put("spikeDifference", spikeDifference);

                    long spikeEndDifference = 0;
                    if (cultureCloudActivityEventEntity.getSpikeEndTime() != null) {
                        spikeEndDifference = (cultureCloudActivityEventEntity.getSpikeEndTime().getTime() - new Date().getTime()) / 1000;
                    }
                    spikeEndDifference = spikeEndDifference >= 0 ? spikeEndDifference : 0;
                    if (cultureCloudActivityEventEntity.getSpikeEndTime() != null){
                        mapActivityEvent.put("spikeEndDifference", spikeEndDifference);
                    }

                    if (cultureCloudActivityEventEntity.getSpikeType() != null && cultureCloudActivityEventEntity.getSpikeType() == 1){
                        if (cultureCloudActivityEventEntity.getSpikeEndTime() != null){
                            if (spikeDifference == 0 && spikeEndDifference == 0){
                                mapActivityEvent.put("spikeSituation", "ended");
                                if (cultureCloudActivityEventEntity.getSpikeTime() != null && cultureCloudActivityEventEntity.getSpikeEndTime() != null){
                                    mapActivityEvent.put("availableCount", 0);
//                                    Runnable runner = new Runnable() {
//                                        @Override
//                                        public void run() {
//                                            RestTemplateModel restTemplateModel = new RestTemplateModel();
//                                            RestTemplate restTemplate = restTemplateModel.restTemplate();
//                                            MultiValueMap<String,Object> params = new LinkedMultiValueMap<String,Object>();
//                                            params.add("eventId", cultureCloudActivityEventEntity.getEventId());
//                                            params.add("shopProvince", staticServer.getCityInfo().split(",")[6]);
//                                            params.add("userId", "1");
//                                            restTemplate.postForEntity(staticServer.getInterfaceGateway() + "why/back/activity/cleanActivityEventAvailableCount", params, Result.class);
                                            cleanActivityEventAvailableCount(cultureCloudActivityEventEntity.getEventUUID()," ", "1");
//                                        }
//                                    };
//                                    new Thread(runner).start();
                                }
                            } else if (spikeDifference == 0 && spikeEndDifference > 0){
                                mapActivityEvent.put("spikeSituation", "in progress");
                            } else if (spikeDifference > 86400){
                                mapActivityEvent.put("spikeSituation", "more than one day");
                            } else {
                                mapActivityEvent.put("spikeSituation", "less than one day");
                            }
                        } else {
                            if (spikeDifference == 0){
                                mapActivityEvent.put("spikeSituation", "in progress");
                            } else if (spikeDifference > 86400){
                                mapActivityEvent.put("spikeSituation", "more than one day");
                            } else {
                                mapActivityEvent.put("spikeSituation", "less than one day");
                            }
                        }
                    }else {
                        //非秒杀活动，如果当前已过报名时间，余票清零
                        if(cmsActivity.getSignEndTime() != null && new Date().after(cmsActivity.getSignEndTime())){
                            mapActivityEvent.put("availableCount", 0);
//                            Runnable runner = new Runnable() {
//                                @Override
//                                public void run() {
//                                    RestTemplateModel restTemplateModel = new RestTemplateModel();
//                                    RestTemplate restTemplate = restTemplateModel.restTemplate();
//                                    MultiValueMap<String,Object> params = new LinkedMultiValueMap<String,Object>();
//                                    params.add("eventId", cultureCloudActivityEventEntity.getEventId());
//                                    params.add("shopProvince", staticServer.getCityInfo().split(",")[6]);
//                                    params.add("userId", "1");
//                                    restTemplate.postForEntity(staticServer.getInterfaceGateway() + "why/back/activity/cleanActivityEventAvailableCount", params, Result.class);
                            cleanActivityEventAvailableCount(cultureCloudActivityEventEntity.getEventUUID()," ", "1");
//                                }
//                            };
//                            new Thread(runner).start();
                        }
                    }

                    listMap.add(mapActivityEvent);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        res.setListMap(listMap);

        return res;
    }
    
    @Override
    public IResponse modifyActivityEvent(CultureCloudActivityEventReq req){



        return null;
    }





    public IResponse saveChangeEventDate(CultureCloudActivityEventReq req, String spikeTimeStr, String spikeEndTimeStr) throws Exception {

        GenericResponse res = new GenericResponse();
        CultureCloudActivityEventEntity activityEvent = cultureCloudActivityEventDao.get(req.getEventId());
        //先注释
//        req.setShopProvince(staticServer.getCityInfo().split(",")[3]);
        if (StringUtils.isNotBlank(spikeTimeStr) && StringUtils.isNotBlank(spikeEndTimeStr)) {
            String strDateFormat = "yyyy-MM-dd HH:mm";
            SimpleDateFormat sdf = new SimpleDateFormat(strDateFormat);
            req.setSpikeTime(new Timestamp(sdf.parse(spikeTimeStr).getTime()));
            req.setSpikeEndTime(new Timestamp(sdf.parse(spikeEndTimeStr).getTime()));
        }
        if (StringUtils.isBlank(req.getEventEndDate())) {
            req.setEventEndDate(req.getEventDate());
        }

        //查询场次订单
        List<String> list = new ArrayList<>();
        //先注释
//        CmsShop shop = (CmsShop) session.getAttribute("shop");
        String shopPath = null;
//        if (shop != null && StringUtils.isNotBlank(shop.getShopId())) {
//            shopPath = shop.getShopPath();
//        }
        UserEntity user = null != getPrincipal(true)?(UserEntity) getPrincipal(true):null;
        if (user != null) {

            String hql = "select distinct CultureCloudActivityEntity a from CultureCloudActivityEntity a left join CultureCloudActivityOrderEntity " +
                    "o on o.activityId=a.id left join CultureCloudActivityEventEntity" +
                    " e ON e.activityId = o" +
                    ".activityId and e.id = o.eventId where o.eventId = "+req.getEventId();

            List<CultureCloudActivityEntity> activityList1 = cultureCloudActivityDao.getListByHql(hql);
            List<CultureCloudActivityEntity> rsList = new ArrayList<>();
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            String nowDate = sdf.format(date);
            if (CollectionUtils.isNotEmpty(activityList1)) {
                for (CultureCloudActivityEntity cmsActivity : activityList1) {
                    if ((short) 1 == cmsActivity.getOrderPayStatus()) {
                        if (StringUtils.isNoneBlank(cmsActivity.getEventDateTimes())) {
                            //2020-04-25 修改未过期活动改为活动结束时间为准
                            String eventDateTime = cmsActivity.getEventDateTimes().substring(0, 11) + cmsActivity.getEventDateTimes().substring(17, 22);
                            int statusDate = TimeCompareUtil.timeCompare2(eventDateTime, nowDate);
                            //返回 0 表示时间日期相同
                            //返回 1 表示日期1>日期2
                            //返回 -1 表示日期1<日期2
                            if (statusDate == -1) {
                                cmsActivity.setOrderPayStatus((short) 5);
                            }
                        }
                    }
                    //还没有 先注释
//                    List<CmsActivityOrderDetail> detailList=cmsActivityOrderDetailService.queryCmsActivityOrderDetailsByOrderId(cmsActivity.getActivityOrderId());
//                    cmsActivity.setDetailList(detailList);
                    rsList.add(cmsActivity);
                }
            }

            List<CultureCloudActivityEntity> activityList = rsList;


            for (CultureCloudActivityEntity cmsActivity : activityList) {
                //只有订单状态为1时需要发送延期短信和标志位
                if (cmsActivity.getOrderPayStatus() != 1) {
                    continue;
                }
                list.add(cmsActivity.getActivityOrderId());
            }
        }
        req.setEventOrderList(list);
        //shopPath
        req.setShopPath(shopPath);
        //封装发送短信的场次时间
        CultureCloudActivityEventEntity CultureCloudActivityEventEntity = cultureCloudActivityEventDao.get(req.getEventId());
        String eventTime = "";
        String newEventTime = "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd号");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        //原来的场次日期
        String startDate = sdf.format(sdf1.parse(CultureCloudActivityEventEntity.getEventDate()));
        //新的场次开始日期
        String newStartDate = sdf.format(sdf1.parse(req.getEventDate()));
        //如果不是同一天的场次 需要拼接结束日期
        if (!CultureCloudActivityEventEntity.getEventDate().equals(CultureCloudActivityEventEntity.getEventEndDate())) {
            eventTime = startDate + "至" + sdf.format(sdf1.parse(CultureCloudActivityEventEntity.getEventEndDate())) + CultureCloudActivityEventEntity.getEventTime();
            newEventTime = newStartDate + "至" + sdf.format(sdf1.parse(req.getEventEndDate()));
        } else {
            eventTime = startDate + CultureCloudActivityEventEntity.getEventTime();
            newEventTime = newStartDate;
        }
        //短信中原场次时间
        req.setSmsEventTime(eventTime);
        //短信中新的场次时间
        req.setSmsNewEventTime(newEventTime);
        //先注释
//        req.setShopProvince(staticServer.getCityInfo().split(",")[3]);
        UserEntity sysUser = null != getPrincipal(true)?(UserEntity) getPrincipal(true):null;
        req.setSysUserId(sysUser.getId().toString());


        //请求传到接口 自己调用自己？ 先注释
//        Result result = currencyInterfaceService.postForEntityResult(staticServer.getInterfaceGateway() + "why/back/activityEvent/saveChangeEventDate", req);
//        if (result != null && result.getStatus().equals(200)) {
            //省级表需要手动同步活动时间
            req.setEventTime(req.getEventStartHours() + ":" + req.getEventStartMinute() + "-" + req.getEventEndHours() + ":" + req.getEventEndMinute());
            CultureCloudActivityEntity activity = cultureCloudActivityDao.get(activityEvent.getActivity().getId());
            //新场次和原场次时间不同
            if ((!req.getEventDate().equals(activityEvent.getEventDate())) ||
                    (!req.getEventEndDate().equals(activityEvent.getEventEndDate())) ||
                    (!(req.getEventTime()).equals(activityEvent.getEventTime()))) {

                activityEvent.setEventDate(req.getEventDate());
                activityEvent.setEventTime(req.getEventTime());
                activityEvent.setEventEndDate(req.getEventEndDate());
                activityEvent.setEventDateTime(req.getEventEndDate() + " " + req.getEventTime());
                //如果修改后的场次日期晚于活动日期，则同步修改活动结束日期为当前修改后的日期；
                String activityEndPoint = activity.getActivityEndTime() + " " + activity.getActivityTime().split("-")[1];
                String eventEndPoint = req.getEventEndDate() + " " + req.getEventEndHours() + ":" + req.getEventEndMinute();
                String strDateFormat = "yyyy-MM-dd HH:mm";
                SimpleDateFormat sdf2 = new SimpleDateFormat(strDateFormat);
                if (sdf2.parse(eventEndPoint).compareTo(sdf2.parse(activityEndPoint)) > 0) {
                    //不跨天修改起始时间
                    /*if (cmsActivityVO.getActivityEndTime().equals(cmsActivityVO.getActivityStartTime())){
                        cmsActivityVO.setActivityStartTime(CultureCloudActivityEventEntityVO.getEventEndDate());
                    }*/
                    //跨天场次需要修改结束时间
                    activity.setActivityEndTime(req.getEventEndDate());
                    activity.setActivityTime(activity.getActivityTime().split("-")[0] + "-" + req.getEventEndHours() + ":" + req.getEventEndMinute());
                    //更新活动结束时间
                    cultureCloudActivityDao.update(activity);
//                    int i = activityMapper.changeEventDateAndAvtivityEndDate(cmsActivityVO);

//                    if (i == 0) {
//                        return Constant.RESULT_STR_FAILURE;
//                    }
                }
            }

            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

            return res;
//        } else {
//            res.setRet(ResponseContext.RES_FAILED_CODE);
//
//            return res;
//        }
    }

    /**
     * 清除活动场次余票数
     * @param eventId
     * @param shopProvince
     * @param userId
     * @return
     */
    public String cleanActivityEventAvailableCount(String eventId,String shopProvince, String userId) {
        if (StringUtils.isNotBlank(eventId) && StringUtils.isNotBlank(userId)) {
            ArrayList<String> arrayList = new ArrayList<String>();
            arrayList.add(eventId);
            String[] eventIds = arrayList.toArray(new String[0]);
            JSONObject json = (JSONObject)updateCleanActivityTickets(eventIds, userId);
            if ("200".equals(json.get("state").toString())) {
                return "200";
            } else {
                return json.get("state").toString();
            }
        } else {
            return "参数缺失";
        }
    }


    public Object updateCleanActivityTickets(String[] eventIds, String userId) {
        JSONObject object= new JSONObject();

        for (int i = 0; i < eventIds.length; i++) {
            String eventId = eventIds[i];
            Map<String, Object> map =new HashMap<String,Object>();
            if(StringUtils.isBlank(eventId)){
                object.put("state", "400");
                object.put("data", "输入的场次编号不能为空！");
                return object;
            }else if(eventId.length()!=32){
                object.put("state", "400");
                object.put("data", "输入的场次编号不对！");
                return object;
            }else{
                map.put("eventId", eventId);
            }
            CultureCloudActivityEventEntity event=cultureCloudActivityEventDao.get(eventId);
            Integer availableCount = event.getAvailableCount();
            if(event.getOrderCount()==null){
                event.setOrderCount(0);
            }
            if(event.getAvailableCount()==null){
                event.setAvailableCount(0);
            }
            event.setOrderCount(event.getOrderCount()-event.getAvailableCount());
            event.setAvailableCount(0);
            Object[] args = { eventId, availableCount+"" };
            String[] params = { "eventId", "cleanOrderNumber" };
            try {
                cultureCloudActivityEventDao.update(event);
//                if(re>0){
//                    cmsActivityLog.createLog(userId, args, params, ActivityInspect.User_Type_Admin,
//                            ActivityOperationEnum.CLEANTICKETS, Constant.RESULT_STR_SUCCESS);

                object.put("state", "200");
                object.put("data", "场次清空票数成功！");
//                } else {
//                }

            }catch (Exception e){
//                cmsActivityLog.createLog(userId, args, params, ActivityInspect.User_Type_Admin,
//                        ActivityOperationEnum.CLEANTICKETS, Constant.RESULT_STR_FAILURE);

                object.put("state", "500");
                object.put("data", "新增票数失败！");
                return object;

            }

        }

        return object;
    }

}
