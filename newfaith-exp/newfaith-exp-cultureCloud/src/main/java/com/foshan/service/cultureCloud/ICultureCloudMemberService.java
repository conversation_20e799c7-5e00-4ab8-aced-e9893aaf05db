package com.foshan.service.cultureCloud;

import javax.servlet.http.HttpServletRequest;

import com.foshan.form.cultureCloud.request.CultureCloudMemberReq;
import com.foshan.form.response.IResponse;

public interface ICultureCloudMemberService {
	public IResponse getCultureCloudMemberList(CultureCloudMemberReq req) ;
	public IResponse modifyCultureCloudMember(CultureCloudMemberReq req);
	public IResponse memberRegist(CultureCloudMemberReq req, HttpServletRequest request);
	public IResponse setMemberCloseComments(CultureCloudMemberReq req);
	public IResponse getCultureCloudMemberInfo(CultureCloudMemberReq req) ;
	public IResponse memberAuthentication(CultureCloudMemberReq req) ;
	public IResponse  submitAuthentication(CultureCloudMemberReq req);
	public IResponse modifyMemberPassword(CultureCloudMemberReq req);

}
