package com.foshan.service.cultureCloud.impl;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.math.BigInteger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.entity.cultureCloud.CultureCloudStatisticsFlowEntity;
import com.foshan.form.cultureCloud.request.CultureCloudStatisticsFlowReq;
import com.foshan.form.cultureCloud.response.cultureCloudStatisticsFlow.GetCultureCloudStatisticsFlowDataRes;
import com.foshan.form.cultureCloud.response.cultureCloudStatisticsFlow.GetCultureCloudStatisticsTotalCountRes;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.cultureCloud.ICultureCloudStatisticsFlowService;
import com.foshan.util.DateUtil;

@Transactional
@Service("cultureCloudStatisticsFlowService")
public class CultureCloudStatisticsFlowServiceImpl extends GenericCultureCloudService implements ICultureCloudStatisticsFlowService{

	private final static Logger logger = LoggerFactory.getLogger(CultureCloudStatisticsFlowServiceImpl.class);

	@Override
	public IResponse getCultureCloudStatisticsFlowData(CultureCloudStatisticsFlowReq req) {
		GetCultureCloudStatisticsFlowDataRes res = new GetCultureCloudStatisticsFlowDataRes();
		if (null!=req.getDataType() && null!=req.getFlowType() && StringUtils.isNotEmpty(req.getFlowDate())) {
			if(req.getDataType()==0) {
				String hql = "select distinct a from CultureCloudStatisticsFlowEntity a " +
					"where a.flowType = " + req.getFlowType() + " " +
					"and a.flowDate like '%" + req.getFlowDate() + "%' " +
					"order by flowDate asc";
				
				List<CultureCloudStatisticsFlowEntity> list = cultureCloudStatisticsFlowDao.getListByHql(hql, "");
				
				list.forEach(o->{
					res.getFlowDateList().add(DateUtil.format(o.getFlowDate(),2));
					res.setPvTotal(res.getPvTotal()+o.getPv());
					res.setUvTotal(res.getUvTotal()+o.getUv());
					res.setIpTotal(res.getIpTotal()+o.getIp());
					res.getPvList().add(o.getPv());
					res.getUvList().add(o.getUv());
					res.getIpList().add(o.getIp());
				});
			}else if(req.getDataType()==1) {
				String sql = "SELECT DATE_FORMAT(flowDate,'%Y-%m') as MONTH, " +
					"SUM(pv) as pv, SUM(uv) as uv, SUM(ip) as ip " +
					"FROM t_culture_cloud_statistics_flow " +
					"WHERE flowDate LIKE '%" + req.getFlowDate() + "%' " +
					"AND flowType = " + req.getFlowType() + " " +
					"GROUP BY MONTH";
					
				@SuppressWarnings("unchecked")
				List<Object[]> list = cultureCloudStatisticsFlowDao.createSQLQuery(sql).list();
					
				list.forEach(o -> {
					res.getFlowDateList().add(o[0].toString());
					res.getPvList().add(Integer.parseInt(o[1].toString()));
					res.getUvList().add(Integer.parseInt(o[2].toString()));
					res.getIpList().add(Integer.parseInt(o[3].toString()));
					res.setPvTotal(res.getPvTotal()+Integer.parseInt(o[1].toString()));
					res.setUvTotal(res.getUvTotal()+Integer.parseInt(o[2].toString()));
					res.setIpTotal(res.getIpTotal()+Integer.parseInt(o[3].toString()));
				});
			}else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
	
		return res;
	}
	
	
	@Override
	public IResponse getCultureCloudStatisticsTotalCount(CultureCloudStatisticsFlowReq req) {
		GetCultureCloudStatisticsTotalCountRes res = new GetCultureCloudStatisticsTotalCountRes();
		if (StringUtils.isNotEmpty(req.getStartDate()) && StringUtils.isNotEmpty(req.getEndDate())) {
			String sql = "SELECT SUM(pv) as pv, SUM(uv) as uv, SUM(ip) as ip " +
				"FROM t_culture_cloud_statistics_flow " +
				"WHERE flowDate >= '" + req.getStartDate() + " 00:00:00' " +
				"AND flowDate <= '" + req.getEndDate() + " 23:59:59' " +
				(null != req.getFlowType() ? "AND flowType = " + req.getFlowType() : "");
				
			@SuppressWarnings("unchecked")
			List<Object[]> list = cultureCloudStatisticsFlowDao.createSQLQuery(sql).list();
			
			list.forEach(o -> {
				res.setPvCount(null != o[0] ? Integer.parseInt(o[0].toString()) : 0);
				res.setUvCount(null != o[1] ? Integer.parseInt(o[1].toString()) : 0);
				res.setIpCount(null != o[2] ? Integer.parseInt(o[2].toString()) : 0);
			});
			
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

//	@Override
//	@Scheduled(cron = "0 */10 * * * ?")
//	public void statisticsVisitData() {
//		logger.info("开始执行10分钟访问量统计...");
//		try {
//			Calendar calendar = Calendar.getInstance();
//			Date endTime = calendar.getTime();
//			calendar.add(Calendar.MINUTE, -10);
//			Date startTime = calendar.getTime();
//
//			// 构建统计SQL
//			String statisticsSql = "SELECT COUNT(*) as pv, " +
//				"COUNT(DISTINCT macAddr) as uv, " +
//				"COUNT(DISTINCT ipAddr) as ip, " +
//				"deviceType as flowType, " +
//				"DATE(visitTime) as visitDate " +
//				"FROM t_visit_real " +
//				"WHERE visitTime >= '" + DateUtil.format(startTime, 1) + "' " +
//				"AND visitTime < '" + DateUtil.format(endTime, 1) + "' " +
//				"GROUP BY deviceType, DATE(visitTime)";
//
//			logger.info("执行统计SQL: " + statisticsSql);
//			logger.info("参数: startTime=" + DateUtil.format(startTime, 1) +
//				", endTime=" + DateUtil.format(endTime, 1));
//
//			@SuppressWarnings("unchecked")
//			List<Object[]> statisticsData = cultureCloudStatisticsFlowDao.createSQLQuery(statisticsSql)
//				.list();
//
//			logger.info("获取到" + statisticsData.size() + "条统计数据");
//
//			// 遍历统计结果并更新或插入
//			int updateCount = 0;
//			int insertCount = 0;
//			for (Object[] data : statisticsData) {
//				String dateStr = data[4].toString() + " 00:00:00";
//				Date visitDate = DateUtil.parse(dateStr, 1);
//				Integer flowType = Integer.parseInt(data[3].toString());
//
//				// 查找当天是否已有记录
//				String hql = "FROM CultureCloudStatisticsFlowEntity " +
//					"WHERE DATE(flowDate) = DATE('" + DateUtil.format(visitDate, 1) + "') " +
//					"AND flowType = " + flowType;
//
//				CultureCloudStatisticsFlowEntity entity = cultureCloudStatisticsFlowDao.getUniqueByHql(hql);
//
//				if (entity == null) {
//					// 新建记录
//					entity = new CultureCloudStatisticsFlowEntity();
//
//					entity.setFlowDate(new Timestamp(visitDate.getTime())); // 转换为Timestamp类型
//					entity.setFlowType(flowType);
//					entity.setPv(Integer.parseInt(data[0].toString()));
//					entity.setUv(Integer.parseInt(data[1].toString()));
//					entity.setIp(Integer.parseInt(data[2].toString()));
//					insertCount++;
//					logger.debug("新增统计记录 - 日期:" + dateStr + ", 类型:" + flowType +
//						", PV:" + data[0].toString() +
//						", UV:" + data[1].toString() +
//						", IP:" + data[2].toString());
//				} else {
//					// 更新已有记录
//					int oldPv = entity.getPv();
//					int oldUv = entity.getUv();
//					int oldIp = entity.getIp();
//					entity.setPv(entity.getPv() + Integer.parseInt(data[0].toString()));
//					entity.setUv(entity.getUv() + Integer.parseInt(data[1].toString()));
//					entity.setIp(entity.getIp() + Integer.parseInt(data[2].toString()));
//					updateCount++;
//					logger.debug("更新统计记录 - 日期:" + dateStr + ", 类型:" + flowType +
//						", PV:" + oldPv + "->" + entity.getPv() +
//						"，UV:" + oldUv + "->" + entity.getUv() +
//						"，IP:" + oldIp + "->" + entity.getIp());
//				}
//
//				cultureCloudStatisticsFlowDao.saveOrUpdate(entity);
//			}
//
//			// 删除已统计数据时输出SQL
//			String deleteSql = "DELETE FROM t_visit_real WHERE visitTime < :endTime";
//			logger.info("执行删除SQL: " + deleteSql);
//			logger.info("参数: endTime=" + DateUtil.format(endTime, 1));
//
//			int deletedCount = cultureCloudStatisticsFlowDao.createSQLQuery(deleteSql)
//				.setParameter("endTime", endTime)
//				.executeUpdate();
//
//			logger.info("统计完成 - 新增:" + insertCount + ", 更新:" + updateCount +
//				", 清理原始数据:" + deletedCount + " 条");
//
//			// 统计完成后，迁移数据到历史表
//			migrateToHistory(startTime, endTime);
//
//		} catch (Exception e) {
//			logger.error("访问量统计失败", e);
//		}
//	}

//	@Override
//	@Scheduled(cron = "0 52 9 * * ?")
//	public void dailyDistinctCount() {
//		logger.info("开始执行前一天UV/IP去重统计...");
//		try {
//			// 获取前一天的日期范围
//			Calendar calendar = Calendar.getInstance();
//			calendar.add(Calendar.DATE, -1);  // 前一天
//			calendar.set(Calendar.HOUR_OF_DAY, 0);
//			calendar.set(Calendar.MINUTE, 0);
//			calendar.set(Calendar.SECOND, 0);
//			Date startTime = calendar.getTime();
//
//			calendar.set(Calendar.HOUR_OF_DAY, 23);
//			calendar.set(Calendar.MINUTE, 59);
//			calendar.set(Calendar.SECOND, 59);
//			Date endTime = calendar.getTime();
//
//			// 构建去重统计SQL，只统计前一天的数据
//			String distinctSql = "SELECT COUNT(DISTINCT macAddr) as uv, " +
//				"COUNT(DISTINCT ipAddr) as ip, " +
//				"deviceType as flowType, " +
//				"DATE(visitTime) as visitDate " +
//				"FROM t_visit_real " +
//				"WHERE visitTime >= '" + DateUtil.format(startTime, 1) + "' " +
//				"AND visitTime <= '" + DateUtil.format(endTime, 1) + "' " +
//				"GROUP BY deviceType, DATE(visitTime)";
//
//			logger.info("执行去重统计SQL: " + distinctSql);
//			logger.info("统计日期: " + DateUtil.format(startTime, 1));
//
//			@SuppressWarnings("unchecked")
//			List<Object[]> distinctData = cultureCloudStatisticsFlowDao.createSQLQuery(distinctSql)
//				.list();
//
//			logger.info("获取到" + distinctData.size() + "个统计数据");
//
//			// 更新统计表中的UV和IP数据
//			int updateCount = 0;
//			for (Object[] data : distinctData) {
//				Integer flowType = Integer.parseInt(data[2].toString());
//				Integer uvCount = Integer.parseInt(data[0].toString());
//				Integer ipCount = Integer.parseInt(data[1].toString());
//				String visitDate = data[3].toString();
//
//				// 构建更新SQL，直接将变量值拼接到SQL中
//				String updateSql = "UPDATE t_culture_cloud_statistics_flow " +
//					"SET uv = " + uvCount + ", ip = " + ipCount + " " +
//					"WHERE DATE(flowDate) = DATE('" + visitDate + "') " +
//					"AND flowType = " + flowType;
//
//				logger.info("执行更新SQL: " + updateSql);
//
//				int affected = cultureCloudStatisticsFlowDao.createSQLQuery(updateSql)
//					.executeUpdate();
//
//				updateCount += affected;
//				logger.debug("更新统计记录 - 日期:" + visitDate + ", 类型:" + flowType +
//					", UV:" + uvCount + ", IP:" + ipCount);
//			}
//
//			logger.info("去重统计完成 - 更新" + updateCount + "条记录");
//
//			// 统计完成后，迁移前一天的数据到历史表
//			migrateToHistory(startTime, endTime);
//
//		} catch (Exception e) {
//			logger.error("UV/IP去重统计失败", e);
//		}
//	}


	// 在类中添加新方法
	@Scheduled(cron = "0 10 0 * * ?")
	public void dailyDistinctCount() {
		logger.info("开始从t_visit_real统计有数据并写入统计表...");
		try {
			// 获取前一天的日期范围
			Calendar calendar = Calendar.getInstance();
			calendar.add(Calendar.DATE, -1);  // 前一天
			calendar.set(Calendar.HOUR_OF_DAY, 0);
			calendar.set(Calendar.MINUTE, 0);
			calendar.set(Calendar.SECOND, 0);
			Date sqlStartTime = calendar.getTime();

			calendar.set(Calendar.HOUR_OF_DAY, 23);
			calendar.set(Calendar.MINUTE, 59);
			calendar.set(Calendar.SECOND, 59);
			Date sqlEndTime = calendar.getTime();

			String statisticsSql = "SELECT COUNT(*) as pv, " +
				"COUNT(DISTINCT macAddr) as uv, " +
				"COUNT(DISTINCT ipAddr) as ip, " +
				"deviceType as flowType, " +
				"DATE(visitTime) as visitDate " +
				"FROM t_visit_real " +
					"WHERE visitTime >= '" + DateUtil.format(sqlStartTime, 1) + "' " +
					"AND visitTime <= '" + DateUtil.format(sqlEndTime, 1) + "' " +
				"GROUP BY deviceType, DATE(visitTime)";
				
			logger.info("执行统计SQL: " + statisticsSql);
				
			@SuppressWarnings("unchecked")
			List<Object[]> statisticsData = cultureCloudStatisticsFlowDao.createSQLQuery(statisticsSql)
				.list();
				
			logger.info("获取到" + statisticsData.size() + "条统计数据");
			
			// 遍历统计结果并更新或插入
			int updateCount = 0;
			int insertCount = 0;
			for (Object[] data : statisticsData) {
				String dateStr = data[4].toString() + " 00:00:00";  // visitDate在第6个位置
				Date visitDate = DateUtil.parse(dateStr, 1);
				Integer flowType = Integer.parseInt(data[3].toString());
				
				// 查找当天是否已有记录
				String hql = "FROM CultureCloudStatisticsFlowEntity " +
					"WHERE DATE(flowDate) = DATE('" + DateUtil.format(visitDate, 1) + "') " +
					"AND flowType = " + flowType;
					
				CultureCloudStatisticsFlowEntity entity = cultureCloudStatisticsFlowDao.getUniqueByHql(hql);
				
				if (entity == null) {
					// 新建记录
					entity = new CultureCloudStatisticsFlowEntity();
					
					entity.setFlowDate(new Timestamp(visitDate.getTime()));
					entity.setFlowType(flowType);
					entity.setPv(Integer.parseInt(data[0].toString()));
					entity.setUv(Integer.parseInt(data[1].toString()));
					entity.setIp(Integer.parseInt(data[2].toString()));
					insertCount++;
					logger.debug("新增统计记录 - 日期:" + dateStr + ", 类型:" + flowType + 
						", PV:" + data[0].toString() + 
						", UV:" + data[1].toString() + 
						", IP:" + data[2].toString());
				} else {
					// 更新已有记录
					int oldPv = entity.getPv();
					int oldUv = entity.getUv();
					int oldIp = entity.getIp();
					entity.setPv(entity.getPv() + Integer.parseInt(data[0].toString()));
					entity.setUv(entity.getUv() + Integer.parseInt(data[1].toString()));
					entity.setIp(entity.getIp() + Integer.parseInt(data[2].toString()));
					updateCount++;
					logger.debug("更新统计记录 - 日期:" + dateStr + ", 类型:" + flowType + 
						", PV:" + oldPv + "->" + entity.getPv() + 
						"，UV:" + oldUv + "->" + entity.getUv() + 
						"，IP:" + oldIp + "->" + entity.getIp());
				}
				
				cultureCloudStatisticsFlowDao.saveOrUpdate(entity);
			}
			
			logger.info("统计数据写入完成 - 新增:" + insertCount + ", 更新:" + updateCount);
			

				// 统计完成后，迁移数据到历史表
				migrateToHistory(sqlStartTime, sqlEndTime);

			
		} catch (Exception e) {
			logger.error("从t_visit_real统计数据失败", e);
		}
	}

	/**
	 * 将已统计的数据迁移到历史表并清除源数据
	 */
	private void migrateToHistory(Date startTime, Date endTime) {
		logger.info("开始迁移数据到历史表...");
		try {
			// 1. 插入历史数据
			String insertSql = "INSERT INTO t_visit_history (visitTime, deviceType, macAddr, ipAddr, serviceId, fromAddr, toAddr) " +
				"SELECT visitTime, deviceType, macAddr, ipAddr, serviceId , fromAddr, toAddr" +
				" FROM t_visit_real " +
				"WHERE visitTime >= '" + DateUtil.format(startTime, 1) + "' " +
				"AND visitTime < '" + DateUtil.format(endTime, 1) + "'";
				
			logger.info("执行数据迁移SQL: " + insertSql);
			logger.info("参数: startTime=" + DateUtil.format(startTime, 1) + 
				", endTime=" + DateUtil.format(endTime, 1));
				
			int insertCount = cultureCloudStatisticsFlowDao.createSQLQuery(insertSql)
				.executeUpdate();
				
			if (insertCount > 0) {
				logger.info("成功迁移 " + insertCount + " 条数据到历史表");
				
				// 2. 删除已迁移的源数据
				String deleteSql = "DELETE FROM t_visit_real " +
					"WHERE visitTime >= '" + DateUtil.format(startTime, 1) + "' " +
					"AND visitTime < '" + DateUtil.format(endTime, 1) + "'";
					
				logger.info("执行删除源数据SQL: " + deleteSql);
					
				int deleteCount = cultureCloudStatisticsFlowDao.createSQLQuery(deleteSql)
					.executeUpdate();
					
				logger.info("成功删除 " + deleteCount + " 条源数据");
					
				if (deleteCount != insertCount) {
					logger.warn("迁移数量(" + insertCount + ")与删除数量(" + deleteCount + ")不一致，请检查数据");
				}
			} else {
				logger.warn("没有数据需要迁移到历史表");
			}
				
		} catch (Exception e) {
			logger.error("数据迁移失败", e);
			throw e;
		}
	}

}
