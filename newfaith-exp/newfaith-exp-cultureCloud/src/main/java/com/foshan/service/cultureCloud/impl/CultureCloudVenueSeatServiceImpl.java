package com.foshan.service.cultureCloud.impl;

import static java.util.Comparator.comparingInt;
import static java.util.stream.Collectors.groupingBy;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foshan.dao.generic.Page;
import com.foshan.entity.cultureCloud.CultureCloudActivityEventEntity;
import com.foshan.entity.cultureCloud.CultureCloudMemberEntity;
import com.foshan.entity.cultureCloud.CultureCloudVenueSeatEntity;
import com.foshan.form.cultureCloud.CultureCloudActivityEventForm;
import com.foshan.form.cultureCloud.CultureCloudVenueSeatAndEventForm;
import com.foshan.form.cultureCloud.CultureCloudVenueSeatForm;
import com.foshan.form.cultureCloud.request.CultureCloudVenueSeatReq;
import com.foshan.form.cultureCloud.response.cultureCloudVenueSeat.GetVenueSeatByActivityListRes;
import com.foshan.form.cultureCloud.response.cultureCloudVenueSeat.GetVenueSeatListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.cultureCloud.ICultureCloudVenueSeatService;

@Transactional
@Service("cultureCloudVenueSeatService")
public class CultureCloudVenueSeatServiceImpl extends GenericCultureCloudService implements ICultureCloudVenueSeatService{
	
	
	public IResponse getVenueSeatList(CultureCloudVenueSeatReq req) {
		GetVenueSeatListRes res = new GetVenueSeatListRes();
		Page<CultureCloudVenueSeatEntity> page = new Page<CultureCloudVenueSeatEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CultureCloudVenueSeatEntity a ");
		hql.append(null!=req.getActivityEventId() ? " inner join a.activityEvent b ":"")
			.append(null!=req.getActivityId() ? " inner join a.activity c ":"")
			.append(null!=req.getVenueSeatTemplateId() ? " inner join a.venueSeatTemplate d ":"")
			.append(null!=req.getMemberId() ? " inner join a.member e ":"")
			.append(" where a.state=1")
			.append(null!=req.getActivityEventId() ? " and b.id="+req.getActivityEventId():" and a.activityEvent is null")
			.append(null!=req.getActivityId() ? " and c.id="+req.getActivityId():"")
			.append(null!=req.getVenueSeatTemplateId() ? " and d.id="+req.getVenueSeatTemplateId():"")
			.append(StringUtils.isNotEmpty(req.getSeatCode()) ? " and a.seatCode like'%"+req.getSeatCode()+"%":"")
			.append(null!=req.getMemberId() ?" and e.id="+req.getMemberId() :"")
			.append(StringUtils.isNotEmpty(req.getSeatStatusList())  ? " and a.seatStatus in("+req.getSeatStatusList()+")":"");
		List<CultureCloudVenueSeatEntity> vsList = cultureCloudVenueSeatDao.getListByHql(hql.toString(), "");
		if(null != vsList) {
			Map<String, List<CultureCloudVenueSeatEntity>> collect1 = (Map<String, List<CultureCloudVenueSeatEntity>>) vsList
					.parallelStream().collect(groupingBy(CultureCloudVenueSeatEntity::getSeatArea));
			List<String> kyList = collect1.keySet()
		            .stream()
		            .collect(Collectors.toList());
			for(String key : collect1.keySet()) {
				Map<Integer, List<CultureCloudVenueSeatEntity>> collect2 = (Map<Integer, List<CultureCloudVenueSeatEntity>>) collect1.get(key)
						.parallelStream().collect(groupingBy(CultureCloudVenueSeatEntity::getSeatRow));
				Map<Integer,List<CultureCloudVenueSeatForm>> map = new HashMap<>();
				for(Integer row : collect2.keySet()) {
					List<CultureCloudVenueSeatEntity> venueSeatList = collect2.get(row);
					venueSeatList.sort(comparingInt(CultureCloudVenueSeatEntity::getSeatColumn));
					List<CultureCloudVenueSeatForm> list = new ArrayList<>();
					for(CultureCloudVenueSeatEntity venueSeat:venueSeatList) {
						CultureCloudVenueSeatForm venueSeatForm = new CultureCloudVenueSeatForm();
						BeanUtils.copyProperties(venueSeat, venueSeatForm);
						venueSeatForm.setSeatId(venueSeat.getId());
						list.add(venueSeatForm);
					}
					map.put(row, list);
				}
				res.getVenueSeatMap().put(key, map);
			}
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	@Override
	public IResponse modifyVenueSeat(CultureCloudVenueSeatReq req) {
		GenericResponse res = new GenericResponse();
		Object userObj = getPrincipal(true);
        if (null == userObj) {
            res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
            return res;
        }
		if (StringUtils.isNotEmpty(req.getVenueSeatIdList())) {
			if(!req.getSeatStatus().toString().equals("7") && !req.getSeatStatus().toString().equals("8")) {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			String[] ids = req.getVenueSeatIdList().split(",");
			for (String id : ids) {
				CultureCloudVenueSeatEntity seat = cultureCloudVenueSeatDao.get(Integer.parseInt(id));
				if(null != seat){
					if(req.getSeatStatus() == 7) {
						if(seat.getSeatStatus()==1 ) {
							seat.setSeatStatus(5);
						}else if(seat.getSeatStatus()==4) {
							seat.setSeatStatus(6);
						}
						if (userObj instanceof CultureCloudMemberEntity) {
							CultureCloudMemberEntity member = (CultureCloudMemberEntity) userObj;
							seat.setMember(member);
						}
					}else if(req.getSeatStatus() == 8){
						if(seat.getSeatStatus()==5 ) {
							seat.setSeatStatus(1);
						}else if(seat.getSeatStatus()==6) {
							seat.setSeatStatus(4);
						}
						seat.setMember(null);
					}
				}else{
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
			}
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	
	
	
	public IResponse getVenueSeatByActivityList(CultureCloudVenueSeatReq req) {
		GetVenueSeatByActivityListRes res = new GetVenueSeatByActivityListRes();
		Page<CultureCloudVenueSeatEntity> page = new Page<CultureCloudVenueSeatEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CultureCloudVenueSeatEntity a ");
		hql.append(StringUtils.isNotEmpty(req.getActivityEventIdList())? " inner join a.activityEvent b ":"")
			.append(" inner join a.activity c ")
			.append(null!=req.getVenueSeatTemplateId() ? " inner join a.venueSeatTemplate d ":"")
			.append(null!=req.getMemberId() ? " inner join a.member e ":"")
			.append(" where a.state=1")
			.append(StringUtils.isNotEmpty(req.getActivityEventIdList()) ? " and b.id in("+req.getActivityEventIdList()+")":" and a.activityEvent is null")
			.append(null!=req.getActivityId() ? " and c.id="+req.getActivityId():"")
			.append(null!=req.getVenueSeatTemplateId() ? " and d.id="+req.getVenueSeatTemplateId():"")
			.append(StringUtils.isNotEmpty(req.getSeatCode()) ? " and a.seatCode like'%"+req.getSeatCode()+"%":"")
			.append(null!=req.getMemberId() ?" and e.id="+req.getMemberId() :"")
			.append(StringUtils.isNotEmpty(req.getSeatStatusList())  ? " and a.seatStatus in("+req.getSeatStatusList()+")":"");
		List<CultureCloudVenueSeatEntity> vsList = cultureCloudVenueSeatDao.getListByHql(hql.toString(), "");
		if(null != vsList) {
			if(StringUtils.isNotEmpty(req.getActivityEventIdList())) {
				Map<CultureCloudActivityEventEntity, List<CultureCloudVenueSeatEntity>> collect1 = (Map<CultureCloudActivityEventEntity, List<CultureCloudVenueSeatEntity>>) vsList
						.parallelStream().collect(groupingBy(CultureCloudVenueSeatEntity::getActivityEvent));
				for(CultureCloudActivityEventEntity activityEvent : collect1.keySet()) {
					CultureCloudVenueSeatAndEventForm seatAndEvent = new CultureCloudVenueSeatAndEventForm();
					seatAndEvent.setEventId(activityEvent.getId());
					CultureCloudActivityEventForm eventForm = new CultureCloudActivityEventForm();
					if(null != activityEvent) {
		                eventForm.setId(activityEvent.getId());
		                BeanUtils.copyProperties(activityEvent, eventForm);
					}
					seatAndEvent.setActivityEvent(eventForm);
					getSeatMap(collect1.get(activityEvent), seatAndEvent);
					res.getVenueSeatList().add(seatAndEvent);
				}
			}else {
				CultureCloudActivityEventForm eventForm = new CultureCloudActivityEventForm();
				CultureCloudVenueSeatAndEventForm seatAndEvent = new CultureCloudVenueSeatAndEventForm();
				seatAndEvent.setEventId(0);
				seatAndEvent.setActivityEvent(eventForm);
				getSeatMap(vsList, seatAndEvent);
				res.getVenueSeatList().add(seatAndEvent);
			}

		}
		res.getVenueSeatList().sort(comparingInt(CultureCloudVenueSeatAndEventForm::getEventId));
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	public void getSeatMap(List<CultureCloudVenueSeatEntity> seatList,CultureCloudVenueSeatAndEventForm seatAndEvent) {	
		Map<String, Map<Integer,List<CultureCloudVenueSeatForm>>> venueSeatMap = new HashMap<>();
		Map<String, List<CultureCloudVenueSeatEntity>> collect2 = (Map<String, List<CultureCloudVenueSeatEntity>>) seatList
				.parallelStream().collect(groupingBy(CultureCloudVenueSeatEntity::getSeatArea));
		List<String> kyList = collect2.keySet()
	            .stream()
	            .collect(Collectors.toList());
		for(String key : collect2.keySet()) {
			Map<Integer, List<CultureCloudVenueSeatEntity>> collect3 = (Map<Integer, List<CultureCloudVenueSeatEntity>>) collect2.get(key)
					.parallelStream().collect(groupingBy(CultureCloudVenueSeatEntity::getSeatRow));
			Map<Integer,List<CultureCloudVenueSeatForm>> seatMap = new HashMap<>();
			for(Integer row : collect3.keySet()) {
				List<CultureCloudVenueSeatEntity> venueSeatList = collect3.get(row);
				venueSeatList.sort(comparingInt(CultureCloudVenueSeatEntity::getSeatColumn));
				List<CultureCloudVenueSeatForm> list = new ArrayList<>();
				for(CultureCloudVenueSeatEntity venueSeat:venueSeatList) {
					CultureCloudVenueSeatForm venueSeatForm = new CultureCloudVenueSeatForm();
					BeanUtils.copyProperties(venueSeat, venueSeatForm);
					venueSeatForm.setSeatId(venueSeat.getId());
					list.add(venueSeatForm);
				}
				seatMap.put(row, list);
			}
			venueSeatMap.put(key, seatMap);
			seatAndEvent.getSeat().put(key, seatMap);
		}
	}
	

}
