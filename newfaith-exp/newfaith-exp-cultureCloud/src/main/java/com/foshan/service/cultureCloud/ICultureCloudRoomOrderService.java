package com.foshan.service.cultureCloud;

import com.foshan.form.cultureCloud.request.CultureCloudRoomOrderReq;
import com.foshan.form.cultureCloud.request.CultureCloudWebRoomOrderReq;
import com.foshan.form.cultureCloud.response.cultureCloudRoomOrder.GetCultureCloudRoomOrderListRes;
import com.foshan.form.request.BasePageRequest;
import com.foshan.form.response.IResponse;

public interface ICultureCloudRoomOrderService {

    IResponse roomOrderCheckIndex(CultureCloudRoomOrderReq req);

    IResponse roomOrderIndex(CultureCloudRoomOrderReq req);

    IResponse roomOrderHistoryIndex(CultureCloudRoomOrderReq req);

    IResponse webUserRoomOrder(CultureCloudWebRoomOrderReq req);

    GetCultureCloudRoomOrderListRes webUserCheckRoomOrder(CultureCloudRoomOrderReq req);

    IResponse cancelRoomOrder(CultureCloudRoomOrderReq req);

    IResponse checkRoomOrder(CultureCloudRoomOrderReq req);
}
